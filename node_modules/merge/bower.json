{"name": "merge", "version": "1.2.1", "homepage": "https://github.com/yeikos/js.merge", "authors": ["yeikos <<EMAIL>>"], "description": "Merge multiple objects into one, optionally creating a new cloned object. Similar to the jQuery.extend but more flexible. Works in Node.js and the browser.", "main": "merge.js", "keywords": ["merge", "recursive", "extend", "clone", "object", "browser"], "license": "MIT", "ignore": ["tests"]}