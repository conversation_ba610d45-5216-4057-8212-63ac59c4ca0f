{"systemParams": "darwin-arm64-93", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@dcloudio/types@*", "@dcloudio/uni-app-plus@^2.0.0-27520200518001", "@dcloudio/uni-automator@^2.0.0-27520200518001", "@dcloudio/uni-cli-shared@^2.0.0-27520200518001", "@dcloudio/uni-h5@^2.0.0-27520200518001", "@dcloudio/uni-helper-json@*", "@dcloudio/uni-migration@^2.0.0-27520200518001", "@dcloudio/uni-mp-alipay@^2.0.0-27520200518001", "@dcloudio/uni-mp-baidu@^2.0.0-27520200518001", "@dcloudio/uni-mp-qq@^2.0.0-27520200518001", "@dcloudio/uni-mp-toutiao@^2.0.0-27520200518001", "@dcloudio/uni-mp-weixin@^2.0.0-27520200518001", "@dcloudio/uni-quickapp-native@^2.0.0-27520200518001", "@dcloudio/uni-quickapp-webview@^2.0.0-27520200518001", "@dcloudio/uni-stat@^2.0.0-27520200518001", "@dcloudio/uni-template-compiler@^2.0.0-27520200518001", "@dcloudio/vue-cli-plugin-hbuilderx@^2.0.0-27520200518001", "@dcloudio/vue-cli-plugin-uni-optimize@^2.0.0-27520200518001", "@dcloudio/vue-cli-plugin-uni@^2.0.0-27520200518001", "@dcloudio/webpack-uni-mp-loader@^2.0.0-27520200518001", "@dcloudio/webpack-uni-pages-loader@^2.0.0-27520200518001", "@vue/cli-plugin-babel@~4.3.0", "@vue/cli-service@~4.3.0", "babel-plugin-import@^1.11.0", "core-js@^3.6.4", "cross-env@^7.0.2", "flyio@^0.6.2", "jest@^25.4.0", "mini-types@*", "miniprogram-api-typings@*", "postcss-comment@^2.0.0", "regenerator-runtime@^0.12.1", "sass-loader@^10.4.1", "sass@1.32.13", "uview-ui@2.x", "vue-template-compiler@^2.6.11", "vue@^2.6.11", "vuex@^3.2.0"], "lockfileEntries": {"@babel/code-frame@^7.0.0": "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.8.3.tgz", "@babel/code-frame@^7.8.3": "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.8.3.tgz", "@babel/compat-data@^7.9.6": "https://registry.npmmirror.com/@babel/compat-data/download/@babel/compat-data-7.9.6.tgz?cache=0&sync_timestamp=1588185911086&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fcompat-data%2Fdownload%2F%40babel%2Fcompat-data-7.9.6.tgz", "@babel/core@^7.1.0": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.9.6.tgz", "@babel/core@^7.3.3": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.9.6.tgz", "@babel/core@^7.3.4": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.9.6.tgz", "@babel/core@^7.7.5": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.9.6.tgz", "@babel/core@^7.9.0": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.9.6.tgz", "@babel/generator@^7.9.6": "https://registry.npmmirror.com/@babel/generator/download/@babel/generator-7.9.6.tgz?cache=0&sync_timestamp=1588187312464&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fgenerator%2Fdownload%2F%40babel%2Fgenerator-7.9.6.tgz", "@babel/helper-annotate-as-pure@^7.8.3": "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.8.3.tgz", "@babel/helper-builder-binary-assignment-operator-visitor@^7.8.3": "https://registry.npmmirror.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-builder-binary-assignment-operator-visitor%2Fdownload%2F%40babel%2Fhelper-builder-binary-assignment-operator-visitor-7.8.3.tgz", "@babel/helper-compilation-targets@^7.8.7": "https://registry.npmmirror.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.9.6.tgz?cache=0&sync_timestamp=1588185905418&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-compilation-targets%2Fdownload%2F%40babel%2Fhelper-compilation-targets-7.9.6.tgz", "@babel/helper-compilation-targets@^7.9.6": "https://registry.npmmirror.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.9.6.tgz?cache=0&sync_timestamp=1588185905418&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-compilation-targets%2Fdownload%2F%40babel%2Fhelper-compilation-targets-7.9.6.tgz", "@babel/helper-create-class-features-plugin@^7.8.3": "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.9.6.tgz", "@babel/helper-create-regexp-features-plugin@^7.8.3": "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.8.8.tgz", "@babel/helper-create-regexp-features-plugin@^7.8.8": "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.8.8.tgz", "@babel/helper-define-map@^7.8.3": "https://registry.npmmirror.com/@babel/helper-define-map/download/@babel/helper-define-map-7.8.3.tgz", "@babel/helper-explode-assignable-expression@^7.8.3": "https://registry.npmmirror.com/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.8.3.tgz", "@babel/helper-function-name@^7.8.3": "https://registry.npmmirror.com/@babel/helper-function-name/download/@babel/helper-function-name-7.9.5.tgz", "@babel/helper-function-name@^7.9.5": "https://registry.npmmirror.com/@babel/helper-function-name/download/@babel/helper-function-name-7.9.5.tgz", "@babel/helper-get-function-arity@^7.8.3": "https://registry.npmmirror.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.8.3.tgz", "@babel/helper-hoist-variables@^7.8.3": "https://registry.npmmirror.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.8.3.tgz", "@babel/helper-member-expression-to-functions@^7.8.3": "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.8.3.tgz", "@babel/helper-module-imports@^7.0.0": "https://registry.npmmirror.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.8.3.tgz", "@babel/helper-module-imports@^7.8.3": "https://registry.npmmirror.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.8.3.tgz", "@babel/helper-module-transforms@^7.9.0": "https://registry.npmmirror.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.9.0.tgz", "@babel/helper-optimise-call-expression@^7.8.3": "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.8.3.tgz", "@babel/helper-plugin-utils@^7.0.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.8.3.tgz", "@babel/helper-plugin-utils@^7.8.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.8.3.tgz", "@babel/helper-plugin-utils@^7.8.3": "https://registry.npmmirror.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.8.3.tgz", "@babel/helper-regex@^7.8.3": "https://registry.npmmirror.com/@babel/helper-regex/download/@babel/helper-regex-7.8.3.tgz?cache=0&sync_timestamp=1578951938163&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-regex%2Fdownload%2F%40babel%2Fhelper-regex-7.8.3.tgz", "@babel/helper-remap-async-to-generator@^7.8.3": "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.8.3.tgz", "@babel/helper-replace-supers@^7.8.3": "https://registry.npmmirror.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.9.6.tgz", "@babel/helper-replace-supers@^7.8.6": "https://registry.npmmirror.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.9.6.tgz", "@babel/helper-replace-supers@^7.9.6": "https://registry.npmmirror.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.9.6.tgz", "@babel/helper-simple-access@^7.8.3": "https://registry.npmmirror.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.8.3.tgz", "@babel/helper-split-export-declaration@^7.8.3": "https://registry.npmmirror.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.8.3.tgz", "@babel/helper-validator-identifier@^7.9.0": "https://registry.npmmirror.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.9.5.tgz", "@babel/helper-validator-identifier@^7.9.5": "https://registry.npmmirror.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.9.5.tgz", "@babel/helper-wrap-function@^7.8.3": "https://registry.npmmirror.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.8.3.tgz", "@babel/helpers@^7.9.6": "https://registry.npmmirror.com/@babel/helpers/download/@babel/helpers-7.9.6.tgz?cache=0&sync_timestamp=1588185908061&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelpers%2Fdownload%2F%40babel%2Fhelpers-7.9.6.tgz", "@babel/highlight@^7.8.3": "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.9.0.tgz", "@babel/parser@^7.1.0": "https://registry.npmmirror.com/@babel/parser/download/@babel/parser-7.9.6.tgz", "@babel/parser@^7.3.3": "https://registry.npmmirror.com/@babel/parser/download/@babel/parser-7.9.6.tgz", "@babel/parser@^7.8.6": "https://registry.npmmirror.com/@babel/parser/download/@babel/parser-7.9.6.tgz", "@babel/parser@^7.9.6": "https://registry.npmmirror.com/@babel/parser/download/@babel/parser-7.9.6.tgz", "@babel/plugin-proposal-async-generator-functions@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-async-generator-functions%2Fdownload%2F%40babel%2Fplugin-proposal-async-generator-functions-7.8.3.tgz", "@babel/plugin-proposal-class-properties@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.8.3.tgz?cache=0&sync_timestamp=1578951896490&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-class-properties%2Fdownload%2F%40babel%2Fplugin-proposal-class-properties-7.8.3.tgz", "@babel/plugin-proposal-decorators@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-decorators%2Fdownload%2F%40babel%2Fplugin-proposal-decorators-7.8.3.tgz", "@babel/plugin-proposal-dynamic-import@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.8.3.tgz", "@babel/plugin-proposal-json-strings@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-json-strings%2Fdownload%2F%40babel%2Fplugin-proposal-json-strings-7.8.3.tgz", "@babel/plugin-proposal-nullish-coalescing-operator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.8.3.tgz?cache=0&sync_timestamp=1578952594995&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-nullish-coalescing-operator%2Fdownload%2F%40babel%2Fplugin-proposal-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-proposal-numeric-separator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.8.3.tgz", "@babel/plugin-proposal-object-rest-spread@^7.9.6": "https://registry.npmmirror.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.9.6.tgz?cache=0&sync_timestamp=1588185906386&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-object-rest-spread%2Fdownload%2F%40babel%2Fplugin-proposal-object-rest-spread-7.9.6.tgz", "@babel/plugin-proposal-optional-catch-binding@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-optional-catch-binding%2Fdownload%2F%40babel%2Fplugin-proposal-optional-catch-binding-7.8.3.tgz", "@babel/plugin-proposal-optional-chaining@^7.9.0": "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.9.0.tgz", "@babel/plugin-proposal-unicode-property-regex@^7.4.4": "https://registry.npmmirror.com/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.8.8.tgz?cache=0&sync_timestamp=1584039006999&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-unicode-property-regex%2Fdownload%2F%40babel%2Fplugin-proposal-unicode-property-regex-7.8.8.tgz", "@babel/plugin-proposal-unicode-property-regex@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.8.8.tgz?cache=0&sync_timestamp=1584039006999&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-unicode-property-regex%2Fdownload%2F%40babel%2Fplugin-proposal-unicode-property-regex-7.8.8.tgz", "@babel/plugin-syntax-async-generators@^7.8.0": "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz", "@babel/plugin-syntax-async-generators@^7.8.4": "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz", "@babel/plugin-syntax-bigint@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz", "@babel/plugin-syntax-class-properties@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.8.3.tgz", "@babel/plugin-syntax-decorators@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-decorators%2Fdownload%2F%40babel%2Fplugin-syntax-decorators-7.8.3.tgz", "@babel/plugin-syntax-dynamic-import@^7.8.0": "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz", "@babel/plugin-syntax-dynamic-import@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz", "@babel/plugin-syntax-json-strings@^7.8.0": "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz", "@babel/plugin-syntax-json-strings@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz", "@babel/plugin-syntax-jsx@^7.2.0": "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.8.3.tgz", "@babel/plugin-syntax-jsx@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.8.3.tgz", "@babel/plugin-syntax-logical-assignment-operators@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.8.3.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.0": "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-numeric-separator@^7.8.0": "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.8.3.tgz", "@babel/plugin-syntax-numeric-separator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.8.3.tgz", "@babel/plugin-syntax-object-rest-spread@^7.8.0": "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz?cache=0&sync_timestamp=1578950070697&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-object-rest-spread%2Fdownload%2F%40babel%2Fplugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-object-rest-spread@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz?cache=0&sync_timestamp=1578950070697&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-object-rest-spread%2Fdownload%2F%40babel%2Fplugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-optional-catch-binding@^7.8.0": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz", "@babel/plugin-syntax-optional-catch-binding@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.8.0": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz?cache=0&sync_timestamp=1578952519472&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-optional-chaining%2Fdownload%2F%40babel%2Fplugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz?cache=0&sync_timestamp=1578952519472&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-optional-chaining%2Fdownload%2F%40babel%2Fplugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-top-level-await@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.8.3.tgz?cache=0&sync_timestamp=1578952595485&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-top-level-await%2Fdownload%2F%40babel%2Fplugin-syntax-top-level-await-7.8.3.tgz", "@babel/plugin-transform-arrow-functions@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.8.3.tgz", "@babel/plugin-transform-async-to-generator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.8.3.tgz", "@babel/plugin-transform-block-scoped-functions@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.8.3.tgz?cache=0&sync_timestamp=1578951934748&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-block-scoped-functions%2Fdownload%2F%40babel%2Fplugin-transform-block-scoped-functions-7.8.3.tgz", "@babel/plugin-transform-block-scoping@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.8.3.tgz", "@babel/plugin-transform-classes@^7.9.5": "https://registry.npmmirror.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.9.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-classes%2Fdownload%2F%40babel%2Fplugin-transform-classes-7.9.5.tgz", "@babel/plugin-transform-computed-properties@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.8.3.tgz", "@babel/plugin-transform-destructuring@^7.9.5": "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.9.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-destructuring%2Fdownload%2F%40babel%2Fplugin-transform-destructuring-7.9.5.tgz", "@babel/plugin-transform-dotall-regex@^7.4.4": "https://registry.npmmirror.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.8.3.tgz", "@babel/plugin-transform-dotall-regex@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.8.3.tgz", "@babel/plugin-transform-duplicate-keys@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.8.3.tgz", "@babel/plugin-transform-exponentiation-operator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.8.3.tgz", "@babel/plugin-transform-for-of@^7.9.0": "https://registry.npmmirror.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.9.0.tgz", "@babel/plugin-transform-function-name@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.8.3.tgz", "@babel/plugin-transform-literals@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.8.3.tgz", "@babel/plugin-transform-member-expression-literals@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.8.3.tgz", "@babel/plugin-transform-modules-amd@^7.9.6": "https://registry.npmmirror.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.9.6.tgz?cache=0&sync_timestamp=1588185902641&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-modules-amd%2Fdownload%2F%40babel%2Fplugin-transform-modules-amd-7.9.6.tgz", "@babel/plugin-transform-modules-commonjs@^7.9.6": "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.9.6.tgz?cache=0&sync_timestamp=1588185907042&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-modules-commonjs%2Fdownload%2F%40babel%2Fplugin-transform-modules-commonjs-7.9.6.tgz", "@babel/plugin-transform-modules-systemjs@^7.9.6": "https://registry.npmmirror.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.9.6.tgz?cache=0&sync_timestamp=1588185909511&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-modules-systemjs%2Fdownload%2F%40babel%2Fplugin-transform-modules-systemjs-7.9.6.tgz", "@babel/plugin-transform-modules-umd@^7.9.0": "https://registry.npmmirror.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.9.0.tgz?cache=0&sync_timestamp=1584746128785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-modules-umd%2Fdownload%2F%40babel%2Fplugin-transform-modules-umd-7.9.0.tgz", "@babel/plugin-transform-named-capturing-groups-regex@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.8.3.tgz", "@babel/plugin-transform-new-target@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.8.3.tgz", "@babel/plugin-transform-object-super@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.8.3.tgz", "@babel/plugin-transform-parameters@^7.9.5": "https://registry.npmmirror.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.9.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-parameters%2Fdownload%2F%40babel%2Fplugin-transform-parameters-7.9.5.tgz", "@babel/plugin-transform-property-literals@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.8.3.tgz", "@babel/plugin-transform-regenerator@^7.8.7": "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.8.7.tgz", "@babel/plugin-transform-reserved-words@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.8.3.tgz", "@babel/plugin-transform-runtime@^7.9.0": "https://registry.npmmirror.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.9.6.tgz", "@babel/plugin-transform-shorthand-properties@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.8.3.tgz", "@babel/plugin-transform-spread@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.8.3.tgz", "@babel/plugin-transform-sticky-regex@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-sticky-regex%2Fdownload%2F%40babel%2Fplugin-transform-sticky-regex-7.8.3.tgz", "@babel/plugin-transform-template-literals@^7.2.0": "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.8.3.tgz", "@babel/plugin-transform-template-literals@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.8.3.tgz", "@babel/plugin-transform-typeof-symbol@^7.8.4": "https://registry.npmmirror.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.8.4.tgz", "@babel/plugin-transform-unicode-regex@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.8.3.tgz", "@babel/preset-env@^7.3.1": "https://registry.npmmirror.com/@babel/preset-env/download/@babel/preset-env-7.9.6.tgz", "@babel/preset-env@^7.9.0": "https://registry.npmmirror.com/@babel/preset-env/download/@babel/preset-env-7.9.6.tgz", "@babel/preset-modules@^0.1.3": "https://registry.npmmirror.com/@babel/preset-modules/download/@babel/preset-modules-0.1.3.tgz", "@babel/register@^7.0.0": "https://registry.npmmirror.com/@babel/register/download/@babel/register-7.9.0.tgz", "@babel/runtime@^7.0.0": "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.9.6.tgz?cache=0&sync_timestamp=1588185905751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.9.6.tgz", "@babel/runtime@^7.3.1": "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.9.6.tgz?cache=0&sync_timestamp=1588185905751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.9.6.tgz", "@babel/runtime@^7.8.4": "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.9.6.tgz?cache=0&sync_timestamp=1588185905751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.9.6.tgz", "@babel/runtime@^7.9.2": "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.9.6.tgz?cache=0&sync_timestamp=1588185905751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.9.6.tgz", "@babel/template@^7.3.3": "https://registry.npmmirror.com/@babel/template/download/@babel/template-7.8.6.tgz", "@babel/template@^7.8.3": "https://registry.npmmirror.com/@babel/template/download/@babel/template-7.8.6.tgz", "@babel/template@^7.8.6": "https://registry.npmmirror.com/@babel/template/download/@babel/template-7.8.6.tgz", "@babel/traverse@^7.1.0": "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.9.6.tgz?cache=0&sync_timestamp=1588185904779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.9.6.tgz", "@babel/traverse@^7.3.3": "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.9.6.tgz?cache=0&sync_timestamp=1588185904779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.9.6.tgz", "@babel/traverse@^7.8.3": "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.9.6.tgz?cache=0&sync_timestamp=1588185904779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.9.6.tgz", "@babel/traverse@^7.9.6": "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.9.6.tgz?cache=0&sync_timestamp=1588185904779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.9.6.tgz", "@babel/types@^7.0.0": "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz", "@babel/types@^7.3.0": "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz", "@babel/types@^7.3.3": "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz", "@babel/types@^7.4.4": "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz", "@babel/types@^7.8.3": "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz", "@babel/types@^7.8.6": "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz", "@babel/types@^7.9.0": "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz", "@babel/types@^7.9.5": "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz", "@babel/types@^7.9.6": "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz", "@bcoe/v8-coverage@^0.2.3": "https://registry.npmmirror.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz", "@cnakazawa/watch@^1.0.3": "https://registry.npmmirror.com/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz", "@dcloudio/types@*": "https://registry.npmmirror.com/@dcloudio/types/download/@dcloudio/types-1.0.0.tgz", "@dcloudio/uni-app-plus@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-app-plus/download/@dcloudio/uni-app-plus-2.0.0-27520200518001.tgz", "@dcloudio/uni-automator@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-automator/download/@dcloudio/uni-automator-2.0.0-27520200518001.tgz", "@dcloudio/uni-cli-shared@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-cli-shared/download/@dcloudio/uni-cli-shared-2.0.0-27520200518001.tgz", "@dcloudio/uni-h5@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-h5/download/@dcloudio/uni-h5-2.0.0-27520200518001.tgz", "@dcloudio/uni-helper-json@*": "https://registry.npmmirror.com/@dcloudio/uni-helper-json/download/@dcloudio/uni-helper-json-1.0.5.tgz", "@dcloudio/uni-migration@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-migration/download/@dcloudio/uni-migration-2.0.0-27520200518001.tgz?cache=0&sync_timestamp=1590046603392&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40dcloudio%2Funi-migration%2Fdownload%2F%40dcloudio%2Funi-migration-2.0.0-27520200518001.tgz", "@dcloudio/uni-mp-alipay@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-mp-alipay/download/@dcloudio/uni-mp-alipay-2.0.0-27520200518001.tgz", "@dcloudio/uni-mp-baidu@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-mp-baidu/download/@dcloudio/uni-mp-baidu-2.0.0-27520200518001.tgz", "@dcloudio/uni-mp-qq@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-mp-qq/download/@dcloudio/uni-mp-qq-2.0.0-27520200518001.tgz", "@dcloudio/uni-mp-toutiao@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-mp-toutiao/download/@dcloudio/uni-mp-toutiao-2.0.0-27520200518001.tgz", "@dcloudio/uni-mp-weixin@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-mp-weixin/download/@dcloudio/uni-mp-weixin-2.0.0-27520200518001.tgz", "@dcloudio/uni-quickapp-native@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-quickapp-native/download/@dcloudio/uni-quickapp-native-2.0.0-27520200518001.tgz", "@dcloudio/uni-quickapp-webview@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-quickapp-webview/download/@dcloudio/uni-quickapp-webview-2.0.0-27520200518001.tgz", "@dcloudio/uni-stat@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-stat/download/@dcloudio/uni-stat-2.0.0-27520200518001.tgz", "@dcloudio/uni-template-compiler@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/uni-template-compiler/download/@dcloudio/uni-template-compiler-2.0.0-27520200518001.tgz", "@dcloudio/vue-cli-plugin-hbuilderx@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/vue-cli-plugin-hbuilderx/download/@dcloudio/vue-cli-plugin-hbuilderx-2.0.0-27520200518001.tgz", "@dcloudio/vue-cli-plugin-uni-optimize@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/vue-cli-plugin-uni-optimize/download/@dcloudio/vue-cli-plugin-uni-optimize-2.0.0-27520200518001.tgz", "@dcloudio/vue-cli-plugin-uni@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/vue-cli-plugin-uni/download/@dcloudio/vue-cli-plugin-uni-2.0.0-27520200518001.tgz", "@dcloudio/webpack-uni-mp-loader@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/webpack-uni-mp-loader/download/@dcloudio/webpack-uni-mp-loader-2.0.0-27520200518001.tgz", "@dcloudio/webpack-uni-pages-loader@^2.0.0-27520200518001": "https://registry.npmmirror.com/@dcloudio/webpack-uni-pages-loader/download/@dcloudio/webpack-uni-pages-loader-2.0.0-27520200518001.tgz?cache=0&sync_timestamp=1590046654411&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40dcloudio%2Fwebpack-uni-pages-loader%2Fdownload%2F%40dcloudio%2Fwebpack-uni-pages-loader-2.0.0-27520200518001.tgz", "@hap-toolkit/aaptjs@^1.0.0": "https://registry.npmmirror.com/@hap-toolkit/aaptjs/download/@hap-toolkit/aaptjs-1.0.0.tgz", "@hap-toolkit/compiler@0.6.15": "https://registry.npmmirror.com/@hap-toolkit/compiler/download/@hap-toolkit/compiler-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fcompiler%2Fdownload%2F%40hap-toolkit%2Fcompiler-0.6.15.tgz", "@hap-toolkit/compiler@^0.6.13": "https://registry.npmmirror.com/@hap-toolkit/compiler/download/@hap-toolkit/compiler-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fcompiler%2Fdownload%2F%40hap-toolkit%2Fcompiler-0.6.15.tgz", "@hap-toolkit/debugger@^0.6.13": "https://registry.npmmirror.com/@hap-toolkit/debugger/download/@hap-toolkit/debugger-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fdebugger%2Fdownload%2F%40hap-toolkit%2Fdebugger-0.6.15.tgz", "@hap-toolkit/dsl-vue@0.6.13": "https://registry.npmmirror.com/@hap-toolkit/dsl-vue/download/@hap-toolkit/dsl-vue-0.6.13.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fdsl-vue%2Fdownload%2F%40hap-toolkit%2Fdsl-vue-0.6.13.tgz", "@hap-toolkit/packager@0.6.13": "https://registry.npmmirror.com/@hap-toolkit/packager/download/@hap-toolkit/packager-0.6.13.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fpackager%2Fdownload%2F%40hap-toolkit%2Fpackager-0.6.13.tgz", "@hap-toolkit/packager@^0.6.13": "https://registry.npmmirror.com/@hap-toolkit/packager/download/@hap-toolkit/packager-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fpackager%2Fdownload%2F%40hap-toolkit%2Fpackager-0.6.15.tgz", "@hap-toolkit/server@0.6.13": "https://registry.npmmirror.com/@hap-toolkit/server/download/@hap-toolkit/server-0.6.13.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fserver%2Fdownload%2F%40hap-toolkit%2Fserver-0.6.13.tgz", "@hap-toolkit/shared-utils@0.6.15": "https://registry.npmmirror.com/@hap-toolkit/shared-utils/download/@hap-toolkit/shared-utils-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fshared-utils%2Fdownload%2F%40hap-toolkit%2Fshared-utils-0.6.15.tgz", "@hap-toolkit/shared-utils@^0.6.13": "https://registry.npmmirror.com/@hap-toolkit/shared-utils/download/@hap-toolkit/shared-utils-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fshared-utils%2Fdownload%2F%40hap-toolkit%2Fshared-utils-0.6.15.tgz", "@hapi/address@2.x.x": "https://registry.npmmirror.com/@hapi/address/download/@hapi/address-2.1.4.tgz?cache=0&sync_timestamp=1584171913757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hapi%2Faddress%2Fdownload%2F%40hapi%2Faddress-2.1.4.tgz", "@hapi/bourne@1.x.x": "https://registry.npmmirror.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz", "@hapi/hoek@8.x.x": "https://registry.npmmirror.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz", "@hapi/hoek@^8.3.0": "https://registry.npmmirror.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz", "@hapi/joi@^15.0.1": "https://registry.npmmirror.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz", "@hapi/topo@3.x.x": "https://registry.npmmirror.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz", "@intervolga/optimize-cssnano-plugin@^1.0.5": "https://registry.npmmirror.com/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz", "@istanbuljs/load-nyc-config@^1.0.0": "https://registry.npmmirror.com/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz", "@istanbuljs/schema@^0.1.2": "https://registry.npmmirror.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.2.tgz", "@jest/console@^25.5.0": "https://registry.npmmirror.com/@jest/console/download/@jest/console-25.5.0.tgz?cache=0&sync_timestamp=1588675319681&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Fconsole%2Fdownload%2F%40jest%2Fconsole-25.5.0.tgz", "@jest/core@^25.5.4": "https://registry.npmmirror.com/@jest/core/download/@jest/core-25.5.4.tgz?cache=0&sync_timestamp=1588675318326&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Fcore%2Fdownload%2F%40jest%2Fcore-25.5.4.tgz", "@jest/environment@^25.5.0": "https://registry.npmmirror.com/@jest/environment/download/@jest/environment-25.5.0.tgz?cache=0&sync_timestamp=1588675332085&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Fenvironment%2Fdownload%2F%40jest%2Fenvironment-25.5.0.tgz", "@jest/fake-timers@^25.5.0": "https://registry.npmmirror.com/@jest/fake-timers/download/@jest/fake-timers-25.5.0.tgz?cache=0&sync_timestamp=1588675324067&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Ffake-timers%2Fdownload%2F%40jest%2Ffake-timers-25.5.0.tgz", "@jest/globals@^25.5.2": "https://registry.npmmirror.com/@jest/globals/download/@jest/globals-25.5.2.tgz?cache=0&sync_timestamp=1588675303209&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Fglobals%2Fdownload%2F%40jest%2Fglobals-25.5.2.tgz", "@jest/reporters@^25.5.1": "https://registry.npmmirror.com/@jest/reporters/download/@jest/reporters-25.5.1.tgz?cache=0&sync_timestamp=1588675337559&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Freporters%2Fdownload%2F%40jest%2Freporters-25.5.1.tgz", "@jest/source-map@^25.5.0": "https://registry.npmmirror.com/@jest/source-map/download/@jest/source-map-25.5.0.tgz", "@jest/test-result@^25.5.0": "https://registry.npmmirror.com/@jest/test-result/download/@jest/test-result-25.5.0.tgz", "@jest/test-sequencer@^25.5.4": "https://registry.npmmirror.com/@jest/test-sequencer/download/@jest/test-sequencer-25.5.4.tgz?cache=0&sync_timestamp=1588675313073&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Ftest-sequencer%2Fdownload%2F%40jest%2Ftest-sequencer-25.5.4.tgz", "@jest/transform@^25.5.1": "https://registry.npmmirror.com/@jest/transform/download/@jest/transform-25.5.1.tgz", "@jest/types@^25.5.0": "https://registry.npmmirror.com/@jest/types/download/@jest/types-25.5.0.tgz?cache=0&sync_timestamp=1588675411534&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Ftypes%2Fdownload%2F%40jest%2Ftypes-25.5.0.tgz", "@mrmlnc/readdir-enhanced@^2.2.1": "https://registry.npmmirror.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz", "@nodelib/fs.stat@^1.1.2": "https://registry.npmmirror.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz", "@sinonjs/commons@^1.7.0": "https://registry.npmmirror.com/@sinonjs/commons/download/@sinonjs/commons-1.8.0.tgz?cache=0&sync_timestamp=1589985579769&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40sinonjs%2Fcommons%2Fdownload%2F%40sinonjs%2Fcommons-1.8.0.tgz", "@soda/friendly-errors-webpack-plugin@^1.7.1": "https://registry.npmmirror.com/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.7.1.tgz", "@soda/get-current-script@^1.0.0": "https://registry.npmmirror.com/@soda/get-current-script/download/@soda/get-current-script-1.0.0.tgz", "@types/babel__core@^7.1.7": "https://registry.npmmirror.com/@types/babel__core/download/@types/babel__core-7.1.7.tgz", "@types/babel__generator@*": "https://registry.npmmirror.com/@types/babel__generator/download/@types/babel__generator-7.6.1.tgz?cache=0&sync_timestamp=1588199664093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fbabel__generator%2Fdownload%2F%40types%2Fbabel__generator-7.6.1.tgz", "@types/babel__template@*": "https://registry.npmmirror.com/@types/babel__template/download/@types/babel__template-7.0.2.tgz?cache=0&sync_timestamp=1588199666194&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fbabel__template%2Fdownload%2F%40types%2Fbabel__template-7.0.2.tgz", "@types/babel__traverse@*": "https://registry.npmmirror.com/@types/babel__traverse/download/@types/babel__traverse-7.0.11.tgz?cache=0&sync_timestamp=1588199665939&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fbabel__traverse%2Fdownload%2F%40types%2Fbabel__traverse-7.0.11.tgz", "@types/babel__traverse@^7.0.6": "https://registry.npmmirror.com/@types/babel__traverse/download/@types/babel__traverse-7.0.11.tgz?cache=0&sync_timestamp=1588199665939&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fbabel__traverse%2Fdownload%2F%40types%2Fbabel__traverse-7.0.11.tgz", "@types/color-name@^1.1.1": "https://registry.npmmirror.com/@types/color-name/download/@types/color-name-1.1.1.tgz", "@types/events@*": "https://registry.npmmirror.com/@types/events/download/@types/events-3.0.0.tgz?cache=0&sync_timestamp=1588200013267&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fevents%2Fdownload%2F%40types%2Fevents-3.0.0.tgz", "@types/formidable@^1.0.31": "https://registry.npmmirror.com/@types/formidable/download/@types/formidable-1.0.31.tgz", "@types/glob@^7.1.1": "https://registry.npmmirror.com/@types/glob/download/@types/glob-7.1.1.tgz", "@types/graceful-fs@^4.1.2": "https://registry.npmmirror.com/@types/graceful-fs/download/@types/graceful-fs-4.1.3.tgz", "@types/istanbul-lib-coverage@*": "https://registry.npmmirror.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.2.tgz", "@types/istanbul-lib-coverage@^2.0.0": "https://registry.npmmirror.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.2.tgz", "@types/istanbul-lib-coverage@^2.0.1": "https://registry.npmmirror.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.2.tgz", "@types/istanbul-lib-report@*": "https://registry.npmmirror.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz?cache=0&sync_timestamp=1588227930185&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fistanbul-lib-report%2Fdownload%2F%40types%2Fistanbul-lib-report-3.0.0.tgz", "@types/istanbul-reports@^1.1.1": "https://registry.npmmirror.com/@types/istanbul-reports/download/@types/istanbul-reports-1.1.2.tgz", "@types/json-schema@^7.0.8": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/minimatch@*": "https://registry.npmmirror.com/@types/minimatch/download/@types/minimatch-3.0.3.tgz", "@types/node@*": "https://registry.npmmirror.com/@types/node/download/@types/node-14.0.4.tgz?cache=0&sync_timestamp=1589930215015&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-14.0.4.tgz", "@types/normalize-package-data@^2.4.0": "https://registry.npmmirror.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz", "@types/prettier@^1.19.0": "https://registry.npmmirror.com/@types/prettier/download/@types/prettier-1.19.1.tgz?cache=0&sync_timestamp=1588202414050&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fprettier%2Fdownload%2F%40types%2Fprettier-1.19.1.tgz", "@types/q@^1.5.1": "https://registry.npmmirror.com/@types/q/download/@types/q-1.5.4.tgz", "@types/stack-utils@^1.0.1": "https://registry.npmmirror.com/@types/stack-utils/download/@types/stack-utils-1.0.1.tgz", "@types/yargs-parser@*": "https://registry.npmmirror.com/@types/yargs-parser/download/@types/yargs-parser-15.0.0.tgz?cache=0&sync_timestamp=1588203262235&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fyargs-parser%2Fdownload%2F%40types%2Fyargs-parser-15.0.0.tgz", "@types/yargs@^15.0.0": "https://registry.npmmirror.com/@types/yargs/download/@types/yargs-15.0.5.tgz?cache=0&sync_timestamp=1589406630222&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fyargs%2Fdownload%2F%40types%2Fyargs-15.0.5.tgz", "@vue/babel-helper-vue-jsx-merge-props@^1.0.0": "https://registry.npmmirror.com/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.0.0.tgz", "@vue/babel-plugin-transform-vue-jsx@^1.1.2": "https://registry.npmmirror.com/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.1.2.tgz", "@vue/babel-preset-app@^4.3.1": "https://registry.npmmirror.com/@vue/babel-preset-app/download/@vue/babel-preset-app-4.3.1.tgz", "@vue/babel-preset-jsx@^1.1.2": "https://registry.npmmirror.com/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.1.2.tgz", "@vue/babel-sugar-functional-vue@^1.1.2": "https://registry.npmmirror.com/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.1.2.tgz", "@vue/babel-sugar-inject-h@^1.1.2": "https://registry.npmmirror.com/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.1.2.tgz", "@vue/babel-sugar-v-model@^1.1.2": "https://registry.npmmirror.com/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.1.2.tgz", "@vue/babel-sugar-v-on@^1.1.2": "https://registry.npmmirror.com/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.1.2.tgz", "@vue/cli-overlay@^4.3.1": "https://registry.npmmirror.com/@vue/cli-overlay/download/@vue/cli-overlay-4.3.1.tgz", "@vue/cli-plugin-babel@~4.3.0": "https://registry.npmmirror.com/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-4.3.1.tgz", "@vue/cli-plugin-router@^4.3.1": "https://registry.npmmirror.com/@vue/cli-plugin-router/download/@vue/cli-plugin-router-4.3.1.tgz", "@vue/cli-plugin-vuex@^4.3.1": "https://registry.npmmirror.com/@vue/cli-plugin-vuex/download/@vue/cli-plugin-vuex-4.3.1.tgz", "@vue/cli-service@~4.3.0": "https://registry.npmmirror.com/@vue/cli-service/download/@vue/cli-service-4.3.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-service%2Fdownload%2F%40vue%2Fcli-service-4.3.1.tgz", "@vue/cli-shared-utils@^4.3.1": "https://registry.npmmirror.com/@vue/cli-shared-utils/download/@vue/cli-shared-utils-4.3.1.tgz", "@vue/component-compiler-utils@^3.0.2": "https://registry.npmmirror.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.1.2.tgz", "@vue/component-compiler-utils@^3.1.0": "https://registry.npmmirror.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.1.2.tgz", "@vue/preload-webpack-plugin@^1.1.0": "https://registry.npmmirror.com/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.1.tgz", "@vue/web-component-wrapper@^1.2.0": "https://registry.npmmirror.com/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.2.0.tgz", "@webassemblyjs/ast@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fast%2Fdownload%2F%40webassemblyjs%2Fast-1.9.0.tgz", "@webassemblyjs/floating-point-hex-parser@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Ffloating-point-hex-parser%2Fdownload%2F%40webassemblyjs%2Ffloating-point-hex-parser-1.9.0.tgz", "@webassemblyjs/helper-api-error@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz", "@webassemblyjs/helper-buffer@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz?cache=0&sync_timestamp=1580600188490&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-buffer%2Fdownload%2F%40webassemblyjs%2Fhelper-buffer-1.9.0.tgz", "@webassemblyjs/helper-code-frame@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-code-frame%2Fdownload%2F%40webassemblyjs%2Fhelper-code-frame-1.9.0.tgz", "@webassemblyjs/helper-fsm@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-fsm%2Fdownload%2F%40webassemblyjs%2Fhelper-fsm-1.9.0.tgz", "@webassemblyjs/helper-module-context@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-module-context%2Fdownload%2F%40webassemblyjs%2Fhelper-module-context-1.9.0.tgz", "@webassemblyjs/helper-wasm-bytecode@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-wasm-bytecode%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-bytecode-1.9.0.tgz", "@webassemblyjs/helper-wasm-section@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-wasm-section%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-section-1.9.0.tgz", "@webassemblyjs/ieee754@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fieee754%2Fdownload%2F%40webassemblyjs%2Fieee754-1.9.0.tgz", "@webassemblyjs/leb128@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fleb128%2Fdownload%2F%40webassemblyjs%2Fleb128-1.9.0.tgz", "@webassemblyjs/utf8@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz", "@webassemblyjs/wasm-edit@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-edit%2Fdownload%2F%40webassemblyjs%2Fwasm-edit-1.9.0.tgz", "@webassemblyjs/wasm-gen@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-gen%2Fdownload%2F%40webassemblyjs%2Fwasm-gen-1.9.0.tgz", "@webassemblyjs/wasm-opt@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-opt%2Fdownload%2F%40webassemblyjs%2Fwasm-opt-1.9.0.tgz", "@webassemblyjs/wasm-parser@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-parser%2Fdownload%2F%40webassemblyjs%2Fwasm-parser-1.9.0.tgz", "@webassemblyjs/wast-parser@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwast-parser%2Fdownload%2F%40webassemblyjs%2Fwast-parser-1.9.0.tgz", "@webassemblyjs/wast-printer@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz?cache=0&sync_timestamp=1580599638157&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwast-printer%2Fdownload%2F%40webassemblyjs%2Fwast-printer-1.9.0.tgz", "@xtuc/ieee754@^1.2.0": "https://registry.npmmirror.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz", "@xtuc/long@4.2.2": "https://registry.npmmirror.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz", "aaptjs@^1.3.1": "https://registry.npmmirror.com/aaptjs/download/aaptjs-1.3.1.tgz", "abab@^2.0.0": "https://registry.npmmirror.com/abab/download/abab-2.0.3.tgz", "accepts@^1.3.5": "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz", "accepts@~1.3.4": "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz", "accepts@~1.3.5": "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz", "accepts@~1.3.7": "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz", "acorn-globals@^4.3.2": "https://registry.npmmirror.com/acorn-globals/download/acorn-globals-4.3.4.tgz", "acorn-walk@^6.0.1": "https://registry.npmmirror.com/acorn-walk/download/acorn-walk-6.2.0.tgz?cache=0&sync_timestamp=1581612804260&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn-walk%2Fdownload%2Facorn-walk-6.2.0.tgz", "acorn-walk@^7.1.1": "https://registry.npmmirror.com/acorn-walk/download/acorn-walk-7.1.1.tgz?cache=0&sync_timestamp=1581612804260&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn-walk%2Fdownload%2Facorn-walk-7.1.1.tgz", "acorn@^5.2.1": "https://registry.npmmirror.com/acorn/download/acorn-5.7.4.tgz?cache=0&sync_timestamp=1589007904036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-5.7.4.tgz", "acorn@^6.0.1": "https://registry.npmmirror.com/acorn/download/acorn-6.4.1.tgz?cache=0&sync_timestamp=1589007904036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-6.4.1.tgz", "acorn@^6.4.1": "https://registry.npmmirror.com/acorn/download/acorn-6.4.1.tgz?cache=0&sync_timestamp=1589007904036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-6.4.1.tgz", "acorn@^7.1.0": "https://registry.npmmirror.com/acorn/download/acorn-7.2.0.tgz?cache=0&sync_timestamp=1589007904036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-7.2.0.tgz", "acorn@^7.1.1": "https://registry.npmmirror.com/acorn/download/acorn-7.2.0.tgz?cache=0&sync_timestamp=1589007904036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-7.2.0.tgz", "adb-commander@^0.1.8": "https://registry.npmmirror.com/adb-commander/download/adb-commander-0.1.8.tgz", "adb-devices-emitter@^0.1.8": "https://registry.npmmirror.com/adb-devices-emitter/download/adb-devices-emitter-0.1.8.tgz", "adb-driver@^0.1.8": "https://registry.npmmirror.com/adb-driver/download/adb-driver-0.1.8.tgz", "address@^1.1.2": "https://registry.npmmirror.com/address/download/address-1.1.2.tgz", "after@0.8.2": "https://registry.npmmirror.com/after/download/after-0.8.2.tgz", "aggregate-error@^3.0.0": "https://registry.npmmirror.com/aggregate-error/download/aggregate-error-3.0.1.tgz?cache=0&sync_timestamp=1570167911603&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faggregate-error%2Fdownload%2Faggregate-error-3.0.1.tgz", "ajv-errors@^1.0.0": "https://registry.npmmirror.com/ajv-errors/download/ajv-errors-1.0.1.tgz", "ajv-keywords@^3.1.0": "https://registry.npmmirror.com/ajv-keywords/download/ajv-keywords-3.4.1.tgz", "ajv-keywords@^3.4.1": "https://registry.npmmirror.com/ajv-keywords/download/ajv-keywords-3.4.1.tgz", "ajv-keywords@^3.5.2": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv@^6.1.0": "https://registry.npmmirror.com/ajv/download/ajv-6.12.2.tgz?cache=0&sync_timestamp=1587338610933&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv%2Fdownload%2Fajv-6.12.2.tgz", "ajv@^6.10.2": "https://registry.npmmirror.com/ajv/download/ajv-6.12.2.tgz?cache=0&sync_timestamp=1587338610933&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv%2Fdownload%2Fajv-6.12.2.tgz", "ajv@^6.12.0": "https://registry.npmmirror.com/ajv/download/ajv-6.12.2.tgz?cache=0&sync_timestamp=1587338610933&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv%2Fdownload%2Fajv-6.12.2.tgz", "ajv@^6.12.5": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^6.5.5": "https://registry.npmmirror.com/ajv/download/ajv-6.12.2.tgz?cache=0&sync_timestamp=1587338610933&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv%2Fdownload%2Fajv-6.12.2.tgz", "alphanum-sort@^1.0.0": "https://registry.npmmirror.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz", "ansi-colors@^3.0.0": "https://registry.npmmirror.com/ansi-colors/download/ansi-colors-3.2.4.tgz", "ansi-escapes@^4.2.1": "https://registry.npmmirror.com/ansi-escapes/download/ansi-escapes-4.3.1.tgz", "ansi-html@0.0.7": "https://registry.npmmirror.com/ansi-html/download/ansi-html-0.0.7.tgz", "ansi-regex@^2.0.0": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-2.1.1.tgz", "ansi-regex@^3.0.0": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-3.0.0.tgz", "ansi-regex@^4.1.0": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-4.1.0.tgz", "ansi-regex@^5.0.0": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.0.tgz", "ansi-styles@^2.2.1": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-2.2.1.tgz", "ansi-styles@^3.2.0": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz", "ansi-styles@^3.2.1": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.2.1.tgz", "ansi-styles@^4.1.0": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.2.1.tgz", "any-promise@^1.0.0": "https://registry.npmmirror.com/any-promise/download/any-promise-1.3.0.tgz", "any-promise@^1.1.0": "https://registry.npmmirror.com/any-promise/download/any-promise-1.3.0.tgz", "anymatch@^2.0.0": "https://registry.npmmirror.com/anymatch/download/anymatch-2.0.0.tgz", "anymatch@^3.0.3": "https://registry.npmmirror.com/anymatch/download/anymatch-3.1.1.tgz", "anymatch@~3.1.1": "https://registry.npmmirror.com/anymatch/download/anymatch-3.1.1.tgz", "anymatch@~3.1.2": "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz", "aproba@^1.1.1": "https://registry.npmmirror.com/aproba/download/aproba-1.2.0.tgz", "arch@^2.1.1": "https://registry.npmmirror.com/arch/download/arch-2.1.2.tgz?cache=0&sync_timestamp=1589130903544&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farch%2Fdownload%2Farch-2.1.2.tgz", "argparse@^1.0.7": "https://registry.npmmirror.com/argparse/download/argparse-1.0.10.tgz", "arr-diff@^4.0.0": "https://registry.npmmirror.com/arr-diff/download/arr-diff-4.0.0.tgz", "arr-flatten@^1.1.0": "https://registry.npmmirror.com/arr-flatten/download/arr-flatten-1.1.0.tgz", "arr-union@^3.1.0": "https://registry.npmmirror.com/arr-union/download/arr-union-3.1.0.tgz", "array-equal@^1.0.0": "https://registry.npmmirror.com/array-equal/download/array-equal-1.0.0.tgz", "array-flatten@1.1.1": "https://registry.npmmirror.com/array-flatten/download/array-flatten-1.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-flatten%2Fdownload%2Farray-flatten-1.1.1.tgz", "array-flatten@^2.1.0": "https://registry.npmmirror.com/array-flatten/download/array-flatten-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-flatten%2Fdownload%2Farray-flatten-2.1.2.tgz", "array-union@^1.0.1": "https://registry.npmmirror.com/array-union/download/array-union-1.0.2.tgz", "array-union@^1.0.2": "https://registry.npmmirror.com/array-union/download/array-union-1.0.2.tgz", "array-uniq@^1.0.1": "https://registry.npmmirror.com/array-uniq/download/array-uniq-1.0.3.tgz", "array-unique@^0.3.2": "https://registry.npmmirror.com/array-unique/download/array-unique-0.3.2.tgz", "arraybuffer.slice@~0.0.7": "https://registry.npmmirror.com/arraybuffer.slice/download/arraybuffer.slice-0.0.7.tgz", "asn1.js@^4.0.0": "https://registry.npmmirror.com/asn1.js/download/asn1.js-4.10.1.tgz", "asn1@~0.2.3": "https://registry.npmmirror.com/asn1/download/asn1-0.2.4.tgz", "assert-plus@1.0.0": "https://registry.npmmirror.com/assert-plus/download/assert-plus-1.0.0.tgz", "assert-plus@^1.0.0": "https://registry.npmmirror.com/assert-plus/download/assert-plus-1.0.0.tgz", "assert@^1.1.1": "https://registry.npmmirror.com/assert/download/assert-1.5.0.tgz", "assign-symbols@^1.0.0": "https://registry.npmmirror.com/assign-symbols/download/assign-symbols-1.0.0.tgz", "astral-regex@^1.0.0": "https://registry.npmmirror.com/astral-regex/download/astral-regex-1.0.0.tgz", "async-each@^1.0.1": "https://registry.npmmirror.com/async-each/download/async-each-1.0.3.tgz", "async-limiter@~1.0.0": "https://registry.npmmirror.com/async-limiter/download/async-limiter-1.0.1.tgz?cache=0&sync_timestamp=1574272018408&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync-limiter%2Fdownload%2Fasync-limiter-1.0.1.tgz", "async@^2.6.2": "https://registry.npmmirror.com/async/download/async-2.6.3.tgz", "asynckit@^0.4.0": "https://registry.npmmirror.com/asynckit/download/asynckit-0.4.0.tgz", "atob@^2.1.2": "https://registry.npmmirror.com/atob/download/atob-2.1.2.tgz", "autoprefixer@^9.7.5": "https://registry.npmmirror.com/autoprefixer/download/autoprefixer-9.8.0.tgz", "aws-sign2@~0.7.0": "https://registry.npmmirror.com/aws-sign2/download/aws-sign2-0.7.0.tgz", "aws4@^1.8.0": "https://registry.npmmirror.com/aws4/download/aws4-1.9.1.tgz", "babel-jest@^25.5.1": "https://registry.npmmirror.com/babel-jest/download/babel-jest-25.5.1.tgz", "babel-loader@^8.0.5": "https://registry.npmmirror.com/babel-loader/download/babel-loader-8.1.0.tgz?cache=0&sync_timestamp=1584715910722&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-loader%2Fdownload%2Fbabel-loader-8.1.0.tgz", "babel-loader@^8.1.0": "https://registry.npmmirror.com/babel-loader/download/babel-loader-8.1.0.tgz?cache=0&sync_timestamp=1584715910722&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-loader%2Fdownload%2Fbabel-loader-8.1.0.tgz", "babel-plugin-dynamic-import-node@^2.3.0": "https://registry.npmmirror.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz", "babel-plugin-dynamic-import-node@^2.3.3": "https://registry.npmmirror.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz", "babel-plugin-import@^1.11.0": "https://registry.npmmirror.com/babel-plugin-import/download/babel-plugin-import-1.13.0.tgz", "babel-plugin-istanbul@^6.0.0": "https://registry.npmmirror.com/babel-plugin-istanbul/download/babel-plugin-istanbul-6.0.0.tgz?cache=0&sync_timestamp=1577063702695&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-plugin-istanbul%2Fdownload%2Fbabel-plugin-istanbul-6.0.0.tgz", "babel-plugin-jest-hoist@^25.5.0": "https://registry.npmmirror.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-25.5.0.tgz", "babel-preset-current-node-syntax@^0.1.2": "https://registry.npmmirror.com/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-0.1.2.tgz", "babel-preset-jest@^25.5.0": "https://registry.npmmirror.com/babel-preset-jest/download/babel-preset-jest-25.5.0.tgz?cache=0&sync_timestamp=1588614908454&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-preset-jest%2Fdownload%2Fbabel-preset-jest-25.5.0.tgz", "backo2@1.0.2": "https://registry.npmmirror.com/backo2/download/backo2-1.0.2.tgz", "balanced-match@^1.0.0": "https://registry.npmmirror.com/balanced-match/download/balanced-match-1.0.0.tgz", "base64-arraybuffer@0.1.5": "https://registry.npmmirror.com/base64-arraybuffer/download/base64-arraybuffer-0.1.5.tgz", "base64-arraybuffer@^0.2.0": "https://registry.npmmirror.com/base64-arraybuffer/download/base64-arraybuffer-0.2.0.tgz", "base64-js@^1.0.2": "https://registry.npmmirror.com/base64-js/download/base64-js-1.3.1.tgz", "base64id@2.0.0": "https://registry.npmmirror.com/base64id/download/base64id-2.0.0.tgz", "base@^0.11.1": "https://registry.npmmirror.com/base/download/base-0.11.2.tgz", "batch@0.6.1": "https://registry.npmmirror.com/batch/download/batch-0.6.1.tgz", "bcrypt-pbkdf@^1.0.0": "https://registry.npmmirror.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz", "better-assert@~1.0.0": "https://registry.npmmirror.com/better-assert/download/better-assert-1.0.2.tgz", "bfj@^6.1.1": "https://registry.npmmirror.com/bfj/download/bfj-6.1.2.tgz", "big.js@^3.1.3": "https://registry.npmmirror.com/big.js/download/big.js-3.2.0.tgz", "big.js@^5.2.2": "https://registry.npmmirror.com/big.js/download/big.js-5.2.2.tgz", "binary-extensions@^1.0.0": "https://registry.npmmirror.com/binary-extensions/download/binary-extensions-1.13.1.tgz", "binary-extensions@^2.0.0": "https://registry.npmmirror.com/binary-extensions/download/binary-extensions-2.0.0.tgz", "bindings@^1.5.0": "https://registry.npmmirror.com/bindings/download/bindings-1.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbindings%2Fdownload%2Fbindings-1.5.0.tgz", "blob@0.0.5": "https://registry.npmmirror.com/blob/download/blob-0.0.5.tgz?cache=0&sync_timestamp=1580722883513&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fblob%2Fdownload%2Fblob-0.0.5.tgz", "bluebird@^3.1.1": "https://registry.npmmirror.com/bluebird/download/bluebird-3.7.2.tgz", "bluebird@^3.5.5": "https://registry.npmmirror.com/bluebird/download/bluebird-3.7.2.tgz", "bn.js@^4.0.0": "https://registry.npmmirror.com/bn.js/download/bn.js-4.11.8.tgz", "bn.js@^4.1.0": "https://registry.npmmirror.com/bn.js/download/bn.js-4.11.8.tgz", "bn.js@^4.4.0": "https://registry.npmmirror.com/bn.js/download/bn.js-4.11.8.tgz", "bn.js@^5.1.1": "https://registry.npmmirror.com/bn.js/download/bn.js-5.1.1.tgz", "body-parser@1.19.0": "https://registry.npmmirror.com/body-parser/download/body-parser-1.19.0.tgz", "bonjour@^3.5.0": "https://registry.npmmirror.com/bonjour/download/bonjour-3.5.0.tgz", "boolbase@^1.0.0": "https://registry.npmmirror.com/boolbase/download/boolbase-1.0.0.tgz", "boolbase@~1.0.0": "https://registry.npmmirror.com/boolbase/download/boolbase-1.0.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmmirror.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "braces@^2.3.1": "https://registry.npmmirror.com/braces/download/braces-2.3.2.tgz", "braces@^2.3.2": "https://registry.npmmirror.com/braces/download/braces-2.3.2.tgz", "braces@^3.0.1": "https://registry.npmmirror.com/braces/download/braces-3.0.2.tgz", "braces@~3.0.2": "https://registry.npmmirror.com/braces/download/braces-3.0.2.tgz", "brorand@^1.0.1": "https://registry.npmmirror.com/brorand/download/brorand-1.1.0.tgz", "browser-process-hrtime@^1.0.0": "https://registry.npmmirror.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz", "browser-resolve@^1.11.3": "https://registry.npmmirror.com/browser-resolve/download/browser-resolve-1.11.3.tgz", "browserify-aes@^1.0.0": "https://registry.npmmirror.com/browserify-aes/download/browserify-aes-1.2.0.tgz", "browserify-aes@^1.0.4": "https://registry.npmmirror.com/browserify-aes/download/browserify-aes-1.2.0.tgz", "browserify-cipher@^1.0.0": "https://registry.npmmirror.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz", "browserify-des@^1.0.0": "https://registry.npmmirror.com/browserify-des/download/browserify-des-1.0.2.tgz", "browserify-rsa@^4.0.0": "https://registry.npmmirror.com/browserify-rsa/download/browserify-rsa-4.0.1.tgz", "browserify-rsa@^4.0.1": "https://registry.npmmirror.com/browserify-rsa/download/browserify-rsa-4.0.1.tgz", "browserify-sign@^4.0.0": "https://registry.npmmirror.com/browserify-sign/download/browserify-sign-4.2.0.tgz", "browserify-zlib@^0.2.0": "https://registry.npmmirror.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz", "browserslist@^4.0.0": "https://registry.npmmirror.com/browserslist/download/browserslist-4.12.0.tgz", "browserslist@^4.11.1": "https://registry.npmmirror.com/browserslist/download/browserslist-4.12.0.tgz", "browserslist@^4.12.0": "https://registry.npmmirror.com/browserslist/download/browserslist-4.12.0.tgz", "browserslist@^4.8.5": "https://registry.npmmirror.com/browserslist/download/browserslist-4.12.0.tgz", "bser@2.1.1": "https://registry.npmmirror.com/bser/download/bser-2.1.1.tgz", "buffer-from@^1.0.0": "https://registry.npmmirror.com/buffer-from/download/buffer-from-1.1.1.tgz", "buffer-indexof@^1.0.0": "https://registry.npmmirror.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz", "buffer-json@^2.0.0": "https://registry.npmmirror.com/buffer-json/download/buffer-json-2.0.0.tgz", "buffer-xor@^1.0.3": "https://registry.npmmirror.com/buffer-xor/download/buffer-xor-1.0.3.tgz", "buffer@^4.3.0": "https://registry.npmmirror.com/buffer/download/buffer-4.9.2.tgz?cache=0&sync_timestamp=1588706716358&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbuffer%2Fdownload%2Fbuffer-4.9.2.tgz", "builtin-status-codes@^3.0.0": "https://registry.npmmirror.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz", "bytes@3.0.0": "https://registry.npmmirror.com/bytes/download/bytes-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbytes%2Fdownload%2Fbytes-3.0.0.tgz", "bytes@3.1.0": "https://registry.npmmirror.com/bytes/download/bytes-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbytes%2Fdownload%2Fbytes-3.1.0.tgz", "cacache@^12.0.2": "https://registry.npmmirror.com/cacache/download/cacache-12.0.4.tgz", "cacache@^12.0.3": "https://registry.npmmirror.com/cacache/download/cacache-12.0.4.tgz", "cacache@^13.0.1": "https://registry.npmmirror.com/cacache/download/cacache-13.0.1.tgz", "cache-base@^1.0.1": "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz", "cache-content-type@^1.0.0": "https://registry.npmmirror.com/cache-content-type/download/cache-content-type-1.0.1.tgz", "cache-loader@^4.1.0": "https://registry.npmmirror.com/cache-loader/download/cache-loader-4.1.0.tgz", "call-me-maybe@^1.0.1": "https://registry.npmmirror.com/call-me-maybe/download/call-me-maybe-1.0.1.tgz", "caller-callsite@^2.0.0": "https://registry.npmmirror.com/caller-callsite/download/caller-callsite-2.0.0.tgz?cache=0&sync_timestamp=1562668933683&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaller-callsite%2Fdownload%2Fcaller-callsite-2.0.0.tgz", "caller-path@^2.0.0": "https://registry.npmmirror.com/caller-path/download/caller-path-2.0.0.tgz", "callsite@1.0.0": "https://registry.npmmirror.com/callsite/download/callsite-1.0.0.tgz", "callsites@^2.0.0": "https://registry.npmmirror.com/callsites/download/callsites-2.0.0.tgz", "callsites@^3.0.0": "https://registry.npmmirror.com/callsites/download/callsites-3.1.0.tgz", "camel-case@3.0.x": "https://registry.npmmirror.com/camel-case/download/camel-case-3.0.0.tgz?cache=0&sync_timestamp=1576748709736&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamel-case%2Fdownload%2Fcamel-case-3.0.0.tgz", "camelcase@^5.0.0": "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz", "camelcase@^5.2.0": "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz", "camelcase@^5.3.1": "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz", "caniuse-api@^3.0.0": "https://registry.npmmirror.com/caniuse-api/download/caniuse-api-3.0.0.tgz", "caniuse-lite@^1.0.0": "https://registry.npmmirror.com/caniuse-lite/download/caniuse-lite-1.0.30001062.tgz?cache=0&sync_timestamp=1589869994538&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaniuse-lite%2Fdownload%2Fcaniuse-lite-1.0.30001062.tgz", "caniuse-lite@^1.0.30001043": "https://registry.npmmirror.com/caniuse-lite/download/caniuse-lite-1.0.30001062.tgz?cache=0&sync_timestamp=1589869994538&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaniuse-lite%2Fdownload%2Fcaniuse-lite-1.0.30001062.tgz", "caniuse-lite@^1.0.30001061": "https://registry.npmmirror.com/caniuse-lite/download/caniuse-lite-1.0.30001062.tgz?cache=0&sync_timestamp=1589869994538&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaniuse-lite%2Fdownload%2Fcaniuse-lite-1.0.30001062.tgz", "capture-exit@^2.0.0": "https://registry.npmmirror.com/capture-exit/download/capture-exit-2.0.0.tgz", "case-sensitive-paths-webpack-plugin@^2.3.0": "https://registry.npmmirror.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.3.0.tgz", "caseless@~0.12.0": "https://registry.npmmirror.com/caseless/download/caseless-0.12.0.tgz", "chalk@^1.1.3": "https://registry.npmmirror.com/chalk/download/chalk-1.1.3.tgz", "chalk@^2.0.0": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz", "chalk@^2.0.1": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz", "chalk@^2.3.0": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz", "chalk@^2.4.1": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz", "chalk@^2.4.2": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz", "chalk@^3.0.0": "https://registry.npmmirror.com/chalk/download/chalk-3.0.0.tgz", "charenc@~0.0.1": "https://registry.npmmirror.com/charenc/download/charenc-0.0.2.tgz", "check-types@^8.0.3": "https://registry.npmmirror.com/check-types/download/check-types-8.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcheck-types%2Fdownload%2Fcheck-types-8.0.3.tgz", "chokidar@>=3.0.0 <4.0.0": "https://registry.npmmirror.com/chokidar/-/chokidar-3.6.0.tgz", "chokidar@^2.1.8": "https://registry.npmmirror.com/chokidar/download/chokidar-2.1.8.tgz?cache=0&sync_timestamp=1587911196018&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-2.1.8.tgz", "chokidar@^3.4.0": "https://registry.npmmirror.com/chokidar/download/chokidar-3.4.0.tgz?cache=0&sync_timestamp=1587911196018&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-3.4.0.tgz", "chownr@^1.1.1": "https://registry.npmmirror.com/chownr/download/chownr-1.1.4.tgz", "chownr@^1.1.2": "https://registry.npmmirror.com/chownr/download/chownr-1.1.4.tgz", "chrome-simple-launcher@0.1.3": "https://registry.npmmirror.com/chrome-simple-launcher/download/chrome-simple-launcher-0.1.3.tgz?cache=0&sync_timestamp=1563437692271&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchrome-simple-launcher%2Fdownload%2Fchrome-simple-launcher-0.1.3.tgz", "chrome-trace-event@^1.0.2": "https://registry.npmmirror.com/chrome-trace-event/download/chrome-trace-event-1.0.2.tgz", "ci-info@^2.0.0": "https://registry.npmmirror.com/ci-info/download/ci-info-2.0.0.tgz", "cipher-base@^1.0.0": "https://registry.npmmirror.com/cipher-base/download/cipher-base-1.0.4.tgz", "cipher-base@^1.0.1": "https://registry.npmmirror.com/cipher-base/download/cipher-base-1.0.4.tgz", "cipher-base@^1.0.3": "https://registry.npmmirror.com/cipher-base/download/cipher-base-1.0.4.tgz", "class-utils@^0.3.5": "https://registry.npmmirror.com/class-utils/download/class-utils-0.3.6.tgz", "clean-css@4.2.x": "https://registry.npmmirror.com/clean-css/download/clean-css-4.2.3.tgz", "clean-stack@^2.0.0": "https://registry.npmmirror.com/clean-stack/download/clean-stack-2.2.0.tgz", "cli-cursor@^2.1.0": "https://registry.npmmirror.com/cli-cursor/download/cli-cursor-2.1.0.tgz", "cli-highlight@^2.1.4": "https://registry.npmmirror.com/cli-highlight/download/cli-highlight-2.1.4.tgz?cache=0&sync_timestamp=1573948719956&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-highlight%2Fdownload%2Fcli-highlight-2.1.4.tgz", "cli-spinners@^2.0.0": "https://registry.npmmirror.com/cli-spinners/download/cli-spinners-2.3.0.tgz?cache=0&sync_timestamp=1586157510340&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-spinners%2Fdownload%2Fcli-spinners-2.3.0.tgz", "clipboardy@^2.3.0": "https://registry.npmmirror.com/clipboardy/download/clipboardy-2.3.0.tgz", "cliui@^5.0.0": "https://registry.npmmirror.com/cliui/download/cliui-5.0.0.tgz?cache=0&sync_timestamp=1573942320052&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcliui%2Fdownload%2Fcliui-5.0.0.tgz", "cliui@^6.0.0": "https://registry.npmmirror.com/cliui/download/cliui-6.0.0.tgz?cache=0&sync_timestamp=1573942320052&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcliui%2Fdownload%2Fcliui-6.0.0.tgz", "clone@^1.0.2": "https://registry.npmmirror.com/clone/download/clone-1.0.4.tgz", "co-body@^5.1.1": "https://registry.npmmirror.com/co-body/download/co-body-5.2.0.tgz", "co-body@^6.0.0": "https://registry.npmmirror.com/co-body/download/co-body-6.0.0.tgz", "co@^4.6.0": "https://registry.npmmirror.com/co/download/co-4.6.0.tgz", "coa@^2.0.2": "https://registry.npmmirror.com/coa/download/coa-2.0.2.tgz", "collect-v8-coverage@^1.0.0": "https://registry.npmmirror.com/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz", "collection-visit@^1.0.0": "https://registry.npmmirror.com/collection-visit/download/collection-visit-1.0.0.tgz", "color-convert@^1.9.0": "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz", "color-convert@^1.9.1": "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz", "color-convert@^2.0.1": "https://registry.npmmirror.com/color-convert/download/color-convert-2.0.1.tgz", "color-name@1.1.3": "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz", "color-name@^1.0.0": "https://registry.npmmirror.com/color-name/download/color-name-1.1.4.tgz", "color-name@~1.1.4": "https://registry.npmmirror.com/color-name/download/color-name-1.1.4.tgz", "color-string@^1.5.2": "https://registry.npmmirror.com/color-string/download/color-string-1.5.3.tgz", "color@^3.0.0": "https://registry.npmmirror.com/color/download/color-3.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcolor%2Fdownload%2Fcolor-3.1.2.tgz", "combined-stream@^1.0.6": "https://registry.npmmirror.com/combined-stream/download/combined-stream-1.0.8.tgz", "combined-stream@~1.0.6": "https://registry.npmmirror.com/combined-stream/download/combined-stream-1.0.8.tgz", "commander@2.17.x": "https://registry.npmmirror.com/commander/download/commander-2.17.1.tgz?cache=0&sync_timestamp=1587781596778&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.17.1.tgz", "commander@^2.18.0": "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1587781596778&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz", "commander@^2.20.0": "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1587781596778&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz", "commander@^4.0.1": "https://registry.npmmirror.com/commander/download/commander-4.1.1.tgz?cache=0&sync_timestamp=1587781596778&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-4.1.1.tgz", "commander@~2.19.0": "https://registry.npmmirror.com/commander/download/commander-2.19.0.tgz?cache=0&sync_timestamp=1587781596778&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.19.0.tgz", "commondir@^1.0.1": "https://registry.npmmirror.com/commondir/download/commondir-1.0.1.tgz", "component-bind@1.0.0": "https://registry.npmmirror.com/component-bind/download/component-bind-1.0.0.tgz", "component-emitter@1.2.1": "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.2.1.tgz", "component-emitter@^1.2.1": "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz", "component-emitter@~1.3.0": "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz", "component-inherit@0.0.3": "https://registry.npmmirror.com/component-inherit/download/component-inherit-0.0.3.tgz", "compressible@~2.0.16": "https://registry.npmmirror.com/compressible/download/compressible-2.0.18.tgz?cache=0&sync_timestamp=1578286264482&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcompressible%2Fdownload%2Fcompressible-2.0.18.tgz", "compression@^1.7.4": "https://registry.npmmirror.com/compression/download/compression-1.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcompression%2Fdownload%2Fcompression-1.7.4.tgz", "concat-map@0.0.1": "https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz", "concat-stream@^1.5.0": "https://registry.npmmirror.com/concat-stream/download/concat-stream-1.6.2.tgz", "connect-history-api-fallback@^1.6.0": "https://registry.npmmirror.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz", "console-browserify@^1.1.0": "https://registry.npmmirror.com/console-browserify/download/console-browserify-1.2.0.tgz", "consolidate@^0.15.1": "https://registry.npmmirror.com/consolidate/download/consolidate-0.15.1.tgz", "constants-browserify@^1.0.0": "https://registry.npmmirror.com/constants-browserify/download/constants-browserify-1.0.0.tgz", "content-disposition@0.5.3": "https://registry.npmmirror.com/content-disposition/download/content-disposition-0.5.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcontent-disposition%2Fdownload%2Fcontent-disposition-0.5.3.tgz", "content-disposition@~0.5.2": "https://registry.npmmirror.com/content-disposition/download/content-disposition-0.5.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcontent-disposition%2Fdownload%2Fcontent-disposition-0.5.3.tgz", "content-type@^1.0.4": "https://registry.npmmirror.com/content-type/download/content-type-1.0.4.tgz", "content-type@~1.0.4": "https://registry.npmmirror.com/content-type/download/content-type-1.0.4.tgz", "convert-source-map@^1.4.0": "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.7.0.tgz", "convert-source-map@^1.6.0": "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.7.0.tgz", "convert-source-map@^1.7.0": "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.7.0.tgz", "cookie-signature@1.0.6": "https://registry.npmmirror.com/cookie-signature/download/cookie-signature-1.0.6.tgz", "cookie@0.3.1": "https://registry.npmmirror.com/cookie/download/cookie-0.3.1.tgz", "cookie@0.4.0": "https://registry.npmmirror.com/cookie/download/cookie-0.4.0.tgz", "cookies@~0.8.0": "https://registry.npmmirror.com/cookies/download/cookies-0.8.0.tgz?cache=0&sync_timestamp=1570851324736&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcookies%2Fdownload%2Fcookies-0.8.0.tgz", "copy-concurrently@^1.0.0": "https://registry.npmmirror.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz", "copy-descriptor@^0.1.0": "https://registry.npmmirror.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz", "copy-to@^2.0.1": "https://registry.npmmirror.com/copy-to/download/copy-to-2.0.1.tgz", "copy-webpack-plugin@^5.1.1": "https://registry.npmmirror.com/copy-webpack-plugin/download/copy-webpack-plugin-5.1.1.tgz", "core-js-compat@^3.6.2": "https://registry.npmmirror.com/core-js-compat/download/core-js-compat-3.6.5.tgz?cache=0&sync_timestamp=1586535809290&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js-compat%2Fdownload%2Fcore-js-compat-3.6.5.tgz", "core-js-compat@^3.6.4": "https://registry.npmmirror.com/core-js-compat/download/core-js-compat-3.6.5.tgz?cache=0&sync_timestamp=1586535809290&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js-compat%2Fdownload%2Fcore-js-compat-3.6.5.tgz", "core-js@^3.6.4": "https://registry.npmmirror.com/core-js/download/core-js-3.6.5.tgz?cache=0&sync_timestamp=1586450269267&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js%2Fdownload%2Fcore-js-3.6.5.tgz", "core-util-is@1.0.2": "https://registry.npmmirror.com/core-util-is/download/core-util-is-1.0.2.tgz", "core-util-is@~1.0.0": "https://registry.npmmirror.com/core-util-is/download/core-util-is-1.0.2.tgz", "cosmiconfig@^5.0.0": "https://registry.npmmirror.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz?cache=0&sync_timestamp=1572710769619&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcosmiconfig%2Fdownload%2Fcosmiconfig-5.2.1.tgz", "create-ecdh@^4.0.0": "https://registry.npmmirror.com/create-ecdh/download/create-ecdh-4.0.3.tgz", "create-hash@^1.1.0": "https://registry.npmmirror.com/create-hash/download/create-hash-1.2.0.tgz", "create-hash@^1.1.2": "https://registry.npmmirror.com/create-hash/download/create-hash-1.2.0.tgz", "create-hash@^1.2.0": "https://registry.npmmirror.com/create-hash/download/create-hash-1.2.0.tgz", "create-hmac@^1.1.0": "https://registry.npmmirror.com/create-hmac/download/create-hmac-1.1.7.tgz", "create-hmac@^1.1.4": "https://registry.npmmirror.com/create-hmac/download/create-hmac-1.1.7.tgz", "create-hmac@^1.1.7": "https://registry.npmmirror.com/create-hmac/download/create-hmac-1.1.7.tgz", "cross-env@^5.2.0": "https://registry.npmmirror.com/cross-env/download/cross-env-5.2.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-env%2Fdownload%2Fcross-env-5.2.1.tgz", "cross-env@^7.0.2": "https://registry.npmmirror.com/cross-env/download/cross-env-7.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-env%2Fdownload%2Fcross-env-7.0.2.tgz", "cross-spawn@^6.0.0": "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz", "cross-spawn@^6.0.5": "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz", "cross-spawn@^7.0.0": "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-7.0.2.tgz", "cross-spawn@^7.0.1": "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-7.0.2.tgz", "crypt@~0.0.1": "https://registry.npmmirror.com/crypt/download/crypt-0.0.2.tgz", "crypto-browserify@^3.11.0": "https://registry.npmmirror.com/crypto-browserify/download/crypto-browserify-3.12.0.tgz", "css-color-names@0.0.4": "https://registry.npmmirror.com/css-color-names/download/css-color-names-0.0.4.tgz", "css-color-names@^0.0.4": "https://registry.npmmirror.com/css-color-names/download/css-color-names-0.0.4.tgz", "css-declaration-sorter@^4.0.1": "https://registry.npmmirror.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz", "css-loader@^2.1.1": "https://registry.npmmirror.com/css-loader/download/css-loader-2.1.1.tgz", "css-loader@^3.4.2": "https://registry.npmmirror.com/css-loader/download/css-loader-3.5.3.tgz", "css-select-base-adapter@^0.1.1": "https://registry.npmmirror.com/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz", "css-select@^1.1.0": "https://registry.npmmirror.com/css-select/download/css-select-1.2.0.tgz", "css-select@^2.0.0": "https://registry.npmmirror.com/css-select/download/css-select-2.1.0.tgz", "css-tree@1.0.0-alpha.37": "https://registry.npmmirror.com/css-tree/download/css-tree-1.0.0-alpha.37.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-tree%2Fdownload%2Fcss-tree-1.0.0-alpha.37.tgz", "css-tree@1.0.0-alpha.39": "https://registry.npmmirror.com/css-tree/download/css-tree-1.0.0-alpha.39.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-tree%2Fdownload%2Fcss-tree-1.0.0-alpha.39.tgz", "css-what@2.1": "https://registry.npmmirror.com/css-what/download/css-what-2.1.3.tgz", "css-what@^2.1.3": "https://registry.npmmirror.com/css-what/download/css-what-2.1.3.tgz", "css-what@^3.2.1": "https://registry.npmmirror.com/css-what/download/css-what-3.2.1.tgz", "css@^2.2.4": "https://registry.npmmirror.com/css/download/css-2.2.4.tgz", "css@~2.2.1": "https://registry.npmmirror.com/css/download/css-2.2.4.tgz", "cssesc@^2.0.0": "https://registry.npmmirror.com/cssesc/download/cssesc-2.0.0.tgz", "cssesc@^3.0.0": "https://registry.npmmirror.com/cssesc/download/cssesc-3.0.0.tgz", "cssnano-preset-default@^4.0.0": "https://registry.npmmirror.com/cssnano-preset-default/download/cssnano-preset-default-4.0.7.tgz", "cssnano-preset-default@^4.0.7": "https://registry.npmmirror.com/cssnano-preset-default/download/cssnano-preset-default-4.0.7.tgz", "cssnano-util-get-arguments@^4.0.0": "https://registry.npmmirror.com/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz", "cssnano-util-get-match@^4.0.0": "https://registry.npmmirror.com/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz", "cssnano-util-raw-cache@^4.0.1": "https://registry.npmmirror.com/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz", "cssnano-util-same-parent@^4.0.0": "https://registry.npmmirror.com/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz", "cssnano@^4.0.0": "https://registry.npmmirror.com/cssnano/download/cssnano-4.1.10.tgz", "cssnano@^4.1.10": "https://registry.npmmirror.com/cssnano/download/cssnano-4.1.10.tgz", "csso@^4.0.2": "https://registry.npmmirror.com/csso/download/csso-4.0.3.tgz?cache=0&sync_timestamp=1585052130344&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcsso%2Fdownload%2Fcsso-4.0.3.tgz", "cssom@^0.4.1": "https://registry.npmmirror.com/cssom/download/cssom-0.4.4.tgz?cache=0&sync_timestamp=1573719337707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssom%2Fdownload%2Fcssom-0.4.4.tgz", "cssom@~0.3.6": "https://registry.npmmirror.com/cssom/download/cssom-0.3.8.tgz?cache=0&sync_timestamp=1573719337707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssom%2Fdownload%2Fcssom-0.3.8.tgz", "cssstyle@^2.0.0": "https://registry.npmmirror.com/cssstyle/download/cssstyle-2.3.0.tgz?cache=0&sync_timestamp=1588171504463&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssstyle%2Fdownload%2Fcssstyle-2.3.0.tgz", "cyclist@^1.0.1": "https://registry.npmmirror.com/cyclist/download/cyclist-1.0.1.tgz", "dashdash@^1.12.0": "https://registry.npmmirror.com/dashdash/download/dashdash-1.14.1.tgz", "data-urls@^1.1.0": "https://registry.npmmirror.com/data-urls/download/data-urls-1.1.0.tgz", "de-indent@^1.0.2": "https://registry.npmmirror.com/de-indent/download/de-indent-1.0.2.tgz", "debug@2.6.9": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz", "debug@^2.2.0": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz", "debug@^2.3.3": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz", "debug@^3.1.0": "https://registry.npmmirror.com/debug/download/debug-3.2.6.tgz", "debug@^3.1.1": "https://registry.npmmirror.com/debug/download/debug-3.2.6.tgz", "debug@^3.2.5": "https://registry.npmmirror.com/debug/download/debug-3.2.6.tgz", "debug@^4.0.1": "https://registry.npmmirror.com/debug/download/debug-4.1.1.tgz", "debug@^4.1.0": "https://registry.npmmirror.com/debug/download/debug-4.1.1.tgz", "debug@^4.1.1": "https://registry.npmmirror.com/debug/download/debug-4.1.1.tgz", "debug@~3.1.0": "https://registry.npmmirror.com/debug/download/debug-3.1.0.tgz", "debug@~4.1.0": "https://registry.npmmirror.com/debug/download/debug-4.1.1.tgz", "decamelize@^1.2.0": "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1580010393599&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz", "decode-uri-component@^0.2.0": "https://registry.npmmirror.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz", "deep-equal@^1.0.1": "https://registry.npmmirror.com/deep-equal/download/deep-equal-1.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.1.1.tgz", "deep-equal@~1.0.1": "https://registry.npmmirror.com/deep-equal/download/deep-equal-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.0.1.tgz", "deep-extend@^0.6.0": "https://registry.npmmirror.com/deep-extend/download/deep-extend-0.6.0.tgz", "deep-is@~0.1.3": "https://registry.npmmirror.com/deep-is/download/deep-is-0.1.3.tgz", "deepmerge@^1.5.2": "https://registry.npmmirror.com/deepmerge/download/deepmerge-1.5.2.tgz", "deepmerge@^4.2.2": "https://registry.npmmirror.com/deepmerge/download/deepmerge-4.2.2.tgz", "default-gateway@^4.2.0": "https://registry.npmmirror.com/default-gateway/download/default-gateway-4.2.0.tgz", "default-gateway@^5.0.5": "https://registry.npmmirror.com/default-gateway/download/default-gateway-5.0.5.tgz", "default-gateway@^6.0.0": "https://registry.npmmirror.com/default-gateway/download/default-gateway-6.0.0.tgz", "defaults@^1.0.3": "https://registry.npmmirror.com/defaults/download/defaults-1.0.3.tgz", "define-properties@^1.1.2": "https://registry.npmmirror.com/define-properties/download/define-properties-1.1.3.tgz", "define-properties@^1.1.3": "https://registry.npmmirror.com/define-properties/download/define-properties-1.1.3.tgz", "define-property@^0.2.5": "https://registry.npmmirror.com/define-property/download/define-property-0.2.5.tgz", "define-property@^1.0.0": "https://registry.npmmirror.com/define-property/download/define-property-1.0.0.tgz", "define-property@^2.0.2": "https://registry.npmmirror.com/define-property/download/define-property-2.0.2.tgz", "del@^4.1.1": "https://registry.npmmirror.com/del/download/del-4.1.1.tgz", "delayed-stream@~1.0.0": "https://registry.npmmirror.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "delegates@^1.0.0": "https://registry.npmmirror.com/delegates/download/delegates-1.0.0.tgz", "depd@^1.1.2": "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz", "depd@~1.1.2": "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz", "depd@~2.0.0": "https://registry.npmmirror.com/depd/download/depd-2.0.0.tgz", "des.js@^1.0.0": "https://registry.npmmirror.com/des.js/download/des.js-1.0.1.tgz", "destroy@^1.0.4": "https://registry.npmmirror.com/destroy/download/destroy-1.0.4.tgz", "destroy@~1.0.4": "https://registry.npmmirror.com/destroy/download/destroy-1.0.4.tgz", "detect-newline@^3.0.0": "https://registry.npmmirror.com/detect-newline/download/detect-newline-3.1.0.tgz", "detect-node@^2.0.4": "https://registry.npmmirror.com/detect-node/download/detect-node-2.0.4.tgz", "diff-sequences@^25.2.6": "https://registry.npmmirror.com/diff-sequences/download/diff-sequences-25.2.6.tgz", "diffie-hellman@^5.0.0": "https://registry.npmmirror.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz", "dir-glob@^2.0.0": "https://registry.npmmirror.com/dir-glob/download/dir-glob-2.2.2.tgz", "dir-glob@^2.2.2": "https://registry.npmmirror.com/dir-glob/download/dir-glob-2.2.2.tgz", "dns-equal@^1.0.0": "https://registry.npmmirror.com/dns-equal/download/dns-equal-1.0.0.tgz", "dns-packet@^1.3.1": "https://registry.npmmirror.com/dns-packet/download/dns-packet-1.3.1.tgz", "dns-txt@^2.0.2": "https://registry.npmmirror.com/dns-txt/download/dns-txt-2.0.2.tgz", "dom-converter@^0.2": "https://registry.npmmirror.com/dom-converter/download/dom-converter-0.2.0.tgz", "dom-serializer@0": "https://registry.npmmirror.com/dom-serializer/download/dom-serializer-0.2.2.tgz?cache=0&sync_timestamp=1589067578490&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdom-serializer%2Fdownload%2Fdom-serializer-0.2.2.tgz", "domain-browser@^1.1.1": "https://registry.npmmirror.com/domain-browser/download/domain-browser-1.2.0.tgz?cache=0&sync_timestamp=1590035413031&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomain-browser%2Fdownload%2Fdomain-browser-1.2.0.tgz", "domelementtype@1": "https://registry.npmmirror.com/domelementtype/download/domelementtype-1.3.1.tgz", "domelementtype@^1.3.0": "https://registry.npmmirror.com/domelementtype/download/domelementtype-1.3.1.tgz", "domelementtype@^1.3.1": "https://registry.npmmirror.com/domelementtype/download/domelementtype-1.3.1.tgz", "domelementtype@^2.0.1": "https://registry.npmmirror.com/domelementtype/download/domelementtype-2.0.1.tgz", "domexception@^1.0.1": "https://registry.npmmirror.com/domexception/download/domexception-1.0.1.tgz?cache=0&sync_timestamp=1576355459111&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomexception%2Fdownload%2Fdomexception-1.0.1.tgz", "domhandler@^2.3.0": "https://registry.npmmirror.com/domhandler/download/domhandler-2.4.2.tgz", "domutils@1.5.1": "https://registry.npmmirror.com/domutils/download/domutils-1.5.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomutils%2Fdownload%2Fdomutils-1.5.1.tgz", "domutils@^1.5.1": "https://registry.npmmirror.com/domutils/download/domutils-1.7.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomutils%2Fdownload%2Fdomutils-1.7.0.tgz", "domutils@^1.7.0": "https://registry.npmmirror.com/domutils/download/domutils-1.7.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomutils%2Fdownload%2Fdomutils-1.7.0.tgz", "dot-prop@^5.2.0": "https://registry.npmmirror.com/dot-prop/download/dot-prop-5.2.0.tgz?cache=0&sync_timestamp=1572621117377&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdot-prop%2Fdownload%2Fdot-prop-5.2.0.tgz", "dotenv-expand@^5.1.0": "https://registry.npmmirror.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz", "dotenv@^8.2.0": "https://registry.npmmirror.com/dotenv/download/dotenv-8.2.0.tgz", "duplexer@^0.1.1": "https://registry.npmmirror.com/duplexer/download/duplexer-0.1.1.tgz", "duplexify@^3.4.2": "https://registry.npmmirror.com/duplexify/download/duplexify-3.7.1.tgz", "duplexify@^3.6.0": "https://registry.npmmirror.com/duplexify/download/duplexify-3.7.1.tgz", "easy-stack@^1.0.0": "https://registry.npmmirror.com/easy-stack/download/easy-stack-1.0.0.tgz", "ecc-jsbn@~0.1.1": "https://registry.npmmirror.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz", "ee-first@1.1.1": "https://registry.npmmirror.com/ee-first/download/ee-first-1.1.1.tgz", "ejs@^2.6.1": "https://registry.npmmirror.com/ejs/download/ejs-2.7.4.tgz", "electron-to-chromium@^1.3.413": "https://registry.npmmirror.com/electron-to-chromium/download/electron-to-chromium-1.3.448.tgz", "elliptic@^6.0.0": "https://registry.npmmirror.com/elliptic/download/elliptic-6.5.2.tgz", "elliptic@^6.5.2": "https://registry.npmmirror.com/elliptic/download/elliptic-6.5.2.tgz", "emoji-regex@^7.0.1": "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-7.0.3.tgz", "emoji-regex@^8.0.0": "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "emojis-list@^2.0.0": "https://registry.npmmirror.com/emojis-list/download/emojis-list-2.1.0.tgz", "emojis-list@^3.0.0": "https://registry.npmmirror.com/emojis-list/download/emojis-list-3.0.0.tgz", "encodeurl@^1.0.2": "https://registry.npmmirror.com/encodeurl/download/encodeurl-1.0.2.tgz", "encodeurl@~1.0.2": "https://registry.npmmirror.com/encodeurl/download/encodeurl-1.0.2.tgz", "end-of-stream@^1.0.0": "https://registry.npmmirror.com/end-of-stream/download/end-of-stream-1.4.4.tgz", "end-of-stream@^1.1.0": "https://registry.npmmirror.com/end-of-stream/download/end-of-stream-1.4.4.tgz", "engine.io-client@~3.4.0": "https://registry.npmmirror.com/engine.io-client/download/engine.io-client-3.4.2.tgz?cache=0&sync_timestamp=1589350950920&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fengine.io-client%2Fdownload%2Fengine.io-client-3.4.2.tgz", "engine.io-parser@~2.2.0": "https://registry.npmmirror.com/engine.io-parser/download/engine.io-parser-2.2.0.tgz?cache=0&sync_timestamp=1589895483603&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fengine.io-parser%2Fdownload%2Fengine.io-parser-2.2.0.tgz", "engine.io@~3.4.0": "https://registry.npmmirror.com/engine.io/download/engine.io-3.4.1.tgz?cache=0&sync_timestamp=1587113655084&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fengine.io%2Fdownload%2Fengine.io-3.4.1.tgz", "enhanced-resolve@^4.1.0": "https://registry.npmmirror.com/enhanced-resolve/download/enhanced-resolve-4.1.1.tgz?cache=0&sync_timestamp=1572991764265&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fenhanced-resolve%2Fdownload%2Fenhanced-resolve-4.1.1.tgz", "entities@^1.1.1": "https://registry.npmmirror.com/entities/download/entities-1.1.2.tgz", "entities@^2.0.0": "https://registry.npmmirror.com/entities/download/entities-2.0.2.tgz", "envinfo@^6.0.1": "https://registry.npmmirror.com/envinfo/download/envinfo-6.0.1.tgz", "errno@^0.1.3": "https://registry.npmmirror.com/errno/download/errno-0.1.7.tgz", "errno@~0.1.7": "https://registry.npmmirror.com/errno/download/errno-0.1.7.tgz", "error-ex@^1.3.1": "https://registry.npmmirror.com/error-ex/download/error-ex-1.3.2.tgz", "error-stack-parser@^2.0.0": "https://registry.npmmirror.com/error-stack-parser/download/error-stack-parser-2.0.6.tgz?cache=0&sync_timestamp=1578288503034&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ferror-stack-parser%2Fdownload%2Ferror-stack-parser-2.0.6.tgz", "es-abstract@^1.17.0-next.1": "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.17.5.tgz", "es-abstract@^1.17.2": "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.17.5.tgz", "es-abstract@^1.17.5": "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.17.5.tgz", "es-to-primitive@^1.2.1": "https://registry.npmmirror.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz", "escape-html@^1.0.3": "https://registry.npmmirror.com/escape-html/download/escape-html-1.0.3.tgz", "escape-html@~1.0.3": "https://registry.npmmirror.com/escape-html/download/escape-html-1.0.3.tgz", "escape-string-regexp@^1.0.2": "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz?cache=0&sync_timestamp=1587627212242&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fescape-string-regexp%2Fdownload%2Fescape-string-regexp-1.0.5.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz?cache=0&sync_timestamp=1587627212242&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fescape-string-regexp%2Fdownload%2Fescape-string-regexp-1.0.5.tgz", "escodegen@^1.11.1": "https://registry.npmmirror.com/escodegen/download/escodegen-1.14.1.tgz", "escodegen@^1.8.1": "https://registry.npmmirror.com/escodegen/download/escodegen-1.14.1.tgz", "eslint-scope@^4.0.3": "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-4.0.3.tgz", "esprima@^4.0.0": "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fesprima%2Fdownload%2Fesprima-4.0.1.tgz", "esprima@^4.0.1": "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fesprima%2Fdownload%2Fesprima-4.0.1.tgz", "esrecurse@^4.1.0": "https://registry.npmmirror.com/esrecurse/download/esrecurse-4.2.1.tgz", "estraverse@^4.1.0": "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1586996117385&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz", "estraverse@^4.1.1": "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1586996117385&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz", "estraverse@^4.2.0": "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1586996117385&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz", "esutils@^2.0.2": "https://registry.npmmirror.com/esutils/download/esutils-2.0.3.tgz?cache=0&sync_timestamp=1564535492241&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fesutils%2Fdownload%2Fesutils-2.0.3.tgz", "etag@~1.8.1": "https://registry.npmmirror.com/etag/download/etag-1.8.1.tgz", "event-pubsub@4.3.0": "https://registry.npmmirror.com/event-pubsub/download/event-pubsub-4.3.0.tgz", "eventemitter3@^4.0.0": "https://registry.npmmirror.com/eventemitter3/download/eventemitter3-4.0.4.tgz?cache=0&sync_timestamp=1589283150629&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feventemitter3%2Fdownload%2Feventemitter3-4.0.4.tgz", "events@^3.0.0": "https://registry.npmmirror.com/events/download/events-3.1.0.tgz?cache=0&sync_timestamp=1578498298945&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fevents%2Fdownload%2Fevents-3.1.0.tgz", "eventsource@^1.0.7": "https://registry.npmmirror.com/eventsource/download/eventsource-1.0.7.tgz", "evp_bytestokey@^1.0.0": "https://registry.npmmirror.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz", "evp_bytestokey@^1.0.3": "https://registry.npmmirror.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz", "exec-sh@^0.3.2": "https://registry.npmmirror.com/exec-sh/download/exec-sh-0.3.4.tgz", "execa@^1.0.0": "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz?cache=0&sync_timestamp=1588947631735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-1.0.0.tgz", "execa@^3.2.0": "https://registry.npmmirror.com/execa/download/execa-3.4.0.tgz?cache=0&sync_timestamp=1588947631735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-3.4.0.tgz", "execa@^3.3.0": "https://registry.npmmirror.com/execa/download/execa-3.4.0.tgz?cache=0&sync_timestamp=1588947631735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-3.4.0.tgz", "execa@^4.0.0": "https://registry.npmmirror.com/execa/download/execa-4.0.1.tgz?cache=0&sync_timestamp=1588947631735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-4.0.1.tgz", "exit@^0.1.2": "https://registry.npmmirror.com/exit/download/exit-0.1.2.tgz", "expand-brackets@^2.1.4": "https://registry.npmmirror.com/expand-brackets/download/expand-brackets-2.1.4.tgz", "expect@^25.5.0": "https://registry.npmmirror.com/expect/download/expect-25.5.0.tgz?cache=0&sync_timestamp=1588675340802&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexpect%2Fdownload%2Fexpect-25.5.0.tgz", "express@^4.16.3": "https://registry.npmmirror.com/express/download/express-4.17.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexpress%2Fdownload%2Fexpress-4.17.1.tgz", "express@^4.17.1": "https://registry.npmmirror.com/express/download/express-4.17.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexpress%2Fdownload%2Fexpress-4.17.1.tgz", "extend-shallow@^2.0.1": "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-2.0.1.tgz", "extend-shallow@^3.0.0": "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz", "extend-shallow@^3.0.2": "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz", "extend@~3.0.2": "https://registry.npmmirror.com/extend/download/extend-3.0.2.tgz", "extglob@^2.0.4": "https://registry.npmmirror.com/extglob/download/extglob-2.0.4.tgz", "extsprintf@1.3.0": "https://registry.npmmirror.com/extsprintf/download/extsprintf-1.3.0.tgz", "extsprintf@^1.2.0": "https://registry.npmmirror.com/extsprintf/download/extsprintf-1.4.0.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmmirror.com/fast-deep-equal/download/fast-deep-equal-3.1.1.tgz", "fast-glob@^2.2.6": "https://registry.npmmirror.com/fast-glob/download/fast-glob-2.2.7.tgz?cache=0&sync_timestamp=1582318661510&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-glob%2Fdownload%2Ffast-glob-2.2.7.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmmirror.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz?cache=0&sync_timestamp=1576367703577&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-json-stable-stringify%2Fdownload%2Ffast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@~2.0.6": "https://registry.npmmirror.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "faye-websocket@^0.10.0": "https://registry.npmmirror.com/faye-websocket/download/faye-websocket-0.10.0.tgz", "faye-websocket@~0.11.1": "https://registry.npmmirror.com/faye-websocket/download/faye-websocket-0.11.3.tgz", "fb-watchman@^2.0.0": "https://registry.npmmirror.com/fb-watchman/download/fb-watchman-2.0.1.tgz", "figgy-pudding@^3.5.1": "https://registry.npmmirror.com/figgy-pudding/download/figgy-pudding-3.5.2.tgz", "file-loader@^4.2.0": "https://registry.npmmirror.com/file-loader/download/file-loader-4.3.0.tgz", "file-uri-to-path@1.0.0": "https://registry.npmmirror.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz", "filesize@^3.6.1": "https://registry.npmmirror.com/filesize/download/filesize-3.6.1.tgz", "fill-range@^4.0.0": "https://registry.npmmirror.com/fill-range/download/fill-range-4.0.0.tgz", "fill-range@^7.0.1": "https://registry.npmmirror.com/fill-range/download/fill-range-7.0.1.tgz", "finalhandler@~1.1.2": "https://registry.npmmirror.com/finalhandler/download/finalhandler-1.1.2.tgz", "find-cache-dir@^2.0.0": "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz?cache=0&sync_timestamp=1583734591888&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-cache-dir%2Fdownload%2Ffind-cache-dir-2.1.0.tgz", "find-cache-dir@^2.1.0": "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz?cache=0&sync_timestamp=1583734591888&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-cache-dir%2Fdownload%2Ffind-cache-dir-2.1.0.tgz", "find-cache-dir@^3.0.0": "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-3.3.1.tgz?cache=0&sync_timestamp=1583734591888&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-cache-dir%2Fdownload%2Ffind-cache-dir-3.3.1.tgz", "find-cache-dir@^3.3.1": "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-3.3.1.tgz?cache=0&sync_timestamp=1583734591888&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-cache-dir%2Fdownload%2Ffind-cache-dir-3.3.1.tgz", "find-up@^2.1.0": "https://registry.npmmirror.com/find-up/download/find-up-2.1.0.tgz", "find-up@^3.0.0": "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz", "find-up@^4.0.0": "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz", "find-up@^4.1.0": "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz", "flush-write-stream@^1.0.0": "https://registry.npmmirror.com/flush-write-stream/download/flush-write-stream-1.1.1.tgz", "flyio@^0.6.2": "https://registry.npmmirror.com/flyio/download/flyio-0.6.14.tgz", "follow-redirects@^1.0.0": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.9.tgz", "for-in@^1.0.2": "https://registry.npmmirror.com/for-in/download/for-in-1.0.2.tgz", "forever-agent@~0.6.1": "https://registry.npmmirror.com/forever-agent/download/forever-agent-0.6.1.tgz", "form-data@~2.3.2": "https://registry.npmmirror.com/form-data/download/form-data-2.3.3.tgz?cache=0&sync_timestamp=1573027118125&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fform-data%2Fdownload%2Fform-data-2.3.3.tgz", "formidable@^1.1.1": "https://registry.npmmirror.com/formidable/download/formidable-1.2.2.tgz", "forwarded@~0.1.2": "https://registry.npmmirror.com/forwarded/download/forwarded-0.1.2.tgz", "fragment-cache@^0.2.1": "https://registry.npmmirror.com/fragment-cache/download/fragment-cache-0.2.1.tgz", "fresh@0.5.2": "https://registry.npmmirror.com/fresh/download/fresh-0.5.2.tgz", "fresh@~0.5.2": "https://registry.npmmirror.com/fresh/download/fresh-0.5.2.tgz", "from2@^2.1.0": "https://registry.npmmirror.com/from2/download/from2-2.3.0.tgz", "fs-extra@^7.0.1": "https://registry.npmmirror.com/fs-extra/download/fs-extra-7.0.1.tgz", "fs-extra@^8.1.0": "https://registry.npmmirror.com/fs-extra/download/fs-extra-8.1.0.tgz", "fs-minipass@^2.0.0": "https://registry.npmmirror.com/fs-minipass/download/fs-minipass-2.1.0.tgz?cache=0&sync_timestamp=1579628584498&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffs-minipass%2Fdownload%2Ffs-minipass-2.1.0.tgz", "fs-write-stream-atomic@^1.0.8": "https://registry.npmmirror.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz", "fs.realpath@^1.0.0": "https://registry.npmmirror.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "fsevents@^1.2.7": "https://registry.npmmirror.com/fsevents/download/fsevents-1.2.13.tgz?cache=0&sync_timestamp=1588787369955&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffsevents%2Fdownload%2Ffsevents-1.2.13.tgz", "fsevents@^2.1.2": "https://registry.npmmirror.com/fsevents/download/fsevents-2.1.3.tgz?cache=0&sync_timestamp=1588787369955&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffsevents%2Fdownload%2Ffsevents-2.1.3.tgz", "fsevents@~2.1.2": "https://registry.npmmirror.com/fsevents/download/fsevents-2.1.3.tgz?cache=0&sync_timestamp=1588787369955&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffsevents%2Fdownload%2Ffsevents-2.1.3.tgz", "fsevents@~2.3.2": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz", "function-bind@^1.1.1": "https://registry.npmmirror.com/function-bind/download/function-bind-1.1.1.tgz", "gensync@^1.0.0-beta.1": "https://registry.npmmirror.com/gensync/download/gensync-1.0.0-beta.1.tgz", "get-caller-file@^2.0.1": "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-2.0.5.tgz", "get-package-type@^0.1.0": "https://registry.npmmirror.com/get-package-type/download/get-package-type-0.1.0.tgz", "get-stream@^4.0.0": "https://registry.npmmirror.com/get-stream/download/get-stream-4.1.0.tgz", "get-stream@^5.0.0": "https://registry.npmmirror.com/get-stream/download/get-stream-5.1.0.tgz", "get-value@^2.0.3": "https://registry.npmmirror.com/get-value/download/get-value-2.0.6.tgz", "get-value@^2.0.6": "https://registry.npmmirror.com/get-value/download/get-value-2.0.6.tgz", "getpass@^0.1.1": "https://registry.npmmirror.com/getpass/download/getpass-0.1.7.tgz", "glob-parent@^3.1.0": "https://registry.npmmirror.com/glob-parent/download/glob-parent-3.1.0.tgz", "glob-parent@~5.1.0": "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.1.tgz", "glob-parent@~5.1.2": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "glob-to-regexp@^0.3.0": "https://registry.npmmirror.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz", "glob@^7.0.0": "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1573078079496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglob%2Fdownload%2Fglob-7.1.6.tgz", "glob@^7.0.3": "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1573078079496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglob%2Fdownload%2Fglob-7.1.6.tgz", "glob@^7.1.1": "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1573078079496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglob%2Fdownload%2Fglob-7.1.6.tgz", "glob@^7.1.2": "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1573078079496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglob%2Fdownload%2Fglob-7.1.6.tgz", "glob@^7.1.3": "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1573078079496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglob%2Fdownload%2Fglob-7.1.6.tgz", "glob@^7.1.4": "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1573078079496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglob%2Fdownload%2Fglob-7.1.6.tgz", "globals@^11.1.0": "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz", "globby@^6.1.0": "https://registry.npmmirror.com/globby/download/globby-6.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-6.1.0.tgz", "globby@^7.1.1": "https://registry.npmmirror.com/globby/download/globby-7.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-7.1.1.tgz", "globby@^9.2.0": "https://registry.npmmirror.com/globby/download/globby-9.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-9.2.0.tgz", "graceful-fs@^4.1.11": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.4.tgz?cache=0&sync_timestamp=1588086876757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.4.tgz", "graceful-fs@^4.1.15": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.4.tgz?cache=0&sync_timestamp=1588086876757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.4.tgz", "graceful-fs@^4.1.2": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.4.tgz?cache=0&sync_timestamp=1588086876757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.4.tgz", "graceful-fs@^4.1.6": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.4.tgz?cache=0&sync_timestamp=1588086876757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.4.tgz", "graceful-fs@^4.2.0": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.4.tgz?cache=0&sync_timestamp=1588086876757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.4.tgz", "graceful-fs@^4.2.2": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.4.tgz?cache=0&sync_timestamp=1588086876757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.4.tgz", "graceful-fs@^4.2.4": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.4.tgz?cache=0&sync_timestamp=1588086876757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.4.tgz", "growly@^1.3.0": "https://registry.npmmirror.com/growly/download/growly-1.3.0.tgz", "gzip-size@^5.0.0": "https://registry.npmmirror.com/gzip-size/download/gzip-size-5.1.1.tgz", "handle-thing@^2.0.0": "https://registry.npmmirror.com/handle-thing/download/handle-thing-2.0.1.tgz", "har-schema@^2.0.0": "https://registry.npmmirror.com/har-schema/download/har-schema-2.0.0.tgz", "har-validator@~5.1.3": "https://registry.npmmirror.com/har-validator/download/har-validator-5.1.3.tgz", "has-ansi@^2.0.0": "https://registry.npmmirror.com/has-ansi/download/has-ansi-2.0.0.tgz", "has-binary2@~1.0.2": "https://registry.npmmirror.com/has-binary2/download/has-binary2-1.0.3.tgz", "has-cors@1.1.0": "https://registry.npmmirror.com/has-cors/download/has-cors-1.1.0.tgz", "has-flag@^3.0.0": "https://registry.npmmirror.com/has-flag/download/has-flag-3.0.0.tgz", "has-flag@^4.0.0": "https://registry.npmmirror.com/has-flag/download/has-flag-4.0.0.tgz", "has-symbols@^1.0.0": "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.1.tgz", "has-symbols@^1.0.1": "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.1.tgz", "has-value@^0.3.1": "https://registry.npmmirror.com/has-value/download/has-value-0.3.1.tgz", "has-value@^1.0.0": "https://registry.npmmirror.com/has-value/download/has-value-1.0.0.tgz", "has-values@^0.1.4": "https://registry.npmmirror.com/has-values/download/has-values-0.1.4.tgz", "has-values@^1.0.0": "https://registry.npmmirror.com/has-values/download/has-values-1.0.0.tgz", "has@^1.0.0": "https://registry.npmmirror.com/has/download/has-1.0.3.tgz", "has@^1.0.3": "https://registry.npmmirror.com/has/download/has-1.0.3.tgz", "hash-base@^3.0.0": "https://registry.npmmirror.com/hash-base/download/hash-base-3.1.0.tgz", "hash-sum@^1.0.2": "https://registry.npmmirror.com/hash-sum/download/hash-sum-1.0.2.tgz", "hash-sum@^2.0.0": "https://registry.npmmirror.com/hash-sum/download/hash-sum-2.0.0.tgz", "hash.js@^1.0.0": "https://registry.npmmirror.com/hash.js/download/hash.js-1.1.7.tgz", "hash.js@^1.0.3": "https://registry.npmmirror.com/hash.js/download/hash.js-1.1.7.tgz", "he@1.2.x": "https://registry.npmmirror.com/he/download/he-1.2.0.tgz", "he@^1.1.0": "https://registry.npmmirror.com/he/download/he-1.2.0.tgz", "hex-color-regex@^1.1.0": "https://registry.npmmirror.com/hex-color-regex/download/hex-color-regex-1.1.0.tgz", "highlight.js@^9.6.0": "https://registry.npmmirror.com/highlight.js/download/highlight.js-9.18.1.tgz", "hmac-drbg@^1.0.0": "https://registry.npmmirror.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz", "hoopy@^0.1.4": "https://registry.npmmirror.com/hoopy/download/hoopy-0.1.4.tgz", "hosted-git-info@^2.1.4": "https://registry.npmmirror.com/hosted-git-info/download/hosted-git-info-2.8.8.tgz?cache=0&sync_timestamp=1583017392137&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhosted-git-info%2Fdownload%2Fhosted-git-info-2.8.8.tgz", "hpack.js@^2.1.6": "https://registry.npmmirror.com/hpack.js/download/hpack.js-2.1.6.tgz", "hsl-regex@^1.0.0": "https://registry.npmmirror.com/hsl-regex/download/hsl-regex-1.0.0.tgz", "hsla-regex@^1.0.0": "https://registry.npmmirror.com/hsla-regex/download/hsla-regex-1.0.0.tgz", "html-comment-regex@^1.1.0": "https://registry.npmmirror.com/html-comment-regex/download/html-comment-regex-1.1.2.tgz", "html-encoding-sniffer@^1.0.2": "https://registry.npmmirror.com/html-encoding-sniffer/download/html-encoding-sniffer-1.0.2.tgz", "html-entities@^1.3.1": "https://registry.npmmirror.com/html-entities/download/html-entities-1.3.1.tgz", "html-escaper@^2.0.0": "https://registry.npmmirror.com/html-escaper/download/html-escaper-2.0.2.tgz", "html-minifier@^3.2.3": "https://registry.npmmirror.com/html-minifier/download/html-minifier-3.5.21.tgz", "html-tags@^2.0.0": "https://registry.npmmirror.com/html-tags/download/html-tags-2.0.0.tgz", "html-webpack-plugin@^3.2.0": "https://registry.npmmirror.com/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz?cache=0&sync_timestamp=1588268411154&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtml-webpack-plugin%2Fdownload%2Fhtml-webpack-plugin-3.2.0.tgz", "htmlparser2@^3.3.0": "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-3.10.1.tgz", "http-assert@^1.3.0": "https://registry.npmmirror.com/http-assert/download/http-assert-1.4.1.tgz", "http-deceiver@^1.2.7": "https://registry.npmmirror.com/http-deceiver/download/http-deceiver-1.2.7.tgz", "http-errors@1.7.2": "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.2.tgz?cache=0&sync_timestamp=1561418493658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.2.tgz", "http-errors@1.7.3": "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz?cache=0&sync_timestamp=1561418493658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.3.tgz", "http-errors@^1.3.1": "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz?cache=0&sync_timestamp=1561418493658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.3.tgz", "http-errors@^1.6.3": "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz?cache=0&sync_timestamp=1561418493658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.3.tgz", "http-errors@~1.6.2": "https://registry.npmmirror.com/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1561418493658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz", "http-errors@~1.7.2": "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz?cache=0&sync_timestamp=1561418493658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.3.tgz", "http-parser-js@>=0.4.0 <0.4.11": "https://registry.npmmirror.com/http-parser-js/download/http-parser-js-0.4.10.tgz?cache=0&sync_timestamp=1572714627611&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-parser-js%2Fdownload%2Fhttp-parser-js-0.4.10.tgz", "http-proxy-middleware@0.19.1": "https://registry.npmmirror.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz?cache=0&sync_timestamp=1589915518285&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-proxy-middleware%2Fdownload%2Fhttp-proxy-middleware-0.19.1.tgz", "http-proxy@^1.17.0": "https://registry.npmmirror.com/http-proxy/download/http-proxy-1.18.1.tgz", "http-signature@~1.2.0": "https://registry.npmmirror.com/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1585807808622&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz", "https-browserify@^1.0.0": "https://registry.npmmirror.com/https-browserify/download/https-browserify-1.0.0.tgz", "human-signals@^1.1.1": "https://registry.npmmirror.com/human-signals/download/human-signals-1.1.1.tgz?cache=0&sync_timestamp=1584198662293&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhuman-signals%2Fdownload%2Fhuman-signals-1.1.1.tgz", "iconv-lite@0.4.24": "https://registry.npmmirror.com/iconv-lite/download/iconv-lite-0.4.24.tgz?cache=0&sync_timestamp=1579333981154&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.4.24.tgz", "icss-replace-symbols@^1.1.0": "https://registry.npmmirror.com/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz", "icss-utils@^4.0.0": "https://registry.npmmirror.com/icss-utils/download/icss-utils-4.1.1.tgz", "icss-utils@^4.1.0": "https://registry.npmmirror.com/icss-utils/download/icss-utils-4.1.1.tgz", "icss-utils@^4.1.1": "https://registry.npmmirror.com/icss-utils/download/icss-utils-4.1.1.tgz", "ieee754@^1.1.4": "https://registry.npmmirror.com/ieee754/download/ieee754-1.1.13.tgz", "iferr@^0.1.5": "https://registry.npmmirror.com/iferr/download/iferr-0.1.5.tgz", "ignore@^3.3.5": "https://registry.npmmirror.com/ignore/download/ignore-3.3.10.tgz?cache=0&sync_timestamp=1565775199290&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fignore%2Fdownload%2Fignore-3.3.10.tgz", "ignore@^4.0.3": "https://registry.npmmirror.com/ignore/download/ignore-4.0.6.tgz?cache=0&sync_timestamp=1565775199290&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fignore%2Fdownload%2Fignore-4.0.6.tgz", "immediate@~3.0.5": "https://registry.npmmirror.com/immediate/download/immediate-3.0.6.tgz", "import-cwd@^2.0.0": "https://registry.npmmirror.com/import-cwd/download/import-cwd-2.1.0.tgz", "import-fresh@^2.0.0": "https://registry.npmmirror.com/import-fresh/download/import-fresh-2.0.0.tgz", "import-from@^2.1.0": "https://registry.npmmirror.com/import-from/download/import-from-2.1.0.tgz", "import-local@^2.0.0": "https://registry.npmmirror.com/import-local/download/import-local-2.0.0.tgz", "import-local@^3.0.2": "https://registry.npmmirror.com/import-local/download/import-local-3.0.2.tgz", "imurmurhash@^0.1.4": "https://registry.npmmirror.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "indent-string@^4.0.0": "https://registry.npmmirror.com/indent-string/download/indent-string-4.0.0.tgz", "indexes-of@^1.0.1": "https://registry.npmmirror.com/indexes-of/download/indexes-of-1.0.1.tgz", "indexof@0.0.1": "https://registry.npmmirror.com/indexof/download/indexof-0.0.1.tgz", "infer-owner@^1.0.3": "https://registry.npmmirror.com/infer-owner/download/infer-owner-1.0.4.tgz", "infer-owner@^1.0.4": "https://registry.npmmirror.com/infer-owner/download/infer-owner-1.0.4.tgz", "inflation@^2.0.0": "https://registry.npmmirror.com/inflation/download/inflation-2.0.0.tgz", "inflight@^1.0.4": "https://registry.npmmirror.com/inflight/download/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "inherits@2.0.1": "https://registry.npmmirror.com/inherits/download/inherits-2.0.1.tgz", "inherits@2.0.3": "https://registry.npmmirror.com/inherits/download/inherits-2.0.3.tgz", "inherits@2.0.4": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "inherits@^2.0.1": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "inherits@^2.0.3": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "inherits@^2.0.4": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "inherits@~2.0.1": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "inherits@~2.0.3": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "ini@~1.3.0": "https://registry.npmmirror.com/ini/download/ini-1.3.5.tgz", "internal-ip@^4.3.0": "https://registry.npmmirror.com/internal-ip/download/internal-ip-4.3.0.tgz", "interpret@^1.0.0": "https://registry.npmmirror.com/interpret/download/interpret-1.2.0.tgz", "intersection-observer@^0.7.0": "https://registry.npmmirror.com/intersection-observer/download/intersection-observer-0.7.0.tgz", "invariant@^2.2.2": "https://registry.npmmirror.com/invariant/download/invariant-2.2.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finvariant%2Fdownload%2Finvariant-2.2.4.tgz", "invariant@^2.2.4": "https://registry.npmmirror.com/invariant/download/invariant-2.2.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finvariant%2Fdownload%2Finvariant-2.2.4.tgz", "ip-regex@^2.1.0": "https://registry.npmmirror.com/ip-regex/download/ip-regex-2.1.0.tgz", "ip@^1.1.0": "https://registry.npmmirror.com/ip/download/ip-1.1.5.tgz", "ip@^1.1.5": "https://registry.npmmirror.com/ip/download/ip-1.1.5.tgz", "ipaddr.js@1.9.1": "https://registry.npmmirror.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz", "ipaddr.js@^1.9.0": "https://registry.npmmirror.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz", "is-absolute-url@^2.0.0": "https://registry.npmmirror.com/is-absolute-url/download/is-absolute-url-2.1.0.tgz?cache=0&sync_timestamp=1569736493122&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-absolute-url%2Fdownload%2Fis-absolute-url-2.1.0.tgz", "is-absolute-url@^3.0.3": "https://registry.npmmirror.com/is-absolute-url/download/is-absolute-url-3.0.3.tgz?cache=0&sync_timestamp=1569736493122&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-absolute-url%2Fdownload%2Fis-absolute-url-3.0.3.tgz", "is-accessor-descriptor@^0.1.6": "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz", "is-accessor-descriptor@^1.0.0": "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "is-arguments@^1.0.4": "https://registry.npmmirror.com/is-arguments/download/is-arguments-1.0.4.tgz", "is-arrayish@^0.2.1": "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.2.1.tgz", "is-arrayish@^0.3.1": "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.3.2.tgz", "is-binary-path@^1.0.0": "https://registry.npmmirror.com/is-binary-path/download/is-binary-path-1.0.1.tgz", "is-binary-path@~2.1.0": "https://registry.npmmirror.com/is-binary-path/download/is-binary-path-2.1.0.tgz", "is-buffer@^1.1.5": "https://registry.npmmirror.com/is-buffer/download/is-buffer-1.1.6.tgz", "is-buffer@~1.1.1": "https://registry.npmmirror.com/is-buffer/download/is-buffer-1.1.6.tgz", "is-callable@^1.1.4": "https://registry.npmmirror.com/is-callable/download/is-callable-1.1.5.tgz", "is-callable@^1.1.5": "https://registry.npmmirror.com/is-callable/download/is-callable-1.1.5.tgz", "is-ci@^2.0.0": "https://registry.npmmirror.com/is-ci/download/is-ci-2.0.0.tgz", "is-color-stop@^1.0.0": "https://registry.npmmirror.com/is-color-stop/download/is-color-stop-1.1.0.tgz", "is-data-descriptor@^0.1.4": "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz", "is-data-descriptor@^1.0.0": "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "is-date-object@^1.0.1": "https://registry.npmmirror.com/is-date-object/download/is-date-object-1.0.2.tgz?cache=0&sync_timestamp=1576729182289&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-date-object%2Fdownload%2Fis-date-object-1.0.2.tgz", "is-descriptor@^0.1.0": "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-0.1.6.tgz", "is-descriptor@^1.0.0": "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-1.0.2.tgz", "is-descriptor@^1.0.2": "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-1.0.2.tgz", "is-directory@^0.3.1": "https://registry.npmmirror.com/is-directory/download/is-directory-0.3.1.tgz", "is-docker@^2.0.0": "https://registry.npmmirror.com/is-docker/download/is-docker-2.0.0.tgz", "is-extendable@^0.1.0": "https://registry.npmmirror.com/is-extendable/download/is-extendable-0.1.1.tgz", "is-extendable@^0.1.1": "https://registry.npmmirror.com/is-extendable/download/is-extendable-0.1.1.tgz", "is-extendable@^1.0.1": "https://registry.npmmirror.com/is-extendable/download/is-extendable-1.0.1.tgz", "is-extglob@^2.1.0": "https://registry.npmmirror.com/is-extglob/download/is-extglob-2.1.1.tgz", "is-extglob@^2.1.1": "https://registry.npmmirror.com/is-extglob/download/is-extglob-2.1.1.tgz", "is-fullwidth-code-point@^2.0.0": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "is-generator-fn@^2.0.0": "https://registry.npmmirror.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz", "is-generator-function@^1.0.7": "https://registry.npmmirror.com/is-generator-function/download/is-generator-function-1.0.7.tgz", "is-glob@^3.1.0": "https://registry.npmmirror.com/is-glob/download/is-glob-3.1.0.tgz", "is-glob@^4.0.0": "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.1.tgz", "is-glob@^4.0.1": "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.1.tgz", "is-glob@~4.0.1": "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.1.tgz", "is-number@^3.0.0": "https://registry.npmmirror.com/is-number/download/is-number-3.0.0.tgz", "is-number@^7.0.0": "https://registry.npmmirror.com/is-number/download/is-number-7.0.0.tgz", "is-obj@^2.0.0": "https://registry.npmmirror.com/is-obj/download/is-obj-2.0.0.tgz", "is-path-cwd@^2.0.0": "https://registry.npmmirror.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz?cache=0&sync_timestamp=1562347283002&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-path-cwd%2Fdownload%2Fis-path-cwd-2.2.0.tgz", "is-path-in-cwd@^2.0.0": "https://registry.npmmirror.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz", "is-path-inside@^2.1.0": "https://registry.npmmirror.com/is-path-inside/download/is-path-inside-2.1.0.tgz", "is-plain-obj@^1.0.0": "https://registry.npmmirror.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz", "is-plain-object@^2.0.3": "https://registry.npmmirror.com/is-plain-object/download/is-plain-object-2.0.4.tgz", "is-plain-object@^2.0.4": "https://registry.npmmirror.com/is-plain-object/download/is-plain-object-2.0.4.tgz", "is-regex@^1.0.4": "https://registry.npmmirror.com/is-regex/download/is-regex-1.0.5.tgz?cache=0&sync_timestamp=1576454688491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-regex%2Fdownload%2Fis-regex-1.0.5.tgz", "is-regex@^1.0.5": "https://registry.npmmirror.com/is-regex/download/is-regex-1.0.5.tgz?cache=0&sync_timestamp=1576454688491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-regex%2Fdownload%2Fis-regex-1.0.5.tgz", "is-resolvable@^1.0.0": "https://registry.npmmirror.com/is-resolvable/download/is-resolvable-1.1.0.tgz", "is-stream@^1.1.0": "https://registry.npmmirror.com/is-stream/download/is-stream-1.1.0.tgz", "is-stream@^2.0.0": "https://registry.npmmirror.com/is-stream/download/is-stream-2.0.0.tgz", "is-svg@^3.0.0": "https://registry.npmmirror.com/is-svg/download/is-svg-3.0.0.tgz", "is-symbol@^1.0.2": "https://registry.npmmirror.com/is-symbol/download/is-symbol-1.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-symbol%2Fdownload%2Fis-symbol-1.0.3.tgz", "is-typedarray@^1.0.0": "https://registry.npmmirror.com/is-typedarray/download/is-typedarray-1.0.0.tgz", "is-typedarray@~1.0.0": "https://registry.npmmirror.com/is-typedarray/download/is-typedarray-1.0.0.tgz", "is-windows@^1.0.2": "https://registry.npmmirror.com/is-windows/download/is-windows-1.0.2.tgz", "is-wsl@^1.1.0": "https://registry.npmmirror.com/is-wsl/download/is-wsl-1.1.0.tgz?cache=0&sync_timestamp=1588494180082&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-wsl%2Fdownload%2Fis-wsl-1.1.0.tgz", "is-wsl@^2.1.1": "https://registry.npmmirror.com/is-wsl/download/is-wsl-2.2.0.tgz?cache=0&sync_timestamp=1588494180082&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-wsl%2Fdownload%2Fis-wsl-2.2.0.tgz", "isarray@0.0.1": "https://registry.npmmirror.com/isarray/download/isarray-0.0.1.tgz", "isarray@1.0.0": "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz", "isarray@2.0.1": "https://registry.npmmirror.com/isarray/download/isarray-2.0.1.tgz", "isarray@^1.0.0": "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz", "isarray@~1.0.0": "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz", "isexe@^2.0.0": "https://registry.npmmirror.com/isexe/download/isexe-2.0.0.tgz", "isobject@^2.0.0": "https://registry.npmmirror.com/isobject/download/isobject-2.1.0.tgz", "isobject@^3.0.0": "https://registry.npmmirror.com/isobject/download/isobject-3.0.1.tgz", "isobject@^3.0.1": "https://registry.npmmirror.com/isobject/download/isobject-3.0.1.tgz", "isstream@~0.1.2": "https://registry.npmmirror.com/isstream/download/isstream-0.1.2.tgz", "istanbul-lib-coverage@^3.0.0": "https://registry.npmmirror.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.0.0.tgz", "istanbul-lib-instrument@^4.0.0": "https://registry.npmmirror.com/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.3.tgz", "istanbul-lib-report@^3.0.0": "https://registry.npmmirror.com/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz", "istanbul-lib-source-maps@^4.0.0": "https://registry.npmmirror.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.0.tgz?cache=0&sync_timestamp=1577062405633&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fistanbul-lib-source-maps%2Fdownload%2Fistanbul-lib-source-maps-4.0.0.tgz", "istanbul-reports@^3.0.2": "https://registry.npmmirror.com/istanbul-reports/download/istanbul-reports-3.0.2.tgz", "javascript-stringify@^2.0.1": "https://registry.npmmirror.com/javascript-stringify/download/javascript-stringify-2.0.1.tgz", "jest-changed-files@^25.5.0": "https://registry.npmmirror.com/jest-changed-files/download/jest-changed-files-25.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-changed-files%2Fdownload%2Fjest-changed-files-25.5.0.tgz", "jest-cli@^25.5.4": "https://registry.npmmirror.com/jest-cli/download/jest-cli-25.5.4.tgz?cache=0&sync_timestamp=1588675322996&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-cli%2Fdownload%2Fjest-cli-25.5.4.tgz", "jest-config@^25.5.4": "https://registry.npmmirror.com/jest-config/download/jest-config-25.5.4.tgz?cache=0&sync_timestamp=1588675314467&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-config%2Fdownload%2Fjest-config-25.5.4.tgz", "jest-diff@^25.5.0": "https://registry.npmmirror.com/jest-diff/download/jest-diff-25.5.0.tgz", "jest-docblock@^25.3.0": "https://registry.npmmirror.com/jest-docblock/download/jest-docblock-25.3.0.tgz", "jest-each@^25.5.0": "https://registry.npmmirror.com/jest-each/download/jest-each-25.5.0.tgz?cache=0&sync_timestamp=1588675329383&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-each%2Fdownload%2Fjest-each-25.5.0.tgz", "jest-environment-jsdom@^25.5.0": "https://registry.npmmirror.com/jest-environment-jsdom/download/jest-environment-jsdom-25.5.0.tgz", "jest-environment-node@^25.5.0": "https://registry.npmmirror.com/jest-environment-node/download/jest-environment-node-25.5.0.tgz?cache=0&sync_timestamp=1588675336982&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-environment-node%2Fdownload%2Fjest-environment-node-25.5.0.tgz", "jest-get-type@^25.2.6": "https://registry.npmmirror.com/jest-get-type/download/jest-get-type-25.2.6.tgz", "jest-haste-map@^25.5.1": "https://registry.npmmirror.com/jest-haste-map/download/jest-haste-map-25.5.1.tgz", "jest-jasmine2@^25.5.4": "https://registry.npmmirror.com/jest-jasmine2/download/jest-jasmine2-25.5.4.tgz?cache=0&sync_timestamp=1588675312987&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-jasmine2%2Fdownload%2Fjest-jasmine2-25.5.4.tgz", "jest-leak-detector@^25.5.0": "https://registry.npmmirror.com/jest-leak-detector/download/jest-leak-detector-25.5.0.tgz?cache=0&sync_timestamp=1588675417838&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-leak-detector%2Fdownload%2Fjest-leak-detector-25.5.0.tgz", "jest-matcher-utils@^25.5.0": "https://registry.npmmirror.com/jest-matcher-utils/download/jest-matcher-utils-25.5.0.tgz?cache=0&sync_timestamp=1588675334251&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-matcher-utils%2Fdownload%2Fjest-matcher-utils-25.5.0.tgz", "jest-message-util@^25.5.0": "https://registry.npmmirror.com/jest-message-util/download/jest-message-util-25.5.0.tgz?cache=0&sync_timestamp=1588675416652&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-message-util%2Fdownload%2Fjest-message-util-25.5.0.tgz", "jest-mock@^25.5.0": "https://registry.npmmirror.com/jest-mock/download/jest-mock-25.5.0.tgz?cache=0&sync_timestamp=1588675318142&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-mock%2Fdownload%2Fjest-mock-25.5.0.tgz", "jest-pnp-resolver@^1.2.1": "https://registry.npmmirror.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.1.tgz", "jest-regex-util@^25.2.6": "https://registry.npmmirror.com/jest-regex-util/download/jest-regex-util-25.2.6.tgz", "jest-resolve-dependencies@^25.5.4": "https://registry.npmmirror.com/jest-resolve-dependencies/download/jest-resolve-dependencies-25.5.4.tgz?cache=0&sync_timestamp=1588675308084&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-resolve-dependencies%2Fdownload%2Fjest-resolve-dependencies-25.5.4.tgz", "jest-resolve@^25.5.1": "https://registry.npmmirror.com/jest-resolve/download/jest-resolve-25.5.1.tgz?cache=0&sync_timestamp=1588675323403&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-resolve%2Fdownload%2Fjest-resolve-25.5.1.tgz", "jest-runner@^25.5.4": "https://registry.npmmirror.com/jest-runner/download/jest-runner-25.5.4.tgz?cache=0&sync_timestamp=1588675313164&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-runner%2Fdownload%2Fjest-runner-25.5.4.tgz", "jest-runtime@^25.5.4": "https://registry.npmmirror.com/jest-runtime/download/jest-runtime-25.5.4.tgz?cache=0&sync_timestamp=1588675313048&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-runtime%2Fdownload%2Fjest-runtime-25.5.4.tgz", "jest-serializer@^25.5.0": "https://registry.npmmirror.com/jest-serializer/download/jest-serializer-25.5.0.tgz", "jest-snapshot@^25.5.1": "https://registry.npmmirror.com/jest-snapshot/download/jest-snapshot-25.5.1.tgz?cache=0&sync_timestamp=1588675302868&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-snapshot%2Fdownload%2Fjest-snapshot-25.5.1.tgz", "jest-util@^25.5.0": "https://registry.npmmirror.com/jest-util/download/jest-util-25.5.0.tgz?cache=0&sync_timestamp=1588675360560&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-util%2Fdownload%2Fjest-util-25.5.0.tgz", "jest-validate@^25.5.0": "https://registry.npmmirror.com/jest-validate/download/jest-validate-25.5.0.tgz?cache=0&sync_timestamp=1588675419522&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-validate%2Fdownload%2Fjest-validate-25.5.0.tgz", "jest-watcher@^25.5.0": "https://registry.npmmirror.com/jest-watcher/download/jest-watcher-25.5.0.tgz?cache=0&sync_timestamp=1588675365689&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-watcher%2Fdownload%2Fjest-watcher-25.5.0.tgz", "jest-worker@^25.4.0": "https://registry.npmmirror.com/jest-worker/download/jest-worker-25.5.0.tgz", "jest-worker@^25.5.0": "https://registry.npmmirror.com/jest-worker/download/jest-worker-25.5.0.tgz", "jest@^25.4.0": "https://registry.npmmirror.com/jest/download/jest-25.5.4.tgz", "js-message@1.0.5": "https://registry.npmmirror.com/js-message/download/js-message-1.0.5.tgz", "js-queue@2.0.0": "https://registry.npmmirror.com/js-queue/download/js-queue-2.0.0.tgz", "js-tokens@^3.0.0 || ^4.0.0": "https://registry.npmmirror.com/js-tokens/download/js-tokens-4.0.0.tgz", "js-tokens@^4.0.0": "https://registry.npmmirror.com/js-tokens/download/js-tokens-4.0.0.tgz", "js-yaml@^3.13.1": "https://registry.npmmirror.com/js-yaml/download/js-yaml-3.13.1.tgz", "jsbn@~0.1.0": "https://registry.npmmirror.com/jsbn/download/jsbn-0.1.1.tgz", "jsdom@^15.2.1": "https://registry.npmmirror.com/jsdom/download/jsdom-15.2.1.tgz", "jsesc@^2.5.1": "https://registry.npmmirror.com/jsesc/download/jsesc-2.5.2.tgz", "jsesc@~0.5.0": "https://registry.npmmirror.com/jsesc/download/jsesc-0.5.0.tgz", "json-parse-better-errors@^1.0.1": "https://registry.npmmirror.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz", "json-parse-better-errors@^1.0.2": "https://registry.npmmirror.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmmirror.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "json-schema@0.2.3": "https://registry.npmmirror.com/json-schema/download/json-schema-0.2.3.tgz", "json-stringify-safe@~5.0.1": "https://registry.npmmirror.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz", "json3@^3.3.2": "https://registry.npmmirror.com/json3/download/json3-3.3.3.tgz", "json5@^0.5.0": "https://registry.npmmirror.com/json5/download/json5-0.5.1.tgz?cache=0&sync_timestamp=1586046271069&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-0.5.1.tgz", "json5@^1.0.1": "https://registry.npmmirror.com/json5/download/json5-1.0.1.tgz?cache=0&sync_timestamp=1586046271069&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-1.0.1.tgz", "json5@^2.1.2": "https://registry.npmmirror.com/json5/download/json5-2.1.3.tgz?cache=0&sync_timestamp=1586046271069&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-2.1.3.tgz", "jsonfile@^4.0.0": "https://registry.npmmirror.com/jsonfile/download/jsonfile-4.0.0.tgz", "jsprim@^1.2.2": "https://registry.npmmirror.com/jsprim/download/jsprim-1.4.1.tgz", "jsrsasign@^7.2.2": "https://registry.npmmirror.com/jsrsasign/download/jsrsasign-7.2.2.tgz", "jszip@^3.1.5": "https://registry.npmmirror.com/jszip/download/jszip-3.4.0.tgz", "jszip@^3.2.0": "https://registry.npmmirror.com/jszip/download/jszip-3.4.0.tgz", "keygrip@~1.1.0": "https://registry.npmmirror.com/keygrip/download/keygrip-1.1.0.tgz", "killable@^1.0.1": "https://registry.npmmirror.com/killable/download/killable-1.0.1.tgz", "kind-of@^3.0.2": "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz", "kind-of@^3.0.3": "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz", "kind-of@^3.2.0": "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz", "kind-of@^4.0.0": "https://registry.npmmirror.com/kind-of/download/kind-of-4.0.0.tgz", "kind-of@^5.0.0": "https://registry.npmmirror.com/kind-of/download/kind-of-5.1.0.tgz", "kind-of@^6.0.0": "https://registry.npmmirror.com/kind-of/download/kind-of-6.0.3.tgz", "kind-of@^6.0.2": "https://registry.npmmirror.com/kind-of/download/kind-of-6.0.3.tgz", "kleur@^3.0.3": "https://registry.npmmirror.com/kleur/download/kleur-3.0.3.tgz", "klona@^2.0.4": "https://registry.npmmirror.com/klona/-/klona-2.0.6.tgz", "koa-body@^4.0.8": "https://registry.npmmirror.com/koa-body/download/koa-body-4.1.3.tgz", "koa-bodyparser@^4.2.1": "https://registry.npmmirror.com/koa-bodyparser/download/koa-bodyparser-4.3.0.tgz", "koa-compose@^3.0.0": "https://registry.npmmirror.com/koa-compose/download/koa-compose-3.2.1.tgz", "koa-compose@^4.1.0": "https://registry.npmmirror.com/koa-compose/download/koa-compose-4.1.0.tgz", "koa-convert@^1.2.0": "https://registry.npmmirror.com/koa-convert/download/koa-convert-1.2.0.tgz", "koa-mount@^4.0.0": "https://registry.npmmirror.com/koa-mount/download/koa-mount-4.0.0.tgz", "koa-router@^7.4.0": "https://registry.npmmirror.com/koa-router/download/koa-router-7.4.0.tgz", "koa-send@^5.0.0": "https://registry.npmmirror.com/koa-send/download/koa-send-5.0.0.tgz", "koa-static@^5.0.0": "https://registry.npmmirror.com/koa-static/download/koa-static-5.0.0.tgz", "koa@^2.7.0": "https://registry.npmmirror.com/koa/download/koa-2.12.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fkoa%2Fdownload%2Fkoa-2.12.0.tgz", "launch-editor-middleware@^2.2.1": "https://registry.npmmirror.com/launch-editor-middleware/download/launch-editor-middleware-2.2.1.tgz", "launch-editor@^2.2.1": "https://registry.npmmirror.com/launch-editor/download/launch-editor-2.2.1.tgz", "leven@^3.1.0": "https://registry.npmmirror.com/leven/download/leven-3.1.0.tgz", "levenary@^1.1.1": "https://registry.npmmirror.com/levenary/download/levenary-1.1.1.tgz", "levn@~0.3.0": "https://registry.npmmirror.com/levn/download/levn-0.3.0.tgz", "licia@^1.21.0": "https://registry.npmmirror.com/licia/download/licia-1.21.2.tgz", "lie@~3.3.0": "https://registry.npmmirror.com/lie/download/lie-3.3.0.tgz", "lines-and-columns@^1.1.6": "https://registry.npmmirror.com/lines-and-columns/download/lines-and-columns-1.1.6.tgz", "loader-runner@^2.3.1": "https://registry.npmmirror.com/loader-runner/download/loader-runner-2.4.0.tgz", "loader-runner@^2.4.0": "https://registry.npmmirror.com/loader-runner/download/loader-runner-2.4.0.tgz", "loader-utils@^0.2.16": "https://registry.npmmirror.com/loader-utils/download/loader-utils-0.2.17.tgz", "loader-utils@^1.0.2": "https://registry.npmmirror.com/loader-utils/download/loader-utils-1.4.0.tgz", "loader-utils@^1.1.0": "https://registry.npmmirror.com/loader-utils/download/loader-utils-1.4.0.tgz", "loader-utils@^1.2.3": "https://registry.npmmirror.com/loader-utils/download/loader-utils-1.4.0.tgz", "loader-utils@^1.4.0": "https://registry.npmmirror.com/loader-utils/download/loader-utils-1.4.0.tgz", "loader-utils@^2.0.0": "https://registry.npmmirror.com/loader-utils/-/loader-utils-2.0.4.tgz", "locate-path@^2.0.0": "https://registry.npmmirror.com/locate-path/download/locate-path-2.0.0.tgz", "locate-path@^3.0.0": "https://registry.npmmirror.com/locate-path/download/locate-path-3.0.0.tgz", "locate-path@^5.0.0": "https://registry.npmmirror.com/locate-path/download/locate-path-5.0.0.tgz", "lodash.defaultsdeep@^4.6.1": "https://registry.npmmirror.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz", "lodash.kebabcase@^4.1.1": "https://registry.npmmirror.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz", "lodash.mapvalues@^4.6.0": "https://registry.npmmirror.com/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz", "lodash.memoize@^4.1.2": "https://registry.npmmirror.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz", "lodash.sortby@^4.7.0": "https://registry.npmmirror.com/lodash.sortby/download/lodash.sortby-4.7.0.tgz", "lodash.transform@^4.6.0": "https://registry.npmmirror.com/lodash.transform/download/lodash.transform-4.6.0.tgz", "lodash.uniq@^4.5.0": "https://registry.npmmirror.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz", "lodash@^4.17.11": "https://registry.npmmirror.com/lodash/download/lodash-4.17.15.tgz", "lodash@^4.17.13": "https://registry.npmmirror.com/lodash/download/lodash-4.17.15.tgz", "lodash@^4.17.14": "https://registry.npmmirror.com/lodash/download/lodash-4.17.15.tgz", "lodash@^4.17.15": "https://registry.npmmirror.com/lodash/download/lodash-4.17.15.tgz", "lodash@^4.17.3": "https://registry.npmmirror.com/lodash/download/lodash-4.17.15.tgz", "log-symbols@^2.2.0": "https://registry.npmmirror.com/log-symbols/download/log-symbols-2.2.0.tgz", "loglevel@^1.6.8": "https://registry.npmmirror.com/loglevel/download/loglevel-1.6.8.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Floglevel%2Fdownload%2Floglevel-1.6.8.tgz", "lolex@^5.0.0": "https://registry.npmmirror.com/lolex/download/lolex-5.1.2.tgz", "loose-envify@^1.0.0": "https://registry.npmmirror.com/loose-envify/download/loose-envify-1.4.0.tgz", "lower-case@^1.1.1": "https://registry.npmmirror.com/lower-case/download/lower-case-1.1.4.tgz", "lru-cache@^4.1.2": "https://registry.npmmirror.com/lru-cache/download/lru-cache-4.1.5.tgz", "lru-cache@^5.1.1": "https://registry.npmmirror.com/lru-cache/download/lru-cache-5.1.1.tgz", "make-dir@^2.0.0": "https://registry.npmmirror.com/make-dir/download/make-dir-2.1.0.tgz?cache=0&sync_timestamp=1587567610342&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmake-dir%2Fdownload%2Fmake-dir-2.1.0.tgz", "make-dir@^2.1.0": "https://registry.npmmirror.com/make-dir/download/make-dir-2.1.0.tgz?cache=0&sync_timestamp=1587567610342&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmake-dir%2Fdownload%2Fmake-dir-2.1.0.tgz", "make-dir@^3.0.0": "https://registry.npmmirror.com/make-dir/download/make-dir-3.1.0.tgz?cache=0&sync_timestamp=1587567610342&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmake-dir%2Fdownload%2Fmake-dir-3.1.0.tgz", "make-dir@^3.0.2": "https://registry.npmmirror.com/make-dir/download/make-dir-3.1.0.tgz?cache=0&sync_timestamp=1587567610342&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmake-dir%2Fdownload%2Fmake-dir-3.1.0.tgz", "makeerror@1.0.x": "https://registry.npmmirror.com/makeerror/download/makeerror-1.0.11.tgz", "map-cache@^0.2.2": "https://registry.npmmirror.com/map-cache/download/map-cache-0.2.2.tgz", "map-visit@^1.0.0": "https://registry.npmmirror.com/map-visit/download/map-visit-1.0.0.tgz", "md5.js@^1.3.4": "https://registry.npmmirror.com/md5.js/download/md5.js-1.3.5.tgz", "md5@^2.2.1": "https://registry.npmmirror.com/md5/download/md5-2.2.1.tgz", "mdn-data@2.0.4": "https://registry.npmmirror.com/mdn-data/download/mdn-data-2.0.4.tgz?cache=0&sync_timestamp=1584029100530&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.4.tgz", "mdn-data@2.0.6": "https://registry.npmmirror.com/mdn-data/download/mdn-data-2.0.6.tgz?cache=0&sync_timestamp=1584029100530&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.6.tgz", "media-typer@0.3.0": "https://registry.npmmirror.com/media-typer/download/media-typer-0.3.0.tgz", "memory-fs@^0.4.1": "https://registry.npmmirror.com/memory-fs/download/memory-fs-0.4.1.tgz?cache=0&sync_timestamp=1570537491040&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmemory-fs%2Fdownload%2Fmemory-fs-0.4.1.tgz", "memory-fs@^0.5.0": "https://registry.npmmirror.com/memory-fs/download/memory-fs-0.5.0.tgz?cache=0&sync_timestamp=1570537491040&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmemory-fs%2Fdownload%2Fmemory-fs-0.5.0.tgz", "merge-descriptors@1.0.1": "https://registry.npmmirror.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz", "merge-source-map@^1.1.0": "https://registry.npmmirror.com/merge-source-map/download/merge-source-map-1.1.0.tgz", "merge-stream@^2.0.0": "https://registry.npmmirror.com/merge-stream/download/merge-stream-2.0.0.tgz", "merge2@^1.2.3": "https://registry.npmmirror.com/merge2/download/merge2-1.3.0.tgz", "merge@^1.2.1": "https://registry.npmmirror.com/merge/download/merge-1.2.1.tgz", "methods@^1.0.1": "https://registry.npmmirror.com/methods/download/methods-1.1.2.tgz", "methods@~1.1.2": "https://registry.npmmirror.com/methods/download/methods-1.1.2.tgz", "micromatch@^3.1.10": "https://registry.npmmirror.com/micromatch/download/micromatch-3.1.10.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-3.1.10.tgz", "micromatch@^3.1.4": "https://registry.npmmirror.com/micromatch/download/micromatch-3.1.10.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-3.1.10.tgz", "micromatch@^4.0.2": "https://registry.npmmirror.com/micromatch/download/micromatch-4.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-4.0.2.tgz", "miller-rabin@^4.0.0": "https://registry.npmmirror.com/miller-rabin/download/miller-rabin-4.0.1.tgz", "mime-db@1.44.0": "https://registry.npmmirror.com/mime-db/download/mime-db-1.44.0.tgz?cache=0&sync_timestamp=1587603398892&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-db%2Fdownload%2Fmime-db-1.44.0.tgz", "mime-db@>= 1.43.0 < 2": "https://registry.npmmirror.com/mime-db/download/mime-db-1.44.0.tgz?cache=0&sync_timestamp=1587603398892&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-db%2Fdownload%2Fmime-db-1.44.0.tgz", "mime-types@^2.1.12": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.27.tgz?cache=0&sync_timestamp=1587700357177&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.27.tgz", "mime-types@^2.1.18": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.27.tgz?cache=0&sync_timestamp=1587700357177&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.27.tgz", "mime-types@~2.1.17": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.27.tgz?cache=0&sync_timestamp=1587700357177&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.27.tgz", "mime-types@~2.1.19": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.27.tgz?cache=0&sync_timestamp=1587700357177&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.27.tgz", "mime-types@~2.1.24": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.27.tgz?cache=0&sync_timestamp=1587700357177&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.27.tgz", "mime@1.6.0": "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz", "mime@^2.4.4": "https://registry.npmmirror.com/mime/download/mime-2.4.5.tgz", "mimic-fn@^1.0.0": "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-1.2.0.tgz", "mimic-fn@^2.1.0": "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-2.1.0.tgz", "mini-css-extract-plugin@^0.5.0": "https://registry.npmmirror.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.5.0.tgz?cache=0&sync_timestamp=1576856580721&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmini-css-extract-plugin%2Fdownload%2Fmini-css-extract-plugin-0.5.0.tgz", "mini-css-extract-plugin@^0.9.0": "https://registry.npmmirror.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.9.0.tgz?cache=0&sync_timestamp=1576856580721&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmini-css-extract-plugin%2Fdownload%2Fmini-css-extract-plugin-0.9.0.tgz", "mini-types@*": "https://registry.npmmirror.com/mini-types/download/mini-types-0.1.2.tgz", "minimalistic-assert@^1.0.0": "https://registry.npmmirror.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz", "minimalistic-assert@^1.0.1": "https://registry.npmmirror.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz", "minimalistic-crypto-utils@^1.0.0": "https://registry.npmmirror.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz", "minimalistic-crypto-utils@^1.0.1": "https://registry.npmmirror.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz", "minimatch@^3.0.4": "https://registry.npmmirror.com/minimatch/download/minimatch-3.0.4.tgz", "minimist@^1.1.1": "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz", "minimist@^1.2.0": "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz", "minimist@^1.2.5": "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz", "minipass-collect@^1.0.2": "https://registry.npmmirror.com/minipass-collect/download/minipass-collect-1.0.2.tgz", "minipass-flush@^1.0.5": "https://registry.npmmirror.com/minipass-flush/download/minipass-flush-1.0.5.tgz", "minipass-pipeline@^1.2.2": "https://registry.npmmirror.com/minipass-pipeline/download/minipass-pipeline-1.2.3.tgz", "minipass@^3.0.0": "https://registry.npmmirror.com/minipass/download/minipass-3.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fminipass%2Fdownload%2Fminipass-3.1.3.tgz", "minipass@^3.1.1": "https://registry.npmmirror.com/minipass/download/minipass-3.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fminipass%2Fdownload%2Fminipass-3.1.3.tgz", "miniprogram-api-typings@*": "https://registry.npmmirror.com/miniprogram-api-typings/download/miniprogram-api-typings-2.11.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fminiprogram-api-typings%2Fdownload%2Fminiprogram-api-typings-2.11.0.tgz", "mississippi@^3.0.0": "https://registry.npmmirror.com/mississippi/download/mississippi-3.0.0.tgz", "mixin-deep@^1.2.0": "https://registry.npmmirror.com/mixin-deep/download/mixin-deep-1.3.2.tgz", "mkdirp@^0.5.1": "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1587535418745&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz", "mkdirp@^0.5.3": "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1587535418745&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz", "mkdirp@~0.5.1": "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1587535418745&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz", "module-alias@^2.1.0": "https://registry.npmmirror.com/module-alias/download/module-alias-2.2.2.tgz", "moment@^2.24.0": "https://registry.npmmirror.com/moment/download/moment-2.26.0.tgz", "move-concurrently@^1.0.1": "https://registry.npmmirror.com/move-concurrently/download/move-concurrently-1.0.1.tgz", "ms@2.0.0": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.0.0.tgz", "ms@2.1.1": "https://registry.npmmirror.com/ms/download/ms-2.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.1.tgz", "ms@^2.1.1": "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.2.tgz", "multicast-dns-service-types@^1.1.0": "https://registry.npmmirror.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz", "multicast-dns@^6.0.1": "https://registry.npmmirror.com/multicast-dns/download/multicast-dns-6.2.3.tgz?cache=0&sync_timestamp=1585239065356&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmulticast-dns%2Fdownload%2Fmulticast-dns-6.2.3.tgz", "mustache@^3.1.0": "https://registry.npmmirror.com/mustache/download/mustache-3.2.1.tgz", "mz@^2.4.0": "https://registry.npmmirror.com/mz/download/mz-2.7.0.tgz", "mz@^2.7.0": "https://registry.npmmirror.com/mz/download/mz-2.7.0.tgz", "nan@^2.12.1": "https://registry.npmmirror.com/nan/download/nan-2.14.1.tgz", "nanomatch@^1.2.9": "https://registry.npmmirror.com/nanomatch/download/nanomatch-1.2.13.tgz", "natural-compare@^1.4.0": "https://registry.npmmirror.com/natural-compare/download/natural-compare-1.4.0.tgz", "negotiator@0.6.2": "https://registry.npmmirror.com/negotiator/download/negotiator-0.6.2.tgz", "neo-async@^2.5.0": "https://registry.npmmirror.com/neo-async/download/neo-async-2.6.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fneo-async%2Fdownload%2Fneo-async-2.6.1.tgz", "neo-async@^2.6.0": "https://registry.npmmirror.com/neo-async/download/neo-async-2.6.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fneo-async%2Fdownload%2Fneo-async-2.6.1.tgz", "neo-async@^2.6.1": "https://registry.npmmirror.com/neo-async/download/neo-async-2.6.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fneo-async%2Fdownload%2Fneo-async-2.6.1.tgz", "neo-async@^2.6.2": "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz", "nice-try@^1.0.4": "https://registry.npmmirror.com/nice-try/download/nice-try-1.0.5.tgz", "no-case@^2.2.0": "https://registry.npmmirror.com/no-case/download/no-case-2.3.2.tgz?cache=0&sync_timestamp=1576748705107&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fno-case%2Fdownload%2Fno-case-2.3.2.tgz", "node-forge@0.9.0": "https://registry.npmmirror.com/node-forge/download/node-forge-0.9.0.tgz?cache=0&sync_timestamp=1569524669712&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-forge%2Fdownload%2Fnode-forge-0.9.0.tgz", "node-int64@^0.4.0": "https://registry.npmmirror.com/node-int64/download/node-int64-0.4.0.tgz", "node-ipc@^9.1.1": "https://registry.npmmirror.com/node-ipc/download/node-ipc-9.1.1.tgz", "node-libs-browser@^2.2.1": "https://registry.npmmirror.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz", "node-modules-regexp@^1.0.0": "https://registry.npmmirror.com/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz", "node-notifier@^6.0.0": "https://registry.npmmirror.com/node-notifier/download/node-notifier-6.0.0.tgz", "node-releases@^1.1.53": "https://registry.npmmirror.com/node-releases/download/node-releases-1.1.56.tgz?cache=0&sync_timestamp=1589962280441&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-releases%2Fdownload%2Fnode-releases-1.1.56.tgz", "normalize-package-data@^2.5.0": "https://registry.npmmirror.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz", "normalize-path@^2.1.1": "https://registry.npmmirror.com/normalize-path/download/normalize-path-2.1.1.tgz", "normalize-path@^3.0.0": "https://registry.npmmirror.com/normalize-path/download/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmmirror.com/normalize-path/download/normalize-path-3.0.0.tgz", "normalize-range@^0.1.2": "https://registry.npmmirror.com/normalize-range/download/normalize-range-0.1.2.tgz", "normalize-url@1.9.1": "https://registry.npmmirror.com/normalize-url/download/normalize-url-1.9.1.tgz", "normalize-url@^3.0.0": "https://registry.npmmirror.com/normalize-url/download/normalize-url-3.3.0.tgz", "npm-run-path@^2.0.0": "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz", "npm-run-path@^4.0.0": "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-4.0.1.tgz", "nth-check@^1.0.2": "https://registry.npmmirror.com/nth-check/download/nth-check-1.0.2.tgz", "nth-check@~1.0.1": "https://registry.npmmirror.com/nth-check/download/nth-check-1.0.2.tgz", "num2fraction@^1.2.2": "https://registry.npmmirror.com/num2fraction/download/num2fraction-1.2.2.tgz", "nwsapi@^2.2.0": "https://registry.npmmirror.com/nwsapi/download/nwsapi-2.2.0.tgz", "oauth-sign@~0.9.0": "https://registry.npmmirror.com/oauth-sign/download/oauth-sign-0.9.0.tgz", "object-assign@^4.0.1": "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz", "object-assign@^4.1.0": "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz", "object-assign@^4.1.1": "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz", "object-component@0.0.3": "https://registry.npmmirror.com/object-component/download/object-component-0.0.3.tgz", "object-copy@^0.1.0": "https://registry.npmmirror.com/object-copy/download/object-copy-0.1.0.tgz", "object-inspect@^1.7.0": "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.7.0.tgz", "object-is@^1.0.1": "https://registry.npmmirror.com/object-is/download/object-is-1.1.2.tgz?cache=0&sync_timestamp=1586894009620&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-is%2Fdownload%2Fobject-is-1.1.2.tgz", "object-keys@^1.0.11": "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz", "object-keys@^1.0.12": "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz", "object-keys@^1.1.1": "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz", "object-visit@^1.0.0": "https://registry.npmmirror.com/object-visit/download/object-visit-1.0.1.tgz", "object.assign@^4.1.0": "https://registry.npmmirror.com/object.assign/download/object.assign-4.1.0.tgz", "object.getownpropertydescriptors@^2.0.3": "https://registry.npmmirror.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.getownpropertydescriptors%2Fdownload%2Fobject.getownpropertydescriptors-2.1.0.tgz", "object.getownpropertydescriptors@^2.1.0": "https://registry.npmmirror.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.getownpropertydescriptors%2Fdownload%2Fobject.getownpropertydescriptors-2.1.0.tgz", "object.pick@^1.3.0": "https://registry.npmmirror.com/object.pick/download/object.pick-1.3.0.tgz", "object.values@^1.1.0": "https://registry.npmmirror.com/object.values/download/object.values-1.1.1.tgz", "obuf@^1.0.0": "https://registry.npmmirror.com/obuf/download/obuf-1.1.2.tgz", "obuf@^1.1.2": "https://registry.npmmirror.com/obuf/download/obuf-1.1.2.tgz", "on-finished@^2.3.0": "https://registry.npmmirror.com/on-finished/download/on-finished-2.3.0.tgz", "on-finished@~2.3.0": "https://registry.npmmirror.com/on-finished/download/on-finished-2.3.0.tgz", "on-headers@~1.0.2": "https://registry.npmmirror.com/on-headers/download/on-headers-1.0.2.tgz", "once@^1.3.0": "https://registry.npmmirror.com/once/download/once-1.4.0.tgz", "once@^1.3.1": "https://registry.npmmirror.com/once/download/once-1.4.0.tgz", "once@^1.4.0": "https://registry.npmmirror.com/once/download/once-1.4.0.tgz", "onetime@^2.0.0": "https://registry.npmmirror.com/onetime/download/onetime-2.0.1.tgz", "onetime@^5.1.0": "https://registry.npmmirror.com/onetime/download/onetime-5.1.0.tgz", "only@~0.0.2": "https://registry.npmmirror.com/only/download/only-0.0.2.tgz", "open@^6.3.0": "https://registry.npmmirror.com/open/download/open-6.4.0.tgz", "opener@^1.5.1": "https://registry.npmmirror.com/opener/download/opener-1.5.1.tgz", "opn@^5.4.0": "https://registry.npmmirror.com/opn/download/opn-5.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fopn%2Fdownload%2Fopn-5.5.0.tgz", "opn@^5.5.0": "https://registry.npmmirror.com/opn/download/opn-5.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fopn%2Fdownload%2Fopn-5.5.0.tgz", "optionator@^0.8.1": "https://registry.npmmirror.com/optionator/download/optionator-0.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Foptionator%2Fdownload%2Foptionator-0.8.3.tgz", "ora@^3.4.0": "https://registry.npmmirror.com/ora/download/ora-3.4.0.tgz?cache=0&sync_timestamp=1587481470351&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fora%2Fdownload%2Fora-3.4.0.tgz", "original@^1.0.0": "https://registry.npmmirror.com/original/download/original-1.0.2.tgz", "os-browserify@^0.3.0": "https://registry.npmmirror.com/os-browserify/download/os-browserify-0.3.0.tgz", "p-each-series@^2.1.0": "https://registry.npmmirror.com/p-each-series/download/p-each-series-2.1.0.tgz", "p-finally@^1.0.0": "https://registry.npmmirror.com/p-finally/download/p-finally-1.0.0.tgz", "p-finally@^2.0.0": "https://registry.npmmirror.com/p-finally/download/p-finally-2.0.1.tgz", "p-limit@^1.1.0": "https://registry.npmmirror.com/p-limit/download/p-limit-1.3.0.tgz", "p-limit@^2.0.0": "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz", "p-limit@^2.2.0": "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz", "p-limit@^2.2.1": "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz", "p-limit@^2.3.0": "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz", "p-locate@^2.0.0": "https://registry.npmmirror.com/p-locate/download/p-locate-2.0.0.tgz", "p-locate@^3.0.0": "https://registry.npmmirror.com/p-locate/download/p-locate-3.0.0.tgz", "p-locate@^4.1.0": "https://registry.npmmirror.com/p-locate/download/p-locate-4.1.0.tgz", "p-map@^2.0.0": "https://registry.npmmirror.com/p-map/download/p-map-2.1.0.tgz", "p-map@^3.0.0": "https://registry.npmmirror.com/p-map/download/p-map-3.0.0.tgz", "p-retry@^3.0.1": "https://registry.npmmirror.com/p-retry/download/p-retry-3.0.1.tgz", "p-try@^1.0.0": "https://registry.npmmirror.com/p-try/download/p-try-1.0.0.tgz", "p-try@^2.0.0": "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz", "pako@~1.0.2": "https://registry.npmmirror.com/pako/download/pako-1.0.11.tgz", "pako@~1.0.5": "https://registry.npmmirror.com/pako/download/pako-1.0.11.tgz", "parallel-transform@^1.1.0": "https://registry.npmmirror.com/parallel-transform/download/parallel-transform-1.2.0.tgz", "param-case@2.1.x": "https://registry.npmmirror.com/param-case/download/param-case-2.1.1.tgz?cache=0&sync_timestamp=1576721608924&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparam-case%2Fdownload%2Fparam-case-2.1.1.tgz", "parse-asn1@^5.0.0": "https://registry.npmmirror.com/parse-asn1/download/parse-asn1-5.1.5.tgz", "parse-asn1@^5.1.5": "https://registry.npmmirror.com/parse-asn1/download/parse-asn1-5.1.5.tgz", "parse-json@^4.0.0": "https://registry.npmmirror.com/parse-json/download/parse-json-4.0.0.tgz", "parse-json@^5.0.0": "https://registry.npmmirror.com/parse-json/download/parse-json-5.0.0.tgz", "parse5-htmlparser2-tree-adapter@^5.1.1": "https://registry.npmmirror.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-5.1.1.tgz?cache=0&sync_timestamp=1586993715810&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse5-htmlparser2-tree-adapter%2Fdownload%2Fparse5-htmlparser2-tree-adapter-5.1.1.tgz", "parse5@5.1.0": "https://registry.npmmirror.com/parse5/download/parse5-5.1.0.tgz", "parse5@^3.0.3": "https://registry.npmmirror.com/parse5/download/parse5-3.0.3.tgz", "parse5@^5.1.1": "https://registry.npmmirror.com/parse5/download/parse5-5.1.1.tgz", "parseqs@0.0.5": "https://registry.npmmirror.com/parseqs/download/parseqs-0.0.5.tgz", "parseuri@0.0.5": "https://registry.npmmirror.com/parseuri/download/parseuri-0.0.5.tgz?cache=0&sync_timestamp=1568821002283&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparseuri%2Fdownload%2Fparseuri-0.0.5.tgz", "parseurl@^1.3.2": "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz", "parseurl@~1.3.2": "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz", "parseurl@~1.3.3": "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz", "pascalcase@^0.1.1": "https://registry.npmmirror.com/pascalcase/download/pascalcase-0.1.1.tgz", "path-browserify@0.0.1": "https://registry.npmmirror.com/path-browserify/download/path-browserify-0.0.1.tgz", "path-dirname@^1.0.0": "https://registry.npmmirror.com/path-dirname/download/path-dirname-1.0.2.tgz", "path-exists@^3.0.0": "https://registry.npmmirror.com/path-exists/download/path-exists-3.0.0.tgz", "path-exists@^4.0.0": "https://registry.npmmirror.com/path-exists/download/path-exists-4.0.0.tgz", "path-is-absolute@1.0.1": "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "path-is-absolute@^1.0.0": "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "path-is-inside@^1.0.2": "https://registry.npmmirror.com/path-is-inside/download/path-is-inside-1.0.2.tgz", "path-key@^2.0.0": "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz?cache=0&sync_timestamp=1574441431664&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-2.0.1.tgz", "path-key@^2.0.1": "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz?cache=0&sync_timestamp=1574441431664&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-2.0.1.tgz", "path-key@^3.0.0": "https://registry.npmmirror.com/path-key/download/path-key-3.1.1.tgz?cache=0&sync_timestamp=1574441431664&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-3.1.1.tgz", "path-key@^3.1.0": "https://registry.npmmirror.com/path-key/download/path-key-3.1.1.tgz?cache=0&sync_timestamp=1574441431664&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-3.1.1.tgz", "path-parse@^1.0.6": "https://registry.npmmirror.com/path-parse/download/path-parse-1.0.6.tgz", "path-to-regexp@0.1.7": "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-0.1.7.tgz", "path-to-regexp@^1.1.1": "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz", "path-type@^3.0.0": "https://registry.npmmirror.com/path-type/download/path-type-3.0.0.tgz", "pbkdf2@^3.0.3": "https://registry.npmmirror.com/pbkdf2/download/pbkdf2-3.0.17.tgz", "performance-now@^2.1.0": "https://registry.npmmirror.com/performance-now/download/performance-now-2.1.0.tgz", "picomatch@^2.0.4": "https://registry.npmmirror.com/picomatch/download/picomatch-2.2.2.tgz", "picomatch@^2.0.5": "https://registry.npmmirror.com/picomatch/download/picomatch-2.2.2.tgz", "picomatch@^2.2.1": "https://registry.npmmirror.com/picomatch/download/picomatch-2.2.2.tgz", "pify@^2.0.0": "https://registry.npmmirror.com/pify/download/pify-2.3.0.tgz", "pify@^2.3.0": "https://registry.npmmirror.com/pify/download/pify-2.3.0.tgz", "pify@^3.0.0": "https://registry.npmmirror.com/pify/download/pify-3.0.0.tgz", "pify@^4.0.1": "https://registry.npmmirror.com/pify/download/pify-4.0.1.tgz", "pinkie-promise@^2.0.0": "https://registry.npmmirror.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz", "pinkie@^2.0.0": "https://registry.npmmirror.com/pinkie/download/pinkie-2.0.4.tgz", "pirates@^4.0.0": "https://registry.npmmirror.com/pirates/download/pirates-4.0.1.tgz", "pirates@^4.0.1": "https://registry.npmmirror.com/pirates/download/pirates-4.0.1.tgz", "pkg-dir@^3.0.0": "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-3.0.0.tgz", "pkg-dir@^4.1.0": "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz", "pkg-dir@^4.2.0": "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz", "pkg-up@^2.0.0": "https://registry.npmmirror.com/pkg-up/download/pkg-up-2.0.0.tgz", "pn@^1.1.0": "https://registry.npmmirror.com/pn/download/pn-1.1.0.tgz", "pnp-webpack-plugin@^1.6.4": "https://registry.npmmirror.com/pnp-webpack-plugin/download/pnp-webpack-plugin-1.6.4.tgz", "portfinder@^1.0.20": "https://registry.npmmirror.com/portfinder/download/portfinder-1.0.26.tgz", "portfinder@^1.0.25": "https://registry.npmmirror.com/portfinder/download/portfinder-1.0.26.tgz", "portfinder@^1.0.26": "https://registry.npmmirror.com/portfinder/download/portfinder-1.0.26.tgz", "posix-character-classes@^0.1.0": "https://registry.npmmirror.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz", "postcss-calc@^7.0.1": "https://registry.npmmirror.com/postcss-calc/download/postcss-calc-7.0.2.tgz?cache=0&sync_timestamp=1582014221563&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-calc%2Fdownload%2Fpostcss-calc-7.0.2.tgz", "postcss-colormin@^4.0.3": "https://registry.npmmirror.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz", "postcss-comment@^2.0.0": "https://registry.npmmirror.com/postcss-comment/download/postcss-comment-2.0.0.tgz", "postcss-convert-values@^4.0.1": "https://registry.npmmirror.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz", "postcss-discard-comments@^4.0.2": "https://registry.npmmirror.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz", "postcss-discard-duplicates@^4.0.2": "https://registry.npmmirror.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz", "postcss-discard-empty@^4.0.1": "https://registry.npmmirror.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz", "postcss-discard-overridden@^4.0.1": "https://registry.npmmirror.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz", "postcss-helpers@^0.3.2": "https://registry.npmmirror.com/postcss-helpers/download/postcss-helpers-0.3.2.tgz", "postcss-import@^12.0.1": "https://registry.npmmirror.com/postcss-import/download/postcss-import-12.0.1.tgz", "postcss-load-config@^2.0.0": "https://registry.npmmirror.com/postcss-load-config/download/postcss-load-config-2.1.0.tgz", "postcss-loader@^3.0.0": "https://registry.npmmirror.com/postcss-loader/download/postcss-loader-3.0.0.tgz", "postcss-merge-longhand@^4.0.11": "https://registry.npmmirror.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz", "postcss-merge-rules@^4.0.3": "https://registry.npmmirror.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz", "postcss-minify-font-values@^4.0.2": "https://registry.npmmirror.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz", "postcss-minify-gradients@^4.0.2": "https://registry.npmmirror.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz", "postcss-minify-params@^4.0.2": "https://registry.npmmirror.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz", "postcss-minify-selectors@^4.0.2": "https://registry.npmmirror.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz", "postcss-modules-extract-imports@^2.0.0": "https://registry.npmmirror.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-2.0.0.tgz", "postcss-modules-local-by-default@^2.0.6": "https://registry.npmmirror.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-2.0.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-local-by-default%2Fdownload%2Fpostcss-modules-local-by-default-2.0.6.tgz", "postcss-modules-local-by-default@^3.0.2": "https://registry.npmmirror.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-3.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-local-by-default%2Fdownload%2Fpostcss-modules-local-by-default-3.0.2.tgz", "postcss-modules-scope@^2.1.0": "https://registry.npmmirror.com/postcss-modules-scope/download/postcss-modules-scope-2.2.0.tgz", "postcss-modules-scope@^2.2.0": "https://registry.npmmirror.com/postcss-modules-scope/download/postcss-modules-scope-2.2.0.tgz", "postcss-modules-values@^2.0.0": "https://registry.npmmirror.com/postcss-modules-values/download/postcss-modules-values-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-values%2Fdownload%2Fpostcss-modules-values-2.0.0.tgz", "postcss-modules-values@^3.0.0": "https://registry.npmmirror.com/postcss-modules-values/download/postcss-modules-values-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-values%2Fdownload%2Fpostcss-modules-values-3.0.0.tgz", "postcss-normalize-charset@^4.0.1": "https://registry.npmmirror.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz", "postcss-normalize-display-values@^4.0.2": "https://registry.npmmirror.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz", "postcss-normalize-positions@^4.0.2": "https://registry.npmmirror.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz", "postcss-normalize-repeat-style@^4.0.2": "https://registry.npmmirror.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz", "postcss-normalize-string@^4.0.2": "https://registry.npmmirror.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz", "postcss-normalize-timing-functions@^4.0.2": "https://registry.npmmirror.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz", "postcss-normalize-unicode@^4.0.1": "https://registry.npmmirror.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz", "postcss-normalize-url@^4.0.1": "https://registry.npmmirror.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz", "postcss-normalize-whitespace@^4.0.2": "https://registry.npmmirror.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz", "postcss-ordered-values@^4.1.2": "https://registry.npmmirror.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz", "postcss-reduce-initial@^4.0.3": "https://registry.npmmirror.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz", "postcss-reduce-transforms@^4.0.2": "https://registry.npmmirror.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz", "postcss-selector-parser@^3.0.0": "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-3.1.2.tgz", "postcss-selector-parser@^5.0.0": "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-5.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-5.0.0.tgz", "postcss-selector-parser@^6.0.0": "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-6.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-6.0.2.tgz", "postcss-selector-parser@^6.0.2": "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-6.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-6.0.2.tgz", "postcss-svgo@^4.0.2": "https://registry.npmmirror.com/postcss-svgo/download/postcss-svgo-4.0.2.tgz", "postcss-unique-selectors@^4.0.1": "https://registry.npmmirror.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz", "postcss-urlrewrite@^0.2.2": "https://registry.npmmirror.com/postcss-urlrewrite/download/postcss-urlrewrite-0.2.2.tgz", "postcss-value-parser@^3.0.0": "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-3.3.1.tgz", "postcss-value-parser@^3.2.3": "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-3.3.1.tgz", "postcss-value-parser@^3.3.0": "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-3.3.1.tgz", "postcss-value-parser@^3.3.1": "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-3.3.1.tgz", "postcss-value-parser@^4.0.0": "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-4.1.0.tgz", "postcss-value-parser@^4.0.2": "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-4.1.0.tgz", "postcss-value-parser@^4.0.3": "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-4.1.0.tgz", "postcss-value-parser@^4.1.0": "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-4.1.0.tgz", "postcss@^6.0.0": "https://registry.npmmirror.com/postcss/download/postcss-6.0.23.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-6.0.23.tgz", "postcss@^7.0.0": "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz", "postcss@^7.0.1": "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz", "postcss@^7.0.14": "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz", "postcss@^7.0.16": "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz", "postcss@^7.0.27": "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz", "postcss@^7.0.30": "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz", "postcss@^7.0.5": "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz", "postcss@^7.0.6": "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz", "postcss@^7.0.7": "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz", "prelude-ls@~1.1.2": "https://registry.npmmirror.com/prelude-ls/download/prelude-ls-1.1.2.tgz", "prepend-http@^1.0.0": "https://registry.npmmirror.com/prepend-http/download/prepend-http-1.0.4.tgz", "prettier@^1.18.2": "https://registry.npmmirror.com/prettier/download/prettier-1.19.1.tgz?cache=0&sync_timestamp=1587491448785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fprettier%2Fdownload%2Fprettier-1.19.1.tgz", "pretty-error@^2.0.2": "https://registry.npmmirror.com/pretty-error/download/pretty-error-2.1.1.tgz", "pretty-format@^25.5.0": "https://registry.npmmirror.com/pretty-format/download/pretty-format-25.5.0.tgz", "private@^0.1.8": "https://registry.npmmirror.com/private/download/private-0.1.8.tgz", "process-nextick-args@~2.0.0": "https://registry.npmmirror.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz", "process@^0.11.10": "https://registry.npmmirror.com/process/download/process-0.11.10.tgz", "promise-inflight@^1.0.1": "https://registry.npmmirror.com/promise-inflight/download/promise-inflight-1.0.1.tgz", "prompts@^2.0.1": "https://registry.npmmirror.com/prompts/download/prompts-2.3.2.tgz?cache=0&sync_timestamp=1584535638103&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fprompts%2Fdownload%2Fprompts-2.3.2.tgz", "proxy-addr@~2.0.5": "https://registry.npmmirror.com/proxy-addr/download/proxy-addr-2.0.6.tgz", "prr@~1.0.1": "https://registry.npmmirror.com/prr/download/prr-1.0.1.tgz", "pseudomap@^1.0.2": "https://registry.npmmirror.com/pseudomap/download/pseudomap-1.0.2.tgz", "psl@^1.1.28": "https://registry.npmmirror.com/psl/download/psl-1.8.0.tgz?cache=0&sync_timestamp=1585142991033&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpsl%2Fdownload%2Fpsl-1.8.0.tgz", "public-encrypt@^4.0.0": "https://registry.npmmirror.com/public-encrypt/download/public-encrypt-4.0.3.tgz", "pump@^2.0.0": "https://registry.npmmirror.com/pump/download/pump-2.0.1.tgz", "pump@^3.0.0": "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz", "pumpify@^1.3.3": "https://registry.npmmirror.com/pumpify/download/pumpify-1.5.1.tgz?cache=0&sync_timestamp=1569938200736&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpumpify%2Fdownload%2Fpumpify-1.5.1.tgz", "punycode@1.3.2": "https://registry.npmmirror.com/punycode/download/punycode-1.3.2.tgz", "punycode@^1.2.4": "https://registry.npmmirror.com/punycode/download/punycode-1.4.1.tgz", "punycode@^2.1.0": "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz", "punycode@^2.1.1": "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz", "q@^1.1.2": "https://registry.npmmirror.com/q/download/q-1.5.1.tgz", "qr-image@^3.2.0": "https://registry.npmmirror.com/qr-image/download/qr-image-3.2.0.tgz", "qrcode-reader@^1.0.4": "https://registry.npmmirror.com/qrcode-reader/download/qrcode-reader-1.0.4.tgz", "qrcode-terminal@^0.12.0": "https://registry.npmmirror.com/qrcode-terminal/download/qrcode-terminal-0.12.0.tgz", "qs@6.7.0": "https://registry.npmmirror.com/qs/download/qs-6.7.0.tgz", "qs@^6.4.0": "https://registry.npmmirror.com/qs/download/qs-6.9.4.tgz", "qs@^6.5.2": "https://registry.npmmirror.com/qs/download/qs-6.9.4.tgz", "qs@~6.5.2": "https://registry.npmmirror.com/qs/download/qs-6.5.2.tgz", "query-string@^4.1.0": "https://registry.npmmirror.com/query-string/download/query-string-4.3.4.tgz", "querystring-es3@^0.2.0": "https://registry.npmmirror.com/querystring-es3/download/querystring-es3-0.2.1.tgz", "querystring@0.2.0": "https://registry.npmmirror.com/querystring/download/querystring-0.2.0.tgz", "querystringify@^2.1.1": "https://registry.npmmirror.com/querystringify/download/querystringify-2.1.1.tgz", "randombytes@^2.0.0": "https://registry.npmmirror.com/randombytes/download/randombytes-2.1.0.tgz", "randombytes@^2.0.1": "https://registry.npmmirror.com/randombytes/download/randombytes-2.1.0.tgz", "randombytes@^2.0.5": "https://registry.npmmirror.com/randombytes/download/randombytes-2.1.0.tgz", "randomfill@^1.0.3": "https://registry.npmmirror.com/randomfill/download/randomfill-1.0.4.tgz", "range-parser@^1.2.1": "https://registry.npmmirror.com/range-parser/download/range-parser-1.2.1.tgz", "range-parser@~1.2.1": "https://registry.npmmirror.com/range-parser/download/range-parser-1.2.1.tgz", "raw-body@2.4.0": "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.0.tgz", "raw-body@^2.2.0": "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.1.tgz", "raw-body@^2.3.3": "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.1.tgz", "rc@^1.0.1": "https://registry.npmmirror.com/rc/download/rc-1.2.8.tgz", "rc@^1.1.6": "https://registry.npmmirror.com/rc/download/rc-1.2.8.tgz", "react-is@^16.12.0": "https://registry.npmmirror.com/react-is/download/react-is-16.13.1.tgz", "read-cache@^1.0.0": "https://registry.npmmirror.com/read-cache/download/read-cache-1.0.0.tgz", "read-pkg-up@^7.0.1": "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg-up%2Fdownload%2Fread-pkg-up-7.0.1.tgz", "read-pkg@^5.1.1": "https://registry.npmmirror.com/read-pkg/download/read-pkg-5.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg%2Fdownload%2Fread-pkg-5.2.0.tgz", "read-pkg@^5.2.0": "https://registry.npmmirror.com/read-pkg/download/read-pkg-5.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg%2Fdownload%2Fread-pkg-5.2.0.tgz", "readable-stream@1 || 2": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "readable-stream@^2.0.0": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "readable-stream@^2.0.1": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "readable-stream@^2.0.2": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "readable-stream@^2.1.5": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "readable-stream@^2.2.2": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "readable-stream@^2.3.3": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "readable-stream@^2.3.6": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "readable-stream@^3.0.6": "https://registry.npmmirror.com/readable-stream/download/readable-stream-3.6.0.tgz", "readable-stream@^3.1.1": "https://registry.npmmirror.com/readable-stream/download/readable-stream-3.6.0.tgz", "readable-stream@^3.6.0": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@~2.3.6": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "readdirp@^2.2.1": "https://registry.npmmirror.com/readdirp/download/readdirp-2.2.1.tgz?cache=0&sync_timestamp=1584985910691&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freaddirp%2Fdownload%2Freaddirp-2.2.1.tgz", "readdirp@~3.4.0": "https://registry.npmmirror.com/readdirp/download/readdirp-3.4.0.tgz?cache=0&sync_timestamp=1584985910691&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freaddirp%2Fdownload%2Freaddirp-3.4.0.tgz", "readdirp@~3.6.0": "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz", "realpath-native@^2.0.0": "https://registry.npmmirror.com/realpath-native/download/realpath-native-2.0.0.tgz?cache=0&sync_timestamp=1588859532761&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frealpath-native%2Fdownload%2Frealpath-native-2.0.0.tgz", "rechoir@^0.6.2": "https://registry.npmmirror.com/rechoir/download/rechoir-0.6.2.tgz", "regenerate-unicode-properties@^8.2.0": "https://registry.npmmirror.com/regenerate-unicode-properties/download/regenerate-unicode-properties-8.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerate-unicode-properties%2Fdownload%2Fregenerate-unicode-properties-8.2.0.tgz", "regenerate@^1.4.0": "https://registry.npmmirror.com/regenerate/download/regenerate-1.4.0.tgz", "regenerator-runtime@^0.12.1": "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.12.1.tgz?cache=0&sync_timestamp=1584052597708&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerator-runtime%2Fdownload%2Fregenerator-runtime-0.12.1.tgz", "regenerator-runtime@^0.13.4": "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.13.5.tgz?cache=0&sync_timestamp=1584052597708&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerator-runtime%2Fdownload%2Fregenerator-runtime-0.13.5.tgz", "regenerator-transform@^0.14.2": "https://registry.npmmirror.com/regenerator-transform/download/regenerator-transform-0.14.4.tgz", "regex-not@^1.0.0": "https://registry.npmmirror.com/regex-not/download/regex-not-1.0.2.tgz", "regex-not@^1.0.2": "https://registry.npmmirror.com/regex-not/download/regex-not-1.0.2.tgz", "regexp.prototype.flags@^1.2.0": "https://registry.npmmirror.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.0.tgz?cache=0&sync_timestamp=1576388379660&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregexp.prototype.flags%2Fdownload%2Fregexp.prototype.flags-1.3.0.tgz", "regexpu-core@^4.7.0": "https://registry.npmmirror.com/regexpu-core/download/regexpu-core-4.7.0.tgz?cache=0&sync_timestamp=1583949899397&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregexpu-core%2Fdownload%2Fregexpu-core-4.7.0.tgz", "registry-auth-token@3.3.2": "https://registry.npmmirror.com/registry-auth-token/download/registry-auth-token-3.3.2.tgz", "registry-url@3.1.0": "https://registry.npmmirror.com/registry-url/download/registry-url-3.1.0.tgz", "regjsgen@^0.5.1": "https://registry.npmmirror.com/regjsgen/download/regjsgen-0.5.1.tgz?cache=0&sync_timestamp=1571560340910&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregjsgen%2Fdownload%2Fregjsgen-0.5.1.tgz", "regjsparser@^0.6.4": "https://registry.npmmirror.com/regjsparser/download/regjsparser-0.6.4.tgz", "relateurl@0.2.x": "https://registry.npmmirror.com/relateurl/download/relateurl-0.2.7.tgz", "remove-trailing-separator@^1.0.1": "https://registry.npmmirror.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz", "renderkid@^2.0.1": "https://registry.npmmirror.com/renderkid/download/renderkid-2.0.3.tgz", "repeat-element@^1.1.2": "https://registry.npmmirror.com/repeat-element/download/repeat-element-1.1.3.tgz", "repeat-string@^1.6.1": "https://registry.npmmirror.com/repeat-string/download/repeat-string-1.6.1.tgz", "request-promise-core@1.1.3": "https://registry.npmmirror.com/request-promise-core/download/request-promise-core-1.1.3.tgz", "request-promise-native@^1.0.7": "https://registry.npmmirror.com/request-promise-native/download/request-promise-native-1.0.8.tgz", "request-promise-native@^1.0.8": "https://registry.npmmirror.com/request-promise-native/download/request-promise-native-1.0.8.tgz", "request@^2.85.0": "https://registry.npmmirror.com/request/download/request-2.88.2.tgz", "request@^2.88.0": "https://registry.npmmirror.com/request/download/request-2.88.2.tgz", "request@^2.88.2": "https://registry.npmmirror.com/request/download/request-2.88.2.tgz", "require-directory@^2.1.1": "https://registry.npmmirror.com/require-directory/download/require-directory-2.1.1.tgz", "require-main-filename@^2.0.0": "https://registry.npmmirror.com/require-main-filename/download/require-main-filename-2.0.0.tgz", "requires-port@^1.0.0": "https://registry.npmmirror.com/requires-port/download/requires-port-1.0.0.tgz", "resolve-cwd@^2.0.0": "https://registry.npmmirror.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz", "resolve-cwd@^3.0.0": "https://registry.npmmirror.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz", "resolve-from@^3.0.0": "https://registry.npmmirror.com/resolve-from/download/resolve-from-3.0.0.tgz", "resolve-from@^5.0.0": "https://registry.npmmirror.com/resolve-from/download/resolve-from-5.0.0.tgz", "resolve-path@^1.4.0": "https://registry.npmmirror.com/resolve-path/download/resolve-path-1.4.0.tgz", "resolve-url@^0.2.1": "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz?cache=0&sync_timestamp=1585438700247&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve-url%2Fdownload%2Fresolve-url-0.2.1.tgz", "resolve@1.1.7": "https://registry.npmmirror.com/resolve/download/resolve-1.1.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.1.7.tgz", "resolve@^1.1.6": "https://registry.npmmirror.com/resolve/download/resolve-1.17.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.17.0.tgz", "resolve@^1.1.7": "https://registry.npmmirror.com/resolve/download/resolve-1.17.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.17.0.tgz", "resolve@^1.10.0": "https://registry.npmmirror.com/resolve/download/resolve-1.17.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.17.0.tgz", "resolve@^1.17.0": "https://registry.npmmirror.com/resolve/download/resolve-1.17.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.17.0.tgz", "resolve@^1.3.2": "https://registry.npmmirror.com/resolve/download/resolve-1.17.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.17.0.tgz", "resolve@^1.8.1": "https://registry.npmmirror.com/resolve/download/resolve-1.17.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.17.0.tgz", "restore-cursor@^2.0.0": "https://registry.npmmirror.com/restore-cursor/download/restore-cursor-2.0.0.tgz", "ret@~0.1.10": "https://registry.npmmirror.com/ret/download/ret-0.1.15.tgz", "retry@^0.12.0": "https://registry.npmmirror.com/retry/download/retry-0.12.0.tgz", "rgb-regex@^1.0.1": "https://registry.npmmirror.com/rgb-regex/download/rgb-regex-1.0.1.tgz", "rgba-regex@^1.0.0": "https://registry.npmmirror.com/rgba-regex/download/rgba-regex-1.0.0.tgz", "rimraf@^2.5.4": "https://registry.npmmirror.com/rimraf/download/rimraf-2.7.1.tgz?cache=0&sync_timestamp=1581229865753&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frimraf%2Fdownload%2Frimraf-2.7.1.tgz", "rimraf@^2.6.3": "https://registry.npmmirror.com/rimraf/download/rimraf-2.7.1.tgz?cache=0&sync_timestamp=1581229865753&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frimraf%2Fdownload%2Frimraf-2.7.1.tgz", "rimraf@^2.7.1": "https://registry.npmmirror.com/rimraf/download/rimraf-2.7.1.tgz?cache=0&sync_timestamp=1581229865753&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frimraf%2Fdownload%2Frimraf-2.7.1.tgz", "rimraf@^3.0.0": "https://registry.npmmirror.com/rimraf/download/rimraf-3.0.2.tgz?cache=0&sync_timestamp=1581229865753&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frimraf%2Fdownload%2Frimraf-3.0.2.tgz", "ripemd160@^2.0.0": "https://registry.npmmirror.com/ripemd160/download/ripemd160-2.0.2.tgz", "ripemd160@^2.0.1": "https://registry.npmmirror.com/ripemd160/download/ripemd160-2.0.2.tgz", "rsvp@^4.8.4": "https://registry.npmmirror.com/rsvp/download/rsvp-4.8.5.tgz", "run-queue@^1.0.0": "https://registry.npmmirror.com/run-queue/download/run-queue-1.0.3.tgz", "run-queue@^1.0.3": "https://registry.npmmirror.com/run-queue/download/run-queue-1.0.3.tgz", "safe-area-insets@^1.4.1": "https://registry.npmmirror.com/safe-area-insets/download/safe-area-insets-1.4.1.tgz", "safe-buffer@5.1.2": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "safe-buffer@>=5.1.0": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "safe-buffer@^5.0.1": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.0": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.1": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.2": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "safe-buffer@^5.2.0": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "safe-buffer@~5.1.0": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "safe-buffer@~5.1.1": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "safe-buffer@~5.2.0": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "safe-regex@^1.1.0": "https://registry.npmmirror.com/safe-regex/download/safe-regex-1.1.0.tgz", "safer-buffer@>= 2.1.2 < 3": "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafer-buffer%2Fdownload%2Fsafer-buffer-2.1.2.tgz", "safer-buffer@^2.0.2": "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafer-buffer%2Fdownload%2Fsafer-buffer-2.1.2.tgz", "safer-buffer@^2.1.0": "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafer-buffer%2Fdownload%2Fsafer-buffer-2.1.2.tgz", "safer-buffer@~2.1.0": "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafer-buffer%2Fdownload%2Fsafer-buffer-2.1.2.tgz", "sane@^4.0.3": "https://registry.npmmirror.com/sane/download/sane-4.1.0.tgz", "sass-loader@^10.4.1": "https://registry.npmmirror.com/sass-loader/-/sass-loader-10.5.2.tgz", "sass@1.32.13": "https://registry.npmmirror.com/sass/-/sass-1.32.13.tgz", "sax@~1.2.4": "https://registry.npmmirror.com/sax/download/sax-1.2.4.tgz", "saxes@^3.1.9": "https://registry.npmmirror.com/saxes/download/saxes-3.1.11.tgz", "schema-utils@^1.0.0": "https://registry.npmmirror.com/schema-utils/download/schema-utils-1.0.0.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-1.0.0.tgz", "schema-utils@^2.0.0": "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.6.6.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.6.6.tgz", "schema-utils@^2.5.0": "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.6.6.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.6.6.tgz", "schema-utils@^2.6.5": "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.6.6.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.6.6.tgz", "schema-utils@^2.6.6": "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.6.6.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.6.6.tgz", "schema-utils@^3.0.0": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz", "select-hose@^2.0.0": "https://registry.npmmirror.com/select-hose/download/select-hose-2.0.0.tgz", "selfsigned@^1.10.7": "https://registry.npmmirror.com/selfsigned/download/selfsigned-1.10.7.tgz", "semver@2 || 3 || 4 || 5": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz", "semver@7.0.0": "https://registry.npmmirror.com/semver/download/semver-7.0.0.tgz", "semver@^5.4.1": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz", "semver@^5.5.0": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz", "semver@^5.5.1": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz", "semver@^5.6.0": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz", "semver@^6.0.0": "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz", "semver@^6.1.0": "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz", "semver@^6.3.0": "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz", "semver@^7.3.2": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "send@0.17.1": "https://registry.npmmirror.com/send/download/send-0.17.1.tgz", "serialize-javascript@^2.1.2": "https://registry.npmmirror.com/serialize-javascript/download/serialize-javascript-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-2.1.2.tgz", "serialize-javascript@^3.0.0": "https://registry.npmmirror.com/serialize-javascript/download/serialize-javascript-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-3.0.0.tgz", "serve-index@^1.9.1": "https://registry.npmmirror.com/serve-index/download/serve-index-1.9.1.tgz", "serve-static@1.14.1": "https://registry.npmmirror.com/serve-static/download/serve-static-1.14.1.tgz", "set-blocking@^2.0.0": "https://registry.npmmirror.com/set-blocking/download/set-blocking-2.0.0.tgz", "set-immediate-shim@~1.0.1": "https://registry.npmmirror.com/set-immediate-shim/download/set-immediate-shim-1.0.1.tgz", "set-value@^2.0.0": "https://registry.npmmirror.com/set-value/download/set-value-2.0.1.tgz?cache=0&sync_timestamp=1585775409029&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fset-value%2Fdownload%2Fset-value-2.0.1.tgz", "set-value@^2.0.1": "https://registry.npmmirror.com/set-value/download/set-value-2.0.1.tgz?cache=0&sync_timestamp=1585775409029&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fset-value%2Fdownload%2Fset-value-2.0.1.tgz", "setimmediate@^1.0.4": "https://registry.npmmirror.com/setimmediate/download/setimmediate-1.0.5.tgz", "setprototypeof@1.1.0": "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.0.tgz?cache=0&sync_timestamp=1563425414995&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsetprototypeof%2Fdownload%2Fsetprototypeof-1.1.0.tgz", "setprototypeof@1.1.1": "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.1.tgz?cache=0&sync_timestamp=1563425414995&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsetprototypeof%2Fdownload%2Fsetprototypeof-1.1.1.tgz", "sha.js@^2.4.0": "https://registry.npmmirror.com/sha.js/download/sha.js-2.4.11.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsha.js%2Fdownload%2Fsha.js-2.4.11.tgz", "sha.js@^2.4.8": "https://registry.npmmirror.com/sha.js/download/sha.js-2.4.11.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsha.js%2Fdownload%2Fsha.js-2.4.11.tgz", "shebang-command@^1.2.0": "https://registry.npmmirror.com/shebang-command/download/shebang-command-1.2.0.tgz", "shebang-command@^2.0.0": "https://registry.npmmirror.com/shebang-command/download/shebang-command-2.0.0.tgz", "shebang-regex@^1.0.0": "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-1.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-3.0.0.tgz", "shell-quote@^1.6.1": "https://registry.npmmirror.com/shell-quote/download/shell-quote-1.7.2.tgz", "shelljs@^0.8.1": "https://registry.npmmirror.com/shelljs/download/shelljs-0.8.4.tgz?cache=0&sync_timestamp=1587787177094&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fshelljs%2Fdownload%2Fshelljs-0.8.4.tgz", "shellwords@^0.1.1": "https://registry.npmmirror.com/shellwords/download/shellwords-0.1.1.tgz", "signal-exit@^3.0.0": "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.3.tgz", "signal-exit@^3.0.2": "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.3.tgz", "simple-swizzle@^0.2.2": "https://registry.npmmirror.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz", "sisteransi@^1.0.4": "https://registry.npmmirror.com/sisteransi/download/sisteransi-1.0.5.tgz", "slash@^1.0.0": "https://registry.npmmirror.com/slash/download/slash-1.0.0.tgz", "slash@^2.0.0": "https://registry.npmmirror.com/slash/download/slash-2.0.0.tgz", "slash@^3.0.0": "https://registry.npmmirror.com/slash/download/slash-3.0.0.tgz", "snapdragon-node@^2.0.1": "https://registry.npmmirror.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz", "snapdragon-util@^3.0.1": "https://registry.npmmirror.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz", "snapdragon@^0.8.1": "https://registry.npmmirror.com/snapdragon/download/snapdragon-0.8.2.tgz", "socket.io-adapter@~1.1.0": "https://registry.npmmirror.com/socket.io-adapter/download/socket.io-adapter-1.1.2.tgz", "socket.io-client@2.3.0": "https://registry.npmmirror.com/socket.io-client/download/socket.io-client-2.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsocket.io-client%2Fdownload%2Fsocket.io-client-2.3.0.tgz", "socket.io-parser@~3.3.0": "https://registry.npmmirror.com/socket.io-parser/download/socket.io-parser-3.3.0.tgz", "socket.io-parser@~3.4.0": "https://registry.npmmirror.com/socket.io-parser/download/socket.io-parser-3.4.1.tgz", "socket.io@^2.2.0": "https://registry.npmmirror.com/socket.io/download/socket.io-2.3.0.tgz?cache=0&sync_timestamp=1569002852515&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsocket.io%2Fdownload%2Fsocket.io-2.3.0.tgz", "sockjs-client@1.4.0": "https://registry.npmmirror.com/sockjs-client/download/sockjs-client-1.4.0.tgz", "sockjs@0.3.20": "https://registry.npmmirror.com/sockjs/download/sockjs-0.3.20.tgz", "sort-keys@^1.0.0": "https://registry.npmmirror.com/sort-keys/download/sort-keys-1.1.2.tgz?cache=0&sync_timestamp=1565864727994&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsort-keys%2Fdownload%2Fsort-keys-1.1.2.tgz", "source-list-map@^2.0.0": "https://registry.npmmirror.com/source-list-map/download/source-list-map-2.0.1.tgz", "source-map-resolve@^0.5.0": "https://registry.npmmirror.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz?cache=0&sync_timestamp=1584831908370&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-resolve%2Fdownload%2Fsource-map-resolve-0.5.3.tgz", "source-map-resolve@^0.5.2": "https://registry.npmmirror.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz?cache=0&sync_timestamp=1584831908370&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-resolve%2Fdownload%2Fsource-map-resolve-0.5.3.tgz", "source-map-support@^0.5.16": "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.19.tgz?cache=0&sync_timestamp=1587719517036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.19.tgz", "source-map-support@^0.5.6": "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.19.tgz?cache=0&sync_timestamp=1587719517036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.19.tgz", "source-map-support@~0.5.12": "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.19.tgz?cache=0&sync_timestamp=1587719517036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.19.tgz", "source-map-url@^0.4.0": "https://registry.npmmirror.com/source-map-url/download/source-map-url-0.4.0.tgz", "source-map@^0.5.0": "https://registry.npmmirror.com/source-map/download/source-map-0.5.7.tgz", "source-map@^0.5.6": "https://registry.npmmirror.com/source-map/download/source-map-0.5.7.tgz", "source-map@^0.6.0": "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz", "source-map@^0.7.3": "https://registry.npmmirror.com/source-map/download/source-map-0.7.3.tgz", "source-map@~0.6.0": "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz", "source-map@~0.6.1": "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz", "spdx-correct@^3.0.0": "https://registry.npmmirror.com/spdx-correct/download/spdx-correct-3.1.0.tgz", "spdx-exceptions@^2.1.0": "https://registry.npmmirror.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz?cache=0&sync_timestamp=1587422410312&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fspdx-exceptions%2Fdownload%2Fspdx-exceptions-2.3.0.tgz", "spdx-expression-parse@^3.0.0": "https://registry.npmmirror.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz", "spdx-license-ids@^3.0.0": "https://registry.npmmirror.com/spdx-license-ids/download/spdx-license-ids-3.0.5.tgz", "spdy-transport@^3.0.0": "https://registry.npmmirror.com/spdy-transport/download/spdy-transport-3.0.0.tgz", "spdy@^4.0.2": "https://registry.npmmirror.com/spdy/download/spdy-4.0.2.tgz", "split-string@^3.0.1": "https://registry.npmmirror.com/split-string/download/split-string-3.1.0.tgz", "split-string@^3.0.2": "https://registry.npmmirror.com/split-string/download/split-string-3.1.0.tgz", "sprintf-js@~1.0.2": "https://registry.npmmirror.com/sprintf-js/download/sprintf-js-1.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsprintf-js%2Fdownload%2Fsprintf-js-1.0.3.tgz", "sshpk@^1.7.0": "https://registry.npmmirror.com/sshpk/download/sshpk-1.16.1.tgz", "ssri@^6.0.1": "https://registry.npmmirror.com/ssri/download/ssri-6.0.1.tgz", "ssri@^7.0.0": "https://registry.npmmirror.com/ssri/download/ssri-7.1.0.tgz", "ssri@^7.1.0": "https://registry.npmmirror.com/ssri/download/ssri-7.1.0.tgz", "stable@^0.1.8": "https://registry.npmmirror.com/stable/download/stable-0.1.8.tgz", "stack-utils@^1.0.1": "https://registry.npmmirror.com/stack-utils/download/stack-utils-1.0.2.tgz", "stackframe@^1.1.1": "https://registry.npmmirror.com/stackframe/download/stackframe-1.1.1.tgz?cache=0&sync_timestamp=1578261993899&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstackframe%2Fdownload%2Fstackframe-1.1.1.tgz", "static-extend@^0.1.1": "https://registry.npmmirror.com/static-extend/download/static-extend-0.1.2.tgz", "statuses@>= 1.4.0 < 2": "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1587328859420&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz", "statuses@>= 1.5.0 < 2": "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1587328859420&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz", "statuses@^1.5.0": "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1587328859420&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz", "statuses@~1.5.0": "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1587328859420&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz", "stealthy-require@^1.1.1": "https://registry.npmmirror.com/stealthy-require/download/stealthy-require-1.1.1.tgz", "stream-browserify@^2.0.1": "https://registry.npmmirror.com/stream-browserify/download/stream-browserify-2.0.2.tgz?cache=0&sync_timestamp=1587041519870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstream-browserify%2Fdownload%2Fstream-browserify-2.0.2.tgz", "stream-each@^1.1.0": "https://registry.npmmirror.com/stream-each/download/stream-each-1.2.3.tgz", "stream-http@^2.7.2": "https://registry.npmmirror.com/stream-http/download/stream-http-2.8.3.tgz?cache=0&sync_timestamp=1588701035785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstream-http%2Fdownload%2Fstream-http-2.8.3.tgz", "stream-shift@^1.0.0": "https://registry.npmmirror.com/stream-shift/download/stream-shift-1.0.1.tgz?cache=0&sync_timestamp=1576147145118&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstream-shift%2Fdownload%2Fstream-shift-1.0.1.tgz", "strict-uri-encode@^1.0.0": "https://registry.npmmirror.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz", "stricter-htmlparser2@^3.9.6": "https://registry.npmmirror.com/stricter-htmlparser2/download/stricter-htmlparser2-3.9.6.tgz", "string-length@^3.1.0": "https://registry.npmmirror.com/string-length/download/string-length-3.1.0.tgz", "string-width@^2.0.0": "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz", "string-width@^3.0.0": "https://registry.npmmirror.com/string-width/download/string-width-3.1.0.tgz", "string-width@^3.1.0": "https://registry.npmmirror.com/string-width/download/string-width-3.1.0.tgz", "string-width@^4.1.0": "https://registry.npmmirror.com/string-width/download/string-width-4.2.0.tgz", "string-width@^4.2.0": "https://registry.npmmirror.com/string-width/download/string-width-4.2.0.tgz", "string.prototype.trimend@^1.0.0": "https://registry.npmmirror.com/string.prototype.trimend/download/string.prototype.trimend-1.0.1.tgz?cache=0&sync_timestamp=1586465409341&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimend%2Fdownload%2Fstring.prototype.trimend-1.0.1.tgz", "string.prototype.trimleft@^2.1.1": "https://registry.npmmirror.com/string.prototype.trimleft/download/string.prototype.trimleft-2.1.2.tgz?cache=0&sync_timestamp=1585584322600&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimleft%2Fdownload%2Fstring.prototype.trimleft-2.1.2.tgz", "string.prototype.trimright@^2.1.1": "https://registry.npmmirror.com/string.prototype.trimright/download/string.prototype.trimright-2.1.2.tgz", "string.prototype.trimstart@^1.0.0": "https://registry.npmmirror.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.1.tgz?cache=0&sync_timestamp=1586465413621&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimstart%2Fdownload%2Fstring.prototype.trimstart-1.0.1.tgz", "string_decoder@^1.0.0": "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.3.0.tgz", "string_decoder@^1.1.1": "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.3.0.tgz", "string_decoder@~1.1.1": "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.1.1.tgz", "strip-ansi@^3.0.0": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz", "strip-ansi@^3.0.1": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz", "strip-ansi@^4.0.0": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz", "strip-ansi@^5.0.0": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "strip-ansi@^5.1.0": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "strip-ansi@^5.2.0": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "strip-ansi@^6.0.0": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.0.tgz", "strip-bom@^4.0.0": "https://registry.npmmirror.com/strip-bom/download/strip-bom-4.0.0.tgz", "strip-eof@^1.0.0": "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz", "strip-final-newline@^2.0.0": "https://registry.npmmirror.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz", "strip-json-comments@^2.0.1": "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz?cache=0&sync_timestamp=1586160054577&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-json-comments%2Fdownload%2Fstrip-json-comments-2.0.1.tgz", "strip-json-comments@~2.0.1": "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz?cache=0&sync_timestamp=1586160054577&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-json-comments%2Fdownload%2Fstrip-json-comments-2.0.1.tgz", "stylehacks@^4.0.0": "https://registry.npmmirror.com/stylehacks/download/stylehacks-4.0.3.tgz", "supports-color@^2.0.0": "https://registry.npmmirror.com/supports-color/download/supports-color-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-2.0.0.tgz", "supports-color@^5.3.0": "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz", "supports-color@^5.4.0": "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz", "supports-color@^6.1.0": "https://registry.npmmirror.com/supports-color/download/supports-color-6.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-6.1.0.tgz", "supports-color@^7.0.0": "https://registry.npmmirror.com/supports-color/download/supports-color-7.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-7.1.0.tgz", "supports-color@^7.1.0": "https://registry.npmmirror.com/supports-color/download/supports-color-7.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-7.1.0.tgz", "supports-hyperlinks@^2.0.0": "https://registry.npmmirror.com/supports-hyperlinks/download/supports-hyperlinks-2.1.0.tgz", "svg-tags@^1.0.0": "https://registry.npmmirror.com/svg-tags/download/svg-tags-1.0.0.tgz", "svgo@^1.0.0": "https://registry.npmmirror.com/svgo/download/svgo-1.3.2.tgz?cache=0&sync_timestamp=1572433264480&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsvgo%2Fdownload%2Fsvgo-1.3.2.tgz", "symbol-tree@^3.2.2": "https://registry.npmmirror.com/symbol-tree/download/symbol-tree-3.2.4.tgz", "tapable@^1.0.0": "https://registry.npmmirror.com/tapable/download/tapable-1.1.3.tgz", "tapable@^1.1.3": "https://registry.npmmirror.com/tapable/download/tapable-1.1.3.tgz", "terminal-link@^2.0.0": "https://registry.npmmirror.com/terminal-link/download/terminal-link-2.1.1.tgz", "terser-webpack-plugin@^1.4.3": "https://registry.npmmirror.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.3.tgz", "terser-webpack-plugin@^2.3.5": "https://registry.npmmirror.com/terser-webpack-plugin/download/terser-webpack-plugin-2.3.6.tgz", "terser@^4.1.2": "https://registry.npmmirror.com/terser/download/terser-4.7.0.tgz?cache=0&sync_timestamp=1589825699609&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fterser%2Fdownload%2Fterser-4.7.0.tgz", "terser@^4.6.12": "https://registry.npmmirror.com/terser/download/terser-4.7.0.tgz?cache=0&sync_timestamp=1589825699609&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fterser%2Fdownload%2Fterser-4.7.0.tgz", "test-exclude@^6.0.0": "https://registry.npmmirror.com/test-exclude/download/test-exclude-6.0.0.tgz", "thenify-all@^1.0.0": "https://registry.npmmirror.com/thenify-all/download/thenify-all-1.6.0.tgz", "thenify@>= 3.1.0 < 4": "https://registry.npmmirror.com/thenify/download/thenify-3.3.0.tgz", "thread-loader@^2.1.3": "https://registry.npmmirror.com/thread-loader/download/thread-loader-2.1.3.tgz", "throat@^5.0.0": "https://registry.npmmirror.com/throat/download/throat-5.0.0.tgz", "through2@^2.0.0": "https://registry.npmmirror.com/through2/download/through2-2.0.5.tgz", "thunky@^1.0.2": "https://registry.npmmirror.com/thunky/download/thunky-1.1.0.tgz?cache=0&sync_timestamp=1571043401546&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthunky%2Fdownload%2Fthunky-1.1.0.tgz", "timers-browserify@^2.0.4": "https://registry.npmmirror.com/timers-browserify/download/timers-browserify-2.0.11.tgz", "timsort@^0.3.0": "https://registry.npmmirror.com/timsort/download/timsort-0.3.0.tgz", "tmpl@1.0.x": "https://registry.npmmirror.com/tmpl/download/tmpl-1.0.4.tgz", "to-array@0.1.4": "https://registry.npmmirror.com/to-array/download/to-array-0.1.4.tgz", "to-arraybuffer@^1.0.0": "https://registry.npmmirror.com/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz", "to-fast-properties@^2.0.0": "https://registry.npmmirror.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz", "to-object-path@^0.3.0": "https://registry.npmmirror.com/to-object-path/download/to-object-path-0.3.0.tgz", "to-regex-range@^2.1.0": "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-2.1.1.tgz", "to-regex-range@^5.0.1": "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "to-regex@^3.0.1": "https://registry.npmmirror.com/to-regex/download/to-regex-3.0.2.tgz", "to-regex@^3.0.2": "https://registry.npmmirror.com/to-regex/download/to-regex-3.0.2.tgz", "toidentifier@1.0.0": "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz", "toposort@^1.0.0": "https://registry.npmmirror.com/toposort/download/toposort-1.0.7.tgz", "tough-cookie@^2.3.3": "https://registry.npmmirror.com/tough-cookie/download/tough-cookie-2.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftough-cookie%2Fdownload%2Ftough-cookie-2.5.0.tgz", "tough-cookie@^3.0.1": "https://registry.npmmirror.com/tough-cookie/download/tough-cookie-3.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftough-cookie%2Fdownload%2Ftough-cookie-3.0.1.tgz", "tough-cookie@~2.5.0": "https://registry.npmmirror.com/tough-cookie/download/tough-cookie-2.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftough-cookie%2Fdownload%2Ftough-cookie-2.5.0.tgz", "tr46@^1.0.1": "https://registry.npmmirror.com/tr46/download/tr46-1.0.1.tgz", "tryer@^1.0.1": "https://registry.npmmirror.com/tryer/download/tryer-1.0.1.tgz", "ts-pnp@^1.1.6": "https://registry.npmmirror.com/ts-pnp/download/ts-pnp-1.2.0.tgz", "tslib@^1.9.0": "https://registry.npmmirror.com/tslib/download/tslib-1.13.0.tgz", "tsscmp@1.0.6": "https://registry.npmmirror.com/tsscmp/download/tsscmp-1.0.6.tgz", "tty-browserify@0.0.0": "https://registry.npmmirror.com/tty-browserify/download/tty-browserify-0.0.0.tgz", "tunnel-agent@^0.6.0": "https://registry.npmmirror.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "tweetnacl@^0.14.3": "https://registry.npmmirror.com/tweetnacl/download/tweetnacl-0.14.5.tgz", "tweetnacl@~0.14.0": "https://registry.npmmirror.com/tweetnacl/download/tweetnacl-0.14.5.tgz", "type-check@~0.3.2": "https://registry.npmmirror.com/type-check/download/type-check-0.3.2.tgz", "type-detect@4.0.8": "https://registry.npmmirror.com/type-detect/download/type-detect-4.0.8.tgz", "type-fest@^0.11.0": "https://registry.npmmirror.com/type-fest/download/type-fest-0.11.0.tgz", "type-fest@^0.6.0": "https://registry.npmmirror.com/type-fest/download/type-fest-0.6.0.tgz", "type-fest@^0.8.1": "https://registry.npmmirror.com/type-fest/download/type-fest-0.8.1.tgz", "type-is@^1.6.14": "https://registry.npmmirror.com/type-is/download/type-is-1.6.18.tgz", "type-is@^1.6.16": "https://registry.npmmirror.com/type-is/download/type-is-1.6.18.tgz", "type-is@~1.6.17": "https://registry.npmmirror.com/type-is/download/type-is-1.6.18.tgz", "type-is@~1.6.18": "https://registry.npmmirror.com/type-is/download/type-is-1.6.18.tgz", "typedarray-to-buffer@^3.1.5": "https://registry.npmmirror.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz", "typedarray@^0.0.6": "https://registry.npmmirror.com/typedarray/download/typedarray-0.0.6.tgz", "uglify-js@3.4.x": "https://registry.npmmirror.com/uglify-js/download/uglify-js-3.4.10.tgz", "unicode-canonical-property-names-ecmascript@^1.0.4": "https://registry.npmmirror.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz", "unicode-match-property-ecmascript@^1.0.4": "https://registry.npmmirror.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz", "unicode-match-property-value-ecmascript@^1.2.0": "https://registry.npmmirror.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funicode-match-property-value-ecmascript%2Fdownload%2Funicode-match-property-value-ecmascript-1.2.0.tgz", "unicode-property-aliases-ecmascript@^1.0.4": "https://registry.npmmirror.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.1.0.tgz?cache=0&sync_timestamp=1583945805856&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funicode-property-aliases-ecmascript%2Fdownload%2Funicode-property-aliases-ecmascript-1.1.0.tgz", "union-value@^1.0.0": "https://registry.npmmirror.com/union-value/download/union-value-1.0.1.tgz", "uniq@^1.0.1": "https://registry.npmmirror.com/uniq/download/uniq-1.0.1.tgz", "uniqs@^2.0.0": "https://registry.npmmirror.com/uniqs/download/uniqs-2.0.0.tgz", "unique-filename@^1.1.1": "https://registry.npmmirror.com/unique-filename/download/unique-filename-1.1.1.tgz", "unique-slug@^2.0.0": "https://registry.npmmirror.com/unique-slug/download/unique-slug-2.0.2.tgz", "universalify@^0.1.0": "https://registry.npmmirror.com/universalify/download/universalify-0.1.2.tgz?cache=0&sync_timestamp=1583531006552&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funiversalify%2Fdownload%2Funiversalify-0.1.2.tgz", "unpipe@1.0.0": "https://registry.npmmirror.com/unpipe/download/unpipe-1.0.0.tgz", "unpipe@~1.0.0": "https://registry.npmmirror.com/unpipe/download/unpipe-1.0.0.tgz", "unquote@~1.1.1": "https://registry.npmmirror.com/unquote/download/unquote-1.1.1.tgz", "unset-value@^1.0.0": "https://registry.npmmirror.com/unset-value/download/unset-value-1.0.0.tgz", "upath@^1.1.1": "https://registry.npmmirror.com/upath/download/upath-1.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fupath%2Fdownload%2Fupath-1.2.0.tgz", "update-check@^1.5.3": "https://registry.npmmirror.com/update-check/download/update-check-1.5.4.tgz", "upper-case@^1.1.1": "https://registry.npmmirror.com/upper-case/download/upper-case-1.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fupper-case%2Fdownload%2Fupper-case-1.1.3.tgz", "uri-js@^4.2.2": "https://registry.npmmirror.com/uri-js/download/uri-js-4.2.2.tgz", "urijs@^1.18.12": "https://registry.npmmirror.com/urijs/download/urijs-1.19.2.tgz", "urijs@^1.19.0": "https://registry.npmmirror.com/urijs/download/urijs-1.19.2.tgz", "urix@^0.1.0": "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz", "url-loader@^2.1.0": "https://registry.npmmirror.com/url-loader/download/url-loader-2.3.0.tgz", "url-loader@^2.2.0": "https://registry.npmmirror.com/url-loader/download/url-loader-2.3.0.tgz", "url-parse@^1.4.3": "https://registry.npmmirror.com/url-parse/download/url-parse-1.4.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furl-parse%2Fdownload%2Furl-parse-1.4.7.tgz", "url@^0.11.0": "https://registry.npmmirror.com/url/download/url-0.11.0.tgz", "use@^3.1.0": "https://registry.npmmirror.com/use/download/use-3.1.1.tgz", "util-deprecate@^1.0.1": "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "util-deprecate@~1.0.1": "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "util.promisify@1.0.0": "https://registry.npmmirror.com/util.promisify/download/util.promisify-1.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.0.tgz", "util.promisify@~1.0.0": "https://registry.npmmirror.com/util.promisify/download/util.promisify-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.1.tgz", "util@0.10.3": "https://registry.npmmirror.com/util/download/util-0.10.3.tgz?cache=0&sync_timestamp=1588238457176&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil%2Fdownload%2Futil-0.10.3.tgz", "util@^0.11.0": "https://registry.npmmirror.com/util/download/util-0.11.1.tgz?cache=0&sync_timestamp=1588238457176&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil%2Fdownload%2Futil-0.11.1.tgz", "utila@^0.4.0": "https://registry.npmmirror.com/utila/download/utila-0.4.0.tgz", "utila@~0.4": "https://registry.npmmirror.com/utila/download/utila-0.4.0.tgz", "utils-merge@1.0.1": "https://registry.npmmirror.com/utils-merge/download/utils-merge-1.0.1.tgz", "uuid@^3.3.2": "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz", "uuid@^3.4.0": "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz", "uview-ui@2.x": "https://registry.npmmirror.com/uview-ui/-/uview-ui-2.0.38.tgz#02a0585f2c966a81c2997c79cfff0e8538a89c61", "v8-to-istanbul@^4.1.3": "https://registry.npmmirror.com/v8-to-istanbul/download/v8-to-istanbul-4.1.4.tgz", "validate-npm-package-license@^3.0.1": "https://registry.npmmirror.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "vary@^1.1.2": "https://registry.npmmirror.com/vary/download/vary-1.1.2.tgz", "vary@~1.1.2": "https://registry.npmmirror.com/vary/download/vary-1.1.2.tgz", "vendors@^1.0.0": "https://registry.npmmirror.com/vendors/download/vendors-1.0.4.tgz?cache=0&sync_timestamp=1579857147055&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvendors%2Fdownload%2Fvendors-1.0.4.tgz", "verror@1.10.0": "https://registry.npmmirror.com/verror/download/verror-1.10.0.tgz", "vm-browserify@^1.0.1": "https://registry.npmmirror.com/vm-browserify/download/vm-browserify-1.1.2.tgz?cache=0&sync_timestamp=1572870776965&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvm-browserify%2Fdownload%2Fvm-browserify-1.1.2.tgz", "vue-hot-reload-api@^2.3.0": "https://registry.npmmirror.com/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-hot-reload-api%2Fdownload%2Fvue-hot-reload-api-2.3.4.tgz", "vue-loader@^15.6.4": "https://registry.npmmirror.com/vue-loader/download/vue-loader-15.9.2.tgz?cache=0&sync_timestamp=1589274987464&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-loader%2Fdownload%2Fvue-loader-15.9.2.tgz", "vue-loader@^15.9.1": "https://registry.npmmirror.com/vue-loader/download/vue-loader-15.9.2.tgz?cache=0&sync_timestamp=1589274987464&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-loader%2Fdownload%2Fvue-loader-15.9.2.tgz", "vue-style-loader@^4.1.0": "https://registry.npmmirror.com/vue-style-loader/download/vue-style-loader-4.1.2.tgz", "vue-style-loader@^4.1.2": "https://registry.npmmirror.com/vue-style-loader/download/vue-style-loader-4.1.2.tgz", "vue-template-compiler@^2.6.10": "https://registry.npmmirror.com/vue-template-compiler/download/vue-template-compiler-2.6.11.tgz", "vue-template-compiler@^2.6.11": "https://registry.npmmirror.com/vue-template-compiler/download/vue-template-compiler-2.6.11.tgz", "vue-template-compiler@^2.6.7": "https://registry.npmmirror.com/vue-template-compiler/download/vue-template-compiler-2.6.11.tgz", "vue-template-es2015-compiler@^1.9.0": "https://registry.npmmirror.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz", "vue@^2.6.11": "https://registry.npmmirror.com/vue/download/vue-2.6.11.tgz", "vuex@^3.2.0": "https://registry.npmmirror.com/vuex/download/vuex-3.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvuex%2Fdownload%2Fvuex-3.4.0.tgz", "w3c-hr-time@^1.0.1": "https://registry.npmmirror.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz?cache=0&sync_timestamp=1583455604765&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fw3c-hr-time%2Fdownload%2Fw3c-hr-time-1.0.2.tgz", "w3c-xmlserializer@^1.1.2": "https://registry.npmmirror.com/w3c-xmlserializer/download/w3c-xmlserializer-1.1.2.tgz", "walker@^1.0.7": "https://registry.npmmirror.com/walker/download/walker-1.0.7.tgz", "walker@~1.0.5": "https://registry.npmmirror.com/walker/download/walker-1.0.7.tgz", "watchpack-chokidar2@^2.0.0": "https://registry.npmmirror.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.0.tgz", "watchpack@^1.6.1": "https://registry.npmmirror.com/watchpack/download/watchpack-1.7.2.tgz", "wbuf@^1.1.0": "https://registry.npmmirror.com/wbuf/download/wbuf-1.7.3.tgz", "wbuf@^1.7.3": "https://registry.npmmirror.com/wbuf/download/wbuf-1.7.3.tgz", "wcwidth@^1.0.1": "https://registry.npmmirror.com/wcwidth/download/wcwidth-1.0.1.tgz", "webidl-conversions@^4.0.2": "https://registry.npmmirror.com/webidl-conversions/download/webidl-conversions-4.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebidl-conversions%2Fdownload%2Fwebidl-conversions-4.0.2.tgz", "webpack-bundle-analyzer@^3.6.1": "https://registry.npmmirror.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.8.0.tgz", "webpack-chain@^6.4.0": "https://registry.npmmirror.com/webpack-chain/download/webpack-chain-6.4.0.tgz?cache=0&sync_timestamp=1580740803516&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-chain%2Fdownload%2Fwebpack-chain-6.4.0.tgz", "webpack-dev-middleware@^3.7.2": "https://registry.npmmirror.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.2.tgz?cache=0&sync_timestamp=1582191620751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-dev-middleware%2Fdownload%2Fwebpack-dev-middleware-3.7.2.tgz", "webpack-dev-server@^3.10.3": "https://registry.npmmirror.com/webpack-dev-server/download/webpack-dev-server-3.11.0.tgz", "webpack-log@^2.0.0": "https://registry.npmmirror.com/webpack-log/download/webpack-log-2.0.0.tgz", "webpack-merge@^4.1.4": "https://registry.npmmirror.com/webpack-merge/download/webpack-merge-4.2.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-merge%2Fdownload%2Fwebpack-merge-4.2.2.tgz", "webpack-merge@^4.2.2": "https://registry.npmmirror.com/webpack-merge/download/webpack-merge-4.2.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-merge%2Fdownload%2Fwebpack-merge-4.2.2.tgz", "webpack-sources@^1.1.0": "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-1.4.3.tgz", "webpack-sources@^1.3.0": "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-1.4.3.tgz", "webpack-sources@^1.4.0": "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-1.4.3.tgz", "webpack-sources@^1.4.1": "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-1.4.3.tgz", "webpack-sources@^1.4.3": "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-1.4.3.tgz", "webpack@^4.0.0": "https://registry.npmmirror.com/webpack/download/webpack-4.43.0.tgz", "webpack@^4.29.5": "https://registry.npmmirror.com/webpack/download/webpack-4.43.0.tgz", "websocket-driver@0.6.5": "https://registry.npmmirror.com/websocket-driver/download/websocket-driver-0.6.5.tgz", "websocket-driver@>=0.5.1": "https://registry.npmmirror.com/websocket-driver/download/websocket-driver-0.7.3.tgz", "websocket-extensions@>=0.1.1": "https://registry.npmmirror.com/websocket-extensions/download/websocket-extensions-0.1.3.tgz", "whatwg-encoding@^1.0.1": "https://registry.npmmirror.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz", "whatwg-encoding@^1.0.5": "https://registry.npmmirror.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz", "whatwg-mimetype@^2.2.0": "https://registry.npmmirror.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz", "whatwg-mimetype@^2.3.0": "https://registry.npmmirror.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz", "whatwg-url@^7.0.0": "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-7.1.0.tgz?cache=0&sync_timestamp=1588965133257&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhatwg-url%2Fdownload%2Fwhatwg-url-7.1.0.tgz", "which-module@^2.0.0": "https://registry.npmmirror.com/which-module/download/which-module-2.0.0.tgz", "which@^1.2.9": "https://registry.npmmirror.com/which/download/which-1.3.1.tgz?cache=0&sync_timestamp=1574116262707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-1.3.1.tgz", "which@^1.3.1": "https://registry.npmmirror.com/which/download/which-1.3.1.tgz?cache=0&sync_timestamp=1574116262707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-1.3.1.tgz", "which@^2.0.1": "https://registry.npmmirror.com/which/download/which-2.0.2.tgz?cache=0&sync_timestamp=1574116262707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-2.0.2.tgz", "which@^2.0.2": "https://registry.npmmirror.com/which/download/which-2.0.2.tgz?cache=0&sync_timestamp=1574116262707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-2.0.2.tgz", "word-wrap@~1.2.3": "https://registry.npmmirror.com/word-wrap/download/word-wrap-1.2.3.tgz", "worker-farm@^1.7.0": "https://registry.npmmirror.com/worker-farm/download/worker-farm-1.7.0.tgz", "wrap-ansi@^5.1.0": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz?cache=0&sync_timestamp=1587574768060&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-5.1.0.tgz", "wrap-ansi@^6.2.0": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz?cache=0&sync_timestamp=1587574768060&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-6.2.0.tgz", "wrap-loader@^0.2.0": "https://registry.npmmirror.com/wrap-loader/download/wrap-loader-0.2.0.tgz", "wrappy@1": "https://registry.npmmirror.com/wrappy/download/wrappy-1.0.2.tgz", "write-file-atomic@^3.0.0": "https://registry.npmmirror.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrite-file-atomic%2Fdownload%2Fwrite-file-atomic-3.0.3.tgz", "ws@^6.0.0": "https://registry.npmmirror.com/ws/download/ws-6.2.1.tgz", "ws@^6.2.1": "https://registry.npmmirror.com/ws/download/ws-6.2.1.tgz", "ws@^7.0.0": "https://registry.npmmirror.com/ws/download/ws-7.3.0.tgz", "ws@^7.1.2": "https://registry.npmmirror.com/ws/download/ws-7.3.0.tgz", "ws@^7.2.3": "https://registry.npmmirror.com/ws/download/ws-7.3.0.tgz", "ws@~6.1.0": "https://registry.npmmirror.com/ws/download/ws-6.1.4.tgz", "x-domhandler@^2.4.2": "https://registry.npmmirror.com/x-domhandler/download/x-domhandler-2.4.2.tgz", "xml-name-validator@^3.0.0": "https://registry.npmmirror.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz", "xmlchars@^2.1.1": "https://registry.npmmirror.com/xmlchars/download/xmlchars-2.2.0.tgz", "xmlhttprequest-ssl@~1.5.4": "https://registry.npmmirror.com/xmlhttprequest-ssl/download/xmlhttprequest-ssl-1.5.5.tgz", "xregexp@4.0.0": "https://registry.npmmirror.com/xregexp/download/xregexp-4.0.0.tgz", "xtend@^4.0.0": "https://registry.npmmirror.com/xtend/download/xtend-4.0.2.tgz", "xtend@~4.0.1": "https://registry.npmmirror.com/xtend/download/xtend-4.0.2.tgz", "y18n@^4.0.0": "https://registry.npmmirror.com/y18n/download/y18n-4.0.0.tgz", "yallist@^2.1.2": "https://registry.npmmirror.com/yallist/download/yallist-2.1.2.tgz", "yallist@^3.0.2": "https://registry.npmmirror.com/yallist/download/yallist-3.1.1.tgz", "yallist@^4.0.0": "https://registry.npmmirror.com/yallist/download/yallist-4.0.0.tgz", "yargs-parser@^13.1.2": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-13.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-13.1.2.tgz", "yargs-parser@^18.1.1": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-18.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-18.1.3.tgz", "yargs@^13.3.2": "https://registry.npmmirror.com/yargs/download/yargs-13.3.2.tgz?cache=0&sync_timestamp=1589566384961&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-13.3.2.tgz", "yargs@^15.0.0": "https://registry.npmmirror.com/yargs/download/yargs-15.3.1.tgz?cache=0&sync_timestamp=1589566384961&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-15.3.1.tgz", "yargs@^15.3.1": "https://registry.npmmirror.com/yargs/download/yargs-15.3.1.tgz?cache=0&sync_timestamp=1589566384961&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-15.3.1.tgz", "yeast@0.1.2": "https://registry.npmmirror.com/yeast/download/yeast-0.1.2.tgz", "ylru@^1.2.0": "https://registry.npmmirror.com/ylru/download/ylru-1.2.1.tgz"}, "files": [], "artifacts": {"fsevents@1.2.13": ["build", "build/config.gypi"]}}