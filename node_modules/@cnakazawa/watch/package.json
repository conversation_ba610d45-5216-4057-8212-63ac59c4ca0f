{"name": "@cnakazawa/watch", "description": "Utilities for watching file trees.", "scripts": {"release:major": "bash scripts/release.sh major", "release:minor": "bash scripts/release.sh minor", "release:patch": "bash scripts/release.sh patch"}, "keywords": ["util", "utility", "fs", "files"], "bin": {"watch": "./cli.js"}, "version": "1.0.4", "homepage": "https://github.com/mikeal/watch", "bugs": {"url": "https://github.com/mikeal/watch/issues"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git://github.com/mikeal/watch.git"}, "author": "<PERSON><PERSON> <<EMAIL>>", "directories": {"lib": "lib"}, "engines": {"node": ">=0.1.95"}, "main": "./main", "dependencies": {"exec-sh": "^0.3.2", "minimist": "^1.2.0"}}