<meta charset="utf-8">
<title><%- htmlWebpackPlugin.options.libName %> demo</title>
<script src="https://unpkg.com/vue"></script>
<script src="./<%- htmlWebpackPlugin.options.assetsFileName %>.umd.js"></script>
<% if (htmlWebpackPlugin.options.cssExtract) { %>
<link rel="stylesheet" href="./<%- htmlWebpackPlugin.options.assetsFileName %>.css">
<% } %>

<div id="app">
  <demo></demo>
</div>

<script>
new Vue({
  components: {
    demo: <%- htmlWebpackPlugin.options.libName %>
  }
}).$mount('#app')
</script>
