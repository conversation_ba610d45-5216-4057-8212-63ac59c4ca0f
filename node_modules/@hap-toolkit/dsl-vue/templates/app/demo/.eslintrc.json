{"env": {"commonjs": true}, "extends": "eslint:recommended", "parser": "babel-es<PERSON>", "parserOptions": {"sourceType": "module", "ecmaFeatures": {"experimentalObjectRestSpread": true, "jsx": true}}, "globals": {"loadData": false, "saveData": false, "history": false, "console": false, "setTimeout": false, "clearTimeout": false, "setInterval": false, "clearInterval": false}, "plugins": ["hybrid"], "rules": {"indent": ["warn", 2], "no-console": ["warn", {"allow": ["info", "warn", "error"]}], "no-unused-vars": ["warn", {"varsIgnorePattern": "prompt"}], "quotes": ["warn", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "linebreak-style": ["warn", "unix"], "semi": ["warn", "never"]}}