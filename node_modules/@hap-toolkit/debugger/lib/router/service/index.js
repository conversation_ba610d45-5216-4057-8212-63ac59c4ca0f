"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getInspectorUrl=getInspectorUrl,exports.routerConf=routerConf,exports.serverConf=serverConf,exports.getDebugInfoFromRequest=getDebugInfoFromRequest,exports.callDeviceWithOwnSn=callDeviceWithOwnSn,exports.LINK_MODE=void 0;var _http=_interopRequireDefault(require("http")),_sharedUtils=require("@hap-toolkit/shared-utils"),_utils=require("../../lib/utils"),_api=_interopRequireDefault(require("../api"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}const LINK_MODE={NULL:0,WIFI:1,ADB:2};function getInspectorUrl(e){const{ws:t,serverPort:r}=e;return`${`http://${(0,_utils.getServerIPAndPort)(r)}`}/inspector/inspector.html${`?ws=${encodeURI(t)}&remoteFrontend=true&dockSide=undocked`}`}function routerConf(e){return e.router.conf}function serverConf(e){return e.conf}function getClientFromRequest(e){const t=(0,_utils.getClientIPAddress)(e),r=(0,_utils.getIPv4IPAddress)(),o=e.header["device-serial-number"];let n=LINK_MODE.NULL;return"127.0.0.1"===t&&o?n=LINK_MODE.ADB:"127.0.0.1"!==t&&t!==r&&(n=LINK_MODE.WIFI),{clientIp:t,sn:o,linkMode:n}}function getDebugInfoFromRequest(e){const{sn:t,linkMode:r}=getClientFromRequest(e),{ws:o,application:n}=e.body,s=o.split(":")[1].split("/")[0];return{sn:t,linkMode:r,ws:o,application:n,devicePort:s}}function callDeviceWithOwnSn(e){const t={host:e.ip,port:e.port,path:_api.default.reportSn,headers:{"device-serial-number":e.sn},timeout:3e3},r=_http.default.request(t,()=>{_sharedUtils.colorconsole.log(`### App Server ### 通知手机设备(${e.sn})下发SN成功`)}).on("error",t=>{_sharedUtils.colorconsole.warn(`### App Server ### 通知手机设备(${e.sn})下发SN失败 错误信息: ${t.message}`)}).on("timeout",(function(){_sharedUtils.colorconsole.warn(`### App Server ### 通知手机设备(${e.sn})下发SN失败`),r.abort()}));r.end()}exports.LINK_MODE=LINK_MODE;
//# sourceMappingURL=index.js.map
