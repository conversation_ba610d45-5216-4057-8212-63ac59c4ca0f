"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.searchModuleImport=searchModuleImport,exports.updateManifest=updateManifest;var _utils=require("./utils");function frameworkInit(){global.framework={module:{base:"system",ext:"service"},reservedFeatures:[],reservedFeatureExclude:[],supportInCard:[],project:{manifestFilePath:null,featureList:[],module:{usedBaseAll:!1,usedExtAll:!1}}};["alarm","audio","barcode","bluetooth","brightness","calendar","cipher","clipboard","configuration","contact","device","fetch","file","geolocation","image","media","network","notification","package","prompt","record","request","resident","sensor","share","shortcut","sms","storage","vibrator","volume","websocketfactory","webview","battery","wifi","mediaquery","zip"].forEach(e=>{global.framework.reservedFeatures.push(`${global.framework.module.base}.${e}`)}),["account","alipay","ad","wxaccount","qqaccount","wbaccount","exchange","health","pay","push","qqaccount","share","stats","wbaccount","wxaccount","wxpay"].forEach(e=>{global.framework.reservedFeatures.push(`${global.framework.module.ext}.${e}`)});["app","model","router"].forEach(e=>{global.framework.reservedFeatureExclude.push(`${global.framework.module.base}.${e}`)});["share","prompt","vibrator","fetch","storage","clipboard","geolocation","network","device","battery","calendar","package","app","router"].forEach(e=>{global.framework.supportInCard.push(`${global.framework.module.base}.${e}`)})}function searchModuleImport(e,r={}){const a=[],o=new RegExp(`['"]@(${global.framework.module.base}|${global.framework.module.ext}).*?['"]`,"g"),t=new RegExp(`['"]@((${global.framework.module.base}|${global.framework.module.ext}).*?)['"]`);return(e.match(o)||[]).forEach(e=>{const o=(e.match(t)||[])[1];o===`@${global.framework.module.base}`?global.framework.project.module.usedBaseAll=!0:o===`@${global.framework.module.ext}`?global.framework.project.module.usedExtAll=!0:-1!==global.framework.reservedFeatures.indexOf(o)?-1===global.framework.project.featureList.indexOf(o)&&global.framework.project.featureList.push(o):r.uxType===_utils.ENTRY_TYPE.CARD&&-1===global.framework.supportInCard.indexOf(o)?a.push({reason:`WARN: 您引入了卡片中未识别的native模块：${o}`}):-1!==global.framework.reservedFeatureExclude.indexOf(o)||a.push({reason:`WARN: 您引入了未识别的native模块：${o}`})}),{fileCont:e,logFeatureList:a}}function checkFeatureInCard(e={}){Object.keys(e).forEach(r=>{e[r].features.every(e=>{-1===global.framework.supportInCard.indexOf(e.name)&&_utils.colorconsole.error(`WARN: manifest.json文件中引入了卡片${r}未识别的features：${e.name}`)})})}function updateManifest(e,r){e.config=e.config||{},e.config.debug=r,e.minPlatformVersion||(e.minPlatformVersion=1020),e.features=e.features||[],checkFeatureInCard(e.router.widgets||{});const a=[].concat(global.framework.project.featureList);e.features.forEach(e=>{const r=a.indexOf(e.name);-1!==r&&a.splice(r,1)});const o=[];if(a.forEach(e=>{-1!==global.framework.reservedFeatures.indexOf(e)&&o.push(e)}),o.length>0){const r=o.map((function(e){return{name:e}}));e.features=e.features.concat(r);const a=o.join(", ");_utils.colorconsole.warn(`请在manifest.json文件里声明项目代码中用到的接口: ${a}\n`)}return e}frameworkInit();
//# sourceMappingURL=shared.js.map
