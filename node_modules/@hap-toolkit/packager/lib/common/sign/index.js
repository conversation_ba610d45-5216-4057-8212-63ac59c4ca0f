"use strict";var _crypto=_interopRequireDefault(require("crypto")),_jsrsasign=_interopRequireDefault(require("jsrsasign")),_sharedUtils=require("@hap-toolkit/shared-utils"),_base=_interopRequireDefault(require("./base64")),_crc=_interopRequireDefault(require("./crc32"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function signZip(e,t,n,s){const r=Buffer.from(_base.default.unarmor(s)),i=new _jsrsasign.default.X509;i.readCertPEM(s.toString());const a=_jsrsasign.default.KEYUTIL.getPEM(i.subjectPublicKeyRSA);if(!e||e.length<=4)return _sharedUtils.colorconsole.error("### App Loader ### Zip文件打开失败"),!1;if(67324752!==e.readInt32LE(0))return _sharedUtils.colorconsole.error("### App Loader ### Zip文件格式错误"),!1;const l=parserZip(e);if(l.options={files:t},l.tag){return Object.keys(l.sections).forEach(t=>{const s=l.sections[t];processChunk(e,s,n)}),signChunk(l,n,a,r),saveChunk(e,l)}return null}function parserZip(e){const t={tag:!1,length:e.length,sections:{header:null,central:null,footer:null}};return t.sections.footer=readEOCD(e),t.sections.footer.tag&&(t.sections.central=readCD(e,t.sections.footer.previous,t.sections.footer.startIndex-t.sections.footer.previous),t.sections.central.tag&&(t.sections.header=readFH(e,t.sections.central.previous,t.sections.central.startIndex-t.sections.central.previous),t.sections.header.tag&&(t.tag=!0))),t}function readEOCD(e){const t={tag:!1};if(e&&e.length>=22){let n,s=e.length-22;for(;s>=0;){if(n=e.readInt32LE(s),101010256===n){t.tag=!0,t.startIndex=s,t.len=e.length-s,t.previous=e.readInt32LE(s+16);break}s-=1}}return t}function readCD(e,t,n){const s={tag:!1};if(e&&e.length>=t){33639248===e.readInt32LE(t)&&(s.tag=!0,s.startIndex=t,s.len=n,s.previous=e.readInt32LE(t+42))}return s}function readFH(e,t,n){const s={tag:!1};if(e&&e.length>=t){67324752===e.readInt32LE(t)&&(s.tag=!0,s.startIndex=t,s.len=n,s.previous=-1)}return s}function processChunk(e,t){const n=t.startIndex,s=t.startIndex+t.len,r=e.slice(n,s),i=Buffer.alloc(5+t.len);i[0]=165,i.writeInt32LE(r.length,1),r.copy(i,5);const a=_crypto.default.createHash("SHA256");a.update(i),t.sign=a.digest()}function getBufferDigest(e){const t=_crypto.default.createHash("SHA256");return t.update(e),t.digest()}function signChunk(e,t,n,s){const r=e.sections,i=r.header.sign.length+r.central.sign.length+r.footer.sign.length+5,a=Buffer.alloc(i);let l=0;function o(e){e.copy(a,l),l+=e.length}a.writeInt8(90,0),a.writeInt32LE(3,1),l+=5,o(r.header.sign),o(r.central.sign),o(r.footer.sign);const c=_crypto.default.createHash("SHA256");c.update(a);const u=c.digest(),g=makeSignChunk(e.options,u,t,n,s);e.signchunk=saveSignChunk(g)}function makeSignChunk(e,t,n,s,r){const i=Buffer.alloc(t.length+12);i.writeInt32LE(t.length+8,0),i.writeInt32LE(259,4),i.writeInt32LE(t.length,8),t.copy(i,12);const a={len:i.length,buffer:i},l=Buffer.alloc(r.length+4);l.writeInt32LE(r.length,0),r.copy(l,4);const o={len:l.length,buffer:l},c={len:12,digests:{size:0,data:[]},certs:{size:0,data:[]},additional:0};c.digests.data.push(a),c.digests.size+=a.len,c.len+=a.len,c.certs.data.push(o),c.certs.size+=o.len,c.len+=o.len;const u=Buffer.from(_base.default.unarmor(s)),g={len:16+u.length,size:12+u.length,signdata:{size:0,buffer:null},signatures:{size:0,data:[]},pubkey:{size:u.length,buffer:u}};g.signdata.buffer=makeSignDataBuffer(c),g.signdata.size=c.len,g.size+=c.len,g.len+=c.len;const f=_crypto.default.createSign("RSA-SHA256");f.update(g.signdata.buffer);const d=f.sign(n),h={len:d.length+12,size:d.length+8,id:259,buffer:d};g.signatures.data.push(h),g.signatures.size+=h.len,g.size+=h.len,g.len+=h.len;const p={len:4,size:0,data:[]};p.data.push(g),p.size+=g.len,p.len+=g.len;const I={len:p.len+12,size:p.len+4,id:16777473,value:p},E={len:32,size:24,data:[]};if(E.data.push(I),E.size+=I.len,E.len+=I.len,e.files){const t=signFiles(e.files,n);if(t){const e={len:4,size:0,data:[]};e.data.push(t),e.size+=t.length,e.len+=t.length;const n={len:e.len+12,size:e.len+4,id:16777729,value:e};E.data.push(n),E.size+=n.len,E.len+=n.len}}return E}function makeSignDataBuffer(e){const t=Buffer.alloc(e.len);let n=0;return t.writeInt32LE(e.digests.size,n),n+=4,e.digests.data.forEach(e=>{e.buffer.copy(t,n),n+=e.len}),t.writeInt32LE(e.certs.size,n),n+=4,e.certs.data.forEach(e=>{e.buffer.copy(t,n),n+=e.len}),t.writeInt32LE(e.additional,n),t}const SigMagic="RPK Sig Block 42";function saveSignChunk(e){const t=Buffer.alloc(e.len);let n=0;return t.writeInt32LE(e.size,n),n+=4,t.writeInt32LE(0,n),n+=4,e.data.forEach(e=>{t.writeInt32LE(e.size,n),n+=4,t.writeInt32LE(0,n),n+=4,t.writeInt32LE(e.id,n),n+=4,t.writeInt32LE(e.value.size,n),n+=4,16777473===e.id?e.value.data.forEach(e=>{t.writeInt32LE(e.size,n),n+=4,t.writeInt32LE(e.signdata.size,n),n+=4,e.signdata.buffer.copy(t,n),n+=e.signdata.buffer.length,t.writeInt32LE(e.signatures.size,n),n+=4,e.signatures.data.forEach(e=>{t.writeInt32LE(e.size,n),n+=4,t.writeInt32LE(e.id,n),n+=4,t.writeInt32LE(e.buffer.length,n),n+=4,e.buffer.copy(t,n),n+=e.buffer.length}),t.writeInt32LE(e.pubkey.size,n),n+=4,e.pubkey.buffer.copy(t,n),n+=e.pubkey.buffer.length}):16777729===e.id&&e.value.data.forEach(e=>{e.copy(t,n),n+=e.length})}),t.writeInt32LE(e.size,n),n+=4,t.writeInt32LE(0,n),n+=4,Buffer.from(SigMagic).copy(t,n),t}function saveChunk(e,t){const n=Buffer.alloc(e.length+t.signchunk.length);let s=0;const r=t.sections;return e.copy(n,s,r.header.startIndex,r.header.startIndex+r.header.len),s+=r.header.len,t.signchunk.copy(n,s),s+=t.signchunk.length,e.copy(n,s,r.central.startIndex,r.central.startIndex+r.central.len),s+=r.central.len,e.writeInt32LE(r.central.startIndex+t.signchunk.length,r.footer.startIndex+16),e.copy(n,s,r.footer.startIndex,r.footer.startIndex+r.footer.len),s+=r.footer.len,n}function signFiles(e,t){const n={len:8,size:4,digests:[],sign:null};return e.forEach(e=>{const t=_crc.default.digest(e.name),s=6+e.hash.length,r=Buffer.alloc(s);let i=0;r.writeInt32LE(t,i),i+=4,r.writeInt16LE(e.hash.length,i),i+=2,e.hash.copy(r,i),i+=e.hash.length,n.digests.push(r),n.size+=s,n.len+=s}),signDigestChunk(n,t),saveDigestChunk(n)}function signDigestChunk(e,t){const n=Buffer.alloc(e.size);let s=0;n.writeInt32LE(259,s),s+=4,e.digests.forEach(e=>{e.copy(n,s),s+=e.length}),e.digests=n.slice();const r=_crypto.default.createSign("RSA-SHA256");r.update(n);const i=r.sign(t);e.sign={len:12+i.length,size:8+i.length,id:259,data:i},e.len+=e.sign.len}function saveDigestChunk(e){const t=Buffer.alloc(e.len);let n=0;return t.writeInt32LE(e.size,n),n+=4,e.digests.copy(t,n),n+=e.digests.length,t.writeInt32LE(e.sign.size,n),n+=4,t.writeInt32LE(e.sign.id,n),n+=4,t.writeInt32LE(e.sign.data.length,n),n+=4,e.sign.data.copy(t,n),n+=e.sign.data.length,t}module.exports={signZip:signZip,getBufferDigest:getBufferDigest};
//# sourceMappingURL=index.js.map
