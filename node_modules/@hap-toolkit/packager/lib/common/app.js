"use strict";const chai=require("hybrid-chai/chai");require("hybrid-mocha/mocha.js"),global.assert=chai.assert,global.expect=chai.expect,global.should=chai.should(),global.model=null,global.assert.approxEqual=function(e,o,l){const t=Math.abs(Math.abs(e)-Math.abs(o));global.assert.isAtMost(t,2,l)},global.loadData=function(e){let o=null;return o="undefined"==typeof window?global[e]:global.localStorage.getItem(e),JSON.parse(o||null)},global.saveData=function(e,o){return"undefined"==typeof window?global[e]=JSON.stringify(o):global.localStorage.setItem(e,JSON.stringify(o)),o},global.pushData=function(e,o){const l=global.loadData(e)||[];l.push(o),global.saveData(e,l)},global.window&&(global.window.onerror=function(){console.info("error: ",arguments)});let timer=0;global.nextTime=function(e){return void 0!==e&&(timer=e),timer+=50},global.setTimeoutDone=function(e,o,l){if(!l)throw new Error("[ERROR] 异步的测试用例请传递Mocha的done函数！");global.setTimeout((function(){try{e()}catch(e){l(e)}}),o)},global.normalize=function(e){return e=e||{},Object.keys(e).forEach((function(o){e[o]&&e[o].replace&&(e[o]=e[o].replace(/px/g,""));let l=e[o];if(["margin","padding","borderWidth","borderColor"].indexOf(o)>-1&&/\s+/.test(l)){const t=l.replace(/,\s+/g,",").split(/\s+/),a=t.reduce((e,o)=>(e[o]=!0,e),{});1===Object.keys(a).length&&(e[o]=t[0])}l=e[o],/^\d*\.?\d*$/.test(l)&&!isNaN(parseFloat(l))&&(e[o]=+l,e[o+"Std"]=Math.round(750*e[o]/global.Env.deviceWidth))})),e},global.nodeRect=function(e){return e=e.ref||e,global.model=global.model||require("@system.model"),global.model.getBoundingRect({ref:e})||{}},global.nodeAttr=function(e){return e=e.ref||e,global.model=global.model||require("@system.model"),global.model.getComputedAttr({ref:e})||{}},global.nodeStyle=function(e){return e=e.ref||e,global.model=global.model||require("@system.model"),global.model.getComputedStyle({ref:e})||{}},global.nodeInfo=function(e){return e=e.ref||e,global.model=global.model||require("@system.model"),global.model.getComponent({ref:e})||{}};
//# sourceMappingURL=app.js.map
