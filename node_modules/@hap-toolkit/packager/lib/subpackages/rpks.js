"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.createPackagesDefinition=createPackagesDefinition,exports.allocateResourceToPackages=allocateResourceToPackages,exports.signZipPkgs=signZipPkgs,exports.DIGEST_ZIP_PATH=void 0;var _path=_interopRequireDefault(require("path")),_fs=_interopRequireDefault(require("fs")),_jszip=_interopRequireDefault(require("jszip")),_sharedUtils=require("@hap-toolkit/shared-utils"),_sign=require("../common/sign");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){_defineProperty(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _defineProperty(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const MAIN_PKG_NAME="base",DIGEST_ZIP_PATH="META-INF/CERT";exports.DIGEST_ZIP_PATH=DIGEST_ZIP_PATH;const DEIGEST_HASH_JSON="hash.json",SINGLE_PKG_SIZE=1048576,FULL_PKG_SIZE=4*SINGLE_PKG_SIZE,COMPRESS_OPTS={type:"nodebuffer",compression:"DEFLATE",compressionOptions:{level:9}};function ClassPkgDef(e){Object.assign(this,{fileName:null,icon:"",standalone:!1,subMatch:null,resourceList:[]},e)}function createFullPackage(){return new ClassPkgDef({fileName:"rpk",standalone:!0})}function createSubPackages(e,t){let n;n=[];const r=new ClassPkgDef({fileName:MAIN_PKG_NAME+".srpk",standalone:!0});return n.push(r),e.forEach(e=>{const r=new ClassPkgDef({subMatch:new RegExp(`^${e.resource}/.*`,"i"),fileName:e.name+".srpk",standalone:e.standalone||!1,icon:e.standalone?e.icon||t:""});n.push(r)}),n}function createPackagesDefinition(e,t){const n=createFullPackage();let r;return e&&e.length>0&&(r=createSubPackages(e,t)),{fullPackage:n,subPackages:r}}function allocateResourceToPackages(e,t,n,r){e.forEach(e=>{const s=_path.default.join(t,e),i=_fs.default.readFileSync(s),o=[e,i,(0,_sign.getBufferDigest)(i)];if(n.addResource(...o),!r)return{fullPackage:n};let a=!0;for(let t=1;t<r.length;t++){const n=r[t];if(n.standalone&&("manifest.json"===e||e.startsWith("i18n")||n.icon&&n.icon.indexOf(e)>0)&&n.addResource(...o),n.subMatch.test(e)){a=!1,n.addResource(...o);break}}if(a){r[0].addResource(...o)}})}function signZipResourcesMeta(e,t,n){const r={};e.forEach(e=>{const t=e.fileBuildPath;r[t]=e.fileContentDigest.toString("hex")});const s=new _jszip.default;return s.file(DEIGEST_HASH_JSON,JSON.stringify({algorithm:"SHA-256",digests:r})),s.generateAsync(COMPRESS_OPTS).then(e=>{const r={name:DEIGEST_HASH_JSON,hash:(0,_sign.getBufferDigest)(e)};return(0,_sign.signZip)(e,[r],t,n)})}function signPackageZip(e,t,n,r,s){const i=e.resourceList,o=new _jszip.default,a=i.map(e=>({name:e.fileBuildPath,hash:e.fileContentDigest}));return new Promise(e=>{s?e():signZipResourcesMeta(i,t,n).then(t=>{o.file(DIGEST_ZIP_PATH,t),a.unshift({name:DIGEST_ZIP_PATH,hash:(0,_sign.getBufferDigest)(t)}),e()})}).then(()=>{const e=_objectSpread({},COMPRESS_OPTS,{comment:r});return i.forEach(e=>{o.file(e.fileBuildPath,e.fileContentBuffer)}),o.generateAsync(e)}).then(e=>(0,_sign.signZip)(e,a,t,n))}function signZipPkgs(e,t,n,r,s,i,o){return signPackageZip(r,t,n,i,o).then(o=>{if(!s)return{rpkBuffer:o};const a=new _jszip.default,c=[];let u=0;const f=s.map(e=>signPackageZip(e,t,n)),l=_objectSpread({},COMPRESS_OPTS,{comment:i});return Promise.all(f).then(t=>{t.forEach((t,n)=>{const r=s[n],i=`${e}.${r.fileName}`,o=t.length;u+=o,_sharedUtils.colorconsole.log(`### App Loader ### '${i}' 大小为 ${Math.ceil(o/1024)} KB`),o>SINGLE_PKG_SIZE&&_sharedUtils.colorconsole.warn(`### App Loader ### 每个分包大小不能大于 ${SINGLE_PKG_SIZE/1024} KB, '${i}' 已超出`),a.file(i,t),c.push({name:i,hash:(0,_sign.getBufferDigest)(t)})}),u>FULL_PKG_SIZE&&_sharedUtils.colorconsole.warn(`### App Loader ### 所有分包总和大小不能大于 ${FULL_PKG_SIZE/1024} KB, 已超出`);const n=`${e}.${r.fileName}`;return a.file(n,o),c.push({name:n,hash:(0,_sign.getBufferDigest)(o)}),a.generateAsync(l)}).then(e=>{return{rpksBuffer:(0,_sign.signZip)(e,c,t,n),rpkBuffer:o}})})}ClassPkgDef.prototype.addResource=function(e,t,n){if(this.resourceList[e])throw new Error(`### App Loader ### ${e} 文件重复添加`);this.resourceList[e]=!0,this.resourceList.push({fileBuildPath:e,fileContentBuffer:t,fileContentDigest:n})};
//# sourceMappingURL=rpks.js.map
