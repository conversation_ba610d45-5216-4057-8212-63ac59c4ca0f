"use strict";var _webpackSources=require("webpack-sources");function SourcemapFixPlugin(o={}){this.options=o}function warpNewLine(o,e){if(/\.js$/.test(o))return new _webpackSources.ConcatSource("\n",e.assets[o])}SourcemapFixPlugin.prototype.apply=function(o){o.hooks.compilation.tap("SourcemapFixPlugin",(function(o){o.hooks.afterOptimizeChunkAssets.tap("SourcemapFixPlugin",(function(e){e.forEach((function(e){e.files.forEach((function(e){const i=warpNewLine(e,o);i&&(o.assets[e]=i)}))}))}))}))},module.exports=SourcemapFixPlugin;
//# sourceMappingURL=sourcemap-fix-plugin.js.map
