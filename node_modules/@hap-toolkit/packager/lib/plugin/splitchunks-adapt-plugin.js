"use strict";var _webpackSources=require("webpack-sources"),_info=require("../common/info"),_compilationConfig=require("@hap-toolkit/shared-utils/compilation-config");const pluginName="splitChunksAdaptPlugin",actualModuleRequireParam=["module.exports","module","module.exports","__webpack_require__"],moduleRequireNativeFunctions=["$app_define$","$app_bootstrap$","$app_require$","$app_define_wrap$"],appModuleRequireNativeFunctions=["$app_define$","$app_bootstrap$","$app_require$"],actualParamStr=actualModuleRequireParam.concat(moduleRequireNativeFunctions).join(", "),formalParamStr=moduleRequireNativeFunctions.join(", "),quickappGlobal="var __quickappGlobal = Object.getPrototypeOf(global) || global;";function replaceWindowWithGlobalStr(e,a){let o=e?"global":"__quickappGlobal";return a.replace(/window(?=\["webpackJsonp"\])/g,o)}class SplitChunksAdaptPlugin{constructor(e){this.options=e}apply(e){e.hooks.compilation.tap(pluginName,a=>{const o=new Set,t={},i={};let s=[],p="";a.hooks.optimizeChunkIds.tap(pluginName,a=>{s=(0,_info.getEntryFiles)(e.options.entry);const t=a.filter(e=>-1===s.indexOf(`${e.name}.js`)).map(e=>`"${e.id}": "${e.name}.js"`);p=`__quickappGlobal.chunkFileMap = __quickappGlobal.chunkFileMap || {${t.join(", ")}};`,a.forEach(e=>{e.name.match(/\bapp$/)&&e.entryModule&&(e.groupsIterable.forEach(e=>{e.chunks.forEach(e=>{o.add(`${e.name}.js`)})}),o.delete("app.js"))})}),a.hooks.optimizeChunkAssets.tapAsync(pluginName,(e,o)=>{e.forEach(e=>{e.files.forEach(o=>{if(!o.match(/\.js$/))return;let t=actualParamStr,i=formalParamStr,s=!1;o.match(/\bapp\.js$/)&&(t=actualModuleRequireParam.concat(appModuleRequireNativeFunctions).join(", "),i=appModuleRequireNativeFunctions.join(", "));let l=a.assets[o].source();e.entryModule?l=l.replace(/\/\/\s+webpackBootstrap/,e=>`${e}\n\n\t\t\t\t\t${quickappGlobal}\n\t\t\t\t\t${p}\n`):s=!0,l=replaceWindowWithGlobalStr(s,l),l=l.replace(/(?<=(if\(installedChunks\[depId\]\s+!==\s+0\)\s+))fulfilled\s+=\s+false;/,"{ fulfilled = false; $app_evaluate$(`${__quickappGlobal.chunkFileMap[depId]}`); }"),l=l.replace(/(?<=(modules\[moduleId\].call\())module.exports,\s+module,\s+module.exports,\s+__webpack_require__/g,t),l=l.replace(/(?<=function\()module,\s+(exports|__webpack_exports__)(,\s+__webpack_require__)?/g,e=>`${e}, ${i}`),a.assets[o]=new _webpackSources.ConcatSource(l)})}),o()}),a.hooks.afterOptimizeChunkAssets.tap(pluginName,e=>{e.forEach(e=>{e.files.forEach(p=>{p.match(/\.js$/)&&-1===s.indexOf(p)&&(o.has(p)?t[p]=a.assets[p].source():i[p]=a.assets[p].source(),a.assets[_compilationConfig.optionsConfig.splitChunksNameEnum.APP]=new _webpackSources.ConcatSource(JSON.stringify(t)),a.assets[_compilationConfig.optionsConfig.splitChunksNameEnum.PAGE]=new _webpackSources.ConcatSource(JSON.stringify(i)),delete a.assets[p],e.files=[])})})})})}}module.exports=SplitChunksAdaptPlugin;
//# sourceMappingURL=splitchunks-adapt-plugin.js.map
