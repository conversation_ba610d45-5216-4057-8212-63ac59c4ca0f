"use strict";var _path=_interopRequireDefault(require("path")),_fsExtra=_interopRequireDefault(require("fs-extra")),_aaptjs=_interopRequireDefault(require("@hap-toolkit/aaptjs")),_glob=_interopRequireDefault(require("glob")),_eventBus=_interopRequireDefault(require("@hap-toolkit/shared-utils/event-bus")),_sharedUtils=require("@hap-toolkit/shared-utils"),_info=require("../common/info"),_shared=require("../common/shared");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}const I18N_DIRECTORY="i18n",{PACKAGER_BUILD_DONE:PACKAGER_BUILD_DONE}=_eventBus.default,FILE_EXT_LIST=_info.name.extList,FILE_EXT_NORES=FILE_EXT_LIST.concat([".js",".jsx",".coffee",".ts",".tsx",".vue",".css",".less",".sass",".styl",".html",".json",".md"]),EXT_PATTERN=FILE_EXT_NORES.map(e=>"*"+e).join("|");function getFiles(e,t){return _glob.default.sync(e,{nodir:!0,cwd:t,ignore:["**/Thumbs.db"],absolute:!0})}function geti18nFiles(e){const t=_path.default.join(e,I18N_DIRECTORY);let s=[];return _fsExtra.default.existsSync(t)&&(s=getFiles("**/*.json",t)),s}function minifyi18nFiles(e){const t=_path.default.join(e,I18N_DIRECTORY);if(_fsExtra.default.existsSync(t)){getFiles("**/*.json",t).forEach(e=>{try{const t=_fsExtra.default.readFileSync(e,"utf8"),s=JSON.parse(t),o=JSON.stringify(s);_fsExtra.default.writeFileSync(e,o)}catch(t){_sharedUtils.colorconsole.error("### App Loader ### i18n配置文件 %s 压缩拷贝失败：%s",(0,_sharedUtils.relateCwd)(e),t.message)}})}}function ResourcePlugin(e){this.options=e}ResourcePlugin.prototype.apply=function(e){const t=this.options,s=e.options;e.hooks.watchRun.tapAsync("ResourcePlugin",(function(e,t){Object.keys(s.entry).forEach((function(e){const t=s.entry[e];t instanceof Array&&!/app\.js/.test(e)&&-1!==t[0].indexOf("webpack-dev-server")&&t.shift()})),t()})),e.hooks.emit.tapAsync("ResourcePlugin",(function(e,s){const o=t.src,r=t.dest;let a,i=getFiles(`**/+(!(${EXT_PATTERN})|manifest.json)`,o);i=i.concat(geti18nFiles(o));try{const e=_path.default.join(o,"manifest.json"),t=_fsExtra.default.readFileSync(e,"utf8");a=JSON.parse(t).icon,a=_path.default.join(o,a)}catch(e){a=""}let n=i.map(e=>({srcFile:e,destFile:_path.default.resolve(r,_path.default.relative(o,e))}));const{projectRoot:l}=t,u=_path.default.join(l,"node_modules"),c=e.fileDependencies;for(let e of c){const t=_path.default.extname(e);e.indexOf(u)>-1&&-1===FILE_EXT_NORES.indexOf(t)&&-1===i.indexOf(e)&&t&&n.push({srcFile:e,destFile:_path.default.resolve(r,_path.default.relative(l,e))})}n=n.filter(e=>{const{srcFile:s}=e;return!t.optimizeUnusedResource||(c.has(s)||s===a||"manifest.json"===_path.default.relative(o,s))});const f=n.map(e=>new Promise((t,s)=>{const{srcFile:o,destFile:r}=e;_fsExtra.default.mkdirp(_path.default.dirname(r),()=>{let e;e=/.+\.9\.png$/.test(o)?_aaptjs.default.singleCrunch(o,r).catch(e=>{e&&_sharedUtils.colorconsole.log(`### App Loader ### 复制文件 ${(0,_sharedUtils.relateCwd)(o)} 失败：${e.message}`)}):_fsExtra.default.copy(o,r).catch(e=>{throw _sharedUtils.colorconsole.log(`复制 ${o} -> ${r} 失败`),e}),e.then(t,s)})}));Promise.all(f).then(()=>{_sharedUtils.colorconsole.log(`### App Loader ### ${(0,_sharedUtils.relateCwd)(r)} 目录构建完成`),_eventBus.default.emit(PACKAGER_BUILD_DONE),s()},e=>{throw _sharedUtils.colorconsole.log("ERROR: 拷贝文件出现错误",e),e})})),e.hooks.emit.tapAsync("ResourcePlugin",(e,s)=>{const o=t.src,r=t.dest,a=_path.default.join(o,"manifest.json"),i=_path.default.join(r,"manifest.json"),n=_fsExtra.default.existsSync(a);if(minifyi18nFiles(r),n){let e;try{const t=_fsExtra.default.readFileSync(a,"utf8");e=JSON.parse(t)}catch(e){throw _sharedUtils.colorconsole.error("ERROR: 解析 manifest.json 文件出错 %s",e.message),e}const o=!t.sign;e=(0,_shared.updateManifest)(e,o);const r=JSON.stringify(e,null,o?2:0);_fsExtra.default.writeFile(i,r,"utf8",e=>{e&&_sharedUtils.colorconsole.error("### App Loader ### 更新 %s 失败：%s",(0,_sharedUtils.relateCwd)(a),e.message),s()})}else _sharedUtils.colorconsole.error("### App Loader ### %s 下无 manifest.json 文件",(0,_sharedUtils.relateCwd)(o)),s()})},module.exports=ResourcePlugin;
//# sourceMappingURL=resource-plugin.js.map
