"use strict";var _webpackSources=require("webpack-sources"),_compilationConfig=require("@hap-toolkit/shared-utils/compilation-config"),_info=require("../common/info");function HandlerPlugin(n){this.options=n}function wrapCode(n,e,o,t){if(/\.js$/.test(n))return _compilationConfig.options.splitChunksMode===_compilationConfig.optionsConfig.splitChunksModeEnum.SMART&&-1===t.indexOf(n)?new _webpackSources.ConcatSource(e.assets[n]):n.match(/\bapp\.js$/)?new _webpackSources.ConcatSource(`(function(){\n    ${o?'global = typeof window === "undefined" ? global.__proto__  : window ;':""}\n    var manifestJson = ${JSON.stringify(global.framework.manifest)}\n    var createAppHandler = function() {\n      return `,e.assets[n],'\n    };\n    if (typeof window === "undefined") {\n      return createAppHandler();\n    }\n    else {\n      window.createAppHandler = createAppHandler\n      // H5注入manifest以获取features\n      global.manifest = manifestJson;\n    }\n  })();'):new _webpackSources.ConcatSource(`(function(){\n    ${o?'global = typeof window === "undefined" ? global.__proto__  : window ;':""}\n    var createPageHandler = function() {\n      return `,e.assets[n],'\n    };\n    if (typeof window === "undefined") {\n      return createPageHandler();\n    }\n    else {\n      window.createPageHandler = createPageHandler\n    }\n  })();')}HandlerPlugin.prototype.apply=function(n){const e=this.options.workers,o=this.options.enableE2e;n.hooks.compilation.tap("HandlerPlugin",(function(t){t.hooks.optimizeChunkAssets.tapAsync("HandlerPlugin",(function(i,a){const r=(0,_info.getEntryFiles)(n.options.entry);i.forEach((function(n){n.files.forEach((function(n){if(n.startsWith(e))return;const i=wrapCode(n,t,o,r);i&&(t.assets[n]=i)}))})),a()}))}))},module.exports=HandlerPlugin;
//# sourceMappingURL=handler-plugin.js.map
