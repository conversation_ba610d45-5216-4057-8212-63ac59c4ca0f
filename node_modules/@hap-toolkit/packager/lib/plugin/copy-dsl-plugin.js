"use strict";var _fs=_interopRequireDefault(require("fs")),_sharedUtils=require("@hap-toolkit/shared-utils");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}const pkgReg=/^quickapp-dsls.*$/;function findPackageName(e,o){let i;for(let t=0,s=e.length;t<s;t++){const s=o.exec(e[t]);if(s){i=s[0];break}}return i}function CopyDslPlugin(e){this.options=e,this.packageList=Object.keys(require(`${this.options.cwd}/package.json`).devDependencies||{})}CopyDslPlugin.prototype.apply=function(e){const o=this;e.hooks.emit.tapAsync("copyDslPlugin",(function(e,i){o.copyFile(i)}))},CopyDslPlugin.prototype.copyFile=function(e){var o,i,t;const s=findPackageName(this.packageList,pkgReg);if(!s)return void e();const n=(null===(o=this.options)||void 0===o?void 0:null===(i=o.config)||void 0===i?void 0:null===(t=i.dsl)||void 0===t?void 0:t.name)||"vue",l=`${this.options.cwd}/node_modules/${s}/dist/release/dsls/${n}.js`,r=`${this.options.cwd}/build/dsl.js`,a=_fs.default.createWriteStream(r);_sharedUtils.colorconsole.log(`复制文件${l}`),_fs.default.createReadStream(l).pipe(a),_sharedUtils.colorconsole.log(`复制到${r}`),a.on("finish",e)},module.exports=CopyDslPlugin;
//# sourceMappingURL=copy-dsl-plugin.js.map
