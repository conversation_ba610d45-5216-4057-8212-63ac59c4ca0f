"use strict";var _fs=_interopRequireDefault(require("fs")),_path=_interopRequireDefault(require("path")),_sharedUtils=require("@hap-toolkit/shared-utils"),_config=_interopRequireDefault(require("@hap-toolkit/shared-utils/config")),_utils=require("../common/utils"),_compilationConfig=require("@hap-toolkit/shared-utils/compilation-config"),_rpks=require("../subpackages/rpks");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function resolveFiles(e,i){let t=(0,_utils.lsdirdeep)(e);return t.includes(_rpks.DIGEST_ZIP_PATH)?(console.warning(`检测到存在快应用保留文件: ${_rpks.DIGEST_ZIP_PATH}`),!1):(t=(0,_utils.sortFilesBy)(t,i),t)}function ZipPlugin(e){this.options=Object.assign({priorities:["manifest.json","app.js"]},e);const i=e.cwd||_config.default.projectPath,t=e.pathSignFolder||"sign",s={debug:{privatekey:_path.default.join(i,t,"debug/private.pem"),certificate:_path.default.join(i,t,"debug/certificate.pem")},release:{privatekey:_path.default.join(i,t,"release/private.pem"),certificate:_path.default.join(i,t,"release/certificate.pem")}};this.pemFiles=s[e.sign]}function generateDistFile(e,i,t,s){const o=`${i.name}.${i.sign}.${s}`,r=_path.default.join(i.output,o);_fs.default.writeFileSync(r,e),_sharedUtils.colorconsole.log(`### App Loader ### ${(0,_sharedUtils.relateCwd)(i.output)}目录签名并生成${s}文件：${o}`)}ZipPlugin.prototype.apply=function(e){const i=this.options;if(!this.pemFiles)return void _sharedUtils.colorconsole.error("> 无签名配置项, 放弃打包: ",i.sign);let t;!i.disableSubpackages&&i.subpackages&&i.subpackages.length>0&&(t=i.subpackages),e.hooks.afterEmit.tapAsync("ZipPlugin",(s,o)=>{function r(e,i){e+=(0,_sharedUtils.relateCwd)(i),s.errors.push(new _sharedUtils.KnownError(e)),o()}const{privatekey:n,certificate:a}=this.pemFiles;if(!_fs.default.existsSync(n))return void r("> 缺少私钥文件, 打包失败: ",n);if(!_fs.default.existsSync(a))return void r("> 缺少证书文件, 打包失败: ",a);const l=_fs.default.readFileSync(n),p=_fs.default.readFileSync(a);if(_compilationConfig.options.splitChunksMode===_compilationConfig.optionsConfig.splitChunksModeEnum.SMART){const e=i.priorities.findIndex(e=>"app.js"===e);i.priorities.splice(e,0,_compilationConfig.optionsConfig.splitChunksNameEnum.APP),i.priorities.splice(e+2,0,_compilationConfig.optionsConfig.splitChunksNameEnum.PAGE)}const u=resolveFiles(i.pathBuild,i.priorities);if(!1===u)return void o();const{fullPackage:c,subPackages:f}=(0,_rpks.createPackagesDefinition)(t,i.icon);(0,_rpks.allocateResourceToPackages)(u,i.pathBuild,c,f),(0,_rpks.signZipPkgs)(i.name,l,p,c,f,i.comment,i.disableStreamPack).then(({rpksBuffer:t,rpkBuffer:s})=>{_fs.default.mkdir(i.output,()=>{t&&generateDistFile(t,i,e.watchMode,"rpks"),generateDistFile(s,i,e.watchMode,"rpk"),o()})})})},module.exports=ZipPlugin;
//# sourceMappingURL=zip-plugin.js.map
