"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _fs=_interopRequireDefault(require("fs")),_path=_interopRequireDefault(require("path")),_qrImage=_interopRequireDefault(require("qr-image")),_moment=_interopRequireDefault(require("moment")),_sharedUtils=require("@hap-toolkit/shared-utils"),_config=_interopRequireDefault(require("@hap-toolkit/shared-utils/config")),_recordClient=require("@hap-toolkit/shared-utils/lib/record-client"),_service=require("../service");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}const CLIENT_PORT=39517,RPKS_SUPPORT_VERSION_FROM=1040;async function index(e,r){const t=e.app.server.address().port,o=(0,_service.getServerAddress)(t),a=_qrImage.default.image(o,{size:9});e.type="image/png",e.body=a,await r()}async function bundle(e,r){const{projectPath:t}=_config.default,o=_path.default.join(t,_config.default.releasePath);let a;const i=(0,_service.serverConf)(e),{options:s={}}=i;if(s.rpkPath)a=s.rpkPath,_path.default.isAbsolute(a)||(a=_path.default.join(t,s.rpkPath)),_sharedUtils.colorconsole.log(`### App Server ### 指定返回rpk：${a}`);else{const r=(0,_service.getProjectName)(t,_config.default.sourceRoot),i=e.request.query.platformVersion;i&&i>=RPKS_SUPPORT_VERSION_FROM&&(a=(0,_service.getDistFilePath)(o,r,"rpks")),a||(a=(0,_service.getDistFilePath)(o,r,"rpk"))}a?(e.body=_fs.default.createReadStream(a),e.set("Content-Type","application/octet-stream")):(_sharedUtils.colorconsole.error(`### App Server ### 项目${(0,_sharedUtils.relateCwd)(o)}目录下不存在rpk或rpks文件：${o}`),e.throw("404","无法找到项目的rpks或rpk文件")),await r()}async function logger(e,r){try{const{clientRecordPath:t}=_config.default,{sn:o,clientIp:a,linkMode:i}=(0,_service.getClientFromRequest)(e.request);let s={sn:o,ip:a,port:CLIENT_PORT};switch(i){case _service.LINK_MODE.WIFI:_sharedUtils.colorconsole.info(`### App Server ### 记录从${a}进入的HTTP请求`),(0,_recordClient.recordClient)(t,s);break;case _service.LINK_MODE.ADB:s=(0,_recordClient.getRecordClient)(t,o,a),s?(_sharedUtils.colorconsole.info(`### App Server ### 记录从设备(${o})进入的HTTP请求`),(0,_recordClient.recordClient)(t,s)):_sharedUtils.colorconsole.warn(`### App Server ### ：记录设备(${o})失败`)}await r()}catch(e){_sharedUtils.colorconsole.error(`### App Server ### 记录log出错: ${e.message}`)}}async function notify(e,r){const t=(0,_service.serverConf)(e).options.callback;if(t&&"function"==typeof t){t({action:"runCompile"})}e.status=200,await r()}async function qrCode(e,r){const t=e.app.server.address().port,o=(0,_service.getServerAddress)(t),a=_qrImage.default.image(o,{size:9});await r(),e.type="image/png",e.body=a}async function saveDataCoverage(e,r){const t=e.request.body.coverage;t||e.throw("请求错误，请携带相关的代码覆盖率数据作为请求参数！");const{projectPath:o,dataCoverage:a}=_config.default,i=_path.default.join(o,a);_fs.default.existsSync(i)||_fs.default.mkdirSync(i),_sharedUtils.colorconsole.info(`### App Server ### 保存项目运行后的代码覆盖率数据到目录：${a}`);const s=`${(0,_moment.default)().format()}.json`,n=_path.default.join(i,s.replace(/[\\/:?*<>'"|]/g,"_"));_fs.default.writeFileSync(n,JSON.stringify(t)),e.status=200}var _default={index:index,bundle:bundle,qrCode:qrCode,logger:logger,notify:notify,saveDataCoverage:saveDataCoverage};exports.default=_default;
//# sourceMappingURL=index.js.map
