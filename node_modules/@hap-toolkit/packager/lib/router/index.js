"use strict";var _koaRouter=_interopRequireDefault(require("koa-router")),_koaBodyparser=_interopRequireDefault(require("koa-bodyparser")),_api=_interopRequireDefault(require("./api")),_routes=_interopRequireDefault(require("./routes"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function applyRouter(e){e.use(_routes.default.logger),e.use((0,_koaBodyparser.default)({jsonLimit:"50mb"}));const t=new _koaRouter.default;return t.get(_api.default.index,_routes.default.index),t.get(_api.default.bundle,_routes.default.bundle),t.get(_api.default.notify,_routes.default.notify),t.post(_api.default.coverage,_routes.default.saveDataCoverage),t}module.exports={applyRouter:applyRouter};
//# sourceMappingURL=index.js.map
