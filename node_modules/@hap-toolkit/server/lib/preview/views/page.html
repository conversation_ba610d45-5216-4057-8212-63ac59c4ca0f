<!DOCTYPE html><html><head><meta charset="utf-8"><title>{{ title }}</title><meta name="viewport" content="width=device-width,initial-scale=1"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="black"><meta name="apple-touch-fullscreen" content="yes"><meta name="format-detection" content="telephone=no, email=no"><meta name="referrer" content="no-referrer"><script src="https://statres.quickapp.cn/quickapp/ide/web.js"></script><script type="text/javascript">window.Hap||document.write('<script src="/preview-static/web.js"><\/script>');</script><script type="text/javascript">!function(){Hap.init({base:"/preview"});var e=new EventSource("/preview/__stream");e.addEventListener("reload",e=>{const o=e.data;o?location.replace(`/preview/${o}`):location.reload()}),e.onerror=function(e){console.log("EventSource error",e)}}();</script></head><body><div id="scriptNotFound" style="display: none"><p>找不到页面 js 文件 {{ script }}</p><p>编译可能存在异常</p></div><script type="text/javascript">if ({{ scriptNotFound }}) {
        setTimeout(() => {
          var el = document.getElementById('scriptNotFound')
          if (el) {
            el.style.display = 'block'
          }
        }, 1000)
      }</script></body></html>