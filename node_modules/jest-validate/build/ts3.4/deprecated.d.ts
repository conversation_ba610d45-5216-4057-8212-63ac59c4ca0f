/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { ValidationOptions } from './types';
export declare const deprecationWarning: (config: Record<string, any>, option: string, deprecatedOptions: Record<string, Function>, options: ValidationOptions) => boolean;
