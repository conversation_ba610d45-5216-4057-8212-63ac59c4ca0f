/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type { Config } from '@jest/types';
import type { TestResult } from '@jest/test-result';
declare const _default: (result: TestResult, globalConfig: Config.GlobalConfig, projectConfig?: Config.ProjectConfig | undefined) => string;
export default _default;
