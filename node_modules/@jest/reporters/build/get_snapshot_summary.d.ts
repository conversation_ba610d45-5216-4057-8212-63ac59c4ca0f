/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type { Config } from '@jest/types';
import type { SnapshotSummary } from '@jest/test-result';
declare const _default: (snapshots: SnapshotSummary, globalConfig: Config.GlobalConfig, updateCommand: string) => string[];
export default _default;
