/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Config } from '@jest/types';
declare const _default: (globalConfig: Config.GlobalConfig, options?: Partial<Pick<Config.GlobalConfig, "bail" | "changedSince" | "collectCoverage" | "collectCoverageFrom" | "collectCoverageOnlyFrom" | "coverageDirectory" | "coverageReporters" | "reporters" | "notify" | "notifyMode" | "testNamePattern" | "updateSnapshot" | "verbose" | "onlyFailures" | "testPathPattern"> & {
    mode: "watch" | "watchAll";
}> & Partial<Pick<Config.GlobalConfig, "passWithNoTests" | "noSCM">>) => Config.GlobalConfig;
export default _default;
