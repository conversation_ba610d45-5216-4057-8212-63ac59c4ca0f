/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Config } from '@jest/types';
declare const _default: ({ allTests, globalConfig, moduleName, }: {
    allTests: import("jest-runner/build/types").Test[];
    globalConfig: Config.GlobalConfig;
    moduleName: "globalSetup" | "globalTeardown";
}) => Promise<void>;
export default _default;
