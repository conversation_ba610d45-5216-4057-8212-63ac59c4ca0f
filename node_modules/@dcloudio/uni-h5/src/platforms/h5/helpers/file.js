import {
  hasOwn
} from 'uni-shared'

/**
 * 暂存的文件对象
 */
const files = {}
/**
 * 从url读取File
 * @param {string} url
 * @param {Promise}
 */
export function urlToFile (url) {
  var file = files[url]
  if (file) {
    return Promise.resolve(file)
  }
  if (/^data:[a-z-]+\/[a-z-]+;base64,/.test(url)) {
    return Promise.resolve(base64ToFile(url))
  }
  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'
    xhr.onload = function () {
      resolve(this.response)
    }
    xhr.onerror = reject
    xhr.send()
  })
}
/**
 * base64转File
 * @param {string} base64
 * @return {File}
 */
export function base64ToFile (base64) {
  base64 = base64.split(',')
  var type = base64[0].match(/:(.*?);/)[1]
  var str = atob(base64[1])
  var n = str.length
  var array = new Uint8Array(n)
  while (n--) {
    array[n] = str.charCodeAt(n)
  }
  var filename = `${Date.now()}.${type.split('/')[1]}`
  return new File([array], filename, { type: type })
}
/**
 * 从本地file或者blob对象创建url
 * @param {Blob|File} file
 * @return {string}
 */
export function fileToUrl (file) {
  for (const key in files) {
    if (hasOwn(files, key)) {
      const oldFile = files[key]
      if (oldFile === file) {
        return key
      }
    }
  }
  var url = (window.URL || window.webkitURL).createObjectURL(file)
  files[url] = file
  return url
}

export function revokeObjectURL (url) {
  (window.URL || window.webkitURL).revokeObjectURL(url)
  delete files[url]
}
