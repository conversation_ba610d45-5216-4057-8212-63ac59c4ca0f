"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var t=e(require("debug")),o=e(require("puppeteer")),a=e(require("postcss-selector-parser"));function n(e){e.walk(e=>{if("tag"===e.type){const t=e.value;e.value="page"===t?"uni-page-body":"uni-"+t}})}const s=["Page.getElement","Page.getElements","Element.getElement","Element.getElements"];const c=t("automator:devtool");let r,l;const i={"Tool.close":{reflect:async()=>{await r.close()}},"App.exit":{reflect:async()=>{}},"App.enableLog":{reflect:()=>Promise.resolve()},"App.captureScreenshot":{reflect:async(e,t)=>{const o=await l.screenshot({encoding:"base64",fullPage:!!t.fullPage});return c("App.captureScreenshot "+o.length),{data:o}}}};!function(e){s.forEach(t=>{e[t]=function(e){return{reflect:async(t,o)=>t(e,o,!1),params:e=>(e.selector&&(e.selector=a(n).processSync(e.selector)),e)}}(t)})}(i);const p={devtools:{name:"google chrome",paths:[],validate:async function(e){return e.options=e.options||{},e.executablePath&&!e.options.executablePath&&(e.options.executablePath=e.executablePath),e.options.defaultViewport=Object.assign({width:375,height:667,deviceScaleFactor:2,hasTouch:!0,isMobile:!0},e.options.defaultViewport||{}),e.teardown||(e.teardown=!1===e.options.headless?"disconnect":"close"),e},create:async function(e,t,a){r=await o.launch(t.options);const n=r.process();n?c("%s %o",n.spawnfile,t.options):c("%o",t.options),l=await r.newPage(),l.on("console",e=>{a.emit("App.logAdded",{type:e.type(),args:[e.text()]})}),l.on("pageerror",e=>{a.emit("App.exceptionThrown",e)}),await l.goto(t.url||e),await l.waitFor(1e3)}},shouldCompile:(e,t)=>!t.url,adapter:i};module.exports=p;
