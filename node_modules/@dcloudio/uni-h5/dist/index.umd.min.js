(function(t,e){"object"===typeof exports&&"object"===typeof module?module.exports=e(require("vue-router"),require("vue")):"function"===typeof define&&define.amd?define([,],e):"object"===typeof exports?exports["index"]=e(require("vue-router"),require("vue")):t["index"]=e(t["VueRouter"],t["Vue"])})("undefined"!==typeof self?self:this,(function(t,e){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="fae3")}({"0001":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"loadFontFace",(function(){return o}));var i=n("a118"),r=n("db70");function o(e,n){var i=Object(r["a"])();if(!i)return{errMsg:"loadFontFace:fail not font page"};t.publishHandler("loadFontFace",{options:e,callbackId:n},i)}t.subscribe("onLoadFontFaceCallback",(function(t){var e=t.callbackId,n=t.data;Object(i["a"])(e,n)}))}.call(this,n("0dd1"))},"00b2":function(t,e,n){},"0138":function(t,e,n){"use strict";n.r(e),function(t){var i=n("052f"),r=n("3d1f"),o=n("98be"),a=n("abbf");n.d(e,"getApp",(function(){return a["b"]})),n.d(e,"getCurrentPages",(function(){return a["c"]})),Object(i["a"])(t.on,{getApp:a["b"],getCurrentPages:a["c"]}),Object(r["a"])(t.subscribe,{getApp:a["b"],getCurrentPages:a["c"]}),e["default"]=o["a"]}.call(this,n("0dd1"))},"01d0":function(t,e,n){},"02c9":function(t,e,n){"use strict";function i(t){if(0===t.indexOf("#")){var e=t.substr(1);return function(t){return!(!t.componentInstance||t.componentInstance.id!==e)||!(!t.data||!t.data.attrs||t.data.attrs.id!==e)}}if(0===t.indexOf(".")){var n=t.substr(1);return function(t){return t.data&&o(n,t.data.staticClass,t.data.class)}}}n.d(e,"a",(function(){return c}));var r=/\s+/;function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return e?-1!==e.split(r).indexOf(t):n&&"string"===typeof n?-1!==n.split(r).indexOf(t):void 0}function a(t,e){if(e(t.$vnode||t._vnode))return t;for(var n=t.$children,i=0;i<n.length;i++){var r=a(n[i],e);if(r)return r}}function s(t,e,n){e(t.$vnode||t._vnode)&&n.push(t);for(var i=t.$children,r=0;r<i.length;r++)s(i[r],e,n);return n}function c(t){t.prototype.createIntersectionObserver=function(t){return uni.createIntersectionObserver(this,t)},t.prototype.selectComponent=function(t){return a(this,i(t))},t.prototype.selectAllComponents=function(t){return s(this,i(t),[])}}},"052f":function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var i=n("a741"),r=n("45db");function o(t,e){var n=t.name,i=t.arg;"postMessage"===n||uni[n](i)}function a(t,e){var n=e.getApp,a=e.getCurrentPages;function s(t){Object(i["a"])(n(),"onError",t)}function c(t){Object(i["a"])(n(),"onPageNotFound",t)}function u(t,e){var n=a().find((function(t){return t.$page.id===e}));n&&Object(i["b"])(n,"onResize",t)}function l(t,e){var n=a().find((function(t){return t.$page.id===e}));n&&(Object(r["setPullDownRefreshPageId"])(e),Object(i["b"])(n,"onPullDownRefresh"))}function h(t,e){var n=a();n.length&&Object(i["b"])(n[n.length-1],t,e)}function f(t){return function(e){h(t,e)}}function d(){Object(i["a"])(n(),"onHide"),h("onHide")}function p(){Object(i["a"])(n(),"onShow"),h("onShow")}t("onError",s),t("onPageNotFound",c),t("onAppEnterBackground",d),t("onAppEnterForeground",p),t("onResize",u),t("onPullDownRefresh",l),t("onTabItemTap",f("onTabItemTap")),t("onNavigationBarButtonTap",f("onNavigationBarButtonTap")),t("onNavigationBarSearchInputChanged",f("onNavigationBarSearchInputChanged")),t("onNavigationBarSearchInputConfirmed",f("onNavigationBarSearchInputConfirmed")),t("onNavigationBarSearchInputClicked",f("onNavigationBarSearchInputClicked")),t("onNavigationBarSearchInputFocusChanged",f("onNavigationBarSearchInputFocusChanged")),t("onWebInvokeAppService",o)}},"0554":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"getLocation",(function(){return o}));var i=n("ffdc");function r(t,e,n){var r=__uniConfig.qqMapKey,o="https://apis.map.qq.com/ws/coord/v1/translate?locations=".concat(t.latitude,",").concat(t.longitude,"&type=1&key=").concat(r,"&output=jsonp");Object(i["a"])(o,{},(function(t){"locations"in t&&t.locations.length?e({longitude:t.locations[0].lng,latitude:t.locations[0].lat}):n(t)}),n)}function o(e,n){var i=e.type,o=e.altitude,a=t,s=a.invokeCallbackHandler;function c(t){s(n,Object.assign(t,{errMsg:"getLocation:ok",verticalAccuracy:t.altitudeAccuracy||0,horizontalAccuracy:t.accuracy}))}navigator.geolocation?navigator.geolocation.getCurrentPosition((function(t){var e=t.coords;"WGS84"===i?c(e):r(e,c,(function(t){s(n,{errMsg:"getLocation:fail "+JSON.stringify(t)})}))}),(function(){s(n,{errMsg:"getLocation:fail"})}),{enableHighAccuracy:o,timeout:3e5}):s(n,{errMsg:"getLocation:fail device nonsupport geolocation"})}}.call(this,n("0dd1"))},"0741":function(t,e,n){"use strict";var i=n("3c79"),r=n.n(i);r.a},"0758":function(t,e,n){"use strict";n.r(e),function(t){function i(e,n,i,r){var o=n.$page.id;t.publishHandler(o+"-map-"+e,{mapId:e,type:i,data:r},o)}n.d(e,"operateMapPlayer",(function(){return i}))}.call(this,n("0dd1"))},"0784":function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var i=n("f2b3"),r=n("a741");function o(t,e){var n=t.$route;t.route=n.meta.pagePath,t.options||(t.options=e);var r=Object(i["h"])(n.params,"__id__")?n.params.__id__:n.meta.id;t.__page__={id:r,path:n.path,route:n.meta.pagePath,options:e,meta:Object.assign({},n.meta)},t.$vm=t,t.$root=t,t.$holder=t.$parent.$parent,t.$mp={mpType:"page",page:t,query:{},status:""}}function a(){return{created:function(){var t=Object(i["c"])(this.$route.query);o(this,t),Object(r["b"])(this,"onLoad",t),Object(r["b"])(this,"onShow")}}}},"091a":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"createIntersectionObserver",(function(){return h}));var i=n("62b5"),r=n("db70");function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function s(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t}var c=Object(i["a"])("requestComponentObserver"),u={thresholds:[0],initialRatio:0,observeAll:!1},l=function(){function e(t,n){o(this,e),this.pageId=t.$page.id,this.component=t._$id||t,this.options=Object.assign({},u,n)}return s(e,[{key:"_makeRootMargin",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.options.rootMargin=["top","right","bottom","left"].map((function(e){return"".concat(Number(t[e])||0,"px")})).join(" ")}},{key:"relativeTo",value:function(t,e){return this.options.relativeToSelector=t,this._makeRootMargin(e),this}},{key:"relativeToViewport",value:function(t){return this.options.relativeToSelector=null,this._makeRootMargin(t),this}},{key:"observe",value:function(e,n){"function"===typeof n&&(this.options.selector=e,this.reqId=c.push(n),t.publishHandler("requestComponentObserver",{reqId:this.reqId,component:this.component,options:this.options},this.pageId))}},{key:"disconnect",value:function(){t.publishHandler("destroyComponentObserver",{reqId:this.reqId},this.pageId)}}]),e}();function h(t,e){return t._isVue||(e=t,t=null),new l(t||Object(r["b"])("createIntersectionObserver"),e)}}.call(this,n("0dd1"))},"0998":function(t,e,n){"use strict";var i=n("927d"),r=n.n(i);r.a},"09e5":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"requestComponentInfo",(function(){return o}));var i=n("62b5"),r=Object(i["a"])("requestComponentInfo");function o(e,n,i){t.publishHandler("requestComponentInfo",{reqId:r.push(i),reqs:n},e.$page.id)}}.call(this,n("0dd1"))},"0a32":function(t,e,n){"use strict";var i=n("e4f1"),r=n.n(i);r.a},"0dd1":function(t,e,n){"use strict";n.r(e),n.d(e,"on",(function(){return c})),n.d(e,"off",(function(){return u})),n.d(e,"once",(function(){return l})),n.d(e,"emit",(function(){return h})),n.d(e,"subscribe",(function(){return f})),n.d(e,"unsubscribe",(function(){return d})),n.d(e,"subscribeHandler",(function(){return p}));var i=n("8bbf"),r=n.n(i),o=n("27a7");n.d(e,"invokeCallbackHandler",(function(){return o["a"]}));var a=n("b865");n.d(e,"publishHandler",(function(){return a["b"]}));var s=new r.a,c=s.$on.bind(s),u=s.$off.bind(s),l=s.$once.bind(s),h=s.$emit.bind(s);function f(t,e){return c("view."+t,e)}function d(t,e){return u("view."+t,e)}function p(t,e,n){return h("view."+t,e,n)}},"0f55":function(t,e,n){"use strict";var i=n("2190"),r=n.n(i);r.a},"0f74":function(t,e,n){"use strict";function i(t,e){if(e){if(0===e.indexOf("/"))return e}else{if(e=t,0===e.indexOf("/"))return e;var n=getCurrentPages();t=n.length?n[n.length-1].$page.route:""}if(0===e.indexOf("./"))return i(t,e.substr(2));for(var r=e.split("/"),o=r.length,a=0;a<o&&".."===r[a];a++);r.splice(0,a),e=r.join("/");var s=t.length>0?t.split("/"):[];return s.splice(s.length-a-1,a+1),"/"+s.concat(r).join("/")}n.d(e,"a",(function(){return i}))},1082:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-image",t._g({},t.$listeners),[n("div",{ref:"content",style:t.modeStyle}),n("img",{attrs:{src:t.realImagePath}}),"widthFix"===t.mode?n("v-uni-resize-sensor",{ref:"sensor",on:{resize:t._resize}}):t._e()],1)},r=[];function o(t){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}var a={name:"Image",props:{src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1}},data:function(){return{originalWidth:0,originalHeight:0,availHeight:""}},computed:{ratio:function(){return this.originalWidth&&this.originalHeight?this.originalWidth/this.originalHeight:0},realImagePath:function(){return this.$getRealPath(this.src)},modeStyle:function(){var t="auto",e="",n="no-repeat";switch(this.mode){case"aspectFit":t="contain",e="center center";break;case"aspectFill":t="cover",e="center center";break;case"widthFix":t="100% 100%";break;case"top":e="center top";break;case"bottom":e="center bottom";break;case"center":e="center center";break;case"left":e="left center";break;case"right":e="right center";break;case"top left":e="left top";break;case"top right":e="right top";break;case"bottom left":e="left bottom";break;case"bottom right":e="right bottom";break;default:t="100% 100%",e="0% 0%";break}return"background-position:".concat(e,";background-size:").concat(t,";background-repeat:").concat(n,";")}},watch:{src:function(t,e){this._setContentImage(),this._loadImage()},mode:function(t,e){"widthFix"===e&&(this.$el.style.height=this.availHeight),"widthFix"===t&&this.ratio&&this._fixSize()}},mounted:function(){this.availHeight=this.$el.style.height||"",this._setContentImage(),this.realImagePath&&this._loadImage()},methods:{_resize:function(){"widthFix"===this.mode&&this._fixSize()},_fixSize:function(){var t=this._getWidth();if(t){var e=t/this.ratio;("undefined"===typeof navigator||o(navigator))&&"Google Inc."===navigator.vendor&&e>10&&(e=2*Math.round(e/2)),this.$el.style.height=e+"px"}},_setContentImage:function(){this.$refs.content.style.backgroundImage=this.src?'url("'.concat(this.realImagePath,'")'):"none"},_loadImage:function(){var t=this,e=new Image;e.onload=function(e){t.originalWidth=this.width,t.originalHeight=this.height,"widthFix"===t.mode&&t._fixSize(),t.$trigger("load",e,{width:this.width,height:this.height})},e.onerror=function(e){t.$trigger("error",e,{errMsg:"GET ".concat(t.src," 404 (Not Found)")})},e.src=this.realImagePath},_getWidth:function(){var t=window.getComputedStyle(this.$el),e=(parseFloat(t.borderLeftWidth,10)||0)+(parseFloat(t.borderRightWidth,10)||0),n=(parseFloat(t.paddingLeft,10)||0)+(parseFloat(t.paddingRight,10)||0);return this.$el.offsetWidth-e-n}}},s=a,c=(n("db18"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},1164:function(t,e,n){"use strict";(function(t){n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return s}));var i=n("23e5"),r=!1;function o(){return r}function a(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=[],r=o();if(!r)return n&&t.error("app is not ready"),[];var a=r.$children[0];if(a&&a.$children.length){var s=a.$children.find((function(t){return"TabBar"===t.$options.name}));a.$children.forEach((function(t){if(s!==t&&t.$children.length&&"Page"===t.$children[0].$options.name&&t.$children[0].$slots.page){var n=t.$children[0].$children.find((function(t){return"PageBody"===t.$options.name})).$children.find((function(t){return!!t.$page}));if(n){var o=!0;!e&&s&&n.$page&&n.$page.meta.isTabBar&&(r.$route.meta&&r.$route.meta.isTabBar?r.$route.path!==n.$page.path&&(o=!1):s.__path__!==n.$page.path&&(o=!1)),o&&i.push(n)}}}))}var c=i.length;if(c>1){var u=i[c-1];u.$page.path!==r.$route.path&&i.splice(c-1,1)}return i}function s(t,e){r=t,r.globalData=r.$options.globalData||{},Object(i["a"])(r,e)}}).call(this,n("3ad9")["default"])},"11fb":function(t,e,n){"use strict";n.r(e),n.d(e,"previewImage",(function(){return r}));var i=n("cb0f"),r={urls:{type:Array,required:!0,validator:function(t,e){var n;if(e.urls=t.map((function(t){if("string"===typeof t)return Object(i["a"])(t);n=!0})),n)return"url is not string"}},current:{type:[String,Number],validator:function(t,e){"number"===typeof t?e.current=t>0&&t<e.urls.length?t:0:"string"===typeof t&&t&&(e.current=Object(i["a"])(t))},default:0}}},1307:function(t,e,n){},1348:function(t,e,n){"use strict";(function(t){var i=n("8af1"),r=["navigate","redirect","switchTab","reLaunch","navigateBack"];e["a"]={name:"Navigator",mixins:[i["c"]],props:{hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:function(t){return~r.indexOf(t)}},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:600}},methods:{_onClick:function(e){if("navigateBack"===this.openType||this.url)switch(this.openType){case"navigate":uni.navigateTo({url:this.url});break;case"redirect":uni.redirectTo({url:this.url});break;case"switchTab":uni.switchTab({url:this.url});break;case"reLaunch":uni.reLaunch({url:this.url});break;case"navigateBack":uni.navigateBack({delta:this.delta});break;default:break}else t.error("<navigator/> should have url attribute when using navigateTo, redirectTo, reLaunch or switchTab")}}}}).call(this,n("3ad9")["default"])},"15bb":function(t,e,n){"use strict";(function(t){var i=n("f2b3");e["a"]={mounted:function(){var e=this;if("transparent"===this.type){for(var n=this.$el.querySelector(".uni-page-head-transparent").style,i=this.$el.querySelector(".uni-page-head__title"),r=this.$el.querySelectorAll(".uni-btn-icon"),o=[],a=this.textColor,s=0;s<r.length;s++)o.push(r[s].style);for(var c=this.$el.querySelectorAll(".uni-page-head-btn"),u=[],l=[],h=0;h<c.length;h++){var f=c[h];u.push(getComputedStyle(f).backgroundColor),l.push(f.style)}this._A=0,t.on("onPageScroll",(function(t){var r=t.scrollTop,s=Math.min(r/e.offset,1);1===s&&1===e._A||(s>.5&&e._A<=.5?o.forEach((function(t){t.color=a})):s<=.5&&e._A>.5&&o.forEach((function(t){t.color="#fff"})),e._A=s,i&&(i.style.opacity=s),n.backgroundColor="rgba(".concat(e._R,",").concat(e._G,",").concat(e._B,",").concat(s,")"),l.forEach((function(t,e){var n=u[e],i=n.match(/[\d+\.]+/g);i[3]=(1-s)*(4===i.length?i[3]:1),t.backgroundColor="rgba(".concat(i,")")})))}))}else if("float"===this.type){for(var d=this.$el.querySelectorAll(".uni-btn-icon"),p=[],g=0;g<d.length;g++)p.push(d[g].style);for(var v=this.$el.querySelectorAll(".uni-page-head-btn"),m=[],b=[],y=0;y<v.length;y++){var _=v[y];m.push(getComputedStyle(_).backgroundColor),b.push(_.style)}}},computed:{color:function(){return"transparent"===this.type?"#fff":this.textColor},offset:function(){return parseInt(this.coverage)},bgColor:function(){if("transparent"===this.type){var t=Object(i["i"])(this.backgroundColor),e=t.r,n=t.g,r=t.b;return this._R=e,this._G=n,this._B=r,"rgba(".concat(e,",").concat(n,",").concat(r,",0)")}return this.backgroundColor}}}}).call(this,n("501c"))},"167a":function(t,e,n){"use strict";var i=n("5d70"),r=n.n(i);r.a},"17fd":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.hoverClass&&"none"!==t.hoverClass?n("uni-navigator",t._g({class:[t.hovering?t.hoverClass:""],on:{touchstart:t._hoverTouchStart,touchend:t._hoverTouchEnd,touchcancel:t._hoverTouchCancel,click:t._onClick}},t.$listeners),[t._t("default")],2):n("uni-navigator",t._g({on:{click:t._onClick}},t.$listeners),[t._t("default")],2)},r=[],o=n("1348"),a=o["a"],s=(n("f7fd"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},"18fd":function(t,e,n){"use strict";n.d(e,"a",(function(){return f}));var i=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,r=/^<\/([-A-Za-z0-9_]+)[^>]*>/,o=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,a=d("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),s=d("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),c=d("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),u=d("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),l=d("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),h=d("script,style");function f(t,e){var n,f,d,p=[],g=t;p.last=function(){return this[this.length-1]};while(t){if(f=!0,p.last()&&h[p.last()])t=t.replace(new RegExp("([\\s\\S]*?)</"+p.last()+"[^>]*>"),(function(t,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),e.chars&&e.chars(n),""})),b("",p.last());else if(0==t.indexOf("\x3c!--")?(n=t.indexOf("--\x3e"),n>=0&&(e.comment&&e.comment(t.substring(4,n)),t=t.substring(n+3),f=!1)):0==t.indexOf("</")?(d=t.match(r),d&&(t=t.substring(d[0].length),d[0].replace(r,b),f=!1)):0==t.indexOf("<")&&(d=t.match(i),d&&(t=t.substring(d[0].length),d[0].replace(i,m),f=!1)),f){n=t.indexOf("<");var v=n<0?t:t.substring(0,n);t=n<0?"":t.substring(n),e.chars&&e.chars(v)}if(t==g)throw"Parse Error: "+t;g=t}function m(t,n,i,r){if(n=n.toLowerCase(),s[n])while(p.last()&&c[p.last()])b("",p.last());if(u[n]&&p.last()==n&&b("",n),r=a[n]||!!r,r||p.push(n),e.start){var h=[];i.replace(o,(function(t,e){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:l[e]?e:"";h.push({name:e,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),e.start&&e.start(n,h,r)}}function b(t,n){if(n){for(i=p.length-1;i>=0;i--)if(p[i]==n)break}else var i=0;if(i>=0){for(var r=p.length-1;r>=i;r--)e.end&&e.end(p[r]);p.length=i}}b()}function d(t){for(var e={},n=t.split(","),i=0;i<n.length;i++)e[n[i]]=!0;return e}},1934:function(t,e,n){"use strict";n.r(e),n.d(e,"setNavigationBarColor",(function(){return r})),n.d(e,"setNavigationBarTitle",(function(){return o}));var i=["#ffffff","#000000"],r={frontColor:{type:String,required:!0,validator:function(t,e){if(-1===i.indexOf(t))return'invalid frontColor "'.concat(t,'"')}},backgroundColor:{type:String,required:!0},animation:{type:Object,default:function(){return{duration:0,timingFunc:"linear"}},validator:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;e.animation={duration:t.duration||0,timingFunc:t.timingFunc||"linear"}}}},o={title:{type:String,required:!0}}},1955:function(t,e,n){"use strict";n.r(e);var i=n("ba15"),r=n("8aec"),o=n("5363"),a=n("72b3"),s=n("f2b3");function c(t){var e=20,n=0,i=0;t.addEventListener("touchstart",(function(t){var e=t.changedTouches[0];n=e.clientX,i=e.clientY})),t.addEventListener("touchend",(function(t){var r=t.changedTouches[0];if(Math.abs(r.clientX-n)<e&&Math.abs(r.clientY-i)<e){var o=new CustomEvent("click",{bubbles:!0,cancelable:!0,target:t.target,currentTarget:t.currentTarget});["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((function(t){o[t]=r[t]})),t.target.dispatchEvent(o)}}))}var u,l,h={name:"PickerViewColumn",mixins:[i["a"],r["a"]],data:function(){return{scope:"picker-view-column-".concat(Date.now()),inited:!1,indicatorStyle:"",indicatorClass:"",indicatorHeight:34,maskStyle:"",maskClass:"",current:this.$parent.getItemValue(this),length:0}},computed:{height:function(){return this.$parent.height},maskSize:function(){return(this.height-this.indicatorHeight)/2}},watch:{indicatorHeight:function(t){this._setItemHeight(t),this.inited&&this.update()},current:function(t){this.$parent.setItemValue(this,t)},length:function(t){this.inited&&this.update(t)}},created:function(){var t=this.$parent;this.indicatorStyle=t.indicatorStyle,this.indicatorClass=t.indicatorClass,this.maskStyle=t.maskStyle,this.maskClass=t.maskClass,this.deltaY=0},mounted:function(){var t=this;this.touchtrack(this.$refs.main,"_handleTrack",!0),this.setCurrent(this.current),this.$nextTick((function(){t.init(),t.update()})),c(this.$el)},methods:{_setItemHeight:function(t){var e=document.createElement("style");e.innerText=".uni-picker-view-content.".concat(this.scope,">*{height: ").concat(t,"px;overflow: hidden;}"),document.head.appendChild(e)},_handleTrack:function(t){if(this._scroller)switch(t.detail.state){case"start":this._handleTouchStart(t),Object(s["e"])({disable:!0});break;case"move":this._handleTouchMove(t);break;case"end":case"cancel":this._handleTouchEnd(t),Object(s["e"])({disable:!1})}},_handleTap:function(t){var e=t.clientY;if(!this._scroller.isScrolling()){var n=this.$el.getBoundingClientRect(),i=e-n.top-this.height/2,r=this.indicatorHeight/2;if(!(Math.abs(i)<=r)){var o=Math.ceil((Math.abs(i)-r)/this.indicatorHeight),a=i<0?-o:o,s=Math.min(this.current+a,this.length-1);this.current=s=Math.max(s,0),this._scroller.scrollTo(s*this.indicatorHeight)}}},_handleWheel:function(t){var e=this.deltaY+t.deltaY;if(Math.abs(e)>10){this.deltaY=0;var n=Math.min(this.current+(e<0?-1:1),this.length-1);this.current=n=Math.max(n,0),this._scroller.scrollTo(n*this.indicatorHeight)}else this.deltaY=e;t.preventDefault()},setCurrent:function(t){t!==this.current&&(this.current=t,this.inited&&this.update())},init:function(){var t=this;this.initScroller(this.$refs.content,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:this.indicatorHeight,friction:new o["a"](1e-4),spring:new a["a"](2,90,20),onSnap:function(e){isNaN(e)||e===t.current||(t.current=e)}}),this.inited=!0},update:function(){var t=this;this.$nextTick((function(){var e=Math.min(t.current,t.length-1);e=Math.max(e,0),t._scroller.update(e*t.indicatorHeight,void 0,t.indicatorHeight)}))},_resize:function(t){var e=t.height;this.indicatorHeight=e}},render:function(t){return this.length=this.$slots.default&&this.$slots.default.length||0,t("uni-picker-view-column",{on:{on:this.$listeners}},[t("div",{ref:"main",staticClass:"uni-picker-view-group",on:{wheel:this._handleWheel,click:this._handleTap}},[t("div",{ref:"mask",staticClass:"uni-picker-view-mask",class:this.maskClass,style:"background-size: 100% ".concat(this.maskSize,"px;").concat(this.maskStyle)}),t("div",{ref:"indicator",staticClass:"uni-picker-view-indicator",class:this.indicatorClass,style:this.indicatorStyle},[t("v-uni-resize-sensor",{attrs:{initial:!0},on:{resize:this._resize}})]),t("div",{ref:"content",staticClass:"uni-picker-view-content",class:this.scope,style:"padding: ".concat(this.maskSize,"px 0;")},[this.$slots.default])])])}},f=h,d=(n("edfa"),n("2877")),p=Object(d["a"])(f,u,l,!1,null,null,null);e["default"]=p.exports},"19c4":function(t,e,n){var i={"./base/base64.js":"6481","./base/can-i-use.js":"957a","./base/event-bus.js":"b0ef","./base/interceptor.js":"a954","./base/upx2px.js":"2289","./context/canvas.js":"82b9","./context/context.js":"3bfb","./device/make-phone-call.js":"f102","./device/set-clipboard-data.js":"b501","./file/open-document.js":"2604","./location/choose-location.js":"e5bb","./location/get-location.js":"19d9","./location/open-location.js":"70bb","./media/choose-image.js":"f1b2","./media/choose-video.js":"ed9f","./media/get-image-info.js":"b866","./media/preview-image.js":"11fb","./network/download-file.js":"439a","./network/request.js":"a201","./network/socket.js":"abb2","./network/upload-file.js":"9a3e","./plugin/get-provider.js":"4e7c","./route/route.js":"332a","./storage/storage.js":"ec33","./ui/load-font-face.js":"5ff9","./ui/navigation-bar.js":"1934","./ui/page-scroll-to.js":"232e","./ui/popup.js":"2246","./ui/tab-bar.js":"5621"};function r(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}r.keys=function(){return Object.keys(i)},r.resolve=o,t.exports=r,r.id="19c4"},"19d9":function(t,e,n){"use strict";n.r(e),n.d(e,"getLocation",(function(){return r}));var i={WGS84:"WGS84",GCJ02:"GCJ02"},r={type:{type:String,validator:function(t,e){t=(t||"").toUpperCase(),e.type=Object.values(i).indexOf(t)<0?i.WGS84:t},default:i.WGS84},altitude:{altitude:Boolean,default:!1}}},"1a12":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"request",(function(){return l}));var i=n("f2b3");function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var c=function(){function t(e){r(this,t),s(this,"_xhr",void 0),this._xhr=e}return a(t,[{key:"abort",value:function(){this._xhr&&(this._xhr.abort(),delete this._xhr)}}]),t}();function u(t){var e={},n=t.split("\n");return n.forEach((function(t){var n=t.match(/(\S+\s*):\s*(.*)/);if(n&&3===n.length){var i=n[1],r=n[2];e[i]=r}})),e}function l(e,n){var r,o=e.url,a=e.data,s=e.header,l=e.method,h=e.dataType,f=e.responseType,d=e.withCredentials,p=t,g=p.invokeCallbackHandler,v=null,m=__uniConfig.networkTimeout&&__uniConfig.networkTimeout.request||6e4;for(var b in s)if(Object(i["h"])(s,b)&&"content-type"===b.toLowerCase()){r=s[b],r=0===r.indexOf("application/json")?"json":0===r.indexOf("application/x-www-form-urlencoded")?"urlencoded":"string";break}if("GET"!==l)if("string"===typeof a||a instanceof ArrayBuffer)v=a;else if("json"===r)try{v=JSON.stringify(a)}catch(x){v=a.toString()}else if("urlencoded"===r){var y=[];for(var _ in a)Object(i["h"])(a,_)&&y.push(encodeURIComponent(_)+"="+encodeURIComponent(a[_]));v=y.join("&")}else v=a.toString();var w=new XMLHttpRequest,S=new c(w);for(var k in w.open(l,o),s)Object(i["h"])(s,k)&&w.setRequestHeader(k,s[k]);var T=setTimeout((function(){w.onload=w.onabort=w.onerror=null,S.abort(),g(n,{errMsg:"request:fail timeout"})}),m);return w.responseType=f,w.onload=function(){clearTimeout(T);var t=w.status,e="text"===f?w.responseText:w.response;if("text"===f&&"json"===h)try{e=JSON.parse(e)}catch(x){}g(n,{data:e,statusCode:t,header:u(w.getAllResponseHeaders()),errMsg:"request:ok"})},w.onabort=function(){clearTimeout(T),g(n,{errMsg:"request:fail abort"})},w.onerror=function(){clearTimeout(T),g(n,{errMsg:"request:fail"})},w.withCredentials=d,w.send(v),S}}.call(this,n("0dd1"))},"1a8c":function(t,e,n){"use strict";n.r(e),n.d(e,"redirectTo",(function(){return o})),n.d(e,"navigateTo",(function(){return a})),n.d(e,"navigateBack",(function(){return s})),n.d(e,"reLaunch",(function(){return c})),n.d(e,"switchTab",(function(){return u}));var i=n("85b6");function r(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.url,r=e.delta,o=e.animationType,a=e.animationDuration,s=e.from,c=void 0===s?"navigateBack":s,u=e.detail,l=getApp().$router;switch(t){case"redirectTo":l.replace({type:t,path:n});break;case"navigateTo":l.push({type:t,path:n,animationType:o,animationDuration:a});break;case"navigateBack":var h=!0,f=getCurrentPages();if(f.length){var d=f[f.length-1];Object(i["a"])(d.$options,"onBackPress")&&!0===d.__call_hook("onBackPress",{from:c})&&(h=!1)}h&&(r>1&&(l._$delta=r),l.go(-r,{animationType:o,animationDuration:a}));break;case"reLaunch":l.replace({type:t,path:n});break;case"switchTab":l.replace({type:t,path:n,params:{detail:u}});break}return{errMsg:t+":ok"}}function o(t){return r("redirectTo",t)}function a(t){return r("navigateTo",t)}function s(t){return r("navigateBack",t)}function c(t){return r("reLaunch",t)}function u(t){return r("switchTab",t)}},"1b6f":function(t,e,n){"use strict";(function(t){var i=n("f2b3");e["a"]={mounted:function(){var t=this;this._toggleListeners("subscribe",this.id),this.$watch("id",(function(e,n){t._toggleListeners("unsubscribe",n,!0),t._toggleListeners("subscribe",e,!0)}))},beforeDestroy:function(){this._toggleListeners("unsubscribe",this.id),this._contextId&&this._toggleListeners("unsubscribe",this._contextId)},methods:{_toggleListeners:function(e,n,r){r&&!n||Object(i["j"])(this._handleSubscribe)&&t[e](this.$page.id+"-"+this.$options.name.replace(/VUni([A-Z])/,"$1").toLowerCase()+"-"+n,this._handleSubscribe)},_getContextInfo:function(){var t="context-".concat(this._uid);return this._contextId||(this._toggleListeners("subscribe",t),this._contextId=t),{name:this.$options.name.replace(/VUni([A-Z])/,"$1").toLowerCase(),id:t,page:this.$page.id}}}}}).call(this,n("501c"))},"1c64":function(t,e,n){"use strict";var i=n("60ee"),r=n.n(i);r.a},"1ca3":function(t,e,n){"use strict";n.r(e),n.d(e,"base64ToArrayBuffer",(function(){return r})),n.d(e,"arrayBufferToBase64",(function(){return o}));var i=n("8390");function r(t){return Object(i["decode"])(t)}function o(t){return Object(i["encode"])(t)}},"1e4d":function(t,e,n){"use strict";function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function o(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t}n.r(e),n.d(e,"createAnimation",(function(){return h}));var a={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"},s=function(){function t(e){i(this,t),this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=Object.assign({},a,e)}return o(t,[{key:"_getOption",value:function(t){var e={transition:Object.assign({},this.option,t)};return e.transformOrigin=e.transition.transformOrigin,delete e.transition.transformOrigin,e}},{key:"_pushAnimates",value:function(t,e){this.currentStepAnimates.push({type:t,args:e})}},{key:"_converType",value:function(t){return t.replace(/[A-Z]/g,(function(t){return"-".concat(t.toLowerCase())}))}},{key:"_getValue",value:function(t){return"number"===typeof t?"".concat(t,"px"):t}},{key:"export",value:function(){var t=this.actions;return this.actions=[],{actions:t}}},{key:"step",value:function(t){var e=this;return this.currentStepAnimates.forEach((function(t){"style"!==t.type?e.currentTransform[t.type]=t:e.currentTransform["".concat(t.type,".").concat(t.args[0])]=t})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(t)}),this.currentStepAnimates=[],this}}]),t}(),c=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"],u=["opacity","backgroundColor"],l=["width","height","left","right","top","bottom"];function h(t){return new s(t)}c.concat(u,l).forEach((function(t){s.prototype[t]=function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];return u.concat(l).includes(t)?this._pushAnimates("style",[this._converType(t),l.includes(t)?this._getValue(n[0]):n[0]]):this._pushAnimates(t,n),this}}))},"1efd":function(t,e,n){"use strict";n.r(e);var i=n("8bbf"),r=n.n(i),o=n("cb0f"),a=n("d4b6"),s={methods:{$getRealPath:function(t){return t?Object(o["a"])(t):t},$trigger:function(t,e,n){this.$emit(t,a["b"].call(this,t,e,n,this.$el,this.$el))}}};function c(t){return f(t)||h(t)||l(t)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){if(t){if("string"===typeof t)return d(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(t,e):void 0}}function h(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function f(t){if(Array.isArray(t))return d(t)}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function p(t){if(/\d+[ur]px$/i.test(t))t.replace(/\d+[ur]px$/i,(function(t){return"".concat(uni.upx2px(parseFloat(t)),"px")}));else if(/^-?[\d\.]+$/.test(t))return"".concat(t,"px");return t||""}function g(t){return t.replace(/[A-Z]/g,(function(t){return"-".concat(t.toLowerCase())})).replace("webkit","-webkit")}function v(t){var e=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],i=["opacity","background-color"],r=["width","height","left","right","top","bottom"],o=t.animates,a=t.option,s=a.transition,u={},l=[];return o.forEach((function(t){var o=t.type,a=c(t.args);if(e.concat(n).includes(o))o.startsWith("rotate")||o.startsWith("skew")?a=a.map((function(t){return parseFloat(t)+"deg"})):o.startsWith("translate")&&(a=a.map(p)),n.indexOf(o)>=0&&(a.length=1),l.push("".concat(o,"(").concat(a.join(","),")"));else if(i.concat(r).includes(a[0])){o=a[0];var s=a[1];u[o]=r.includes(o)?p(s):s}})),u.transform=u.webkitTransform=l.join(" "),u.transition=u.webkitTransition=Object.keys(u).map((function(t){return"".concat(g(t)," ").concat(s.duration,"ms ").concat(s.timingFunction," ").concat(s.delay,"ms")})).join(","),u.transformOrigin=u.webkitTransformOrigin=a.transformOrigin,u}function m(t){var e=t.animation;if(e&&e.actions&&e.actions.length){var n=0,i=e.actions,r=e.actions.length;o()}function o(){var e=i[n],a=e.option.transition,s=v(e);Object.keys(s).forEach((function(e){t.$el.style[e]=s[e]})),n+=1,n<r&&setTimeout(o,a.duration+a.delay)}}var b={props:["animation"],watch:{animation:function(){m(this)}},mounted:function(){m(this)}},y=[n("5408"),n("93a5")];y.forEach((function(t,e){t.keys().forEach((function(e){var n=t(e),i=n.default||n;i.mixins=i.mixins?[].concat(s,i.mixins):[s],i.mixins.push(b),i.name="VUni"+i.name,i.isReserved=!0,r.a.component(i.name,i)}))}))},"1ff3":function(t,e,n){"use strict";n.r(e),n.d(e,"uploadFile",(function(){return l}));var i=n("a118"),r=n("db70");function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function s(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t}var c=function(){function t(e,n){o(this,t),this.id=e,this._callbackId=n,this._callbacks=[]}return s(t,[{key:"abort",value:function(){Object(r["c"])("operateRequestTask",{uploadTaskId:this.id,operationType:"abort"})}},{key:"onProgressUpdate",value:function(t){"function"===typeof t&&this._callbacks.push(t)}},{key:"onHeadersReceived",value:function(){}},{key:"offProgressUpdate",value:function(t){var e=this._callbacks.indexOf(t);e>=0&&this._callbacks.splice(e,1)}},{key:"offHeadersReceived",value:function(){}}]),t}(),u=Object.create(null);function l(t,e){var n=Object(r["c"])("createUploadTask",t),i=n.uploadTaskId,o=new c(i,e);return u[i]=o,o}Object(r["d"])("onUploadTaskStateChange",(function(t){var e=t.uploadTaskId,n=t.state,r=t.data,o=t.statusCode,a=t.progress,s=t.totalBytesSent,c=t.totalBytesExpectedToSend,l=t.errMsg,h=u[e],f=h._callbackId;switch(n){case"progressUpdate":h._callbacks.forEach((function(t){t({progress:a,totalBytesSent:s,totalBytesExpectedToSend:c})}));break;case"success":Object(i["a"])(f,{data:r,statusCode:o,errMsg:"request:ok"});case"fail":Object(i["a"])(f,{errMsg:"request:fail "+l});default:setTimeout((function(){delete u[e]}),100);break}}))},2190:function(t,e,n){},2246:function(t,e,n){"use strict";n.r(e),n.d(e,"showModal",(function(){return r})),n.d(e,"showToast",(function(){return o})),n.d(e,"showLoading",(function(){return a})),n.d(e,"showActionSheet",(function(){return s}));var i=n("cb0f"),r={title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"取消"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"确定"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean,default:!0}},o={title:{type:String,default:""},icon:{default:"success",validator:function(t,e){-1===["success","loading","none"].indexOf(t)&&(e.icon="success")}},image:{type:String,default:"",validator:function(t,e){t&&(e.image=Object(i["a"])(t))}},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean,default:!0}},a={title:{type:String,default:""},icon:{type:String,default:"loading"},duration:{type:Number,default:1e8},mask:{type:Boolean,default:!1},visible:{type:Boolean,default:!0}},s={itemList:{type:Array,required:!0,validator:function(t,e){if(!t.length)return"parameter.itemList should have at least 1 item"}},itemColor:{type:String,default:"#000000"},visible:{type:Boolean,default:!0},popover:{type:Object}}},2289:function(t,e,n){"use strict";n.r(e),n.d(e,"upx2px",(function(){return i}));var i=[{name:"upx",type:[Number,String],required:!0}]},"232e":function(t,e,n){"use strict";n.r(e),n.d(e,"pageScrollTo",(function(){return i}));var i={scrollTop:{type:Number,required:!0},duration:{type:Number,default:300,validator:function(t,e){e.duration=Math.max(0,t)}}}},2399:function(t,e,n){},"23e5":function(t,e,n){"use strict";(function(t){n.d(e,"b",(function(){return c})),n.d(e,"a",(function(){return g}));var i=n("a741");function r(t){-1===this.keepAliveInclude.indexOf(t)&&this.keepAliveInclude.push(t)}var o=[];function a(t){if("number"===typeof t)o=this.keepAliveInclude.splice(-(t-1)).map((function(t){return parseInt(t.split("-").pop())}));else{var e=this.keepAliveInclude.indexOf(t);-1!==e&&this.keepAliveInclude.splice(e,1)}}var s=Object.create(null);function c(t){return s[t]}function u(t){s[t]={x:window.pageXOffset,y:window.pageYOffset}}function l(t,e,n){e&&n&&e.meta.isTabBar&&n.meta.isTabBar&&u(n.params.__id__);for(var r=getCurrentPages(),o=r.length-1;o>=0;o--){var s=r[o],c=s.$page.meta;c.isTabBar||(a.call(this,c.name+"-"+s.$page.id),Object(i["b"])(s,"onUnload"))}}function h(t){__uniConfig.reLaunch=(__uniConfig.reLaunch||1)+1;for(var e=getCurrentPages(!0),n=e.length-1;n>=0;n--)Object(i["b"])(e[n],"onUnload"),e[n].$destroy();this.keepAliveInclude=[],s=Object.create(null)}var f=[];function d(t,e,n,i){f=getCurrentPages(!0);var o=e.params.__id__,s=t.params.__id__,c=t.meta.name+"-"+s;if(s===o&&"reLaunch"!==t.type)t.fullPath!==e.fullPath?(a.call(this,c),n()):n(!1);else if(t.meta.id&&t.meta.id!==s)n({path:t.path,replace:!0});else{var u=e.meta.name+"-"+o;switch(t.type){case"navigateTo":break;case"redirectTo":a.call(this,u),e.meta&&e.meta.isQuit&&(t.meta.isQuit=!0,t.meta.isEntry=!!e.meta.isEntry);break;case"switchTab":l.call(this,i,t,e);break;case"reLaunch":h.call(this,c),t.meta.isQuit=!0;break;default:o&&o>s&&(a.call(this,u),this.$router._$delta>1&&a.call(this,this.$router._$delta));break}if("reLaunch"!==t.type&&e.meta.id&&r.call(this,u),r.call(this,c),t.meta&&t.meta.name){document.body.className="uni-body "+t.meta.name;var d="nvue-dir-"+__uniConfig.nvue["flex-direction"];t.meta.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(d,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(d))}n()}}function p(e,n){var r=n.params.__id__,a=e.params.__id__,s=f.find((function(t){return t.$page.id===r}));switch(e.type){case"navigateTo":s&&Object(i["b"])(s,"onHide");break;case"redirectTo":s&&Object(i["b"])(s,"onUnload");break;case"switchTab":n.meta.isTabBar&&s&&Object(i["b"])(s,"onHide");break;case"reLaunch":break;default:r&&r>a&&(s&&Object(i["b"])(s,"onUnload"),this.$router._$delta>1&&o.reverse().forEach((function(t){var e=f.find((function(e){return e.$page.id===t}));e&&Object(i["b"])(e,"onUnload")})));break}if(delete this.$router._$delta,o.length=0,"reLaunch"!==e.type){var c=getCurrentPages(!0).find((function(t){return t.$page.id===a}));c&&(setTimeout((function(){t.emit("onNavigationBarChange",c.$parent.$parent.navigationBar),Object(i["b"])(c,"onShow")}),0),document.title=c.$parent.$parent.navigationBar.titleText)}}function g(t,e){t.$router.beforeEach((function(n,i,r){d.call(t,n,i,r,e)})),t.$router.afterEach((function(e,n){p.call(t,e,n)}))}}).call(this,n("0dd1"))},"24d9":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return a}));var i=n("f2b3");function r(t){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function o(t){return t.mp=Object.assign({"@warning":"mp is deprecated"},t),t._processed=!0,t}function a(t,e){return Object(i["k"])(e)&&(Object(i["h"])(e,"backgroundColor")&&(t.backgroundColor=e.backgroundColor),Object(i["h"])(e,"buttons")&&(t.buttons=e.buttons),Object(i["h"])(e,"titleColor")&&(t.textColor=e.titleColor),Object(i["h"])(e,"titleText")&&(t.titleText=e.titleText),Object(i["h"])(e,"titleSize")&&(t.titleSize=e.titleSize),Object(i["h"])(e,"type")&&(t.type=e.type),Object(i["h"])(e,"searchInput")&&"object"===r(e.searchInput)&&(t.searchInput=Object.assign({autoFocus:!1,align:"center",color:"#000000",backgroundColor:"rgba(255,255,255,0.5)",borderRadius:"0px",placeholder:"",placeholderColor:"#CCCCCC",disabled:!1},e.searchInput))),t}},"250d":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-input",t._g({on:{change:function(t){t.stopPropagation()}}},t.$listeners),[n("div",{ref:"wrapper",staticClass:"uni-input-wrapper"},[n("div",{directives:[{name:"show",rawName:"v-show",value:!(t.composing||t.valueSync.length),expression:"!(composing || valueSync.length)"}],ref:"placeholder",staticClass:"uni-input-placeholder",class:t.placeholderClass,style:t.placeholderStyle,domProps:{textContent:t._s(t.placeholder)}}),"checkbox"===t.inputType?n("input",{directives:[{name:"model",rawName:"v-model",value:t.valueSync,expression:"valueSync"}],ref:"input",staticClass:"uni-input-input",attrs:{disabled:t.disabled,maxlength:t.maxlength,step:t.step,autocomplete:"off",type:"checkbox"},domProps:{checked:Array.isArray(t.valueSync)?t._i(t.valueSync,null)>-1:t.valueSync},on:{focus:t._onFocus,blur:t._onBlur,input:function(e){return e.stopPropagation(),t._onInput(e)},compositionstart:t._onComposition,compositionend:t._onComposition,keyup:function(e){return e.stopPropagation(),t._onKeyup(e)},change:function(e){var n=t.valueSync,i=e.target,r=!!i.checked;if(Array.isArray(n)){var o=null,a=t._i(n,o);i.checked?a<0&&(t.valueSync=n.concat([o])):a>-1&&(t.valueSync=n.slice(0,a).concat(n.slice(a+1)))}else t.valueSync=r}}}):"radio"===t.inputType?n("input",{directives:[{name:"model",rawName:"v-model",value:t.valueSync,expression:"valueSync"}],ref:"input",staticClass:"uni-input-input",attrs:{disabled:t.disabled,maxlength:t.maxlength,step:t.step,autocomplete:"off",type:"radio"},domProps:{checked:t._q(t.valueSync,null)},on:{focus:t._onFocus,blur:t._onBlur,input:function(e){return e.stopPropagation(),t._onInput(e)},compositionstart:t._onComposition,compositionend:t._onComposition,keyup:function(e){return e.stopPropagation(),t._onKeyup(e)},change:function(e){t.valueSync=null}}}):n("input",{directives:[{name:"model",rawName:"v-model",value:t.valueSync,expression:"valueSync"}],ref:"input",staticClass:"uni-input-input",attrs:{disabled:t.disabled,maxlength:t.maxlength,step:t.step,autocomplete:"off",type:t.inputType},domProps:{value:t.valueSync},on:{focus:t._onFocus,blur:t._onBlur,input:[function(e){e.target.composing||(t.valueSync=e.target.value)},function(e){return e.stopPropagation(),t._onInput(e)}],compositionstart:t._onComposition,compositionend:t._onComposition,keyup:function(e){return e.stopPropagation(),t._onKeyup(e)}}})])])},r=[],o=n("8af1"),a=["text","number","idcard","digit","password"],s=["number","digit"],c={name:"Input",mixins:[o["a"]],props:{name:{type:String,default:""},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:"input-placeholder"},disabled:{type:[Boolean,String],default:!1},maxlength:{type:[Number,String],default:140},focus:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"done"}},data:function(){return{composing:!1,wrapperHeight:0,cachedValue:""}},computed:{inputType:function(){var t="";switch(this.type){case"text":"search"===this.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~a.indexOf(this.type)?this.type:"text";break}return this.password?"password":t},step:function(){return~s.indexOf(this.type)?"0.000000000000000001":""}},watch:{focus:function(t){t&&this._focusInput()},maxlength:function(t){var e=this.valueSync.slice(0,parseInt(t,10));e!==this.valueSync&&(this.valueSync=e)}},created:function(){this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this})},mounted:function(){if("search"===this.confirmType){var t=document.createElement("form");t.action="",t.onsubmit=function(){return!1},t.className="uni-input-form",t.appendChild(this.$refs.input),this.$refs.wrapper.appendChild(t)}var e=this;while(e){var n=e.$options._scopeId;n&&this.$refs.placeholder.setAttribute(n,""),e=e.$parent}this.initKeyboard(this.$refs.input),this.focus&&this._focusInput()},beforeDestroy:function(){this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})},methods:{_onKeyup:function(t){13===t.keyCode&&this.$trigger("confirm",t,{value:t.target.value})},_onInput:function(t){if(!this.composing){if(~s.indexOf(this.type)){if(this.$refs.input.validity&&!this.$refs.input.validity.valid)return t.target.value=this.cachedValue,void(this.valueSync=t.target.value);this.cachedValue=this.valueSync}if("number"===this.inputType){var e=parseInt(this.maxlength,10);if(e>0&&t.target.value.length>e)return t.target.value=t.target.value.slice(0,e),void(this.valueSync=t.target.value)}this.$triggerInput(t,{value:this.valueSync})}},_onFocus:function(t){this.$trigger("focus",t,{value:t.target.value})},_onBlur:function(t){this.$trigger("blur",t,{value:t.target.value})},_focusInput:function(){var t=this;setTimeout((function(){t.$refs.input.focus()}),350)},_blurInput:function(){var t=this;setTimeout((function(){t.$refs.input.blur()}),350)},_onComposition:function(t){"compositionstart"===t.type?this.composing=!0:this.composing=!1},_resetFormData:function(){this.valueSync=""},_getFormData:function(){return this.name?{value:this.valueSync,key:this.name}:{}}}},u=c,l=(n("0f55"),n("2877")),h=Object(l["a"])(u,i,r,!1,null,null,null);e["default"]=h.exports},"25ce":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-checkbox-group",t._g({},t.$listeners),[t._t("default")],2)},r=[],o=n("8af1"),a={name:"CheckboxGroup",mixins:[o["b"],o["f"]],props:{name:{type:String,default:""}},data:function(){return{checkboxList:[]}},listeners:{"@checkbox-change":"_changeHandler","@checkbox-group-update":"_checkboxGroupUpdateHandler"},created:function(){this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this})},beforeDestroy:function(){this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})},methods:{_changeHandler:function(t){var e=[];this.checkboxList.forEach((function(t){t.checkboxChecked&&e.push(t.value)})),this.$trigger("change",t,{value:e})},_checkboxGroupUpdateHandler:function(t){if("add"===t.type)this.checkboxList.push(t.vm);else{var e=this.checkboxList.indexOf(t.vm);this.checkboxList.splice(e,1)}},_getFormData:function(){var t={};if(""!==this.name){var e=[];this.checkboxList.forEach((function(t){t.checkboxChecked&&e.push(t.value)})),t.value=e,t.key=this.name}return t}}},s=a,c=(n("0998"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},2604:function(t,e,n){"use strict";n.r(e),n.d(e,"openDocument",(function(){return i}));var i={filePath:{type:String,required:!0},fileType:{type:String}}},2608:function(t,e,n){"use strict";(function(t){function i(e){return function(){try{return e.apply(e,arguments)}catch(n){t.error(n)}}}function r(e){return function(){try{return e.apply(e,arguments)}catch(n){t.error(n)}}}n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return r}))}).call(this,n("3ad9")["default"])},2765:function(t,e,n){"use strict";var i=n("3590"),r=n.n(i);r.a},"27a7":function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return m})),n.d(e,"c",(function(){return b})),n.d(e,"b",(function(){return _}));var i=n("f2b3"),r=n("2608"),o=n("ed1a"),a=n("cc76"),s=n("de29");function c(t,e,n){var i="".concat(e,":fail ").concat(t);if(-1===n)throw new Error(i);return"number"===typeof n&&m(n,{errMsg:i}),!1}var u=[{name:"callback",type:Function,required:!0}],l=["beforeValidate","beforeAll","beforeSuccess"];function h(t,e,n){var r=a["a"][t];if(!r&&Object(o["a"])(t)&&(r=u),r){if(Array.isArray(r)&&Array.isArray(e)){var h=Object.create(null),f=Object.create(null),d=e.length;r.forEach((function(t,n){h[t.name]=t,d>n&&(f[t.name]=e[n])})),r=h,e=f}if(Object(i["j"])(r.beforeValidate)){var p=r.beforeValidate(e);if(p)return c(p,t,n)}for(var g=Object.keys(r),v=0;v<g.length;v++)if(-1===l.indexOf(g[v])){var m=Object(s["a"])(g[v],r,e);if(m)return c(m,t,n)}}return!0}var f=1,d={};function p(t,e){var n=f++,i="api."+t+"."+n,r=function(t,n){e(t,n)};return d[n]={name:i,keepAlive:!0,callback:r},n}function g(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!Object(i["k"])(e))return{params:e};e=Object.assign({},e);var o={};for(var a in e){var s=e[a];Object(i["j"])(s)&&(o[a]=Object(r["a"])(s),delete e[a])}var c=o.success,u=o.fail,l=o.cancel,h=o.complete,p=Object(i["j"])(c),g=Object(i["j"])(u),v=Object(i["j"])(l),m=Object(i["j"])(h);if(!p&&!g&&!v&&!m)return{params:e};var b={};for(var y in n){var _=n[y];Object(i["j"])(_)&&(b[y]=Object(r["b"])(_))}var w=b.beforeSuccess,S=b.afterSuccess,k=b.beforeFail,T=b.afterFail,x=b.beforeCancel,C=b.afterCancel,O=b.beforeAll,E=b.afterAll,M=f++,j="api."+t+"."+M,A=function(e){if(e.errMsg=e.errMsg||t+":ok",-1!==e.errMsg.indexOf(":ok"))e.errMsg=t+":ok";else if(-1!==e.errMsg.indexOf(":cancel"))e.errMsg=t+":cancel";else if(-1!==e.errMsg.indexOf(":fail")){var n="",r=e.errMsg.indexOf(" ");r>-1&&(n=e.errMsg.substr(r)),e.errMsg=t+":fail"+n}Object(i["j"])(O)&&O(e);var o=e.errMsg;0===o.indexOf(t+":ok")?(Object(i["j"])(w)&&w(e),p&&c(e),Object(i["j"])(S)&&S(e)):0===o.indexOf(t+":cancel")?(e.errMsg=e.errMsg.replace(t+":cancel",t+":fail cancel"),g&&u(e),Object(i["j"])(x)&&x(e),v&&l(e),Object(i["j"])(C)&&C(e)):0===o.indexOf(t+":fail")&&(Object(i["j"])(k)&&k(e),g&&u(e),Object(i["j"])(T)&&T(e)),m&&h(e),Object(i["j"])(E)&&E(e)};return d[M]={name:j,callback:A},{params:e,callbackId:M}}function v(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=g(t,e,n),o=r.params,a=r.callbackId;return Object(i["k"])(o)&&!h(t,o,a)?{params:o,callbackId:!1}:{params:o,callbackId:a}}function m(t,e,n){if("number"===typeof t){var i=d[t];if(i)return i.keepAlive||delete d[t],i.callback(e,n)}return e}function b(e){return function(n){t.error("API `"+e+"` is not yet implemented")}}function y(t,e){var n=a["a"][t];n&&(Object(i["j"])(n.beforeAll)&&(e.beforeAll=n.beforeAll),Object(i["j"])(n.beforeSuccess)&&(e.beforeSuccess=n.beforeSuccess))}function _(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Object(i["j"])(e)?(y(t,n),function(){for(var r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];if(Object(o["b"])(t)){if(h(t,a,-1))return e.apply(null,a)}else if(Object(o["a"])(t)){if(h(t,a,-1))return e(p(t,a[0]))}else{var c={};a.length&&(c=a[0]);var u,l=v(t,c,n),f=l.params,d=l.callbackId;if(!1!==d)return u=Object(i["j"])(f)?e(d):e(f,d),u&&!Object(o["c"])(t)&&(u=m(d,u),Object(i["k"])(u)&&(u.errMsg=u.errMsg||t+":ok")),u}}):e}}).call(this,n("3ad9")["default"])},"27ab":function(t,e,n){"use strict";n.r(e);var i=n("f2b3");function r(t){return c(t)||s(t)||a(t)||o()}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function a(t,e){if(t){if("string"===typeof t)return u(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?u(t,e):void 0}}function s(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function c(t){if(Array.isArray(t))return u(t)}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var l,h,f={name:"PickerView",props:{value:{type:Array,default:function(){return[]},validator:function(t){return Array.isArray(t)&&t.filter((function(t){return"number"===typeof t})).length===t.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},data:function(){return{valueSync:r(this.value),height:34,items:[],changeSource:""}},watch:{value:function(t,e){var n=this;(t===e||t.length!==e.length||t.findIndex((function(t,n){return t!==e[n]}))>=0)&&(this.valueSync.length=t.length,t.forEach((function(t,e){t!==n.valueSync[e]&&n.$set(n.valueSync,e,t)})))},valueSync:{deep:!0,handler:function(t,e){if(""===this.changeSource)this._valueChanged(t);else{this.changeSource="";var n=t.map((function(t){return t}));this.$emit("update:value",n),this.$trigger("change",{},{value:n})}}}},methods:{getItemIndex:function(t){return this.items.indexOf(t)},getItemValue:function(t){return this.valueSync[this.getItemIndex(t.$vnode)]||0},setItemValue:function(t,e){var n=this.getItemIndex(t.$vnode),i=this.valueSync[n];i!==e&&(this.changeSource="touch",this.$set(this.valueSync,n,e))},_valueChanged:function(t){this.items.forEach((function(e,n){e.componentInstance.setCurrent(t[n]||0)}))},_resize:function(t){var e=t.height;this.height=e}},render:function(t){var e=[];return this.$slots.default&&Object(i["d"])(this.$slots.default,t).forEach((function(t){t.componentOptions&&"v-uni-picker-view-column"===t.componentOptions.tag&&e.push(t)})),this.items=e,t("uni-picker-view",{on:this.$listeners},[t("v-uni-resize-sensor",{attrs:{initial:!0},on:{resize:this._resize}}),t("div",{ref:"wrapper",class:"uni-picker-view-wrapper"},e)])}},d=f,p=(n("6062"),n("2877")),g=Object(p["a"])(d,l,h,!1,null,null,null);e["default"]=g.exports},"27c2":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-editor",t._g({staticClass:"ql-container",attrs:{id:t.id}},t.$listeners))},r=[],o=n("8188"),a=o["a"],s=(n("e298"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},"27d0":function(t,e,n){"use strict";(function(t){var i=n("85b6"),r=n("65a8"),o=n("f2b3"),a=n("24d9"),s=n("2d02"),c=n("a402"),u=n("90f7"),l=n("be12"),h=n("d8c8"),f=n.n(h);function d(t){return d="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}e["a"]={name:"Page",mpType:"page",components:{PageHead:s["a"],PageBody:c["a"],PageRefresh:u["a"]},mixins:[l["a"]],props:{isQuit:{type:Boolean,default:!1},isEntry:{type:Boolean,default:!1},isTabBar:{type:Boolean,default:!1},tabBarIndex:{type:Number,default:-1},navigationBarBackgroundColor:{type:String,default:"#000"},navigationBarTextStyle:{default:"white",validator:function(t){return-1!==["white","black"].indexOf(t)}},navigationBarTitleText:{type:String,default:""},navigationStyle:{default:"default",validator:function(t){return-1!==["default","custom"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundTextStyle:{default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColorTop:{type:String,default:"#fff"},backgroundColorBottom:{type:String,default:"#fff"},enablePullDownRefresh:{type:Boolean,default:!1},onReachBottomDistance:{type:Number,default:50},disableScroll:{type:Boolean,default:!1},titleNView:{type:[Boolean,Object,String],default:""},pullToRefresh:{type:Object,default:function(){return{}}},titleImage:{type:String,default:""},transparentTitle:{type:String,default:""},titlePenetrate:{type:String,default:"NO"},navigationBarShadow:{type:Object,default:function(){return{}}}},data:function(){var t={none:"default",auto:"transparent",always:"float"},e=this.titleNView;e=!1===e||"false"===e||"custom"===this.navigationStyle&&!Object(o["k"])(e)||"always"===this.transparentTitle&&!Object(o["k"])(e)?{type:"none"}:Object.assign({},{type:"custom"===this.navigationStyle?"none":"default"},this.transparentTitle in t?{type:t[this.transparentTitle]}:null,"object"===d(e)?e:"boolean"===typeof e?{type:e?"default":"none"}:null);var n={YES:!0,NO:!1},s=Object(a["a"])({loading:!1,backButton:!this.isQuit&&!this.$route.meta.isQuit,backgroundColor:this.navigationBarBackgroundColor,textColor:"black"===this.navigationBarTextStyle?"#000":"#fff",titleText:this.navigationBarTitleText,titleImage:this.titleImage,duration:"0",timingFunc:"",titlePenetrate:n[this.titlePenetrate]},e);s.shadow=this.navigationBarShadow;var c=Object.assign({support:!0,color:"#2BD009",style:"circle",height:70,range:150,offset:0},this.pullToRefresh),u=Object(i["d"])(c.offset);return"none"!==e.type&&"transparent"!==e.type&&(u+=r["a"]+f.a.top),c.offset=u,c.height=Object(i["d"])(c.height),c.range=Object(i["d"])(c.range),{navigationBar:s,refreshOptions:c}},created:function(){var e=this.navigationBar;document.title=e.titleText,t.emit("onNavigationBarChange",e)}}}).call(this,n("0dd1"))},2877:function(t,e,n){"use strict";function i(t,e,n,i,r,o,a,s){var c,u="function"===typeof t?t.options:t;if(e&&(u.render=e,u.staticRenderFns=n,u._compiled=!0),i&&(u.functional=!0),o&&(u._scopeId="data-v-"+o),a?(c=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),r&&r.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(a)},u._ssrRegister=c):r&&(c=s?function(){r.call(this,this.$root.$options.shadowRoot)}:r),c)if(u.functional){u._injectStyles=c;var l=u.render;u.render=function(t,e){return c.call(e),l(t,e)}}else{var h=u.beforeCreate;u.beforeCreate=h?[].concat(h,c):[c]}return{exports:t,options:u}}n.d(e,"a",(function(){return i}))},2883:function(t,e,n){},"28da":function(t,e,n){},"29a2":function(t,e,n){},"2bbe":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.hoverClass&&"none"!==t.hoverClass?n("uni-view",t._g({class:[t.hovering?t.hoverClass:""],on:{touchstart:t._hoverTouchStart,touchend:t._hoverTouchEnd,touchcancel:t._hoverTouchCancel}},t.$listeners),[t._t("default")],2):n("uni-view",t._g({},t.$listeners),[t._t("default")],2)},r=[],o=n("83a6"),a={name:"View",mixins:[o["a"]],listeners:{"label-click":"clickHandler"}},s=a,c=(n("e865"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},"2bdd":function(t,e,n){"use strict";n.r(e),n.d(e,"enableAccelerometer",(function(){return o}));var i,r=n("b865");function o(t){var e=t.enable;return e?a():s()}function a(){if(window.DeviceMotionEvent)return i=function(t){var e=t.acceleration||t.accelerationIncludingGravity;Object(r["a"])("onAccelerometerChange",{x:e.x||0,y:e.y||0,z:e.z||0,errMsg:"onAccelerometerChange:ok"})},window.addEventListener("devicemotion",i,!1),{};throw new Error("device nonsupport devicemotion")}function s(){return i&&(window.removeEventListener("devicemotion",i,!1),i=null),{}}},"2c45":function(t,e,n){},"2c67":function(t,e,n){"use strict";n.r(e),n.d(e,"createInnerAudioContext",(function(){return f}));var i=n("db70");function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}var s=["canplay","play","pause","stop","ended","timeUpdate","error","waiting","seeking","seeked"],c=[{name:"src",cache:!0},{name:"startTime",default:0,cache:!0},{name:"autoplay",default:!1,cache:!0},{name:"loop",default:!1,cache:!0},{name:"obeyMuteSwitch",default:!0,readonly:!0,cache:!0},{name:"duration",readonly:!0},{name:"currentTime",readonly:!0},{name:"paused",readonly:!0},{name:"buffered",readonly:!0},{name:"volume"}],u=function(){function t(e){var n=this;r(this,t),this.id=e,this._callbacks={},this._options={},s.forEach((function(t){n._callbacks[t.toLowerCase()]=[]})),c.forEach((function(t){var e=t.name,r={get:function(){var n=t.cache?this._options:Object(i["c"])("getAudioState",{audioId:this.id}),r=e in n?n[e]:t.default;return"number"===typeof r&&"volume"!==e?r/1e3:r}};t.readonly||(r.set=function(t){this._options[e]=t,Object(i["c"])("setAudioState",Object.assign({},this._options,{audioId:this.id}))}),Object.defineProperty(n,e,r)}))}return a(t,[{key:"play",value:function(){this._operate("play")}},{key:"pause",value:function(){this._operate("pause")}},{key:"stop",value:function(){this._operate("stop")}},{key:"seek",value:function(t){this._operate("seek",{currentTime:1e3*t})}},{key:"destroy",value:function(){clearInterval(this.__timing),Object(i["c"])("destroyAudioInstance",{audioId:this.id}),delete h[this.id]}},{key:"_operate",value:function(t,e){Object(i["c"])("operateAudio",Object.assign({},e,{audioId:this.id,operationType:t}))}}]),t}();function l(t,e,n,i){t._callbacks[e].forEach((function(t){"function"===typeof t&&t("error"===e?{errMsg:n,errCode:i}:{})}))}s.forEach((function(t){var e=t[0].toUpperCase()+t.substr(1);t=t.toLowerCase(),u.prototype["on".concat(e)]=function(e){this._callbacks[t].push(e)},u.prototype["off".concat(e)]=function(e){var n=this._callbacks[t],i=n.indexOf(e);i>=0&&n.splice(i,1)}})),Object(i["d"])("onAudioStateChange",(function(t){var e=t.state,n=t.audioId,i=t.errMsg,r=t.errCode,o=h[n];if(o)if(l(o,e,i,r),"play"===e){var a=o.currentTime;o.__timing=setInterval((function(){var t=o.currentTime;t!==a&&l(o,"timeupdate")}),200)}else"pause"!==e&&"stop"!==e&&"error"!==e||clearInterval(o.__timing)}));var h=Object.create(null);function f(){var t=Object(i["c"])("createAudioInstance"),e=t.audioId,n=new u(e);return h[e]=n,n}},"2d02":function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-page-head",{attrs:{"uni-page-head-type":t.type}},[n("div",{staticClass:"uni-page-head",class:t.headClass,style:{transitionDuration:t.duration,transitionTimingFunction:t.timingFunc,backgroundColor:t.bgColor,color:t.textColor}},[n("div",{staticClass:"uni-page-head-hd"},[n("div",{directives:[{name:"show",rawName:"v-show",value:t.backButton,expression:"backButton"}],staticClass:"uni-page-head-btn",on:{click:t._back}},[n("i",{staticClass:"uni-btn-icon",style:{color:t.color,fontSize:"27px"}},[t._v("")])]),t._l(t.btns,(function(e,i){return["left"===e.float?n("div",{key:i,staticClass:"uni-page-head-btn",class:{"uni-page-head-btn-red-dot":e.redDot||e.badgeText,"uni-page-head-btn-select":e.select},style:{backgroundColor:"transparent"===t.type?e.background:"transparent",width:e.width},attrs:{"badge-text":e.badgeText}},[n("i",{staticClass:"uni-btn-icon",style:t._formatBtnStyle(e),domProps:{innerHTML:t._s(t._formatBtnFontText(e))},on:{click:function(e){return t._onBtnClick(i)}}})]):t._e()]}))],2),t.searchInput?t._e():n("div",{staticClass:"uni-page-head-bd"},[n("div",{staticClass:"uni-page-head__title",style:{fontSize:t.titleSize,opacity:"transparent"===t.type?0:1}},[t.loading?n("i",{staticClass:"uni-loading"}):t._e(),""!==t.titleImage?n("img",{staticClass:"uni-page-head__title_image",attrs:{src:t.titleImage}}):[t._v(" "+t._s(t.titleText)+" ")]],2)]),t.searchInput?n("div",{staticClass:"uni-page-head-search",style:{"border-radius":t.searchInput.borderRadius,"background-color":t.searchInput.backgroundColor}},[n("div",{staticClass:"uni-page-head-search-placeholder",class:["uni-page-head-search-placeholder-"+(t.focus||t.text?"left":t.searchInput.align)],style:{color:t.searchInput.placeholderColor},domProps:{textContent:t._s(t.text||t.composing?"":t.searchInput.placeholder)}}),n("v-uni-input",{ref:"input",staticClass:"uni-page-head-search-input",style:{color:t.searchInput.color},attrs:{focus:t.searchInput.autoFocus,disabled:t.searchInput.disabled,"placeholder-style":"color:"+t.searchInput.placeholderColor,"confirm-type":"search"},on:{focus:t._focus,blur:t._blur,"update:value":t._input},model:{value:t.text,callback:function(e){t.text=e},expression:"text"}})],1):t._e(),n("div",{staticClass:"uni-page-head-ft"},[t._l(t.btns,(function(e,i){return["left"!==e.float?n("div",{key:i,staticClass:"uni-page-head-btn",class:{"uni-page-head-btn-red-dot":e.redDot||e.badgeText,"uni-page-head-btn-select":e.select},style:{backgroundColor:"transparent"===t.type?e.background:"transparent",width:e.width},attrs:{"badge-text":e.badgeText}},[n("i",{staticClass:"uni-btn-icon",style:t._formatBtnStyle(e),domProps:{innerHTML:t._s(t._formatBtnFontText(e))},on:{click:function(e){return t._onBtnClick(i)}}})]):t._e()]}))],2)]),"transparent"!==t.type&&"float"!==t.type?n("div",{staticClass:"uni-placeholder",class:{"uni-placeholder-titlePenetrate":t.titlePenetrate}}):t._e()])},r=[],o=n("dd35"),a=o["a"],s=(n("8e16"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["a"]=c.exports},"2d89":function(t,e,n){"use strict";var i=n("d29c"),r=n.n(i);r.a},"2eae":function(t,e,n){"use strict";n.r(e),n.d(e,"interceptors",(function(){return r}));var i=n("8542");n.d(e,"addInterceptor",(function(){return i["a"]})),n.d(e,"removeInterceptor",(function(){return i["d"]}));var r={promiseInterceptor:i["c"]}},"2ec6":function(t,e,n){"use strict";n.r(e),function(t){function i(e){var n=getCurrentPages();return n.length&&t.publishHandler("setPageMeta",e,n[n.length-1].$page.id),{}}n.d(e,"setPageMeta",(function(){return i}))}.call(this,n("0dd1"))},"2ef3":function(t,e,n){"use strict";(function(t,e,i){var r=n("8bbf"),o=n.n(r),a=n("442e");e.UniViewJSBridge={subscribe:t.subscribe,publishHandler:t.publishHandler,subscribeHandler:t.subscribeHandler},e.UniServiceJSBridge={subscribe:i.subscribe,publishHandler:i.publishHandler,subscribeHandler:i.subscribeHandler};var s=n("0138"),c=s.default,u=s.getApp,l=s.getCurrentPages,h=["chooseImage"];h.forEach((function(t){Object.defineProperty(c,t,{writable:!1,configurable:!1})})),e.uni=c,e.wx=e.uni,e.getApp=u,e.getCurrentPages=l,o.a.use(n("4ca9").default,{routes:__uniRoutes}),o.a.use(n("8c15").default,{routes:__uniRoutes}),Object(a["a"])(o.a),n("8f7e"),n("1efd")}).call(this,n("501c"),n("c8ba"),n("0dd1"))},"303f":function(t,e,n){"use strict";n.r(e),function(t,i){n.d(e,"CanvasContext",(function(){return j})),n.d(e,"createCanvasContext",(function(){return A})),n.d(e,"canvasGetImageData",(function(){return I})),n.d(e,"canvasPutImageData",(function(){return $})),n.d(e,"canvasToTempFilePath",(function(){return P}));var r=n("f2b3"),o=n("62b5"),a=n("db70"),s=n("a118");function c(t){return c="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(t){return d(t)||f(t)||h(t)||l()}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){if(t){if("string"===typeof t)return p(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(t,e):void 0}}function f(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function d(t){if(Array.isArray(t))return p(t)}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function v(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function m(t,e,n){return e&&v(t.prototype,e),n&&v(t,n),t}var b=Object(o["a"])("canvasEvent");function y(e,n,i,r){t.publishHandler(n+"-canvas-"+e,{canvasId:e,type:i,data:r},n)}t.subscribe("onDrawCanvas",(function(t){var e=t.callbackId,n=t.data,i=b.pop(e);i&&i(n)})),t.subscribe("onCanvasMethodCallback",(function(t){var e=t.callbackId,n=t.data,i=b.pop(e);i&&i(n)}));var _={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function w(t){t=t||"#000000";var e=null;if(null!=(e=/^#([0-9|A-F|a-f]{6})$/.exec(t))){var n=parseInt(e[1].slice(0,2),16),o=parseInt(e[1].slice(2,4),16),a=parseInt(e[1].slice(4),16);return[n,o,a,255]}if(null!=(e=/^#([0-9|A-F|a-f]{3})$/.exec(t))){var s=e[1].slice(0,1),c=e[1].slice(1,2),u=e[1].slice(2,3);return s=parseInt(s+s,16),c=parseInt(c+c,16),u=parseInt(u+u,16),[s,c,u,255]}if(null!=(e=/^rgb\((.+)\)$/.exec(t)))return e[1].split(",").map((function(t){return Math.min(255,parseInt(t.trim()))})).concat(255);if(null!=(e=/^rgba\((.+)\)$/.exec(t)))return e[1].split(",").map((function(t,e){return 3===e?Math.floor(255*parseFloat(t.trim())):Math.min(255,parseInt(t.trim()))}));var l=t.toLowerCase();if(Object(r["h"])(_,l)){e=/^#([0-9|A-F|a-f]{6,8})$/.exec(_[l]);var h=parseInt(e[1].slice(0,2),16),f=parseInt(e[1].slice(2,4),16),d=parseInt(e[1].slice(4,6),16),p=parseInt(e[1].slice(6,8),16);return p=p>=0?p:255,[h,f,d,p]}return i.group("非法颜色: "+t),i.error("不支持颜色："+t),i.groupEnd(),[0,0,0,255]}function S(t,e){this.image=t,this.repetition=e}var k,T=function(){function t(e,n){g(this,t),this.type=e,this.data=n,this.colorStop=[]}return m(t,[{key:"addColorStop",value:function(t,e){this.colorStop.push([t,w(e)])}}]),t}(),x=["scale","rotate","translate","setTransform","transform"],C=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],O=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];function E(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return k||(k=document.createElement("canvas")),k.width=t,k.height=e,k}function M(t){this.width=t}var j=function(){function t(e,n){g(this,t),this.id=e,this.pageId=n,this.actions=[],this.path=[],this.subpath=[],this.currentTransform=[],this.currentStepAnimates=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}return m(t,[{key:"draw",value:function(){var t,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1?arguments[1]:void 0,i=u(this.actions);this.actions=[],this.path=[],"function"===typeof n&&(t=b.push(n)),y(this.id,this.pageId,"actionsChanged",{actions:i,reserve:e,callbackId:t})}},{key:"createLinearGradient",value:function(t,e,n,i){return new T("linear",[t,e,n,i])}},{key:"createCircularGradient",value:function(t,e,n){return new T("radial",[t,e,n])}},{key:"createPattern",value:function(t,e){if(void 0===e)i.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(e)<0))return new S(t,e);i.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+e+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}},{key:"measureText",value:function(t){if("object"===("undefined"===typeof document?"undefined":c(document))){var e=E().getContext("2d");return e.font=this.state.font,new M(e.measureText(t).width||0)}return new M(0)}},{key:"save",value:function(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}},{key:"restore",value:function(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}},{key:"beginPath",value:function(){this.path=[],this.subpath=[]}},{key:"moveTo",value:function(t,e){this.path.push({method:"moveTo",data:[t,e]}),this.subpath=[[t,e]]}},{key:"lineTo",value:function(t,e){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[t,e]}):this.path.push({method:"lineTo",data:[t,e]}),this.subpath.push([t,e])}},{key:"quadraticCurveTo",value:function(t,e,n,i){this.path.push({method:"quadraticCurveTo",data:[t,e,n,i]}),this.subpath.push([n,i])}},{key:"bezierCurveTo",value:function(t,e,n,i,r,o){this.path.push({method:"bezierCurveTo",data:[t,e,n,i,r,o]}),this.subpath.push([r,o])}},{key:"arc",value:function(t,e,n,i,r){var o=arguments.length>5&&void 0!==arguments[5]&&arguments[5];this.path.push({method:"arc",data:[t,e,n,i,r,o]}),this.subpath.push([t,e])}},{key:"rect",value:function(t,e,n,i){this.path.push({method:"rect",data:[t,e,n,i]}),this.subpath=[[t,e]]}},{key:"arcTo",value:function(t,e,n,i,r){this.path.push({method:"arcTo",data:[t,e,n,i,r]}),this.subpath.push([n,i])}},{key:"clip",value:function(){this.actions.push({method:"clip",data:u(this.path)})}},{key:"closePath",value:function(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}},{key:"clearActions",value:function(){this.actions=[],this.path=[],this.subpath=[]}},{key:"getActions",value:function(){var t=u(this.actions);return this.clearActions(),t}},{key:"lineDashOffset",set:function(t){this.actions.push({method:"setLineDashOffset",data:[t]})}},{key:"globalCompositeOperation",set:function(t){this.actions.push({method:"setGlobalCompositeOperation",data:[t]})}},{key:"shadowBlur",set:function(t){this.actions.push({method:"setShadowBlur",data:[t]})}},{key:"shadowColor",set:function(t){this.actions.push({method:"setShadowColor",data:[t]})}},{key:"shadowOffsetX",set:function(t){this.actions.push({method:"setShadowOffsetX",data:[t]})}},{key:"shadowOffsetY",set:function(t){this.actions.push({method:"setShadowOffsetY",data:[t]})}},{key:"font",set:function(t){var e=this;this.state.font=t;var n=t.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var r=n[1].trim().split(/\s/),o=parseFloat(n[3]),a=n[7],s=[];r.forEach((function(t,n){["italic","oblique","normal"].indexOf(t)>-1?(s.push({method:"setFontStyle",data:[t]}),e.state.fontStyle=t):["bold","normal"].indexOf(t)>-1?(s.push({method:"setFontWeight",data:[t]}),e.state.fontWeight=t):0===n?(s.push({method:"setFontStyle",data:["normal"]}),e.state.fontStyle="normal"):1===n&&c()})),1===r.length&&c(),r=s.map((function(t){return t.data[0]})).join(" "),this.state.fontSize=o,this.state.fontFamily=a,this.actions.push({method:"setFont",data:["".concat(r," ").concat(o,"px ").concat(a)]})}else i.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function c(){s.push({method:"setFontWeight",data:["normal"]}),e.state.fontWeight="normal"}},get:function(){return this.state.font}},{key:"fillStyle",set:function(t){this.setFillStyle(t)}},{key:"strokeStyle",set:function(t){this.setStrokeStyle(t)}},{key:"globalAlpha",set:function(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:"setGlobalAlpha",data:[t]})}},{key:"textAlign",set:function(t){this.actions.push({method:"setTextAlign",data:[t]})}},{key:"lineCap",set:function(t){this.actions.push({method:"setLineCap",data:[t]})}},{key:"lineJoin",set:function(t){this.actions.push({method:"setLineJoin",data:[t]})}},{key:"lineWidth",set:function(t){this.actions.push({method:"setLineWidth",data:[t]})}},{key:"miterLimit",set:function(t){this.actions.push({method:"setMiterLimit",data:[t]})}},{key:"textBaseline",set:function(t){this.actions.push({method:"setTextBaseline",data:[t]})}}]),t}();function A(e,n){if(n)return new j(e,n.$page.id);var i=Object(a["a"])();if(i)return new j(e,i);t.emit("onError","createCanvasContext:fail")}function I(t,e){var n=t.canvasId,i=t.x,r=t.y,o=t.width,c=t.height,u=Object(a["a"])();if(u){var l=b.push((function(t){var n=t.data;n&&n.length&&(t.data=new Uint8ClampedArray(n)),Object(s["a"])(e,t)}));y(n,u,"getImageData",{x:i,y:r,width:o,height:c,callbackId:l})}else Object(s["a"])(e,{errMsg:"canvasGetImageData:fail"})}function $(t,e){var n=t.canvasId,i=t.data,r=t.x,o=t.y,c=t.width,u=t.height,l=Object(a["a"])();if(l){var h=b.push((function(t){Object(s["a"])(e,t)}));y(n,l,"putImageData",{data:Array.prototype.slice.call(i),x:r,y:o,width:c,height:u,callbackId:h})}else Object(s["a"])(e,{errMsg:"canvasPutImageData:fail"})}function P(t,e){var n=t.x,i=void 0===n?0:n,r=t.y,o=void 0===r?0:r,c=t.width,u=t.height,l=t.destWidth,h=t.destHeight,f=t.canvasId,d=t.fileType,p=t.qualit,g=Object(a["a"])();if(g){var v=b.push((function(t){var n=t.base64;n&&n.length||Object(s["a"])(e,{errMsg:"canvasToTempFilePath:fail"}),Object(a["c"])("base64ToTempFilePath",{base64Data:n,x:i,y:o,width:c,height:u,destWidth:l,destHeight:h,canvasId:f,fileType:d,qualit:p},e)}));y(f,g,"getDataUrl",{x:i,y:o,width:c,height:u,destWidth:l,destHeight:h,hidpi:!1,fileType:d,qualit:p,callbackId:v})}else Object(s["a"])(e,{errMsg:"canvasToTempFilePath:fail"})}[].concat(x,C).forEach((function(t){function e(t){switch(t){case"fill":case"stroke":return function(){this.actions.push({method:t+"Path",data:u(this.path)})};case"fillRect":return function(t,e,n,i){this.actions.push({method:"fillPath",data:[{method:"rect",data:[t,e,n,i]}]})};case"strokeRect":return function(t,e,n,i){this.actions.push({method:"strokePath",data:[{method:"rect",data:[t,e,n,i]}]})};case"fillText":case"strokeText":return function(e,n,i,r){var o=[e.toString(),n,i];"number"===typeof r&&o.push(r),this.actions.push({method:t,data:o})};case"drawImage":return function(e,n,i,r,o,a,s,c,u){var l;function h(t){return"number"===typeof t}void 0===u&&(a=n,s=i,c=r,u=o,n=void 0,i=void 0,r=void 0,o=void 0),l=h(n)&&h(i)&&h(r)&&h(o)?[e,a,s,c,u,n,i,r,o]:h(c)&&h(u)?[e,a,s,c,u]:[e,a,s],this.actions.push({method:t,data:l})};default:return function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];this.actions.push({method:t,data:n})}}}j.prototype[t]=e(t)})),O.forEach((function(t){function e(t){switch(t){case"setFillStyle":case"setStrokeStyle":return function(e){"object"!==c(e)?this.actions.push({method:t,data:["normal",w(e)]}):this.actions.push({method:t,data:[e.type,e.data,e.colorStop]})};case"setGlobalAlpha":return function(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:t,data:[e]})};case"setShadow":return function(e,n,i,r){r=w(r),this.actions.push({method:t,data:[e,n,i,r]}),this.state.shadowBlur=i,this.state.shadowColor=r,this.state.shadowOffsetX=e,this.state.shadowOffsetY=n};case"setLineDash":return function(e,n){e=e||[0,0],n=n||0,this.actions.push({method:t,data:[e,n]}),this.state.lineDash=e};case"setFontSize":return function(e){this.state.font=this.state.font.replace(/\d+\.?\d*px/,e+"px"),this.state.fontSize=e,this.actions.push({method:t,data:[e]})};default:return function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];this.actions.push({method:t,data:n})}}}j.prototype[t]=e(t)}))}.call(this,n("0dd1"),n("3ad9")["default"])},"31e2":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-video",t._g({attrs:{id:t.id}},t.$listeners),[n("div",{ref:"container",staticClass:"uni-video-container",on:{click:t.triggerControls,touchstart:t.touchstart,touchend:t.touchend,touchmove:t.touchmove,fullscreenchange:function(e){return e.stopPropagation(),t.onFullscreenChange(e)},webkitfullscreenchange:function(e){return e.stopPropagation(),t.onFullscreenChange(e,!0)}}},[n("video",t._b({ref:"video",staticClass:"uni-video-video",style:{objectFit:t.objectFit},attrs:{loop:t.loop,src:t.srcSync,poster:t.poster,autoplay:t.autoplay,"webkit-playsinline":"",playsinline:""},domProps:{muted:t.muted},on:{durationchange:t.onDurationChange,loadedmetadata:t.onLoadedMetadata,progress:t.onProgress,waiting:t.onWaiting,error:t.onVideoError,play:t.onPlay,pause:t.onPause,ended:t.onEnded,timeupdate:t.onTimeUpdate,webkitbeginfullscreen:function(e){return t.emitFullscreenChange(!0)},x5videoenterfullscreen:function(e){return t.emitFullscreenChange(!0)},webkitendfullscreen:function(e){return t.emitFullscreenChange(!1)},x5videoexitfullscreen:function(e){return t.emitFullscreenChange(!1)}}},"video",t.$attrs,!1)),n("div",{directives:[{name:"show",rawName:"v-show",value:t.controlsShow,expression:"controlsShow"}],staticClass:"uni-video-bar uni-video-bar-full",on:{click:function(t){t.stopPropagation()}}},[n("div",{staticClass:"uni-video-controls"},[n("div",{directives:[{name:"show",rawName:"v-show",value:t.showPlayBtn,expression:"showPlayBtn"}],staticClass:"uni-video-control-button",class:{"uni-video-control-button-play":!t.playing,"uni-video-control-button-pause":t.playing},on:{click:function(e){return e.stopPropagation(),t.trigger(e)}}}),n("div",{staticClass:"uni-video-current-time"},[t._v(" "+t._s(t._f("time")(t.currentTime))+" ")]),n("div",{ref:"progress",staticClass:"uni-video-progress-container",on:{click:function(e){return e.stopPropagation(),t.clickProgress(e)}}},[n("div",{staticClass:"uni-video-progress"},[n("div",{staticClass:"uni-video-progress-buffered",style:{width:t.buffered+"%"}}),n("div",{ref:"ball",staticClass:"uni-video-ball",style:{left:t.progress+"%"}},[n("div",{staticClass:"uni-video-inner"})])])]),n("div",{staticClass:"uni-video-duration"},[t._v(" "+t._s(t._f("time")(t.duration||t.durationTime))+" ")])]),t.danmuBtn?n("div",{staticClass:"uni-video-danmu-button",class:{"uni-video-danmu-button-active":t.enableDanmuSync},on:{click:function(e){return e.stopPropagation(),t.triggerDanmu(e)}}},[t._v(" 弹幕 ")]):t._e(),n("div",{directives:[{name:"show",rawName:"v-show",value:t.showFullscreenBtn,expression:"showFullscreenBtn"}],staticClass:"uni-video-fullscreen",class:{"uni-video-type-fullscreen":t.fullscreen},on:{click:function(e){return e.stopPropagation(),t.triggerFullscreen(!t.fullscreen)}}})]),n("div",{directives:[{name:"show",rawName:"v-show",value:t.start&&t.enableDanmuSync,expression:"start&&enableDanmuSync"}],ref:"danmu",staticClass:"uni-video-danmu",staticStyle:{"z-index":"0"}}),t.centerPlayBtnShow?n("div",{staticClass:"uni-video-cover",on:{click:function(t){t.stopPropagation()}}},[n("div",{staticClass:"uni-video-cover-play-button",on:{click:function(e){return e.stopPropagation(),t.play(e)}}}),n("p",{staticClass:"uni-video-cover-duration"},[t._v(" "+t._s(t._f("time")(t.duration||t.durationTime))+" ")])]):t._e(),n("div",{staticClass:"uni-video-toast",class:{"uni-video-toast-volume":"volume"===t.gestureType}},[n("div",{staticClass:"uni-video-toast-title"},[t._v(" 音量 ")]),n("svg",{staticClass:"uni-video-toast-icon",attrs:{width:"200px",height:"200px",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"}},[n("path",{attrs:{d:"M475.400704 201.19552l0 621.674496q0 14.856192-10.856448 25.71264t-25.71264 10.856448-25.71264-10.856448l-190.273536-190.273536-149.704704 0q-14.856192 0-25.71264-10.856448t-10.856448-25.71264l0-219.414528q0-14.856192 10.856448-25.71264t25.71264-10.856448l149.704704 0 190.273536-190.273536q10.856448-10.856448 25.71264-10.856448t25.71264 10.856448 10.856448 25.71264zm219.414528 310.837248q0 43.425792-24.28416 80.851968t-64.2816 53.425152q-5.71392 2.85696-14.2848 2.85696-14.856192 0-25.71264-10.570752t-10.856448-25.998336q0-11.999232 6.856704-20.284416t16.570368-14.2848 19.427328-13.142016 16.570368-20.284416 6.856704-32.569344-6.856704-32.569344-16.570368-20.284416-19.427328-13.142016-16.570368-14.2848-6.856704-20.284416q0-15.427584 10.856448-25.998336t25.71264-10.570752q8.57088 0 14.2848 2.85696 39.99744 15.427584 64.2816 53.139456t24.28416 81.137664zm146.276352 0q0 87.422976-48.56832 161.41824t-128.5632 107.707392q-7.428096 2.85696-14.2848 2.85696-15.427584 0-26.284032-10.856448t-10.856448-25.71264q0-22.284288 22.284288-33.712128 31.997952-16.570368 43.425792-25.141248 42.283008-30.855168 65.995776-77.423616t23.712768-99.136512-23.712768-99.136512-65.995776-77.423616q-11.42784-8.57088-43.425792-25.141248-22.284288-11.42784-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 79.99488 33.712128 128.5632 107.707392t48.56832 161.41824zm146.276352 0q0 131.42016-72.566784 241.41312t-193.130496 161.989632q-7.428096 2.85696-14.856192 2.85696-14.856192 0-25.71264-10.856448t-10.856448-25.71264q0-20.570112 22.284288-33.712128 3.999744-2.285568 12.85632-5.999616t12.85632-5.999616q26.284032-14.2848 46.854144-29.140992 70.281216-51.996672 109.707264-129.705984t39.426048-165.132288-39.426048-165.132288-109.707264-129.705984q-20.570112-14.856192-46.854144-29.140992-3.999744-2.285568-12.85632-5.999616t-12.85632-5.999616q-22.284288-13.142016-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 120.563712 51.996672 193.130496 161.989632t72.566784 241.41312z"}})]),n("div",{staticClass:"uni-video-toast-value"},[n("div",{staticClass:"uni-video-toast-value-content",style:{width:100*t.volumeNew+"%"}},[n("div",{staticClass:"uni-video-toast-volume-grids"},t._l(10,(function(t,e){return n("div",{key:e,staticClass:"uni-video-toast-volume-grids-item"})})),0)])])]),n("div",{staticClass:"uni-video-toast",class:{"uni-video-toast-progress":"progress"==t.gestureType}},[n("div",{staticClass:"uni-video-toast-title"},[t._v(" "+t._s(t._f("time")(t.currentTimeNew))+" / "+t._s(t._f("time")(t.durationTime))+" ")])])]),n("div",{staticStyle:{position:"absolute",top:"0",width:"100%",height:"100%",overflow:"hidden","pointer-events":"none"}},[t._t("default")],2)])},r=[],o=n("8af1"),a=n("f2b3"),s=!!a["o"]&&{passive:!1},c={NONE:"none",STOP:"stop",VOLUME:"volume",PROGRESS:"progress"},u={name:"Video",filters:{time:function(t){t=t>0&&t<1/0?t:0;var e=Math.floor(t/3600),n=Math.floor(t%3600/60),i=Math.floor(t%3600%60);e=(e<10?"0":"")+e,n=(n<10?"0":"")+n,i=(i<10?"0":"")+i;var r=n+":"+i;return"00"!==e&&(r=e+":"+r),r}},mixins:[o["g"],o["d"]],props:{id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:function(){return[]}},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},data:function(){return{start:!1,playing:!1,currentTime:0,durationTime:0,progress:0,touching:!1,enableDanmuSync:Boolean(this.enableDanmu),controlsVisible:!0,fullscreen:!1,controlsTouching:!1,touchStartOrigin:{x:0,y:0},gestureType:c.NONE,currentTimeOld:0,currentTimeNew:0,volumeOld:null,volumeNew:null,buffered:0,isSafari:/^Apple/.test(navigator.vendor)}},computed:{centerPlayBtnShow:function(){return this.showCenterPlayBtn&&!this.start},controlsShow:function(){return!this.centerPlayBtnShow&&this.controls&&this.controlsVisible},autoHideContorls:function(){return this.controlsShow&&this.playing&&!this.controlsTouching},srcSync:function(){return this.$getRealPath(this.src)}},watch:{enableDanmuSync:function(t){this.$emit("update:enableDanmu",t)},autoHideContorls:function(t){t?this.autoHideStart():this.autoHideEnd()},srcSync:function(t){this.playing=!1,this.currentTime=0},currentTime:function(){this.updateProgress()},duration:function(){this.updateProgress()},buffered:function(t){0!==t&&this.$trigger("progress",{},{buffered:t})}},created:function(){this.otherData={danmuList:[],danmuIndex:{time:0,index:-1},hideTiming:null};var t=this.otherData.danmuList=JSON.parse(JSON.stringify(this.danmuList||[]));t.sort((function(t,e){return(t.time||0)-(t.time||0)}))},mounted:function(){var t,e,n,i=this,r=this,o=!0,a=this.$refs.ball;function c(i){var a=i.targetTouches[0],s=a.pageX,c=a.pageY;if(o&&Math.abs(s-t)<Math.abs(c-e))u();else{o=!1;var l=r.$refs.progress.offsetWidth,h=n+(s-t)/l*100;h<0?h=0:h>100&&(h=100),r.progress=h,i.preventDefault(),i.stopPropagation()}}function u(t){r.controlsTouching=!1,r.touching&&(a.removeEventListener("touchmove",c,s),o||(t.preventDefault(),t.stopPropagation(),r.seek(r.$refs.video.duration*r.progress/100)),r.touching=!1)}a.addEventListener("touchstart",(function(r){i.controlsTouching=!0;var u=r.targetTouches[0];t=u.pageX,e=u.pageY,n=i.progress,o=!0,i.touching=!0,a.addEventListener("touchmove",c,s)})),a.addEventListener("touchend",u),a.addEventListener("touchcancel",u)},beforeDestroy:function(){this.triggerFullscreen(!1),clearTimeout(this.otherData.hideTiming)},methods:{_handleSubscribe:function(t){var e,n=t.type,i=t.data,r=void 0===i?{}:i,o=["play","pause","seek","sendDanmu","playbackRate","requestFullScreen","exitFullScreen"];switch(n){case"seek":e=r.position;break;case"sendDanmu":e=r;break;case"playbackRate":e=r.rate;break}o.indexOf(n)>=0&&this[n](e)},trigger:function(){this.playing?this.$refs.video.pause():this.$refs.video.play()},play:function(){this.start=!0,this.$refs.video.play()},pause:function(){this.$refs.video.pause()},seek:function(t){t=Number(t),"number"!==typeof t||isNaN(t)||(this.$refs.video.currentTime=t)},clickProgress:function(t){var e=this.$refs.progress,n=t.target,i=t.offsetX;while(n!==e)i+=n.offsetLeft,n=n.parentNode;var r=e.offsetWidth,o=0;i>=0&&i<=r&&(o=i/r,this.seek(this.$refs.video.duration*o))},triggerDanmu:function(){this.enableDanmuSync=!this.enableDanmuSync},playDanmu:function(t){var e=document.createElement("p");e.className="uni-video-danmu-item",e.innerText=t.text;var n="bottom: ".concat(100*Math.random(),"%;color: ").concat(t.color,";");e.setAttribute("style",n),this.$refs.danmu.appendChild(e),setTimeout((function(){n+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",e.setAttribute("style",n),setTimeout((function(){e.remove()}),4e3)}),17)},sendDanmu:function(t){var e=this.otherData;e.danmuList.splice(e.danmuIndex.index+1,0,{text:String(t.text),color:t.color,time:this.$refs.video.currentTime||0})},playbackRate:function(t){this.$refs.video.playbackRate=t},triggerFullscreen:function(t){var e,n=this.$refs.container,i=this.$refs.video;t?!document.fullscreenEnabled&&!document.webkitFullscreenEnabled||this.isSafari&&!this.userInteract?i.webkitEnterFullScreen?i.webkitEnterFullScreen():(e=!0,n.remove(),n.classList.add("uni-video-type-fullscreen"),document.body.appendChild(n)):n[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():i.webkitExitFullScreen?i.webkitExitFullScreen():(e=!0,n.remove(),n.classList.remove("uni-video-type-fullscreen"),this.$el.appendChild(n)),e&&this.emitFullscreenChange(t)},onFullscreenChange:function(t,e){e&&document.fullscreenEnabled||this.emitFullscreenChange(!(!document.fullscreenElement&&!document.webkitFullscreenElement))},emitFullscreenChange:function(t){this.fullscreen=t,this.$trigger("fullscreenchange",{},{fullScreen:t,direction:"vertical"})},requestFullScreen:function(){this.triggerFullscreen(!0)},exitFullScreen:function(){this.triggerFullscreen(!1)},onDurationChange:function(t){var e=t.target;this.durationTime=e.duration},onLoadedMetadata:function(t){var e=Number(this.initialTime)||0,n=t.target;e>0&&(n.currentTime=e),this.$trigger("loadedmetadata",t,{width:n.videoWidth,height:n.videoHeight,duration:n.duration}),this.onProgress(t)},onProgress:function(t){var e=t.target,n=e.buffered;n.length&&(this.buffered=n.end(n.length-1)/e.duration*100)},onWaiting:function(t){this.$trigger("waiting",t,{})},onVideoError:function(t){this.playing=!1,this.$trigger("error",t,{})},onPlay:function(t){this.start=!0,this.playing=!0,this.$trigger("play",t,{})},onPause:function(t){this.playing=!1,this.$trigger("pause",t,{})},onEnded:function(t){this.playing=!1,this.$trigger("ended",t,{})},onTimeUpdate:function(t){var e=t.target,n=this.otherData,i=this.currentTime=e.currentTime,r=n.danmuIndex,o={time:i,index:r.index},a=n.danmuList;if(i>r.time)for(var s=r.index+1;s<a.length;s++){var c=a[s];if(!(i>=(c.time||0)))break;o.index=s,this.playing&&this.enableDanmuSync&&this.playDanmu(c)}else if(i<r.time)for(var u=r.index-1;u>-1;u--){var l=a[u];if(!(i<=(l.time||0)))break;o.index=u-1}n.danmuIndex=o,this.$trigger("timeupdate",t,{currentTime:i,duration:e.duration})},triggerControls:function(){this.controlsVisible=!this.controlsVisible},touchstart:function(t){var e=t.targetTouches[0];this.touchStartOrigin={x:e.pageX,y:e.pageY},this.gestureType=c.NONE,this.volumeOld=null,this.currentTimeOld=this.currentTimeNew=0},touchmove:function(t){function e(){t.stopPropagation(),t.preventDefault()}this.fullscreen&&e();var n=this.gestureType;if(n!==c.STOP){var i=t.targetTouches[0],r=i.pageX,o=i.pageY,a=this.touchStartOrigin;if(n===c.PROGRESS?this.changeProgress(r-a.x):n===c.VOLUME&&this.changeVolume(o-a.y),n===c.NONE)if(Math.abs(r-a.x)>Math.abs(o-a.y)){if(!this.enableProgressGesture)return void(this.gestureType=c.STOP);this.gestureType=c.PROGRESS,this.currentTimeOld=this.currentTimeNew=this.$refs.video.currentTime,this.fullscreen||e()}else{if(!this.pageGesture)return void(this.gestureType=c.STOP);this.gestureType=c.VOLUME,this.volumeOld=this.$refs.video.volume,this.fullscreen||e()}}},touchend:function(t){this.gestureType!==c.NONE&&this.gestureType!==c.STOP&&(t.stopPropagation(),t.preventDefault()),this.gestureType===c.PROGRESS&&this.currentTimeOld!==this.currentTimeNew&&(this.$refs.video.currentTime=this.currentTimeNew),this.gestureType=c.NONE},changeProgress:function(t){var e=this.$refs.video.duration,n=t/600*e+this.currentTimeOld;n<0?n=0:n>e&&(n=e),this.currentTimeNew=n},changeVolume:function(t){var e,n=this.volumeOld;"number"===typeof n&&(e=n-t/200,e<0?e=0:e>1&&(e=1),this.$refs.video.volume=e,this.volumeNew=e)},autoHideStart:function(){var t=this;this.otherData.hideTiming=setTimeout((function(){t.controlsVisible=!1}),3e3)},autoHideEnd:function(){var t=this.otherData;t.hideTiming&&(clearTimeout(t.hideTiming),t.hideTiming=null)},updateProgress:function(){this.touching||(this.progress=this.currentTime/this.durationTime*100)}}},l=u,h=(n("856e"),n("2877")),f=Object(h["a"])(l,i,r,!1,null,null,null);e["default"]=f.exports},"324c":function(t,e,n){},"332a":function(t,e,n){"use strict";n.r(e),n.d(e,"redirectTo",(function(){return u})),n.d(e,"reLaunch",(function(){return l})),n.d(e,"navigateTo",(function(){return h})),n.d(e,"switchTab",(function(){return f})),n.d(e,"navigateBack",(function(){return d}));var i,r=n("0f74");function o(t){if("string"!==typeof t)return t;var e=t.indexOf("?");if(-1===e)return t;var n=t.substr(e+1).trim().replace(/^(\?|#|&)/,"");if(!n)return t;t=t.substr(0,e);var i=[];return n.split("&").forEach((function(t){var e=t.replace(/\+/g," ").split("="),n=e.shift(),r=e.length>0?e.join("="):"";i.push(n+"="+encodeURIComponent(r))})),i.length?t+"?"+i.join("&"):t}function a(t){return function(e,n){e=Object(r["a"])(e);var a=e.split("?")[0],s=__uniRoutes.find((function(t){var e=t.path,n=t.alias;return e===a||n===a}));if(!s)return"page `"+e+"` is not found";if("navigateTo"===t||"redirectTo"===t){if(s.meta.isTabBar)return"can not ".concat(t," a tabbar page")}else if("switchTab"===t&&!s.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"===t&&s.meta.isTabBar&&"appLaunch"!==n.openType&&(e=a),s.meta.isEntry&&(e=e.replace(s.alias,"/")),n.url=o(e),i===e)return"".concat(i," locked");__uniConfig.ready&&!1!==__uniConfig.enableNavigatorLock&&(i=e)}}function s(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.assign({url:{type:String,required:!0,validator:a(t)},beforeAll:function(){i=""}},e)}function c(t){return{animationType:{type:String,validator:function(e){if(e&&-1===t.indexOf(e))return"`"+e+"` is not supported for `animationType` (supported values are: `"+t.join("`|`")+"`)"}},animationDuration:{type:Number}}}var u=s("redirectTo"),l=s("reLaunch"),h=s("navigateTo",c(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"])),f=s("switchTab"),d=Object.assign({delta:{type:Number,validator:function(t,e){t=parseInt(t)||1,e.delta=Math.min(getCurrentPages().length-1,t)}}},c(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]))},"33b4":function(t,e,n){},"33ed":function(t,e,n){"use strict";(function(t){n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return c}));var i,r=n("4a59");function o(t){t.preventDefault()}function a(t){var e=t.scrollTop,n=t.duration,i=document.documentElement,r=i.clientHeight,o=i.scrollHeight;function a(t){if(t<=0)window.scrollTo(0,e);else{var n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),a(t-10)}))}}e=Math.min(e,o-r),0!==n?window.scrollY!==e&&a(n):i.scrollTop=document.body.scrollTop=e}var s=0;function c(e,n){var o=n.enablePageScroll,a=n.enablePageReachBottom,c=n.onReachBottomDistance,u=n.enableTransparentTitleNView,l=!1,h=!1,f=!0;function d(){var t=document.documentElement.scrollHeight,e=window.innerHeight,n=window.scrollY,i=n>0&&t>e&&n+e+c>=t,r=Math.abs(t-s)>c;return!i||h&&!r?(!i&&h&&(h=!1),!1):(s=t,h=!0,!0)}function p(){var n=getCurrentPages();if(n.length&&n[n.length-1].$page.id===e){var s=window.pageYOffset;o&&Object(r["a"])("onPageScroll",{scrollTop:s},e),u&&t.emit("onPageScroll",{scrollTop:s}),a&&f&&(c()||(i=setTimeout(c,300))),l=!1}function c(){if(d())return Object(r["a"])("onReachBottom",{},e),f=!1,setTimeout((function(){f=!0}),350),!0}}return function(){clearTimeout(i),l||requestAnimationFrame(p),l=!0}}}).call(this,n("501c"))},"34b2":function(t,e,n){"use strict";n.r(e),function(t){function i(){return window.location.protocol+"//"+window.location.host}function r(e,n){var r=e.src,o=t,a=o.invokeCallbackHandler,s=new Image,c=r;s.onload=function(){a(n,{errMsg:"getImageInfo:ok",width:s.naturalWidth,height:s.naturalHeight,path:0===c.indexOf("/")?i()+c:c})},s.onerror=function(t){a(n,{errMsg:"getImageInfo:fail"})},s.src=r}n.d(e,"getImageInfo",(function(){return r}))}.call(this,n("0dd1"))},3590:function(t,e,n){},3648:function(t,e,n){"use strict";n.r(e),n.d(e,"canIUse",(function(){return a}));var i=n("f2b3");function r(t){return window.CSS&&window.CSS.supports&&window.CSS.supports(t)}var o={"css.var":r("--a:0"),"css.env":r("top:env(a)"),"css.constant":r("top:constant(a)")};function a(t){return!Object(i["h"])(o,t)||o[t]}},3676:function(t,e,n){"use strict";n.r(e),n.d(e,"getRecorderManager",(function(){return l}));var i=n("db70");function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}var s,c={pause:null,resume:null,start:null,stop:null,error:null},u=function(){function t(){r(this,t),Object(i["d"])("onRecorderStateChange",(function(t){var e=t.state;delete t.state,delete t.errMsg,"function"===typeof c[e]&&c[e](t)}))}return a(t,[{key:"onError",value:function(t){c.error=t}},{key:"onFrameRecorded",value:function(t){}},{key:"onInterruptionBegin",value:function(t){}},{key:"onInterruptionEnd",value:function(t){}},{key:"onPause",value:function(t){c.pause=t}},{key:"onResume",value:function(t){c.resume=t}},{key:"onStart",value:function(t){c.start=t}},{key:"onStop",value:function(t){c.stop=t}},{key:"pause",value:function(){Object(i["c"])("operateRecorder",{operationType:"pause"})}},{key:"resume",value:function(){Object(i["c"])("operateRecorder",{operationType:"resume"})}},{key:"start",value:function(t){Object(i["c"])("operateRecorder",Object.assign({},t,{operationType:"start"}))}},{key:"stop",value:function(){Object(i["c"])("operateRecorder",{operationType:"stop"})}}]),t}();function l(){return s||(s=new u)}},"3ad9":function(t,e,n){"use strict";n.r(e),function(t){var n=Array.prototype.unshift;function i(t){return n.call(t,"[system]"),t}function r(e){return function(){var n=!0;"debug"!==e||__uniConfig.debug||(n=!1),n&&t.console[e].apply(t.console,i(arguments))}}e["default"]={log:r("log"),info:r("info"),warn:r("warn"),debug:r("debug"),error:r("error")}}.call(this,n("c8ba"))},"3b54":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"getFileInfo",(function(){return a}));var i=n("e2e2"),r=t,o=r.invokeCallbackHandler;function a(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.filePath,n=arguments.length>1?arguments[1]:void 0;Object(i["c"])(e).then((function(t){o(n,{errMsg:"getFileInfo:ok",size:t.size})})).catch((function(t){o(n,{errMsg:"getFileInfo:fail 文件["+e+"] getFileInfo 失败:"+t.message})}))}}.call(this,n("0dd1"))},"3b67":function(t,e,n){"use strict";var i=Object.create(null),r=n("e3a7");r.keys().forEach((function(t){Object.assign(i,r(t))})),e["a"]=i},"3bfb":function(t,e,n){"use strict";n.r(e),n.d(e,"createAudioContext",(function(){return r})),n.d(e,"createVideoContext",(function(){return o})),n.d(e,"createMapContext",(function(){return a})),n.d(e,"createCanvasContext",(function(){return s}));var i=[{name:"id",type:String,required:!0}],r=i,o=i,a=i,s=[{name:"canvasId",type:String,required:!0},{name:"componentInstance",type:Object}]},"3c79":function(t,e,n){},"3d1f":function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var i=n("62b5"),r=n("a741");function o(t,e){e.getApp;var n=e.getCurrentPages;function o(t){return function(e,i){i=parseInt(i);var o=n(),a=o.find((function(t){return t.$page.id===i}));a&&Object(r["b"])(a,t,e)}}var a=Object(i["a"])("requestComponentInfo");function s(t){var e=t.reqId,n=t.res,i=a.pop(e);i&&i(n)}var c=Object(i["a"])("requestComponentObserver");function u(t){var e=t.reqId,n=t.reqEnd,i=t.res,r=c.get(e);if(r){if(n)return void c.pop(e);r(i)}}t("onPageReady",o("onReady")),t("onPageScroll",o("onPageScroll")),t("onReachBottom",o("onReachBottom")),t("onRequestComponentInfo",s),t("onRequestComponentObserver",u)}},"3d64":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"onNetworkStatusChange",(function(){return s})),n.d(e,"getNetworkType",(function(){return c}));var i=t,r=i.invokeCallbackHandler,o=[];function a(){var t=c(),e=t.networkType;o.forEach((function(t){r(t,{errMsg:"onNetworkStatusChange:ok",isConnected:"none"!==e,networkType:e})}))}function s(t){var e=navigator.connection||navigator.webkitConnection;o.push(t),e?e.addEventListener("change",a):(window.addEventListener("offline",a),window.addEventListener("online",a))}function c(){var t=navigator.connection||navigator.webkitConnection,e="unknown";return t?(e=t.type,"cellular"===e&&t.effectiveType?e=t.effectiveType.replace("slow-",""):["none","wifi"].includes(e)||(e="unknown")):!1===navigator.onLine&&(e="none"),{errMsg:"getNetworkType:ok",networkType:e}}}.call(this,n("0dd1"))},"3da9":function(t,e,n){"use strict";var i=n("bfbd"),r=n.n(i);r.a},"3e8c":function(t,e,n){"use strict";n.r(e);var i,r,o={name:"ResizeSensor",props:{initial:{type:[Boolean,String],default:!1}},data:function(){return{size:{width:-1,height:-1}}},watch:{size:{deep:!0,handler:function(t){this.$emit("resize",Object.assign({},t))}}},mounted:function(){!0===this.initial&&this.$nextTick(this.update),this.$el.offsetParent!==this.$el.parentNode&&(this.$el.parentNode.style.position="relative"),"AnimationEvent"in window||this.reset()},methods:{reset:function(){var t=this.$el.firstChild,e=this.$el.lastChild;t.scrollLeft=1e5,t.scrollTop=1e5,e.scrollLeft=1e5,e.scrollTop=1e5},update:function(){this.size.width=this.$el.offsetWidth,this.size.height=this.$el.offsetHeight,this.reset()}},render:function(t){return t("uni-resize-sensor",{on:{"~animationstart":this.update}},[t("div",{on:{scroll:this.update}},[t("div")]),t("div",{on:{scroll:this.update}},[t("div")])])}},a=o,s=(n("64d0"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},"3f7e":function(t,e,n){"use strict";var i=n("e692"),r=n.n(i);r.a},"439a":function(t,e,n){"use strict";n.r(e),n.d(e,"downloadFile",(function(){return i}));var i={url:{type:String,required:!0},header:{type:Object,validator:function(t,e){e.header=t||{}}}}},"442e":function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return a}));var i=n("5129"),r=n.n(i),o=n("85b6");function a(e){e.config.errorHandler=function(e){var n="function"===typeof getApp&&getApp();n&&Object(o["a"])(n.$options,"onError")?n.__call_hook("onError",e):t.error(e)};var n=e.config.isReservedTag;e.config.isReservedTag=function(t){return-1!==r.a.indexOf(t)||n(t)},e.config.ignoredElements=r.a;var i=e.config.getTagNamespace,a=["switch","image","text","view"];e.config.getTagNamespace=function(t){return!~a.indexOf(t)&&(i(t)||!1)}}}).call(this,n("3ad9")["default"])},"44de":function(t,e,n){"use strict";n.r(e),n.d(e,"vibrateLong",(function(){return r})),n.d(e,"vibrateShort",(function(){return o}));var i=!!window.navigator.vibrate;function r(){return i&&window.navigator.vibrate(400)?{errMsg:"vibrateLong:ok"}:{errMsg:"vibrateLong:fail"}}function o(){return i&&window.navigator.vibrate(15)?{errMsg:"vibrateShort:ok"}:{errMsg:"vibrateShort:fail"}}},"454d":function(t,e,n){"use strict";n.r(e),n.d(e,"removeTabBarBadge",(function(){return o})),n.d(e,"showTabBarRedDot",(function(){return a})),n.d(e,"hideTabBarRedDot",(function(){return s})),n.d(e,"onTabBarMidButtonTap",(function(){return u}));var i=n("db70"),r=n("a118");function o(t){var e=t.index;return Object(i["c"])("setTabBarBadge",{index:e,type:"none"})}function a(t){var e=t.index;return Object(i["c"])("setTabBarBadge",{index:e,type:"redDot"})}var s=o,c=[];function u(t){c.push(t)}Object(i["d"])("onTabBarMidButtonTap",(function(t){c.forEach((function(e){Object(r["a"])(e,t)}))}))},"45d2":function(t,e,n){"use strict";n.r(e),n.d(e,"upx2px",(function(){return u}));var i=1e-4,r=750,o=!1,a=0,s=0;function c(){var t=uni.getSystemInfoSync(),e=t.platform,n=t.pixelRatio,i=t.windowWidth;a=i,s=n,o="ios"===e}function u(t,e){if(0===a&&c(),t=Number(t),0===t)return 0;var n=t/r*(e||a);return n<0&&(n=-n),n=Math.floor(n+i),0===n?1!==s&&o?.5:1:t<0?-n:n}},"45db":function(t,e,n){"use strict";n.r(e),function(t){var i;function r(t){i=t}function o(){i&&t.emit(i+".stopPullDownRefresh",{},i);var e=getCurrentPages();return e.length&&(i=e[e.length-1].$page.id,t.emit(i+".startPullDownRefresh",{},i)),{}}function a(){if(i)t.emit(i+".stopPullDownRefresh",{},i),i=null;else{var e=getCurrentPages();e.length&&(i=e[e.length-1].$page.id,t.emit(i+".stopPullDownRefresh",{},i))}return{}}n.d(e,"setPullDownRefreshPageId",(function(){return r})),n.d(e,"startPullDownRefresh",(function(){return o})),n.d(e,"stopPullDownRefresh",(function(){return a}))}.call(this,n("0dd1"))},"4a59":function(t,e,n){"use strict";(function(t){function i(e,n,i){t.UniServiceJSBridge.subscribeHandler(e,n,i)}n.d(e,"a",(function(){return i}))}).call(this,n("c8ba"))},"4ca9":function(t,e,n){"use strict";n.r(e),function(t){var i=n("6389"),r=n.n(i),o=n("85b6"),a=n("abbf"),s=n("0784"),c=n("aa92"),u=n("02c9"),l=n("23e5");function h(t){var e=0;return t.forEach((function(t){t.meta.id&&e++})),e}function f(){var t=window.location.href,e=t.indexOf("#");return-1===e?"":decodeURI(t.slice(e+1))}function d(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/",e=decodeURI(window.location.pathname);return t&&0===e.indexOf(t)&&(e=e.slice(t.length)),(e||"/")+window.location.search+window.location.hash}e["default"]={install:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=n.routes;e.config.devtools&&"undefined"!==typeof window&&-1!==window.navigator.userAgent.toLowerCase().indexOf("hbuilderx")&&(e.config.devtools=!1),Object(u["a"])(e),Object(c["a"])(e),"undefined"!==typeof __UNI_ROUTER_BASE__&&(__uniConfig.router.base=__UNI_ROUTER_BASE__);var p=h(i),g=new r.a({id:p,mode:__uniConfig.router.mode,base:__uniConfig.router.base,routes:i,scrollBehavior:function(t,e,n){if(n)return n;if(t&&e&&t.meta.isTabBar&&e.meta.isTabBar){var i=Object(l["b"])(t.params.__id__);if(i)return i}return{x:0,y:0}}}),v=[],m=g.match("history"===__uniConfig.router.mode?d(__uniConfig.router.base):f());if(m.meta.name&&(m.meta.id?v.push(m.meta.name+"-"+m.meta.id):v.push(m.meta.name+"-"+(p+1))),m.meta&&m.meta.name&&(document.body.className="uni-body "+m.meta.name,m.meta.isNVue)){var b="nvue-dir-"+__uniConfig.nvue["flex-direction"];document.body.setAttribute("nvue",""),document.body.setAttribute(b,"")}e.mixin({beforeCreate:function(){var e=this.$options;if("app"===e.mpType){e.data=function(){return{keepAliveInclude:v}};var n=Object(a["a"])(i,m);Object.keys(n).forEach((function(t){e[t]=e[t]?[].concat(n[t],e[t]):[n[t]]})),e.router=g,Array.isArray(e.onError)&&0!==e.onError.length||(e.onError=[function(e){t.error(e)}])}else if(Object(o["b"])(this)){var r=Object(s["a"])();Object.keys(r).forEach((function(t){e.mpOptions?e[t]=e[t]?[].concat(e[t],r[t]):[r[t]]:e[t]=e[t]?[].concat(r[t],e[t]):[r[t]]}))}else this.$parent&&this.$parent.__page__&&(this.__page__=this.$parent.__page__)}}),Object.defineProperty(e.prototype,"$page",{get:function(){return this.__page__}}),e.prototype.createSelectorQuery=function(){return uni.createSelectorQuery().in(this)},e.prototype.createIntersectionObserver=function(t){return uni.createIntersectionObserver(this,t)},e.use(r.a)}}}.call(this,n("3ad9")["default"])},"4da7":function(t,e,n){"use strict";n.r(e);var i,r,o=n("5881"),a=o["a"],s=(n("c8ed"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},"4e0b":function(t,e,n){},"4e7c":function(t,e,n){"use strict";n.r(e),n.d(e,"getProvider",(function(){return r}));var i={OAUTH:"OAUTH",SHARE:"SHARE",PAYMENT:"PAYMENT",PUSH:"PUSH"},r={service:{type:String,required:!0,validator:function(t,e){if(t=(t||"").toUpperCase(),t&&Object.values(i).indexOf(t)<0)return"service error"}}}},"4f1c":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-switch",t._g({attrs:{disabled:t.disabled},on:{click:t._onClick}},t.$listeners),[n("div",{staticClass:"uni-switch-wrapper"},[n("div",{directives:[{name:"show",rawName:"v-show",value:"switch"===t.type,expression:"type === 'switch'"}],staticClass:"uni-switch-input",class:[t.switchChecked?"uni-switch-input-checked":""],style:{backgroundColor:t.switchChecked?t.color:"#DFDFDF",borderColor:t.switchChecked?t.color:"#DFDFDF"}}),n("div",{directives:[{name:"show",rawName:"v-show",value:"checkbox"===t.type,expression:"type === 'checkbox'"}],staticClass:"uni-checkbox-input",class:[t.switchChecked?"uni-checkbox-input-checked":""],style:{color:t.color}})])])},r=[],o=n("8af1"),a={name:"Switch",mixins:[o["b"],o["f"]],props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#007aff"}},data:function(){return{switchChecked:this.checked}},watch:{checked:function(t){this.switchChecked=t}},created:function(){this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this})},beforeDestroy:function(){this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})},listeners:{"label-click":"_onClick","@label-click":"_onClick"},methods:{_onClick:function(t){this.disabled||(this.switchChecked=!this.switchChecked,this.$trigger("change",t,{value:this.switchChecked}))},_resetFormData:function(){this.switchChecked=!1},_getFormData:function(){var t={};return""!==this.name&&(t.value=this.switchChecked,t.key=this.name),t}}},s=a,c=(n("a5ec"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},"4f43":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"downloadFile",(function(){return u}));var i=n("e2e2");function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var c=function(){function t(e){r(this,t),s(this,"_xhr",void 0),s(this,"_callbacks",[]),this._xhr=e}return a(t,[{key:"onProgressUpdate",value:function(t){"function"===typeof t&&this._callbacks.push(t)}},{key:"offProgressUpdate",value:function(t){var e=this._callbacks.indexOf(t);e>=0&&this._callbacks.splice(e,1)}},{key:"abort",value:function(){this._xhr&&(this._xhr.abort(),delete this._xhr)}}]),t}();function u(e,n){var r,o=e.url,a=e.header,s=__uniConfig.networkTimeout&&__uniConfig.networkTimeout.downloadFile||6e4,u=t,l=u.invokeCallbackHandler,h=new XMLHttpRequest,f=new c(h);return h.open("GET",o,!0),Object.keys(a).forEach((function(t){h.setRequestHeader(t,a[t])})),h.responseType="blob",h.onload=function(){clearTimeout(r);var t=h.status,e=this.response;l(n,{errMsg:"downloadFile:ok",statusCode:t,tempFilePath:Object(i["a"])(e)})},h.onabort=function(){clearTimeout(r),l(n,{errMsg:"downloadFile:fail abort"})},h.onerror=function(){clearTimeout(r),l(n,{errMsg:"downloadFile:fail"})},h.onprogress=function(t){f._callbacks.forEach((function(e){var n=t.loaded,i=t.total,r=Math.round(n/i*100);e({progress:r,totalBytesWritten:n,totalBytesExpectedToWrite:i})}))},h.send(),r=setTimeout((function(){h.onprogress=h.onload=h.onabort=h.onerror=null,f.abort(),l(n,{errMsg:"downloadFile:fail timeout"})}),s),f}}.call(this,n("0dd1"))},"4fef":function(t,e,n){"use strict";var i=n("7572"),r=n.n(i);r.a},"501c":function(t,e,n){"use strict";n.r(e),n.d(e,"on",(function(){return g})),n.d(e,"off",(function(){return v})),n.d(e,"once",(function(){return m})),n.d(e,"emit",(function(){return b})),n.d(e,"subscribe",(function(){return y})),n.d(e,"unsubscribe",(function(){return _})),n.d(e,"subscribeHandler",(function(){return w})),n.d(e,"publishHandler",(function(){return d["a"]}));var i=n("8bbf"),r=n.n(i);function o(t){var e=t.pageStyle,n=t.rootFontSize,i=document.querySelector("uni-page-body")||document.body;i.setAttribute("style",e),n&&document.documentElement.style.fontSize!==n&&(document.documentElement.style.fontSize=n)}var a=n("6bdf"),s=n("5dc1"),c={setPageMeta:o,requestComponentInfo:a["a"],requestComponentObserver:s["b"],destroyComponentObserver:s["a"]},u=n("33ed"),l=n("7107"),h=n("764a");function f(t){Object.keys(c).forEach((function(e){t(e,c[e])})),t("pageScrollTo",u["c"]),t("loadFontFace",l["a"]),Object(h["a"])(t)}var d=n("4a59"),p=new r.a,g=p.$on.bind(p),v=p.$off.bind(p),m=p.$once.bind(p),b=p.$emit.bind(p);function y(t,e){return g("service."+t,e)}function _(t,e){return v("service."+t,e)}function w(t,e,n){b("service."+t,e,n)}f(y)},"50c5":function(t,e,n){},5129:function(t,e){t.exports=["uni-app","uni-tabbar","uni-page","uni-page-head","uni-page-wrapper","uni-page-body","uni-page-refresh","uni-actionsheet","uni-modal","uni-toast","uni-resize-sensor","uni-shadow-root","uni-ad","uni-audio","uni-button","uni-camera","uni-canvas","uni-checkbox","uni-checkbox-group","uni-cover-image","uni-cover-view","uni-editor","uni-form","uni-functional-page-navigator","uni-icon","uni-image","uni-input","uni-label","uni-live-player","uni-live-pusher","uni-map","uni-movable-area","uni-movable-view","uni-navigator","uni-official-account","uni-open-data","uni-picker","uni-picker-view","uni-picker-view-column","uni-progress","uni-radio","uni-radio-group","uni-rich-text","uni-scroll-view","uni-slider","uni-swiper","uni-swiper-item","uni-switch","uni-text","uni-textarea","uni-video","uni-view","uni-web-view"]},"515d":function(t,e,n){},5222:function(t,e,n){"use strict";(function(t){var i=n("5a56");e["a"]={name:"Toast",mixins:[i["default"]],props:{title:{type:String,default:""},icon:{default:"success",validator:function(t){return-1!==["success","loading","none"].indexOf(t)}},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean,default:!1}},computed:{iconClass:function(){return"success"===this.icon?"uni-icon-success-no-circle":"loading"===this.icon?"uni-loading":""}},beforeUpdate:function(){this.visible&&(this.timeoutId&&clearTimeout(this.timeoutId),this.timeoutId=setTimeout((function(){t.emit("onHideToast")}),this.duration))}}}).call(this,n("0dd1"))},5363:function(t,e,n){"use strict";function i(t){this._drag=t,this._dragLog=Math.log(t),this._x=0,this._v=0,this._startTime=0}n.d(e,"a",(function(){return i})),i.prototype.set=function(t,e){this._x=t,this._v=e,this._startTime=(new Date).getTime()},i.prototype.setVelocityByEnd=function(t){this._v=(t-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)},i.prototype.x=function(t){var e;return void 0===t&&(t=((new Date).getTime()-this._startTime)/1e3),e=t===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,t),this._dt=t,this._x+this._v*e/this._dragLog-this._v/this._dragLog},i.prototype.dx=function(t){var e;return void 0===t&&(t=((new Date).getTime()-this._startTime)/1e3),e=t===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,t),this._dt=t,this._v*e},i.prototype.done=function(){return Math.abs(this.dx())<3},i.prototype.reconfigure=function(t){var e=this.x(),n=this.dx();this._drag=t,this._dragLog=Math.log(t),this.set(e,n)},i.prototype.configuration=function(){var t=this;return[{label:"Friction",read:function(){return t._drag},write:function(e){t.reconfigure(e)},min:.001,max:.1,step:.001}]}},5408:function(t,e,n){var i={"./button/index.vue":"d3bd","./canvas/index.vue":"bacd","./checkbox-group/index.vue":"25ce","./checkbox/index.vue":"7bb3","./editor/index.vue":"27c2","./form/index.vue":"b34d","./icon/index.vue":"9a8b","./image/index.vue":"1082","./input/index.vue":"250d","./label/index.vue":"70f4","./movable-area/index.vue":"c61c","./movable-view/index.vue":"8842","./navigator/index.vue":"17fd","./picker-view-column/index.vue":"1955","./picker-view/index.vue":"27ab","./progress/index.vue":"9b1f","./radio-group/index.vue":"d5ec","./radio/index.vue":"6491","./resize-sensor/index.vue":"3e8c","./rich-text/index.vue":"b705","./scroll-view/index.vue":"f1ef","./slider/index.vue":"9f96","./swiper-item/index.vue":"9213","./swiper/index.vue":"5513","./switch/index.vue":"4f1c","./text/index.vue":"4da7","./textarea/index.vue":"5768","./view/index.vue":"2bbe"};function r(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}r.keys=function(){return Object.keys(i)},r.resolve=o,t.exports=r,r.id="5408"},"54bc":function(t,e,n){},5513:function(t,e,n){"use strict";n.r(e);var i,r,o=n("ba15"),a=n("f2b3"),s={name:"Swiper",mixins:[o["a"]],props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1}},data:function(){return{currentSync:Math.round(this.current)||0,currentItemIdSync:this.currentItemId||"",userTracking:!1,currentChangeSource:"",items:[]}},computed:{intervalNumber:function(){var t=Number(this.interval);return isNaN(t)?5e3:t},durationNumber:function(){var t=Number(this.duration);return isNaN(t)?500:t},displayMultipleItemsNumber:function(){var t=Math.round(this.displayMultipleItems);return isNaN(t)?1:t},slidesStyle:function(){var t={};return(this.nextMargin||this.previousMargin)&&(t=this.vertical?{left:0,right:0,top:this._upx2px(this.previousMargin),bottom:this._upx2px(this.nextMargin)}:{top:0,bottom:0,left:this._upx2px(this.previousMargin),right:this._upx2px(this.nextMargin)}),t},slideFrameStyle:function(){var t=Math.abs(100/this.displayMultipleItemsNumber)+"%";return{width:this.vertical?"100%":t,height:this.vertical?t:"100%"}},circularEnabled:function(){return this.circular&&this.items.length>this.displayMultipleItemsNumber}},watch:{vertical:function(){this._resetLayout()},circular:function(){this._resetLayout()},intervalNumber:function(t){this._timer&&(this._cancelSchedule(),this._scheduleAutoplay())},current:function(t){this._currentCheck()},currentSync:function(t,e){this._currentChanged(t,e),this.$emit("update:current",t)},currentItemId:function(t){this._currentCheck()},currentItemIdSync:function(t){this.$emit("update:currentItemId",t)},displayMultipleItemsNumber:function(){this._resetLayout()}},created:function(){this._invalid=!0,this._viewportPosition=0,this._viewportMoveRatio=1,this._animating=null,this._requestedAnimation=!1,this._userDirectionChecked=!1,this._contentTrackViewport=0,this._contentTrackSpeed=0,this._contentTrackT=0},mounted:function(){var t=this;this._currentCheck(),this.touchtrack(this.$refs.slidesWrapper,"_handleContentTrack",!0),this._resetLayout(),this.$watch((function(){return t.autoplay&&!t.userTracking}),this._inintAutoplay),this._inintAutoplay(this.autoplay&&!this.userTracking),this.$watch("items.length",this._resetLayout)},beforeDestroy:function(){this._cancelSchedule(),cancelAnimationFrame(this._animationFrame)},methods:{_inintAutoplay:function(t){t?this._scheduleAutoplay():this._cancelSchedule()},_currentCheck:function(){var t=-1;if(this.currentItemId)for(var e=0,n=this.items;e<n.length;e++){var i=n[e].componentInstance;if(i&&i.itemId===this.currentItemId){t=e;break}}t<0&&(t=Math.round(this.current)||0),t=t<0?0:t,this.currentSync!==t&&(this.currentChangeSource="",this.currentSync=t)},_itemReady:function(t,e){t.componentInstance&&t.componentInstance._isMounted?e():(t._callbacks=t._callbacks||[],t._callbacks.push(e))},_currentChanged:function(t,e){var n=this,i=this.currentChangeSource;if(this.currentChangeSource="",!i){var r=this.items.length;this._animateViewport(t,"",this.circularEnabled&&e+(r-t)%r>r/2?1:0)}var o=this.items[t];o&&this._itemReady(o,(function(){var t=n.currentItemIdSync=o.componentInstance.itemId||"";n.$trigger("change",{},{current:n.currentSync,currentItemId:t,source:i})}))},_scheduleAutoplay:function(){var t=this;function e(){t._timer=null,t.currentChangeSource="autoplay",t.circularEnabled?t.currentSync=t._normalizeCurrentValue(t.currentSync+1):t.currentSync=t.currentSync+t.displayMultipleItemsNumber<t.items.length?t.currentSync+1:0,t._animateViewport(t.currentSync,"autoplay",t.circularEnabled?1:0),t._timer=setTimeout(e,t.intervalNumber)}this._cancelSchedule(),!this._isMounted||this._invalid||this.items.length<=this.displayMultipleItemsNumber||(this._timer=setTimeout(e,this.intervalNumber))},_cancelSchedule:function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},_normalizeCurrentValue:function(t){var e=this.items.length;if(!e)return-1;var n=(Math.round(t)%e+e)%e;if(this.circularEnabled){if(e<=this.displayMultipleItemsNumber)return 0}else if(n>e-this.displayMultipleItemsNumber)return e-this.displayMultipleItemsNumber;return n},_upx2px:function(t){return/\d+[ur]px$/i.test(t)&&t.replace(/\d+[ur]px$/i,(function(t){return"".concat(uni.upx2px(parseFloat(t)),"px")})),t||""},_resetLayout:function(){if(this._isMounted){this._cancelSchedule(),this._endViewportAnimation();for(var t=this.items,e=0;e<t.length;e++)this._updateItemPos(e,e);if(this._viewportMoveRatio=1,1===this.displayMultipleItemsNumber&&t.length){var n=t[0].componentInstance.$el.getBoundingClientRect(),i=this.$refs.slideFrame.getBoundingClientRect();this._viewportMoveRatio=n.width/i.width,this._viewportMoveRatio>0&&this._viewportMoveRatio<1||(this._viewportMoveRatio=1)}var r=this._viewportPosition;this._viewportPosition=-2;var o=this.currentSync;o>=0?(this._invalid=!1,this.userTracking?(this._updateViewport(r+o-this._contentTrackViewport),this._contentTrackViewport=o):(this._updateViewport(o),this.autoplay&&this._scheduleAutoplay())):(this._invalid=!0,this._updateViewport(-this.displayMultipleItemsNumber-1))}},_checkCircularLayout:function(t){if(!this._invalid)for(var e=this.items,n=e.length,i=t+this.displayMultipleItemsNumber,r=0;r<n;r++){var o=e[r],a=o._position,s=Math.floor(t/n)*n+r,c=s+n,u=s-n,l=Math.max(t-(s+1),s-i,0),h=Math.max(t-(c+1),c-i,0),f=Math.max(t-(u+1),u-i,0),d=Math.min(l,h,f),p=[s,c,u][[l,h,f].indexOf(d)];a!==p&&this._updateItemPos(r,p)}},_updateItemPos:function(t,e){var n=this.vertical?"0":100*e+"%",i=this.vertical?100*e+"%":"0",r="translate("+n+", "+i+") translateZ(0)",o=this.items[t];this._itemReady(o,(function(){var t=o.componentInstance.$el;t.style["-webkit-transform"]=r,t.style.transform=r,t._position=e}))},_updateViewport:function(t){Math.floor(2*this._viewportPosition)===Math.floor(2*t)&&Math.ceil(2*this._viewportPosition)===Math.ceil(2*t)||this.circularEnabled&&this._checkCircularLayout(t);var e=this.vertical?"0":100*-t*this._viewportMoveRatio+"%",n=this.vertical?100*-t*this._viewportMoveRatio+"%":"0",i="translate("+e+", "+n+") translateZ(0)",r=this.$refs.slideFrame;if(r&&(r.style["-webkit-transform"]=i,r.style.transform=i),this._viewportPosition=t,!this._transitionStart){if(t%1===0)return;this._transitionStart=t}t-=Math.floor(this._transitionStart),t<=-(this.items.length-1)?t+=this.items.length:t>=this.items.length&&(t-=this.items.length),t=this._transitionStart%1>.5||this._transitionStart<0?t-1:t,this.$trigger("transition",{},{dx:this.vertical?0:t*r.offsetWidth,dy:this.vertical?t*r.offsetHeight:0})},_animateFrameFuncProto:function(){var t=this;if(this._animating){var e=this._animating,n=e.toPos,i=e.acc,r=e.endTime,o=e.source,a=r-Date.now();if(a<=0){this._updateViewport(n),this._animating=null,this._requestedAnimation=!1,this._transitionStart=null;var s=this.items[this.currentSync];s&&this._itemReady(s,(function(){var e=s.componentInstance.itemId||"";t.$trigger("animationfinish",{},{current:t.currentSync,currentItemId:e,source:o})}))}else{var c=i*a*a/2,u=n+c;this._updateViewport(u),this._animationFrame=requestAnimationFrame(this._animateFrameFuncProto.bind(this))}}else this._requestedAnimation=!1},_animateViewport:function(t,e,n){this._cancelViewportAnimation();var i=this.durationNumber,r=this.items.length,o=this._viewportPosition;if(this.circularEnabled)if(n<0){for(;o<t;)o+=r;for(;o-r>t;)o-=r}else if(n>0){for(;o>t;)o-=r;for(;o+r<t;)o+=r}else{for(;o+r<t;)o+=r;for(;o-r>t;)o-=r;o+r-t<t-o&&(o+=r)}this._animating={toPos:t,acc:2*(o-t)/(i*i),endTime:Date.now()+i,source:e},this._requestedAnimation||(this._requestedAnimation=!0,this._animationFrame=requestAnimationFrame(this._animateFrameFuncProto.bind(this)))},_cancelViewportAnimation:function(){this._animating=null},_endViewportAnimation:function(){this._animating&&(this._updateViewport(this._animating.toPos),this._animating=null)},_handleTrackStart:function(){this._cancelSchedule(),this._contentTrackViewport=this._viewportPosition,this._contentTrackSpeed=0,this._contentTrackT=Date.now(),this._cancelViewportAnimation()},_handleTrackMove:function(t){var e=this,n=this._contentTrackT;this._contentTrackT=Date.now();var i=this.items.length,r=i-this.displayMultipleItemsNumber;function o(t){return.5-.25/(t+.5)}function a(t,n){var i=e._contentTrackViewport+t;e._contentTrackSpeed=.6*e._contentTrackSpeed+.4*n,e.circularEnabled||(i<0||i>r)&&(i<0?i=-o(-i):i>r&&(i=r+o(i-r)),e._contentTrackSpeed=0),e._updateViewport(i)}var s=this._contentTrackT-n||1;this.vertical?a(-t.dy/this.$refs.slideFrame.offsetHeight,-t.ddy/s):a(-t.dx/this.$refs.slideFrame.offsetWidth,-t.ddx/s)},_handleTrackEnd:function(t){this.userTracking=!1;var e=this._contentTrackSpeed/Math.abs(this._contentTrackSpeed),n=0;!t&&Math.abs(this._contentTrackSpeed)>.2&&(n=.5*e);var i=this._normalizeCurrentValue(this._viewportPosition+n);t?this._updateViewport(this._contentTrackViewport):(this.currentChangeSource="touch",this.currentSync=i,this._animateViewport(i,"touch",0!==n?n:0===i&&this.circularEnabled&&this._viewportPosition>=1?1:0))},_handleContentTrack:function(t){if(!this.disableTouch&&!this._invalid){if("start"===t.detail.state)return this.userTracking=!0,this._userDirectionChecked=!1,this._handleTrackStart();if("end"===t.detail.state)return this._handleTrackEnd(!1);if("cancel"===t.detail.state)return this._handleTrackEnd(!0);if(this.userTracking){if(!this._userDirectionChecked){this._userDirectionChecked=!0;var e=Math.abs(t.detail.dx),n=Math.abs(t.detail.dy);if((e>=n&&this.vertical||e<=n&&!this.vertical)&&(this.userTracking=!1),!this.userTracking)return void(this.autoplay&&this._scheduleAutoplay())}return this._handleTrackMove(t.detail),!1}}}},render:function(t){var e=this,n=[],i=[];this.$slots.default&&Object(a["d"])(this.$slots.default,t).forEach((function(t){t.componentOptions&&"v-uni-swiper-item"===t.componentOptions.tag&&i.push(t)}));for(var r=function(i,r){var o=e.currentSync;n.push(t("div",{on:{click:function(){e._animateViewport(e.currentSync=i,e.currentChangeSource="click",e.circularEnabled?1:0)}},class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":i<o+e.displayMultipleItemsNumber&&i>=o||i<o+e.displayMultipleItemsNumber-r},style:{background:i===o?e.indicatorActiveColor:e.indicatorColor}}))},o=0,s=i.length;o<s;o++)r(o,s);this.items=i;var c=[t("div",{ref:"slides",style:this.slidesStyle,class:"uni-swiper-slides"},[t("div",{ref:"slideFrame",class:"uni-swiper-slide-frame",style:this.slideFrameStyle},i)])];return this.indicatorDots&&c.push(t("div",{ref:"slidesDots",class:["uni-swiper-dots",this.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},n)),t("uni-swiper",{on:this.$listeners},[t("div",{ref:"slidesWrapper",class:"uni-swiper-wrapper"},c)])}},c=s,u=(n("1c64"),n("2877")),l=Object(u["a"])(c,i,r,!1,null,null,null);e["default"]=l.exports},5621:function(t,e,n){"use strict";n.r(e),n.d(e,"setTabBarItem",(function(){return a})),n.d(e,"setTabBarStyle",(function(){return s})),n.d(e,"hideTabBar",(function(){return c})),n.d(e,"showTabBar",(function(){return u})),n.d(e,"hideTabBarRedDot",(function(){return l})),n.d(e,"showTabBarRedDot",(function(){return h})),n.d(e,"removeTabBarBadge",(function(){return f})),n.d(e,"setTabBarBadge",(function(){return d}));var i=n("f2b3"),r=n("cb0f"),o={type:Number,required:!0},a={index:o,text:{type:String},iconPath:{type:String},selectedIconPath:{type:String}},s={color:{type:String},selectedColor:{type:String},backgroundColor:{type:String},backgroundImage:{type:String,validator:function(t,e){t&&!/^(linear|radial)-gradient\(.+?\);?$/.test(t)&&(e.backgroundImage=Object(r["a"])(t))}},backgroundRepeat:{type:String},borderStyle:{type:String,validator:function(t,e){t&&(e.borderStyle="black"===t?"black":"white")}}},c={animation:{type:Boolean,default:!1}},u={animation:{type:Boolean,default:!1}},l={index:o},h={index:o},f={index:o},d={index:o,text:{type:String,required:!0,validator:function(t,e){Object(i["g"])(t)>=4&&(e.text="...")}}}},5676:function(t,e,n){"use strict";var i=n("c33a"),r=n.n(i);r.a},"56e9":function(t,e,n){"use strict";n.r(e),function(t){function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}n.d(e,"showModal",(function(){return s})),n.d(e,"showToast",(function(){return c})),n.d(e,"hideToast",(function(){return u})),n.d(e,"showLoading",(function(){return l})),n.d(e,"hideLoading",(function(){return h})),n.d(e,"showActionSheet",(function(){return f}));var r=t,o=r.emit,a=r.invokeCallbackHandler;function s(t,e){o("onShowModal",t,(function(t){a(e,i({},t,!0))}))}function c(t){return o("onShowToast",t),{}}function u(){return o("onHideToast"),{}}function l(t){return o("onShowLoading",t),{}}function h(){return o("onHideLoading"),{}}function f(t,e){o("onShowActionSheet",t,(function(t){a(e,-1===t?{errMsg:"showActionSheet:fail cancel"}:{tapIndex:t})}))}}.call(this,n("0dd1"))},5727:function(t,e,n){"use strict";var i=n("28da"),r=n.n(i);r.a},5768:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-textarea",t._g({on:{change:function(t){t.stopPropagation()}}},t.$listeners),[n("div",{staticClass:"uni-textarea-wrapper"},[n("div",{directives:[{name:"show",rawName:"v-show",value:!(t.composition||t.valueSync.length),expression:"!(composition||valueSync.length)"}],ref:"placeholder",staticClass:"uni-textarea-placeholder",class:t.placeholderClass,style:t.placeholderStyle,domProps:{textContent:t._s(t.placeholder)}}),n("div",{ref:"line",staticClass:"uni-textarea-line",domProps:{textContent:t._s(" ")}}),n("div",{staticClass:"uni-textarea-compute"},[t._l(t.valueCompute,(function(e,i){return n("div",{key:i,domProps:{textContent:t._s(e.trim()?e:".")}})})),n("v-uni-resize-sensor",{ref:"sensor",on:{resize:t._resize}})],2),n("textarea",{directives:[{name:"model",rawName:"v-model",value:t.valueSync,expression:"valueSync"}],ref:"textarea",staticClass:"uni-textarea-textarea",class:{"uni-textarea-textarea-fix-margin":t.fixMargin},style:{"overflow-y":t.autoHeight?"hidden":"auto"},attrs:{disabled:t.disabled,maxlength:t.maxlengthNumber,autofocus:t.autoFocus},domProps:{value:t.valueSync},on:{compositionstart:t._compositionstart,compositionend:t._compositionend,input:[function(e){e.target.composing||(t.valueSync=e.target.value)},function(e){return e.stopPropagation(),t._input(e)}],focus:t._focus,blur:t._blur,"&touchstart":function(e){return t._touchstart(e)}}})])])},r=[],o=n("8af1"),a="(prefers-color-scheme: dark)",s={name:"Textarea",mixins:[o["a"]],props:{name:{type:String,default:""},maxlength:{type:[Number,String],default:140},placeholder:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},placeholderClass:{type:String,default:"textarea-placeholder"},placeholderStyle:{type:String,default:""},autoHeight:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1}},data:function(){return{valueComposition:"",composition:!1,focusSync:this.focus,height:0,focusChangeSource:"",fixMargin:0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(a).media!==a}},computed:{maxlengthNumber:function(){var t=Number(this.maxlength);return isNaN(t)?140:t},cursorNumber:function(){var t=Number(this.cursor);return isNaN(t)?-1:t},selectionStartNumber:function(){var t=Number(this.selectionStart);return isNaN(t)?-1:t},selectionEndNumber:function(){var t=Number(this.selectionEnd);return isNaN(t)?-1:t},valueCompute:function(){return(this.composition?this.valueComposition:this.valueSync).split("\n")}},watch:{focus:function(t){t?(this.focusChangeSource="focus",this.$refs.textarea&&this.$refs.textarea.focus()):this.$refs.textarea&&this.$refs.textarea.blur()},focusSync:function(t){this.$emit("update:focus",t),this._checkSelection(),this._checkCursor()},cursorNumber:function(){this._checkCursor()},selectionStartNumber:function(){this._checkSelection()},selectionEndNumber:function(){this._checkSelection()},height:function(t){var e=parseFloat(getComputedStyle(this.$el).lineHeight);isNaN(e)&&(e=this.$refs.line.offsetHeight);var n=Math.round(t/e);this.$trigger("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:n}),this.autoHeight&&(this.$el.style.height=this.height+"px")}},created:function(){this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this})},mounted:function(){this._resize({height:this.$refs.sensor.$el.offsetHeight});var t=this;while(t){var e=t.$options._scopeId;e&&this.$refs.placeholder.setAttribute(e,""),t=t.$parent}this.initKeyboard(this.$refs.textarea)},beforeDestroy:function(){this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})},methods:{_focus:function(t){this.focusSync=!0,this.$trigger("focus",t,{value:this.valueSync})},_checkSelection:function(){this.focusSync&&!this.focusChangeSource&&this.selectionStartNumber>-1&&this.selectionEndNumber>-1&&(this.$refs.textarea.selectionStart=this.selectionStartNumber,this.$refs.textarea.selectionEnd=this.selectionEndNumber)},_checkCursor:function(){this.focusSync&&("focus"===this.focusChangeSource||!this.focusChangeSource&&this.selectionStartNumber<0&&this.selectionEndNumber<0)&&this.cursorNumber>-1&&(this.$refs.textarea.selectionEnd=this.$refs.textarea.selectionStart=this.cursorNumber)},_blur:function(t){this.focusSync=!1,this.$trigger("blur",t,{value:this.valueSync,cursor:this.$refs.textarea.selectionEnd})},_compositionstart:function(t){this.composition=!0},_compositionend:function(t){this.composition=!1},_confirm:function(t){this.$trigger("confirm",t,{value:this.valueSync})},_linechange:function(t){this.$trigger("linechange",t,{value:this.valueSync})},_touchstart:function(){this.focusChangeSource="touch"},_resize:function(t){var e=t.height;this.height=e},_input:function(t){this.composition?this.valueComposition=t.target.value:this.$triggerInput(t,{value:this.valueSync,cursor:this.$refs.textarea.selectionEnd})},_getFormData:function(){return{value:this.valueSync,key:this.name}},_resetFormData:function(){this.valueSync=""}}},c=s,u=(n("9400"),n("2877")),l=Object(u["a"])(c,i,r,!1,null,null,null);e["default"]=l.exports},5881:function(t,e,n){"use strict";(function(t){var n={ensp:" ",emsp:" ",nbsp:" "};e["a"]={name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},methods:{_decodeHtml:function(t){return this.space&&n[this.space]&&(t=t.replace(/ /g,n[this.space])),this.decode&&(t=t.replace(/&nbsp;/g,n.nbsp).replace(/&ensp;/g,n.ensp).replace(/&emsp;/g,n.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")),t}},render:function(e){var n=this,i=[];return this.$slots.default&&this.$slots.default.forEach((function(r){if(r.text){var o=r.text.replace(/\\n/g,"\n"),a=o.split("\n");a.forEach((function(t,r){i.push(n._decodeHtml(t)),r!==a.length-1&&i.push(e("br"))}))}else r.componentOptions&&"v-uni-text"!==r.componentOptions.tag&&t.warn("<text> 组件内只支持嵌套 <text>，不支持其它组件或自定义组件，否则会引发在不同平台的渲染差异。"),i.push(r)})),e("uni-text",{on:this.$listeners,attrs:{selectable:!!this.selectable}},[e("span",{},i)])}}}).call(this,n("3ad9")["default"])},"594d":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-map",t._g({attrs:{id:t.id}},t.$listeners),[n("div",{ref:"map",staticStyle:{width:"100%",height:"100%",position:"relative",overflow:"hidden"}}),n("div",{staticStyle:{position:"absolute",top:"0",width:"100%",height:"100%",overflow:"hidden","pointer-events":"none"}},[t._t("default")],2)])},r=[],o=n("738e"),a=o["a"],s=(n("3f7e"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},5964:function(t,e,n){"use strict";n.r(e),function(t){function i(e,n){var i=getCurrentPages();if(i.length){var r=i[i.length-1].$holder;switch(e){case"setNavigationBarColor":var o=n.frontColor,a=n.backgroundColor,s=n.animation,c=s.duration,u=s.timingFunc;o&&(r.navigationBar.textColor="#000000"===o?"black":"white"),a&&(r.navigationBar.backgroundColor=a),t.emit("onNavigationBarChange",{textColor:"#000000"===o?"#000":"#fff",backgroundColor:r.navigationBar.backgroundColor}),r.navigationBar.duration=c+"ms",r.navigationBar.timingFunc=u;break;case"showNavigationBarLoading":r.navigationBar.loading=!0;break;case"hideNavigationBarLoading":r.navigationBar.loading=!1;break;case"setNavigationBarTitle":var l=n.title;r.navigationBar.titleText=l,document.title=l,t.emit("onNavigationBarChange",{titleText:l});break}}return{}}function r(t){return i("setNavigationBarColor",t)}function o(){return i("showNavigationBarLoading")}function a(){return i("hideNavigationBarLoading")}function s(t){return i("setNavigationBarTitle",t)}n.d(e,"setNavigationBarColor",(function(){return r})),n.d(e,"showNavigationBarLoading",(function(){return o})),n.d(e,"hideNavigationBarLoading",(function(){return a})),n.d(e,"setNavigationBarTitle",(function(){return s}))}.call(this,n("0dd1"))},"5a23":function(t,e,n){"use strict";(function(t){var i=n("f2b3");function r(){document.activeElement.blur()}function o(){}e["a"]={name:"Keyboard",props:{cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:Boolean,default:!0}},watch:{focus:function(t){t&&this.showSoftKeybord()}},mounted:function(){(this.autoFocus||this.focus)&&this.showSoftKeybord()},beforeDestroy:function(){this.onKeyboardHide()},methods:{initKeyboard:function(e){var n=this;e.addEventListener("focus",(function(){n.hideKeyboardTemp=function(){r()},t.subscribe("hideKeyboard",n.hideKeyboardTemp),document.addEventListener("click",o,!1),n.setSoftinputNavBar(),n.setSoftinputTemporary()})),e.addEventListener("blur",this.onKeyboardHide.bind(this))},showSoftKeybord:function(){Object(i["m"])((function(){plus.key.showSoftKeybord()}))},setSoftinputTemporary:function(){var t=this;Object(i["m"])((function(){var e=plus.webview.currentWebview(),n=e.getStyle()||{},i=t.$el.getBoundingClientRect();e.setSoftinputTemporary&&e.setSoftinputTemporary({mode:"adjustResize"===n.softinputMode?"adjustResize":t.adjustPosition?"adjustPan":"nothing",position:{top:i.top,height:i.height+(Number(t.cursorSpacing)||0)}})}))},setSoftinputNavBar:function(){var t=this;"auto"!==this.showConfirmBar?Object(i["m"])((function(){var e=plus.webview.currentWebview(),n=e.getStyle()||{},i=n.softinputNavBar,r="none"!==i;r!==t.showConfirmBar?(t.__softinputNavBar=i||"auto",e.setStyle({softinputNavBar:t.showConfirmBar?"auto":"none"})):delete t.__softinputNavBar})):delete this.__softinputNavBar},resetSoftinputNavBar:function(){var t=this.__softinputNavBar;t&&Object(i["m"])((function(){var e=plus.webview.currentWebview();e.setStyle({softinputNavBar:t})}))},onKeyboardHide:function(){t.unsubscribe("hideKeyboard",this.hideKeyboardTemp),document.removeEventListener("click",o,!1),this.resetSoftinputNavBar(),0===String(navigator.vendor).indexOf("Apple")&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}}}}).call(this,n("501c"))},"5a56":function(t,e,n){"use strict";n.r(e),e["default"]={methods:{beforeTransition:function(){},afterTransition:function(){}}}},"5ab3":function(t,e,n){"use strict";var i=n("b2bb"),r=n.n(i);r.a},"5abe":function(t,e){(function(){"use strict";if("object"===typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var t=window.document,e=[];i.prototype.THROTTLE_TIMEOUT=100,i.prototype.POLL_INTERVAL=null,i.prototype.USE_MUTATION_OBSERVER=!0,i.prototype.observe=function(t){var e=this._observationTargets.some((function(e){return e.element==t}));if(!e){if(!t||1!=t.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:t,entry:null}),this._monitorIntersections(),this._checkForIntersections()}},i.prototype.unobserve=function(t){this._observationTargets=this._observationTargets.filter((function(e){return e.element!=t})),this._observationTargets.length||(this._unmonitorIntersections(),this._unregisterInstance())},i.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorIntersections(),this._unregisterInstance()},i.prototype.takeRecords=function(){var t=this._queuedEntries.slice();return this._queuedEntries=[],t},i.prototype._initThresholds=function(t){var e=t||[0];return Array.isArray(e)||(e=[e]),e.sort().filter((function(t,e,n){if("number"!=typeof t||isNaN(t)||t<0||t>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return t!==n[e-1]}))},i.prototype._parseRootMargin=function(t){var e=t||"0px",n=e.split(/\s+/).map((function(t){var e=/^(-?\d*\.?\d+)(px|%)$/.exec(t);if(!e)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(e[1]),unit:e[2]}}));return n[1]=n[1]||n[0],n[2]=n[2]||n[0],n[3]=n[3]||n[1],n},i.prototype._monitorIntersections=function(){this._monitoringIntersections||(this._monitoringIntersections=!0,this.POLL_INTERVAL?this._monitoringInterval=setInterval(this._checkForIntersections,this.POLL_INTERVAL):(a(window,"resize",this._checkForIntersections,!0),a(t,"scroll",this._checkForIntersections,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in window&&(this._domObserver=new MutationObserver(this._checkForIntersections),this._domObserver.observe(t,{attributes:!0,childList:!0,characterData:!0,subtree:!0}))))},i.prototype._unmonitorIntersections=function(){this._monitoringIntersections&&(this._monitoringIntersections=!1,clearInterval(this._monitoringInterval),this._monitoringInterval=null,s(window,"resize",this._checkForIntersections,!0),s(t,"scroll",this._checkForIntersections,!0),this._domObserver&&(this._domObserver.disconnect(),this._domObserver=null))},i.prototype._checkForIntersections=function(){var t=this._rootIsInDom(),e=t?this._getRootRect():l();this._observationTargets.forEach((function(i){var o=i.element,a=u(o),s=this._rootContainsTarget(o),c=i.entry,l=t&&s&&this._computeTargetAndRootIntersection(o,e),h=i.entry=new n({time:r(),target:o,boundingClientRect:a,rootBounds:e,intersectionRect:l});c?t&&s?this._hasCrossedThreshold(c,h)&&this._queuedEntries.push(h):c&&c.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)},i.prototype._computeTargetAndRootIntersection=function(e,n){if("none"!=window.getComputedStyle(e).display){var i=u(e),r=i,o=f(e),a=!1;while(!a){var s=null,l=1==o.nodeType?window.getComputedStyle(o):{};if("none"==l.display)return;if(o==this.root||o==t?(a=!0,s=n):o!=t.body&&o!=t.documentElement&&"visible"!=l.overflow&&(s=u(o)),s&&(r=c(s,r),!r))break;o=f(o)}return r}},i.prototype._getRootRect=function(){var e;if(this.root)e=u(this.root);else{var n=t.documentElement,i=t.body;e={top:0,left:0,right:n.clientWidth||i.clientWidth,width:n.clientWidth||i.clientWidth,bottom:n.clientHeight||i.clientHeight,height:n.clientHeight||i.clientHeight}}return this._expandRectByRootMargin(e)},i.prototype._expandRectByRootMargin=function(t){var e=this._rootMarginValues.map((function(e,n){return"px"==e.unit?e.value:e.value*(n%2?t.width:t.height)/100})),n={top:t.top-e[0],right:t.right+e[1],bottom:t.bottom+e[2],left:t.left-e[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},i.prototype._hasCrossedThreshold=function(t,e){var n=t&&t.isIntersecting?t.intersectionRatio||0:-1,i=e.isIntersecting?e.intersectionRatio||0:-1;if(n!==i)for(var r=0;r<this.thresholds.length;r++){var o=this.thresholds[r];if(o==n||o==i||o<n!==o<i)return!0}},i.prototype._rootIsInDom=function(){return!this.root||h(t,this.root)},i.prototype._rootContainsTarget=function(e){return h(this.root||t,e)},i.prototype._registerInstance=function(){e.indexOf(this)<0&&e.push(this)},i.prototype._unregisterInstance=function(){var t=e.indexOf(this);-1!=t&&e.splice(t,1)},window.IntersectionObserver=i,window.IntersectionObserverEntry=n}function n(t){this.time=t.time,this.target=t.target,this.rootBounds=t.rootBounds,this.boundingClientRect=t.boundingClientRect,this.intersectionRect=t.intersectionRect||l(),this.isIntersecting=!!t.intersectionRect;var e=this.boundingClientRect,n=e.width*e.height,i=this.intersectionRect,r=i.width*i.height;this.intersectionRatio=n?Number((r/n).toFixed(4)):this.isIntersecting?1:0}function i(t,e){var n=e||{};if("function"!=typeof t)throw new Error("callback must be a function");if(n.root&&1!=n.root.nodeType)throw new Error("root must be an Element");this._checkForIntersections=o(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=t,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map((function(t){return t.value+t.unit})).join(" ")}function r(){return window.performance&&performance.now&&performance.now()}function o(t,e){var n=null;return function(){n||(n=setTimeout((function(){t(),n=null}),e))}}function a(t,e,n,i){"function"==typeof t.addEventListener?t.addEventListener(e,n,i||!1):"function"==typeof t.attachEvent&&t.attachEvent("on"+e,n)}function s(t,e,n,i){"function"==typeof t.removeEventListener?t.removeEventListener(e,n,i||!1):"function"==typeof t.detatchEvent&&t.detatchEvent("on"+e,n)}function c(t,e){var n=Math.max(t.top,e.top),i=Math.min(t.bottom,e.bottom),r=Math.max(t.left,e.left),o=Math.min(t.right,e.right),a=o-r,s=i-n;return a>=0&&s>=0&&{top:n,bottom:i,left:r,right:o,width:a,height:s}}function u(t){var e;try{e=t.getBoundingClientRect()}catch(n){}return e?(e.width&&e.height||(e={top:e.top,right:e.right,bottom:e.bottom,left:e.left,width:e.right-e.left,height:e.bottom-e.top}),e):l()}function l(){return{top:0,bottom:0,left:0,right:0,width:0,height:0}}function h(t,e){var n=e;while(n){if(n==t)return!0;n=f(n)}return!1}function f(t){var e=t.parentNode;return e&&11==e.nodeType&&e.host?e.host:e&&e.assignedSlot?e.assignedSlot.parentNode:e}})()},"5d1d":function(t,e,n){"use strict";var i=n("50c5"),r=n.n(i);r.a},"5d70":function(t,e,n){},"5dc1":function(t,e,n){"use strict";(function(t){n.d(e,"b",(function(){return s})),n.d(e,"a",(function(){return c}));n("5abe");var i=n("85b6"),r=n("db8e");function o(t){return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}var a={};function s(e,n){var s=e.reqId,c=e.component,u=e.options,l=getCurrentPages(),h=l.find((function(t){return t.$page.id===n}));if(!h)throw new Error("Not Found：Page[".concat(n,"]"));var f=h.$vm,d=Object(r["a"])(c,f),p=u.relativeToSelector?d.querySelector(u.relativeToSelector):null,g=a[s]=new IntersectionObserver((function(e,n){e.forEach((function(e){t.publishHandler("onRequestComponentObserver",{reqId:s,res:{intersectionRatio:e.intersectionRatio,intersectionRect:o(e.intersectionRect),boundingClientRect:o(e.boundingClientRect),relativeRect:o(e.rootBounds),time:Date.now(),dataset:Object(i["c"])(e.target.dataset||{}),id:e.target.id}},f.$page.id)}))}),{root:p,rootMargin:u.rootMargin,threshold:u.thresholds});u.observeAll?(g.USE_MUTATION_OBSERVER=!0,Array.prototype.map.call(d.querySelectorAll(u.selector),(function(t){g.observe(t)}))):(g.USE_MUTATION_OBSERVER=!1,g.observe(d.querySelector(u.selector)))}function c(e){var n=e.reqId,i=a[n];i&&(i.disconnect(),delete a[n],t.publishHandler("onRequestComponentObserver",{reqId:n,reqEnd:!0}))}}).call(this,n("501c"))},"5dc4":function(t,e,n){},"5ff9":function(t,e,n){"use strict";n.r(e),n.d(e,"loadFontFace",(function(){return i}));var i={family:{type:String,required:!0},source:{type:String,required:!0},desc:{type:Object,required:!1},success:{type:Function,required:!1},fail:{type:Function,required:!1},complete:{type:Function,required:!1}}},6062:function(t,e,n){"use strict";var i=n("ef36"),r=n.n(i);r.a},"60db":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"EditorContext",(function(){return u}));var i=n("f2b3");function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}function s(e,n,i,r){t.publishHandler(n+"-editor-"+e,{componentId:e,type:i,data:r},n)}t.subscribe("onEditorMethodCallback",(function(t){var e=t.callbackId,n=t.data;i["a"].invoke(e,n)}));var c=["insertDivider","insertImage","insertText","setContents","getContents","clear","removeFormat","undo","redo"],u=function(){function t(e,n){r(this,t),this.id=e,this.pageId=n}return a(t,[{key:"format",value:function(t,e){s(this.id,this.pageId,"format",{options:{name:t,value:e}})}}]),t}();c.forEach((function(t){u.prototype[t]=i["a"].warp((function(e,n){s(this.id,this.pageId,t,{options:e,callbackId:n})}))}))}.call(this,n("0dd1"))},"60ee":function(t,e,n){},"61c2":function(t,e,n){"use strict";n.d(e,"a",(function(){return l}));var i=n("f2b3"),r=n("8af1");function o(){this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this})}function a(){this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})}var s={name:"uni://form-field",init:function(t,e){e.constructor.options.props&&e.constructor.options.props.name&&e.constructor.options.props.value||(e.constructor.options.props||(e.constructor.options.props={}),e.constructor.options.props.name||(e.constructor.options.props.name=t.props.name={type:String}),e.constructor.options.props.value||(e.constructor.options.props.value=t.props.value={type:null})),t.propsData||(t.propsData={});var n=e.$vnode;if(n&&n.data&&n.data.attrs&&(Object(i["h"])(n.data.attrs,"name")&&(t.propsData.name=n.data.attrs.name),Object(i["h"])(n.data.attrs,"value")&&(t.propsData.value=n.data.attrs.value)),!e.constructor.options.methods||!e.constructor.options.methods._getFormData){e.constructor.options.methods||(e.constructor.options.methods={}),t.methods||(t.methods={});var s={_getFormData:function(){return this.name?{key:this.name,value:this.value}:{}},_resetFormData:function(){this.value=""}};Object.assign(e.constructor.options.methods,s),Object.assign(t.methods,s),Object.assign(e.constructor.options.methods,r["b"].methods),Object.assign(t.methods,r["b"].methods);var c=t.created;e.constructor.options.created=t.created=c?[].concat(o,c):[o];var u=t.beforeDestroy;e.constructor.options.beforeDestroy=t.beforeDestroy=u?[].concat(a,u):[a]}}};function c(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var u=c({},s.name,s);function l(t,e){t.behaviors.forEach((function(n){var i=u[n];i&&i.init(t,e)}))}},6226:function(t,e,n){"use strict";var i=n("77d5"),r=n.n(i);r.a},"626d":function(t,e,n){"use strict";n.r(e),function(t){var i=n("f2b3");e["default"]={data:function(){return{showActionSheet:{visible:!1}}},created:function(){var e=this;t.on("onShowActionSheet",(function(t,n){e.showActionSheet=t,e.onActionSheetCloseCallback=n})),t.on("onHidePopup",(function(t){e.showActionSheet.visible=!1}))},methods:{_onActionSheetClose:function(t){this.showActionSheet.visible=!1,Object(i["j"])(this.onActionSheetCloseCallback)&&this.onActionSheetCloseCallback(t)}}}}.call(this,n("0dd1"))},"62b5":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var i={};function r(t){var e=i[t];return e||(e={id:1,callbacks:Object.create(null)},i[t]=e),{get:function(t){return e.callbacks[t]},pop:function(t){var n=e.callbacks[t];return n&&delete e.callbacks[t],n},push:function(t){var n=e.id++;return e.callbacks[n]=t,n}}}},6389:function(e,n){e.exports=t},6428:function(t,e,n){"use strict";var i=n("f756"),r=n.n(i);r.a},6481:function(t,e,n){"use strict";n.r(e),n.d(e,"base64ToArrayBuffer",(function(){return i})),n.d(e,"arrayBufferToBase64",(function(){return r}));var i=[{name:"base64",type:String,required:!0}],r=[{name:"arrayBuffer",type:[ArrayBuffer,Uint8Array],required:!0}]},6491:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-radio",t._g({attrs:{disabled:t.disabled},on:{click:t._onClick}},t.$listeners),[n("div",{staticClass:"uni-radio-wrapper"},[n("div",{staticClass:"uni-radio-input",class:t.radioChecked?"uni-radio-input-checked":"",style:t.radioChecked?t.checkedStyle:""}),t._t("default")],2)])},r=[],o=n("8af1"),a={name:"Radio",mixins:[o["b"],o["f"]],props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#007AFF"},value:{type:String,default:""}},data:function(){return{radioChecked:this.checked,radioValue:this.value}},computed:{checkedStyle:function(){return"background-color: ".concat(this.color,";border-color: ").concat(this.color,";")}},watch:{checked:function(t){this.radioChecked=t},value:function(t){this.radioValue=t}},listeners:{"label-click":"_onClick","@label-click":"_onClick"},created:function(){this.$dispatch("RadioGroup","uni-radio-group-update",{type:"add",vm:this}),this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this})},beforeDestroy:function(){this.$dispatch("RadioGroup","uni-radio-group-update",{type:"remove",vm:this}),this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})},methods:{_onClick:function(t){this.disabled||this.radioChecked||(this.radioChecked=!0,this.$dispatch("RadioGroup","uni-radio-change",t,this))},_resetFormData:function(){this.radioChecked=this.min}}},s=a,c=(n("c96e"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},"64d0":function(t,e,n){"use strict";var i=n("c0e5"),r=n.n(i);r.a},6575:function(t,e,n){"use strict";n.r(e),function(t){function i(e,n){var i=e.latitude,r=e.longitude,o=e.scale,a=e.name,s=e.address,c=t,u=c.invokeCallbackHandler;getApp().$router.push({type:"navigateTo",path:"/open-location",query:{latitude:i,longitude:r,scale:o,name:a,address:s}},(function(){u(n,{errMsg:"openLocation:ok"})}),(function(){u(n,{errMsg:"openLocation:fail"})}))}n.d(e,"openLocation",(function(){return i}))}.call(this,n("0dd1"))},"65a8":function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return r}));var i=44,r=50},"65f0":function(t,e,n){"use strict";(function(t){e["a"]={name:"SystemHeader",props:{confirm:{type:Boolean,default:!1}},created:function(){document.title=this.$slots.default[0].text,t.emit("onNavigationBarChange",{titleText:document.title,textColor:"#fff",backgroundColor:"#000"})},methods:{_back:function(){this.$emit("back")},_confirm:function(){this.$emit("confirm")}}}}).call(this,n("0dd1"))},6730:function(t,e,n){"use strict";var i=n("00b2"),r=n.n(i);r.a},"69c3":function(t,e,n){},"6bdf":function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return u}));var i=n("85b6"),r=n("a470"),o=n("db8e");function a(t){var e={};return t.id&&(e.id=""),t.dataset&&(e.dataset={}),t.rect&&(e.left=0,e.right=0,e.top=0,e.bottom=0),t.size&&(e.width=document.documentElement.clientWidth,e.height=document.documentElement.clientHeight),t.scrollOffset&&(e.scrollLeft=document.documentElement.scrollLeft||document.body.scrollLeft||0,e.scrollTop=document.documentElement.scrollTop||document.body.scrollTop||0),e}function s(t,e){var n={},o=Object(r["a"])(),a=o.top;if(e.id&&(n.id=t.id),e.dataset&&(n.dataset=Object(i["c"])(t.dataset||{})),e.rect||e.size){var s=t.getBoundingClientRect();e.rect&&(n.left=s.left,n.right=s.right,n.top=s.top-a,n.bottom=s.bottom-a),e.size&&(n.width=s.width,n.height=s.height)}return e.properties&&e.properties.forEach((function(t){t=t.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()}))})),e.scrollOffset&&("UNI-SCROLL-VIEW"===t.tagName&&t.__vue__&&t.__vue__.getScrollPosition?Object.assign(n,t.__vue__.getScrollPosition()):(n.scrollLeft=0,n.scrollTop=0)),e.context&&t.__vue__&&t.__vue__._getContextInfo&&(n.context=t.__vue__._getContextInfo()),n}function c(t,e,n,i,r){var a=Object(o["a"])(e,t);if(!a||a&&8===a.nodeType)return i?null:[];if(i){var c=a.matches(n)?a:a.querySelector(n);return c?s(c,r):null}var u=[],l=a.querySelectorAll(n);return l&&l.length&&(u=[].map.call(l,(function(t){return s(t,r)}))),a.matches(n)&&u.unshift(s(a,r)),u}function u(e,n){var i=e.reqId,r=e.reqs,o=getCurrentPages(),s=o.find((function(t){return t.$page.id===n}));if(!s)throw new Error("Not Found：Page[".concat(n,"]"));var u=s.$vm,l=[];r.forEach((function(t){var e=t.component,n=t.selector,i=t.single,r=t.fields;0===e?l.push(a(r)):l.push(c(u,e,n,i,r))})),t.publishHandler("onRequestComponentInfo",{reqId:i,res:l},u.$page.id)}}).call(this,n("501c"))},"6e0c":function(t,e,n){"use strict";n.r(e),n.d(e,"$on",(function(){return s})),n.d(e,"$off",(function(){return c})),n.d(e,"$once",(function(){return u})),n.d(e,"$emit",(function(){return l}));var i=n("8bbf"),r=n.n(i),o=new r.a;function a(t,e,n){return t[e].apply(t,n)}function s(){return a(o,"$on",Array.prototype.slice.call(arguments))}function c(){return a(o,"$off",Array.prototype.slice.call(arguments))}function u(){return a(o,"$once",Array.prototype.slice.call(arguments))}function l(){return a(o,"$emit",Array.prototype.slice.call(arguments))}},"6fa7":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-picker",t._g({attrs:{disabled:t.disabled},on:{click:t._show}},t.$listeners),[n("div",{ref:"picker",staticClass:"uni-picker-container",on:{touchmove:function(t){t.preventDefault()}}},[n("transition",{attrs:{name:"uni-fade"}},[n("div",{directives:[{name:"show",rawName:"v-show",value:t.visible,expression:"visible"}],staticClass:"uni-mask",on:{click:t._cancel}})]),n("div",{staticClass:"uni-picker",class:{"uni-picker-toggle":t.visible}},[n("div",{staticClass:"uni-picker-header",on:{click:function(t){t.stopPropagation()}}},[n("div",{staticClass:"uni-picker-action uni-picker-action-cancel",on:{click:t._cancel}},[t._v(" 取消 ")]),n("div",{staticClass:"uni-picker-action uni-picker-action-confirm",on:{click:t._change}},[t._v(" 确定 ")])]),t.visible?n("v-uni-picker-view",{staticClass:"uni-picker-content",attrs:{value:t.valueArray},on:{"update:value":function(e){t.valueArray=e}}},t._l(t.rangeArray,(function(e,i){return n("v-uni-picker-view-column",{key:i},t._l(e,(function(e,r){return n("div",{key:r,staticClass:"uni-picker-item"},[t._v(" "+t._s("object"===typeof e?e[t.rangeKey]||"":e)+t._s(t.units[i]||"")+" ")])})),0)})),1):t._e()],1)],1),n("div",[t._t("default")],2)])},r=[],o=n("8af1"),a=n("f2b3");function s(t){return h(t)||l(t)||u(t)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"===typeof t)return f(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(t,e):void 0}}function l(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function h(t){if(Array.isArray(t))return f(t)}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function d(){if(this.mode===g.TIME)return"00:00";if(this.mode===g.DATE){var t=(new Date).getFullYear()-100;switch(this.fields){case v.YEAR:return t;case v.MONTH:return t+"-01";case v.DAY:return t+"-01-01"}}return""}function p(){if(this.mode===g.TIME)return"23:59";if(this.mode===g.DATE){var t=(new Date).getFullYear()+100;switch(this.fields){case v.YEAR:return t;case v.MONTH:return t+"-12";case v.DAY:return t+"-12-31"}}return""}var g={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},v={YEAR:"year",MONTH:"month",DAY:"day"},m={name:"Picker",mixins:[o["b"]],props:{name:{type:String,default:""},range:{type:Array,default:function(){return[]}},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:g.SELECTOR,validator:function(t){return Object.values(g).indexOf(t)>=0}},fields:{type:String,default:"day",validator:function(t){return Object.values(v).indexOf(t)>=0}},start:{type:String,default:d},end:{type:String,default:p},disabled:{type:[Boolean,String],default:!1}},data:function(){return{valueSync:null,visible:!1,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[]}},computed:{rangeArray:function(){var t=this.range;switch(this.mode){case g.SELECTOR:return[t];case g.MULTISELECTOR:return t;case g.TIME:return this.timeArray;case g.DATE:var e=this.dateArray;switch(this.fields){case v.YEAR:return[e[0]];case v.MONTH:return[e[0],e[1]];case v.DAY:return[e[0],e[1],e[2]]}}return[]},startArray:function(){return this._getDateValueArray(this.start,d.bind(this)())},endArray:function(){return this._getDateValueArray(this.end,p.bind(this)())},units:function(){switch(this.mode){case g.DATE:return["年","月","日"];case g.TIME:return["时","分"];default:return[]}}},watch:{value:function(){this._setValueSync()},mode:function(){this._setValueSync()},range:function(){this._setValueSync()},valueSync:function(){this._setValueArray()},valueArray:function(t){var e=this;if(this.mode===g.TIME||this.mode===g.DATE){var n=this.mode===g.TIME?this._getTimeValue:this._getDateValue,i=this.valueArray,r=this.startArray,o=this.endArray;if(this.mode===g.DATE){var a=this.dateArray,s=a[2].length,c=Number(a[2][i[2]])||1,u=new Date("".concat(a[0][i[0]],"/").concat(a[1][i[1]],"/").concat(c)).getDate();u<c&&(i[2]-=u+s-c)}n(i)<n(r)?this._cloneArray(i,r):n(i)>n(o)&&this._cloneArray(i,o)}t.forEach((function(t,n){t!==e.oldValueArray[n]&&(e.oldValueArray[n]=t,e.mode===g.MULTISELECTOR&&e.$trigger("columnchange",{},{column:n,value:t}))}))}},created:function(){this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this}),this._createTime(),this._createDate(),this._setValueSync()},beforeDestroy:function(){this.$refs.picker.remove(),this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})},methods:{_show:function(){var t=this;if(!this.disabled){this.valueChangeSource="";var e=this.$refs.picker;e.remove(),(document.querySelector("uni-app")||document.body).appendChild(e),e.style.display="block",setTimeout((function(){t.visible=!0}),20)}},_getFormData:function(){return{value:this.valueSync,key:this.name}},_resetFormData:function(){switch(this.mode){case g.SELECTOR:this.valueSync=-1;break;case g.MULTISELECTOR:this.valueSync=this.value.map((function(t){return 0}));break;case g.DATE:case g.TIME:this.valueSync="";break;default:break}},_createTime:function(){var t=[],e=[];t.splice(0,t.length);for(var n=0;n<24;n++)t.push((n<10?"0":"")+n);e.splice(0,e.length);for(var i=0;i<60;i++)e.push((i<10?"0":"")+i);this.timeArray.push(t,e)},_createDate:function(){for(var t=[],e=(new Date).getFullYear(),n=e-150,i=e+150;n<=i;n++)t.push(String(n));for(var r=[],o=1;o<=12;o++)r.push((o<10?"0":"")+o);for(var a=[],s=1;s<=31;s++)a.push((s<10?"0":"")+s);this.dateArray.push(t,r,a)},_getTimeValue:function(t){return 60*t[0]+t[1]},_getDateValue:function(t){return 366*t[0]+31*(t[1]||0)+(t[2]||0)},_cloneArray:function(t,e){for(var n=0;n<t.length&&n<e.length;n++)t[n]=e[n]},_setValueSync:function(){var t=this.value;switch(this.mode){case g.MULTISELECTOR:Array.isArray(t)||(t=[]),Array.isArray(this.valueSync)||(this.valueSync=[]);for(var e=this.valueSync.length=Math.max(t.length,this.range.length),n=0;n<e;n++){var i=Number(t[n]),r=Number(this.valueSync[n]),o=isNaN(i)?isNaN(r)?0:r:i,a=this.range[n]?this.range[n].length-1:0;this.valueSync.splice(n,1,o>a?0:o)}break;case g.TIME:case g.DATE:this.valueSync=String(t);break;default:this.valueSync=Number(t)||0;break}},_setValueArray:function(){var t,e=this.valueSync;switch(this.mode){case g.MULTISELECTOR:t=s(e);break;case g.TIME:t=this._getDateValueArray(e,Object(a["f"])({mode:g.TIME}));break;case g.DATE:t=this._getDateValueArray(e,Object(a["f"])({mode:g.DATE}));break;default:t=[e];break}this.oldValueArray=s(t),this.valueArray=s(t)},_getValue:function(){var t=this,e=this.valueArray;switch(this.mode){case g.SELECTOR:return e[0];case g.MULTISELECTOR:return e.map((function(t){return t}));case g.TIME:return this.valueArray.map((function(e,n){return t.timeArray[n][e]})).join(":");case g.DATE:return this.valueArray.map((function(e,n){return t.dateArray[n][e]})).join("-")}},_getDateValueArray:function(t,e){var n,i=this.mode===g.DATE?"-":":",r=this.mode===g.DATE?this.dateArray:this.timeArray;if(this.mode===g.TIME)n=2;else switch(this.fields){case v.YEAR:n=1;break;case v.MONTH:n=2;break;default:n=3;break}for(var o=String(t).split(i),a=[],s=0;s<n;s++){var c=o[s];a.push(r[s].indexOf(c))}return a.indexOf(-1)>=0&&(a=e?this._getDateValueArray(e):a.map((function(){return 0}))),a},_change:function(){this._close(),this.valueChangeSource="click";var t=this._getValue();this.valueSync=Array.isArray(t)?t.map((function(t){return t})):t,this.$trigger("change",{},{value:t})},_cancel:function(){this._close(),this.$trigger("cancel",{},{})},_close:function(){var t=this;this.visible=!1,setTimeout((function(){var e=t.$refs.picker;e.remove(),t.$el.prepend(e),e.style.display="none"}),260)}}},b=m,y=(n("2d89"),n("2877")),_=Object(y["a"])(b,i,r,!1,null,null,null);e["default"]=_.exports},"70bb":function(t,e,n){"use strict";n.r(e),n.d(e,"openLocation",(function(){return i}));var i={latitude:{type:Number,required:!0},longitude:{type:Number,required:!0},scale:{type:Number,validator:function(t,e){t=Math.floor(t),e.scale=t>=5&&t<=18?t:18},default:18},name:{type:String},address:{type:String}}},"70f4":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-label",t._g({class:{"uni-label-pointer":t.pointer},on:{click:t._onClick}},t.$listeners),[t._t("default")],2)},r=[],o=n("f2ce"),a=o["a"],s=(n("6730"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},7107:function(t,e,n){"use strict";(function(t){function i(e){var n=e.options,i=e.callbackId,r=n.family,o=n.source,a=n.desc,s=void 0===a?{}:a,c=document.fonts;if(c){var u=new FontFace(r,o,s);u.load().then((function(){c.add(u),t.publishHandler("onLoadFontFaceCallback",{callbackId:i,data:{errMsg:"loadFontFace:ok"}})})).catch((function(e){t.publishHandler("onLoadFontFaceCallback",{callbackId:i,data:{errMsg:"loadFontFace:fail ".concat(e)}})}))}else{var l=document.createElement("style");l.innerText='@font-face{font-family:"'.concat(r,'";src:').concat(o,";font-style:").concat(s.style,";font-weight:").concat(s.weight,";font-stretch:").concat(s.stretch,";unicode-range:").concat(s.unicodeRange,";font-variant:").concat(s.variant,";font-feature-settings:").concat(s.featureSettings,";}"),document.head.appendChild(l),t.publishHandler("onLoadFontFaceCallback",{callbackId:i,data:{errMsg:"loadFontFace:ok"}})}}n.d(e,"a",(function(){return i}))}).call(this,n("501c"))},"72ad":function(t,e,n){},"72b3":function(t,e,n){"use strict";function i(t,e,n){return t>e-n&&t<e+n}function r(t,e){return i(t,0,e)}function o(t,e,n){this._m=t,this._k=e,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}n.d(e,"a",(function(){return o})),o.prototype._solve=function(t,e){var n=this._c,i=this._m,r=this._k,o=n*n-4*i*r;if(0===o){var a=-n/(2*i),s=t,c=e/(a*t);return{x:function(t){return(s+c*t)*Math.pow(Math.E,a*t)},dx:function(t){var e=Math.pow(Math.E,a*t);return a*(s+c*t)*e+c*e}}}if(o>0){var u=(-n-Math.sqrt(o))/(2*i),l=(-n+Math.sqrt(o))/(2*i),h=(e-u*t)/(l-u),f=t-h;return{x:function(t){var e,n;return t===this._t&&(e=this._powER1T,n=this._powER2T),this._t=t,e||(e=this._powER1T=Math.pow(Math.E,u*t)),n||(n=this._powER2T=Math.pow(Math.E,l*t)),f*e+h*n},dx:function(t){var e,n;return t===this._t&&(e=this._powER1T,n=this._powER2T),this._t=t,e||(e=this._powER1T=Math.pow(Math.E,u*t)),n||(n=this._powER2T=Math.pow(Math.E,l*t)),f*u*e+h*l*n}}}var d=Math.sqrt(4*i*r-n*n)/(2*i),p=-n/2*i,g=t,v=(e-p*t)/d;return{x:function(t){return Math.pow(Math.E,p*t)*(g*Math.cos(d*t)+v*Math.sin(d*t))},dx:function(t){var e=Math.pow(Math.E,p*t),n=Math.cos(d*t),i=Math.sin(d*t);return e*(v*d*n-g*d*i)+p*e*(v*i+g*n)}}},o.prototype.x=function(t){return void 0===t&&(t=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(t):0},o.prototype.dx=function(t){return void 0===t&&(t=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(t):0},o.prototype.setEnd=function(t,e,n){if(n||(n=(new Date).getTime()),t!==this._endPosition||!r(e,.4)){e=e||0;var i=this._endPosition;this._solution&&(r(e,.4)&&(e=this._solution.dx((n-this._startTime)/1e3)),i=this._solution.x((n-this._startTime)/1e3),r(e,.4)&&(e=0),r(i,.4)&&(i=0),i+=this._endPosition),this._solution&&r(i-t,.4)&&r(e,.4)||(this._endPosition=t,this._solution=this._solve(i-this._endPosition,e),this._startTime=n)}},o.prototype.snap=function(t){this._startTime=(new Date).getTime(),this._endPosition=t,this._solution={x:function(){return 0},dx:function(){return 0}}},o.prototype.done=function(t){return t||(t=(new Date).getTime()),i(this.x(),this._endPosition,.4)&&r(this.dx(),.4)},o.prototype.reconfigure=function(t,e,n){this._m=t,this._k=e,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},o.prototype.springConstant=function(){return this._k},o.prototype.damping=function(){return this._c},o.prototype.configuration=function(){function t(t,e){t.reconfigure(1,e,t.damping())}function e(t,e){t.reconfigure(1,t.springConstant(),e)}return[{label:"Spring Constant",read:this.springConstant.bind(this),write:t.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:e.bind(this,this),min:1,max:500}]}},"738e":function(t,e,n){"use strict";(function(t){var i,r,o=n("8af1"),a=n("f2b3");function s(t){if(i)t();else if(window.qq&&window.qq.maps)i=window.qq.maps,t();else if(r)r.push(t);else{r=[t];var e=__uniConfig.qqMapKey,n="_callback"+Date.now();window[n]=function(){delete window[n],i=window.qq.maps;var t=i.Callout=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.option=t;var e=t.map;this.position=t.position,this.index=1,this.visible=this.alwaysVisible="ALWAYS"===t.display,this.init(),Object.defineProperty(this,"onclick",{setter:function(t){this.div.onclick=t},getter:function(){return this.div.onclick}}),e&&this.setMap(e)};t.prototype=new i.Overlay,t.prototype.init=function(){var t=this.option,e=this.div=document.createElement("div"),n=e.style;n.position="absolute",n.whiteSpace="nowrap",n.transform="translateX(-50%) translateY(-100%)",n.zIndex=1,n.boxShadow=t.boxShadow||"none",n.display=this.visible?"block":"none";var i=this.triangle=document.createElement("div");i.setAttribute("style","position: absolute;white-space: nowrap;border-width: 4px;border-style: solid;border-color: #fff transparent transparent;border-image: initial;font-size: 12px;padding: 0px;background-color: transparent;width: 0px;height: 0px;transform: translate(-50%, 100%);left: 50%;bottom: 0;"),this.setStyle(t),this.changed=function(t){n.display=this.visible?"block":"none"},e.appendChild(i)},t.prototype.construct=function(){var t=this.div,e=this.getPanes();e.floatPane.appendChild(t)},t.prototype.draw=function(){var t=this.getProjection();if(this.position&&this.div&&t){var e=t.fromLatLngToDivPixel(this.position),n=this.div.style;n.left=e.x+"px",n.top=e.y+"px"}},t.prototype.destroy=function(){this.div.parentNode.removeChild(this.div),this.div=null,this.triangle=null},t.prototype.setOption=function(t){this.option=t,this.setPosition(t.position),"ALWAYS"===t.display?this.alwaysVisible=this.visible=!0:this.alwaysVisible=!1,this.setStyle(t)},t.prototype.setStyle=function(t){var e=this.div,n=e.style;e.innerText=t.content,n.lineHeight=(t.fontSize||14)+"px",n.fontSize=(t.fontSize||14)+"px",n.padding=(t.padding||8)+"px",n.color=t.color||"#000",n.borderRadius=(t.borderRadius||0)+"px",n.backgroundColor=t.bgColor||"#fff",n.marginTop="-"+(t.top+5)+"px",this.triangle.style.borderColor="".concat(t.bgColor||"#fff"," transparent transparent")},t.prototype.setPosition=function(t){this.position=t,this.draw()},r.forEach((function(t){return t()})),r=null};var o=document.createElement("script");o.src="https://map.qq.com/api/js?v=2.exp&key=".concat(e,"&callback=").concat(n,"&libraries=geometry"),document.body.appendChild(o)}}e["a"]={name:"Map",mixins:[o["g"]],props:{id:{type:String,default:""},latitude:{type:[String,Number],default:39.92},longitude:{type:[String,Number],default:116.46},scale:{type:[String,Number],default:16},markers:{type:Array,default:function(){return[]}},covers:{type:Array,default:function(){return[]}},includePoints:{type:Array,default:function(){return[]}},polyline:{type:Array,default:function(){return[]}},circles:{type:Array,default:function(){return[]}},controls:{type:Array,default:function(){return[]}},showLocation:{type:[Boolean,String],default:!1}},data:function(){return{center:{latitude:116.46,longitude:116.46},isMapReady:!1,isBoundsReady:!1,markersSync:[],polylineSync:[],circlesSync:[],controlsSync:[]}},watch:{latitude:function(){this.centerChange()},longitude:function(){this.centerChange()},scale:function(t){var e=this;this.mapReady((function(){e._map.setZoom(Number(t)||16)}))},markers:function(t,e){var n=this;this.mapReady((function(){var i=[],r=[],o=[],a=[],s=[];t.forEach((function(t){if("id"in t){for(var n=!1,s=0;s<e.length;s++){var c=e[s];"id"in c?c.id===t.id&&(n=!0,r.push(c.id),JSON.stringify(c)!==JSON.stringify(t)&&(o.push(c.id),a.push(t)),e.splice(s--,1)):e.splice(s--,1)}n||i.push(t)}else i.push(t)}));var c=n.markersSync;c.forEach((function(t){var e,i=t.id;r.indexOf(i)>=0?(e=o.indexOf(i))>=0&&n.changeMarker(t,a[e]):s.push(t)})),n.removeMarkers(s),n.createMarkers(i)}))},polyline:function(t){var e=this;this.mapReady((function(){e.createPolyline()}))},circles:function(){var t=this;this.mapReady((function(){t.createCircles()}))},controls:function(){var t=this;this.mapReady((function(){t.createControls()}))},includePoints:function(){var t=this;this.mapReady((function(){t.fitBounds(t.includePoints)}))},showLocation:function(t){var e=this;this.mapReady((function(){e[t?"createLocation":"removeLocation"]()}))}},created:function(){var t=this.latitude,e=this.longitude;t&&e&&(this.center.latitude=t,this.center.longitude=e)},mounted:function(){var t=this;s((function(){t.init()}))},beforeDestroy:function(){this.removeMarkers(this.markersSync),this.removePolyline(),this.removeCircles(),this.removeControls(),this.removeLocation()},methods:{_handleSubscribe:function(t){var e=this,n=t.type,r=t.data,o=void 0===r?{}:r;function a(t,e){t=t||{},t.errMsg="".concat(n,":").concat(e?"fail"+e:"ok");var i=e?o.fail:o.success;"function"===typeof i&&i(t),"function"===typeof o.complete&&o.complete(t)}switch(n){case"getCenterLocation":this.mapReady((function(){var t,n,i=e._map.getCenter();t=i.getLat(),n=i.getLng(),a({latitude:t,longitude:n})}));break;case"moveToLocation":var s=o.latitude,c=o.longitude,u=s&&c?new i.LatLng(s,c):this._locationPosition;u&&(this._map.setCenter(u),a({}));break;case"translateMarker":this.mapReady((function(){try{var t=e.getMarker(o.markerId),n=o.destination,r=o.duration,s=!!o.autoRotate,c=Number(o.rotate)?o.rotate:0,u=t.getRotation(),l=t.getPosition(),h=new i.LatLng(n.latitude,n.longitude),f=i.geometry.spherical.computeDistanceBetween(l,h)/1e3,d=("number"===typeof r?r:1e3)/36e5,p=f/d,g=i.event.addListener(t,"moving",(function(e){var n=e.latLng,i=t.label;i&&i.setPosition(n);var r=t.callout;r&&r.setPosition(n)})),v=i.event.addListener(t,"moveend",(function(e){v.remove(),g.remove(),t.lastPosition=l,t.setPosition(h);var n=t.label;n&&n.setPosition(h);var i=t.callout;i&&i.setPosition(h);var r=o.animationEnd;"function"===typeof r&&r()})),m=0;s&&(t.lastPosition&&(m=i.geometry.spherical.computeHeading(t.lastPosition,l)),c=i.geometry.spherical.computeHeading(l,h)-m),t.setRotation(u+c),t.moveTo(h,p)}catch(b){a(null,b)}}));break;case"includePoints":this.fitBounds(o.points);break;case"getRegion":this.boundsReady((function(){var t=e._map.getBounds(),n=t.getSouthWest(),i=t.getNorthEast();a({southwest:{latitude:n.getLat(),longitude:n.getLng()},northeast:{latitude:i.getLat(),longitude:i.getLng()}})}));break;case"getScale":this.mapReady((function(){a({scale:Number(e.scale)})}));break}},init:function(){var t=this,e=new i.LatLng(this.center.latitude,this.center.longitude),n=this._map=new i.Map(this.$refs.map,{center:e,zoom:Number(this.scale),scrollwheel:!1,disableDoubleClickZoom:!0,mapTypeControl:!1,zoomControl:!1,scaleControl:!1,minZoom:5,maxZoom:18,draggable:!0}),r=i.event.addListener(n,"bounds_changed",(function(e){r.remove(),t.isBoundsReady=!0,t.$emit("boundsready")}));i.event.addListener(n,"click",(function(){t.$trigger("click",{},{})})),i.event.addListener(n,"dragstart",(function(){t.$trigger("regionchange",{},{type:"begin"})})),i.event.addListener(n,"dragend",(function(){t.$trigger("regionchange",{},{type:"end"})})),i.event.addListener(n,"zoom_changed",(function(){t.$emit("update:scale",n.getZoom())})),i.event.addListener(n,"center_changed",(function(){var e,i,r=n.getCenter();e=r.getLat(),i=r.getLng(),t.$emit("update:latitude",e),t.$emit("update:longitude",i)})),this.markers&&Array.isArray(this.markers)&&this.markers.length&&this.createMarkers(this.markers),this.polyline&&Array.isArray(this.polyline)&&this.polyline.length&&this.createPolyline(),this.circles&&Array.isArray(this.circles)&&this.circles.length&&this.createCircles(),this.controls&&Array.isArray(this.controls)&&this.controls.length&&this.createControls(),this.showLocation&&this.createLocation(),this.includePoints&&Array.isArray(this.includePoints)&&this.includePoints.length&&this.fitBounds(this.includePoints,(function(){n.setCenter(e)})),this.isMapReady=!0,this.$emit("mapready")},centerChange:function(){var t=this,e=Number(this.latitude),n=Number(this.longitude);e===this.center.latitude&&n===this.center.longitude||(this.center.latitude=e,this.center.longitude=n,this._map&&this.mapReady((function(){t._map.setCenter(new i.LatLng(e,n))})))},createMarkers:function(t){var e=this,n=this._map,r=this.markersSync;t.forEach((function(t){var o=new i.Marker({map:n,flat:!0,autoRotation:!1});o.id=t.id,e.changeMarker(o,t),i.event.addListener(o,"click",(function(n){var i=o.callout;if(i){var r=i.div,s=r.parentNode;i.alwaysVisible||i.set("visible",!i.visible),i.visible&&(s.removeChild(r),s.appendChild(r))}Object(a["h"])(t,"id")&&e.$trigger("markertap",{},{markerId:t.id})})),r.push(o)}))},changeMarker:function(t,e){var n=this,r=this._map,o=e.title||e.name,s=new i.LatLng(e.latitude,e.longitude),c=new Image;c.onload=function(){var u,l,h,f,d=e.anchor||{},p=d.x,g=d.y;e.iconPath&&(e.width||e.height)?(l=e.width||c.width/c.height*e.height,h=e.height||c.height/c.width*e.width):(l=c.width/2,h=c.height/2),p=("number"===typeof p?p:.5)*l,g=("number"===typeof g?g:1)*h,f=h-(h-g),u=new i.MarkerImage(c.src,null,null,new i.Point(p,g),new i.Size(l,h)),t.setPosition(s),t.setIcon(u),t.setRotation(e.rotate||0);var v,m=e.label||{};t.label&&(t.label.setMap(null),delete t.label),m.content&&(v=new i.Label({position:s,map:r,clickable:!1,content:m.content,style:{border:"none",padding:"8px",background:"none",color:m.color,fontSize:(m.fontSize||14)+"px",lineHeight:(m.fontSize||14)+"px",marginLeft:m.x,marginTop:m.y}}),t.label=v);var b,y=e.callout||{},_=t.callout;y.content?b={id:e.id,position:s,map:r,top:f,content:y.content,color:y.color,fontSize:y.fontSize,borderRadius:y.borderRadius,bgColor:y.bgColor,padding:y.padding,boxShadow:y.boxShadow,display:y.display}:o&&(b={id:e.id,position:s,map:r,top:f,content:o,boxShadow:"0px 0px 3px 1px rgba(0,0,0,0.5)"}),b?_?_.setOption(b):(_=t.callout=new i.Callout(b),_.div.onclick=function(t){Object(a["h"])(e,"id")&&n.$trigger("callouttap",t,{markerId:e.id}),t.stopPropagation(),t.preventDefault()}):_&&(_.setMap(null),delete t.callout)},c.src=e.iconPath?this.$getRealPath(e.iconPath):"data:image/png;base64,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"},removeMarkers:function(t){for(var e=0;e<t.length;e++){var n=t[e];n.label&&n.label.setMap(null),n.callout&&n.callout.setMap(null),n.setMap(null),t.splice(e--,1)}},createPolyline:function(){var t=this._map,e=this.polylineSync;this.removePolyline(),this.polyline.forEach((function(n){var r=[];if(n.points.forEach((function(t){r.push(new i.LatLng(t.latitude,t.longitude))})),n.borderWidth){var o=new i.Polyline({map:t,clickable:!1,path:r,strokeWeight:n.width+n.borderWidth,strokeColor:n.borderColor,strokeDashStyle:n.dottedLine?"dash":"solid"});e.push(o)}var a=new i.Polyline({map:t,clickable:!1,path:r,strokeWeight:n.width,strokeColor:n.color,strokeDashStyle:n.dottedLine?"dash":"solid"});e.push(a)}))},removePolyline:function(){var t=this.polylineSync;t.forEach((function(t){t.setMap(null)})),t.splice(0,t.length)},createCircles:function(){var t=this._map,e=this.circlesSync;this.removeCircles(),this.circles.forEach((function(n){var r=new i.LatLng(n.latitude,n.longitude);function o(t){var e=t.match(/#[0-9A-Fa-f]{6}([0-9A-Fa-f]{2})?/);return e&&e.length?i.Color.fromHex(e[0],Number("0x"+e[1]||!1)/255):void 0}var a=new i.Circle({map:t,center:r,clickable:!1,radius:n.radius,strokeWeight:n.strokeWidth,fillColor:o(n.fillColor),strokeColor:o(n.color),strokeDashStyle:"solid"});e.push(a)}))},removeCircles:function(){var t=this.circlesSync;t.forEach((function(t){t.setMap(null)})),t.splice(0,t.length)},createControls:function(){var t=this,e=this,n=this._map,r=this.controlsSync;this.removeControls(),this.controls.forEach((function(o){var a=o.position||{},s=document.createElement("div"),c=new Image;s.appendChild(c);var u=s.style;u.position="absolute",u.width=0,u.height=0,c.onload=function(){o.position.width&&(c.width=o.position.width),o.position.height&&(c.height=o.position.height);var t=c.style;t.position="absolute",t.left=(a.left||0)+"px",t.top=(a.top||0)+"px",t.maxWidth="initial"},c.src=t.$getRealPath(o.iconPath),c.onclick=function(t){o.clickable&&e.$trigger("controltap",t,{controlId:o.id})},n.controls[i.ControlPosition.TOP_LEFT].push(s),r.push(s)}))},removeControls:function(){var t=this.controlsSync;t.forEach((function(t){t.remove()})),t.splice(0,t.length)},createLocation:function(){var e=this,n=this._map,r=this._location;r&&this.removeLocation(),uni.getLocation({type:"gcj02",success:function(t){if(r===e._location){var o=new i.LatLng(t.latitude,t.longitude);r=new i.Marker({position:o,map:n,icon:new i.MarkerImage("data:image/png;base64,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",null,null,new i.Point(22,22),new i.Size(44,44)),flat:!0,rotation:0}),e._location=r,a(),uni.onCompassChange((function(t){r.setRotation(t.direction)}))}},fail:function(e){t.error(e)}});var o=this;function a(){r===o._location&&setTimeout((function(){uni.getLocation({type:"gcj02",success:function(t){var e=o._locationPosition=new i.LatLng(t.latitude,t.longitude);r.setPosition(e)},fail:function(e){t.error(e)},complete:function(){a()}})}),1e3)}},removeLocation:function(){var t=this._location;t&&(t.setMap(null),this._location=null,this._locationPosition=null,uni.stopCompass())},fitBounds:function(t,e){var n=this;this.boundsReady((function(){var r=n._map,o=new i.LatLngBounds;t.forEach((function(t){var e=t.longitude,n=t.latitude,r=new i.LatLng(n,e);o.extend(r)})),r.fitBounds(o),"function"===typeof e&&e()}))},mapReady:function(t){this.isMapReady?t():this.$once("mapready",(function(){t()}))},boundsReady:function(t){this.isBoundsReady?t():this.$once("boundsready",(function(){t()}))},getMarker:function(t){for(var e=this.markersSync,n=0;n<e.length;n++){var i=e[n];if(i.id===t)return i}}}}}).call(this,n("3ad9")["default"])},7572:function(t,e,n){},"764a":function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return u}));var i=n("f2b3"),r=n("85b6"),o=n("65a8"),a=n("33ed"),s=!!i["o"]&&{passive:!1};function c(e){if(uni.canIUse("css.var")){var n=e.$parent.$parent,i=n.navigationBar.type,r="default"===i||"float"===i?o["a"]:0,a=getApp().$children[0].showTabBar?o["b"]:0,s=uni.canIUse("css.env")?"env":uni.canIUse("css.constant")?"constant":"",c=r&&s?"calc(".concat(r,"px + ").concat(s,"(safe-area-inset-top))"):"".concat(r,"px"),u=a&&s?"calc(".concat(a,"px + ").concat(s,"(safe-area-inset-bottom))"):"".concat(a,"px"),l=document.documentElement.style;l.setProperty("--window-top",c),l.setProperty("--window-bottom",u),t.debug("".concat(e.$page.route,"[").concat(e.$page.id,"]：--window-top=").concat(c)),t.debug("".concat(e.$page.route,"[").concat(e.$page.id,"]：--window-bottom=").concat(u))}}function u(t){var e=!1,n=!1;t("onPageLoad",(function(t){c(t)})),t("onPageShow",(function(t){var o=t.$parent.$parent;t._isMounted&&c(t),n&&document.removeEventListener("touchmove",n,s),o.disableScroll&&(n=a["b"],document.addEventListener("touchmove",n,s));var u=Object(r["a"])(t.$options,"onPageScroll"),l=Object(r["a"])(t.$options,"onReachBottom"),h=o.onReachBottomDistance,f=Object(i["k"])(o.titleNView)&&"transparent"===o.titleNView.type||Object(i["k"])(o.navigationBar)&&"transparent"===o.navigationBar.type;e&&document.removeEventListener("scroll",e),(f||u||l)&&(e=Object(a["a"])(t.$page.id,{enablePageScroll:u,enablePageReachBottom:l,onReachBottomDistance:h,enableTransparentTitleNView:f}),requestAnimationFrame((function(){document.addEventListener("scroll",e)})))}))}}).call(this,n("3ad9")["default"])},"77d5":function(t,e,n){},"77e0":function(t,e,n){"use strict";n.r(e),function(t,n){e["default"]={data:function(){return{showToast:{visible:!1}}},created:function(){var e=this,i="",r=function(t){return function(n){i=t,setTimeout((function(){e.showToast=n}),10)}};t.on("onShowToast",r("onShowToast")),t.on("onShowLoading",r("onShowLoading"));var o=function(t){return function(){if(i){var r="";if("onHideToast"===t&&"onShowToast"!==i?r="请注意 showToast 与 hideToast 必须配对使用":"onHideLoading"===t&&"onShowLoading"!==i&&(r="请注意 showLoading 与 hideLoading 必须配对使用"),r)return n.warn(r);i="",setTimeout((function(){e.showToast.visible=!1}),10)}}};t.on("onHidePopup",o("onHidePopup")),t.on("onHideToast",o("onHideToast")),t.on("onHideLoading",o("onHideLoading"))}}}.call(this,n("0dd1"),n("3ad9")["default"])},"78a1":function(t,e,n){"use strict";n.r(e),n.d(e,"onKeyboardHeightChange",(function(){return a}));var i,r=n("a118"),o=n("db70");function a(t){i=t}Object(o["d"])("onKeyboardHeightChange",(function(t){i&&Object(r["a"])(i,t)}))},"78c8":function(t,e,n){"use strict";n.r(e),n.d(e,"getSystemInfoSync",(function(){return u})),n.d(e,"getSystemInfo",(function(){return l}));var i=n("a470"),r=n("d8c8"),o=n.n(r),a=navigator.userAgent,s=/android/i.test(a),c=/iphone|ipad|ipod/i.test(a);function u(){var t,e,n,r=window.screen,u=window.devicePixelRatio,l=90===Math.abs(window.orientation),h=Math[l?"max":"min"](r.width,r.height),f=Math[l?"min":"max"](r.height,r.width),d=Math.min(window.innerWidth,document.documentElement.clientWidth,h),p=window.innerHeight,g=navigator.language,v=o.a.top;if(c){t="iOS";var m=a.match(/OS\s([\w_]+)\slike/);m&&(e=m[1].replace(/_/g,"."));var b=a.match(/\(([a-zA-Z]+);/);b&&(n=b[1])}else if(s){t="Android";var y=a.match(/Android[\s/]([\w\.]+)[;\s]/);y&&(e=y[1]);for(var _=a.match(/\((.+?)\)/),w=_?_[1].split(";"):a.split(" "),S=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i],k=0;k<w.length;k++){var T=w[k];if(T.indexOf("Build")>0){n=T.split("Build")[0].trim();break}for(var x=void 0,C=0;C<S.length;C++)if(S[C].test(T)){x=!0;break}if(!x){n=T.trim();break}}}else t="Other",e="0";var O="".concat(t," ").concat(e),E=t.toLocaleLowerCase(),M={left:o.a.left,right:d-o.a.right,top:o.a.top,bottom:p-o.a.bottom,width:d-o.a.left-o.a.right,height:p-o.a.top-o.a.bottom},j=Object(i["a"])(),A=j.top,I=j.bottom;return p-=A,p-=I,{windowTop:A,windowBottom:I,windowWidth:d,windowHeight:p,pixelRatio:u,screenWidth:h,screenHeight:f,language:g,statusBarHeight:v,system:O,platform:E,model:n,safeArea:M,safeAreaInsets:{top:o.a.top,right:o.a.right,bottom:o.a.bottom,left:o.a.left}}}function l(){return u()}},"7bb3":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-checkbox",t._g({attrs:{disabled:t.disabled},on:{click:t._onClick}},t.$listeners),[n("div",{staticClass:"uni-checkbox-wrapper"},[n("div",{staticClass:"uni-checkbox-input",class:[t.checkboxChecked?"uni-checkbox-input-checked":""],style:{color:t.color}}),t._t("default")],2)])},r=[],o=n("8af1"),a={name:"Checkbox",mixins:[o["b"],o["f"]],props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#007aff"},value:{type:String,default:""}},data:function(){return{checkboxChecked:this.checked,checkboxValue:this.value}},watch:{checked:function(t){this.checkboxChecked=t},value:function(t){this.checkboxValue=t}},listeners:{"label-click":"_onClick","@label-click":"_onClick"},created:function(){this.$dispatch("CheckboxGroup","uni-checkbox-group-update",{type:"add",vm:this}),this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this})},beforeDestroy:function(){this.$dispatch("CheckboxGroup","uni-checkbox-group-update",{type:"remove",vm:this}),this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})},methods:{_onClick:function(t){this.disabled||(this.checkboxChecked=!this.checkboxChecked,this.$dispatch("CheckboxGroup","uni-checkbox-change",t))},_resetFormData:function(){this.checkboxChecked=!1}}},s=a,c=(n("f53a"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},"7c2b":function(t,e,n){"use strict";var i=n("2c45"),r=n.n(i);r.a},"7d13":function(t,e,n){"use strict";n.r(e),n.d(e,"onAccelerometerChange",(function(){return s})),n.d(e,"startAccelerometer",(function(){return c})),n.d(e,"stopAccelerometer",(function(){return u}));var i=n("a118"),r=n("db70"),o=[];Object(r["d"])("onAccelerometerChange",(function(t){o.forEach((function(e){Object(i["a"])(e,t)}))}));var a=!1;function s(t){o.push(t),a||c()}function c(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};t.interval;if(!a)return a=!0,Object(r["c"])("enableAccelerometer",{enable:!0})}function u(){return a=!1,Object(r["c"])("enableAccelerometer",{enable:!1})}},"7d18":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"uploadFile",(function(){return u}));var i=n("e2e2");function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var c=function(){function t(e,n){r(this,t),s(this,"_xhr",void 0),s(this,"_isAbort",void 0),s(this,"_callbacks",[]),this._xhr=e,this._callbackId=n}return a(t,[{key:"onProgressUpdate",value:function(t){"function"===typeof t&&this._callbacks.push(t)}},{key:"offProgressUpdate",value:function(t){var e=this._callbacks.indexOf(t);e>=0&&this._callbacks.splice(e,1)}},{key:"abort",value:function(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}}]),t}();function u(e,n){var r=e.url,o=e.file,a=e.filePath,s=e.name,u=e.files,l=e.header,h=e.formData,f=__uniConfig.networkTimeout&&__uniConfig.networkTimeout.uploadFile||6e4,d=t,p=d.invokeCallbackHandler,g=new c(null,n);function v(t){var e,i=new XMLHttpRequest,o=new FormData;Object.keys(h).forEach((function(t){o.append(t,h[t])})),Object.values(u).forEach((function(e,n){var i=e.name,r=t[n];o.append(i||"file",r,r.name||"file-".concat(Date.now()))})),i.open("POST",r),Object.keys(l).forEach((function(t){i.setRequestHeader(t,l[t])})),i.upload.onprogress=function(t){g._callbacks.forEach((function(e){var n=t.loaded,i=t.total,r=Math.round(n/i*100);e({progress:r,totalBytesSent:n,totalBytesExpectedToSend:i})}))},i.onerror=function(){clearTimeout(e),p(n,{errMsg:"uploadFile:fail"})},i.onabort=function(){clearTimeout(e),p(n,{errMsg:"uploadFile:fail abort"})},i.onload=function(){clearTimeout(e);var t=i.status;p(n,{errMsg:"uploadFile:ok",statusCode:t,data:i.responseText||i.response})},g._isAbort?p(n,{errMsg:"uploadFile:fail abort"}):(e=setTimeout((function(){i.upload.onprogress=i.onload=i.onabort=i.onerror=null,g.abort(),p(n,{errMsg:"uploadFile:fail timeout"})}),f),i.send(o),g._xhr=i)}return Array.isArray(u)&&u.length||(u=[{name:s,file:o,uri:a}]),Promise.all(u.map((function(t){var e=t.file,n=t.uri;return e instanceof File?Promise.resolve(e):Object(i["c"])(n)}))).then(v).catch((function(){setTimeout((function(){p(n,{errMsg:"uploadFile:fail file error"})}),0)})),g}}.call(this,n("0dd1"))},"7df2":function(t,e,n){},"7e6a":function(t,e,n){"use strict";var i=n("515d"),r=n.n(i);r.a},"7f4e":function(t,e,n){"use strict";function i(t){var e=t.phoneNumber;return window.location.href="tel:".concat(e),{errMsg:"makePhoneCall:ok"}}n.r(e),n.d(e,"makePhoneCall",(function(){return i}))},"811a":function(t,e,n){"use strict";n.r(e),n.d(e,"connectSocket",(function(){return f})),n.d(e,"sendSocketMessage",(function(){return d})),n.d(e,"closeSocket",(function(){return p})),n.d(e,"onSocketOpen",(function(){return g})),n.d(e,"onSocketError",(function(){return v})),n.d(e,"onSocketMessage",(function(){return m})),n.d(e,"onSocketClose",(function(){return b}));var i=n("a118"),r=n("db70");function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function s(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t}var c=function(){function t(e){o(this,t),this.id=e,this._callbacks={open:[],close:[],error:[],message:[]},this.CLOSED=3,this.CLOSING=2,this.CONNECTING=0,this.OPEN=1,this.readyState=this.CLOSED}return s(t,[{key:"send",value:function(t){this.readyState!==this.OPEN&&this._callback(t,"sendSocketMessage:fail WebSocket is not connected");var e=Object(r["c"])("operateSocketTask",Object.assign({},t,{operationType:"send",socketTaskId:this.id})),n=e.errMsg;this._callback(t,n.replace("operateSocketTask","sendSocketMessage"))}},{key:"close",value:function(t){this.readyState=this.CLOSING;var e=Object(r["c"])("operateSocketTask",Object.assign({},t,{operationType:"close",socketTaskId:this.id})),n=e.errMsg;this._callback(t,n.replace("operateSocketTask","closeSocket"))}},{key:"onOpen",value:function(t){this._callbacks.open.push(t)}},{key:"onClose",value:function(t){this._callbacks.close.push(t)}},{key:"onError",value:function(t){this._callbacks.error.push(t)}},{key:"onMessage",value:function(t){this._callbacks.message.push(t)}},{key:"_callback",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.success,n=t.fail,i=t.complete,r=arguments.length>1?arguments[1]:void 0,o={errMsg:r};/:ok$/.test(r)?"function"===typeof e&&e(o):"function"===typeof n&&n(o),"function"===typeof i&&i(o)}}]),t}(),u=Object.create(null),l=[],h=Object.create(null);function f(t,e){var n=Object(r["c"])("createSocketTask",t),o=n.socketTaskId,a=new c(o);return u[o]=a,l.push(a),setTimeout((function(){Object(i["a"])(e,{errMsg:"connectSocket:ok"})}),0),a}function d(t,e){var n=l[0];if(n&&n.readyState===n.OPEN)return Object(r["c"])("operateSocketTask",Object.assign({},t,{operationType:"send",socketTaskId:n.id}));Object(i["a"])(e,{errMsg:"sendSocketMessage:fail WebSocket is not connected"})}function p(t,e){var n=l[0];if(n)return n.readyState=n.CLOSING,Object(r["c"])("operateSocketTask",Object.assign({},t,{operationType:"close",socketTaskId:n.id}));Object(i["a"])(e,{errMsg:"closeSocket:fail WebSocket is not connected"})}function g(t){h.open=t}function v(t){h.error=t}function m(t){h.message=t}function b(t){h.close=t}Object(r["d"])("onSocketTaskStateChange",(function(t){var e=t.socketTaskId,n=t.state,r=t.data,o=(t.errMsg,u[e]);if(o){if("open"===n&&(o.readyState=o.OPEN),o===l[0]&&h[n]&&Object(i["a"])(h[n],"message"===n?{data:r}:{}),"error"===n||"close"===n){o.readyState=o.CLOSED,delete u[e];var a=l.indexOf(o);a>=0&&l.splice(a,1)}o._callbacks[n].forEach((function(t){"function"===typeof t&&t("message"===n?{data:r}:{})}))}}))},8188:function(t,e,n){"use strict";(function(t){var i=n("8af1"),r=n("18fd"),o=n("b253");function a(t){return a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}e["a"]={name:"Editor",mixins:[i["g"],i["b"],i["e"]],props:{id:{type:String,default:""},readOnly:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},showImgSize:{type:[Boolean,String],default:!1},showImgToolbar:{type:[Boolean,String],default:!1},showImgResize:{type:[Boolean,String],default:!1}},data:function(){return{quillReady:!1}},computed:{},watch:{readOnly:function(t){if(this.quillReady){var e=this.quill;e.enable(!t),t||e.blur()}},placeholder:function(t){this.quillReady&&this.quill.root.setAttribute("data-placeholder",t)}},mounted:function(){var t=this,e=[];this.showImgSize&&e.push("DisplaySize"),this.showImgToolbar&&e.push("Toolbar"),this.showImgResize&&e.push("Resize"),this.loadQuill((function(){e.length?t.loadImageResizeModule((function(){t.initQuill(e)})):t.initQuill(e)}))},methods:{_handleSubscribe:function(e){var n,i,r,o=e.type,s=e.data,c=s.options,u=s.callbackId,l=this.quill,h=window.Quill;if(this.quillReady){switch(o){case"format":var f=c.name,d=void 0===f?"":f,p=c.value,g=void 0!==p&&p;i=l.getSelection(!0);var v=l.getFormat(i)[d]||!1;if(["bold","italic","underline","strike","ins"].includes(d))g=!v;else if("direction"===d){g=("rtl"!==g||!v)&&g;var m=l.getFormat(i).align;"rtl"!==g||m?g||"right"!==m||l.format("align",!1,h.sources.USER):l.format("align","right",h.sources.USER)}else if("indent"===d){var b="rtl"===l.getFormat(i).direction;g="+1"===g,b&&(g=!g),g=g?"+1":"-1"}else"list"===d&&(g="check"===g?"unchecked":g,v="checked"===v?"unchecked":v),g=v&&v!==(g||!1)||!v&&g?g:!v;l.format(d,g,h.sources.USER);break;case"insertDivider":i=l.getSelection(!0),l.insertText(i.index,"\n",h.sources.USER),l.insertEmbed(i.index+1,"divider",!0,h.sources.USER),l.setSelection(i.index+2,h.sources.SILENT);break;case"insertImage":i=l.getSelection(!0);var y=c.src,_=void 0===y?"":y,w=c.alt,S=void 0===w?"":w,k=c.width,T=void 0===k?"":k,x=c.height,C=void 0===x?"":x,O=c.extClass,E=void 0===O?"":O,M=c.data,j=void 0===M?{}:M,A=this.$getRealPath(_);l.insertEmbed(i.index,"image",A,h.sources.USER);var I=!!/^(file|blob):/.test(A)&&A;l.formatText(i.index,1,"data-local",I),l.formatText(i.index,1,"alt",S),l.formatText(i.index,1,"width",T),l.formatText(i.index,1,"height",C),l.formatText(i.index,1,"class",E),l.formatText(i.index,1,"data-custom",Object.keys(j).map((function(t){return"".concat(t,"=").concat(j[t])})).join("&")),l.setSelection(i.index+1,h.sources.SILENT);break;case"insertText":i=l.getSelection(!0);var $=c.text,P=void 0===$?"":$;l.insertText(i.index,P,h.sources.USER),l.setSelection(i.index+P.length,0,h.sources.SILENT);break;case"setContents":var B=c.delta,L=c.html;"object"===a(B)?l.setContents(B,h.sources.SILENT):"string"===typeof L?l.setContents(this.html2delta(L),h.sources.SILENT):r="contents is missing";break;case"getContents":n=this.getContents();break;case"clear":l.setContents([]);break;case"removeFormat":i=l.getSelection(!0);var N=h.import("parchment");i.length?l.removeFormat(i,h.sources.USER):Object.keys(l.getFormat(i)).forEach((function(t){N.query(t,N.Scope.INLINE)&&l.format(t,!1)}));break;case"undo":l.history.undo();break;case"redo":l.history.redo();break;default:break}this.updateStatus(i)}else r="not ready";u&&t.publishHandler("onEditorMethodCallback",{callbackId:u,data:Object.assign({},n,{errMsg:"".concat(o,":").concat(r?"fail "+r:"ok")})},this.$page.id)},loadQuill:function(t){if("function"!==typeof window.Quill){var e=document.createElement("script");e.src=window.plus?"./__uniappquill.js":"https://unpkg.com/quill@1.3.7/dist/quill.min.js",document.body.appendChild(e),e.onload=t}else"function"===typeof t&&t()},loadImageResizeModule:function(t){if("function"!==typeof window.ImageResize){var e=document.createElement("script");e.src=window.plus?"./__uniappquillimageresize.js":"https://unpkg.com/quill-image-resize-mp@3.0.1/image-resize.min.js",document.body.appendChild(e),e.onload=t}else"function"===typeof t&&t()},initQuill:function(t){var e=this,n=window.Quill;o["a"](n);var i={toolbar:!1,readOnly:this.readOnly,placeholder:this.placeholder,modules:{}};t.length&&(n.register("modules/ImageResize",window.ImageResize.default),i.modules.ImageResize={modules:t});var r=this.quill=new n(this.$el,i),a=r.root,s=["focus","blur","input"];s.forEach((function(t){a.addEventListener(t,(function(n){"input"===t?n.stopPropagation():e.$trigger(t,n,e.getContents())}))})),r.on(n.events.TEXT_CHANGE,(function(){e.$trigger("input",{},e.getContents())})),r.on(n.events.SELECTION_CHANGE,this.updateStatus.bind(this)),r.on(n.events.SCROLL_OPTIMIZE,(function(){var t=r.selection.getRange()[0];e.updateStatus(t)})),r.clipboard.addMatcher(Node.ELEMENT_NODE,(function(t,n){return e.skipMatcher?n:{ops:n.ops.filter((function(t){var e=t.insert;return"string"===typeof e})).map((function(t){var e=t.insert;return{insert:e}}))}})),this.initKeyboard(a),this.quillReady=!0,this.$trigger("ready",event,{})},getContents:function(){var t=this.quill,e=t.root.innerHTML,n=t.getText(),i=t.getContents();return{html:e,text:n,delta:i}},html2delta:function(t){var e,n=["span","strong","b","ins","em","i","u","a","del","s","sub","sup","img","div","p","h1","h2","h3","h4","h5","h6","hr","ol","ul","li","br"],i="";Object(r["a"])(t,{start:function(t,r,o){if(n.includes(t)){e=!1;var a=r.map((function(t){var e=t.name,n=t.value;return"".concat(e,'="').concat(n,'"')})).join(" "),s="<".concat(t," ").concat(a," ").concat(o?"/":"",">");i+=s}else e=!o},end:function(t){e||(i+="</".concat(t,">"))},chars:function(t){e||(i+=t)}}),this.skipMatcher=!0;var o=this.quill.clipboard.convert(i);return this.skipMatcher=!1,o},updateStatus:function(t){var e=this,n=t?this.quill.getFormat(t):{},i=Object.keys(n);(i.length!==Object.keys(this.__status||{}).length||i.find((function(t){return n[t]!==e.__status[t]})))&&(this.__status=n,this.$trigger("statuschange",{},n))}}}}).call(this,n("501c"))},"81ea":function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-tabbar",[n("div",{staticClass:"uni-tabbar",style:{backgroundColor:t.backgroundColor}},[n("div",{staticClass:"uni-tabbar-border",style:{backgroundColor:t.borderColor}}),t._l(t.list,(function(e,i){return n("div",{key:e.pagePath,staticClass:"uni-tabbar__item",on:{click:function(n){return t._switchTab(e,i)}}},[n("div",{staticClass:"uni-tabbar__bd"},[e.iconPath?n("div",{staticClass:"uni-tabbar__icon",class:{"uni-tabbar__icon__diff":!e.text}},[n("img",{attrs:{src:t._getRealPath(t.$route.meta.pagePath===e.pagePath?e.selectedIconPath:e.iconPath)}}),e.redDot?n("div",{staticClass:"uni-tabbar__reddot",class:{"uni-tabbar__badge":!!e.badge}},[t._v(" "+t._s(e.badge)+" ")]):t._e()]):t._e(),e.text?n("div",{staticClass:"uni-tabbar__label",style:{color:t.$route.meta.pagePath===e.pagePath?t.selectedColor:t.color,fontSize:e.iconPath?"10px":"14px"}},[t._v(" "+t._s(e.text)+" "),e.redDot&&!e.iconPath?n("div",{staticClass:"uni-tabbar__reddot",class:{"uni-tabbar__badge":!!e.badge}},[t._v(" "+t._s(e.badge)+" ")]):t._e()]):t._e()])])}))],2),n("div",{staticClass:"uni-placeholder"})])},r=[],o=n("a919"),a=o["a"],s=(n("f4e0"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null),u=c.exports,l=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("transition",{attrs:{name:"uni-fade"}},[t.visible?n("uni-toast",{attrs:{"data-duration":t.duration}},[t.mask?n("div",{staticClass:"uni-mask",staticStyle:{background:"transparent"},on:{touchmove:function(t){t.preventDefault()}}}):t._e(),t.image||t.iconClass?n("div",{staticClass:"uni-toast"},[t.image?n("img",{staticClass:"uni-toast__icon",attrs:{src:t.image}}):n("i",{staticClass:"uni-icon_toast",class:t.iconClass}),n("p",{staticClass:"uni-toast__content"},[t._v(" "+t._s(t.title)+" ")])]):n("div",{staticClass:"uni-sample-toast"},[n("p",{staticClass:"uni-simple-toast__text"},[t._v(" "+t._s(t.title)+" ")])])]):t._e()],1)},h=[],f=n("5222"),d=f["a"],p=(n("ff28"),Object(s["a"])(d,l,h,!1,null,null,null)),g=p.exports,v=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("transition",{attrs:{name:"uni-fade"}},[n("uni-modal",{directives:[{name:"show",rawName:"v-show",value:t.visible,expression:"visible"}],on:{touchmove:function(t){t.preventDefault()}}},[n("div",{staticClass:"uni-mask"}),n("div",{staticClass:"uni-modal"},[t.title?n("div",{staticClass:"uni-modal__hd"},[n("strong",{staticClass:"uni-modal__title",domProps:{textContent:t._s(t.title)}})]):t._e(),n("div",{staticClass:"uni-modal__bd",domProps:{textContent:t._s(t.content)},on:{touchmove:function(t){t.stopPropagation()}}}),n("div",{staticClass:"uni-modal__ft"},[t.showCancel?n("div",{staticClass:"uni-modal__btn uni-modal__btn_default",style:{color:t.cancelColor},on:{click:function(e){return t._close("cancel")}}},[t._v(" "+t._s(t.cancelText)+" ")]):t._e(),n("div",{staticClass:"uni-modal__btn uni-modal__btn_primary",style:{color:t.confirmColor},on:{click:function(e){return t._close("confirm")}}},[t._v(" "+t._s(t.confirmText)+" ")])])])])],1)},m=[],b=n("5a56"),y={name:"Modal",mixins:[b["default"]],props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"取消"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"确定"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean,default:!1}},methods:{_close:function(t){this.$emit("close",t)}}},_=y,w=(n("2765"),Object(s["a"])(_,v,m,!1,null,null,null)),S=w.exports,k=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-actionsheet",{on:{touchmove:function(t){t.preventDefault()}}},[n("transition",{attrs:{name:"uni-fade"}},[n("div",{directives:[{name:"show",rawName:"v-show",value:t.visible,expression:"visible"}],staticClass:"uni-mask",on:{click:function(e){return t._close(-1)}}})]),n("div",{staticClass:"uni-actionsheet",class:{"uni-actionsheet_toggle":t.visible}},[n("div",{staticClass:"uni-actionsheet__menu"},[t.title?n("div",{staticClass:"uni-actionsheet__title"},[t._v(" "+t._s(t.title)+" ")]):t._e(),t._l(t.itemList,(function(e,i){return n("div",{key:i,staticClass:"uni-actionsheet__cell",style:{color:t.itemColor},on:{click:function(e){return t._close(i)}}},[t._v(" "+t._s(e)+" ")])}))],2),n("div",{staticClass:"uni-actionsheet__action"},[n("div",{staticClass:"uni-actionsheet__cell",style:{color:t.itemColor},on:{click:function(e){return t._close(-1)}}},[t._v(" 取消 ")])])])],1)},T=[],x={name:"ActionSheet",props:{title:{type:String,default:""},itemList:{type:Array,default:function(){return[]}},itemColor:{type:String,default:"#000000"},visible:{type:Boolean,default:!1}},methods:{_close:function(t){this.$emit("close",t)}}},C=x,O=(n("4fef"),Object(s["a"])(C,k,T,!1,null,null,null)),E=O.exports,M={Toast:g,Modal:S,ActionSheet:E};function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function A(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach((function(e){I(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function I(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}e["a"]=A({TabBar:u},M)},"82b9":function(t,e,n){"use strict";function i(t){return function(e,n){e&&(n[t]=Math.round(e))}}n.r(e),n.d(e,"canvasGetImageData",(function(){return r})),n.d(e,"canvasPutImageData",(function(){return o})),n.d(e,"canvasToTempFilePath",(function(){return s})),n.d(e,"drawCanvas",(function(){return c}));var r={canvasId:{type:String,required:!0},x:{type:Number,required:!0,validator:i("x")},y:{type:Number,required:!0,validator:i("y")},width:{type:Number,required:!0,validator:i("width")},height:{type:Number,required:!0,validator:i("height")}},o={canvasId:{type:String,required:!0},data:{type:Uint8ClampedArray,required:!0},x:{type:Number,required:!0,validator:i("x")},y:{type:Number,required:!0,validator:i("y")},width:{type:Number,required:!0,validator:i("width")},height:{type:Number,validator:i("height")}},a={PNG:"png",JPG:"jpg",JPEG:"jpg"},s={x:{type:Number,default:0,validator:i("x")},y:{type:Number,default:0,validator:i("y")},width:{type:Number,validator:i("width")},height:{type:Number,validator:i("height")},destWidth:{type:Number,validator:i("destWidth")},destHeight:{type:Number,validator:i("destHeight")},canvasId:{type:String,require:!0},fileType:{type:String,validator:function(t,e){t=(t||"").toUpperCase(),e.fileType=t in a?a[t]:a.PNG}},quality:{type:Number,validator:function(t,e){t=Math.floor(t),e.quality=t>0&&t<1?t:1}}},c={canvasId:{type:String,require:!0},actions:{type:Array,require:!0},reserve:{type:Boolean,default:!1}}},"82c2":function(t,e,n){"use strict";n.r(e),n.d(e,"request",(function(){return f}));var i=n("f2b3"),r=n("a118"),o=n("db70");function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function c(t,e,n){return e&&s(t.prototype,e),n&&s(t,n),t}var u=Object.create(null);function l(t,e){if("string"===typeof t.data&&65279===t.data.charCodeAt(0)&&(t.data=t.data.substr(1)),t.statusCode=parseInt(t.statusCode,10),Object(i["k"])(t.header)&&(t.header=Object.keys(t.header).reduce((function(e,n){var i=t.header[n];return Array.isArray(i)?e[n]=i.join(","):"string"===typeof i&&(e[n]=i),e}),{})),e.dataType&&"json"===e.dataType.toLowerCase())try{t.data=JSON.parse(t.data)}catch(n){}return t}Object(o["d"])("onRequestTaskStateChange",(function(t){var e=t.requestTaskId,n=t.state,i=t.data,o=t.statusCode,a=t.header,s=t.errMsg,c=u[e]||{},h=c.args,f=c.callbackId;if(f)switch(delete u[e],n){case"success":Object(r["a"])(f,l({data:i,statusCode:o,header:a,errMsg:"request:ok"},h));break;case"fail":Object(r["a"])(f,{errMsg:"request:fail "+s});break}}));var h=function(){function t(e){a(this,t),this.id=e}return c(t,[{key:"abort",value:function(){Object(o["c"])("operateRequestTask",{requestTaskId:this.id,operationType:"abort"})}},{key:"offHeadersReceived",value:function(){}},{key:"onHeadersReceived",value:function(){}}]),t}();function f(t,e){var n;for(var r in t.header)if("content-type"===r.toLowerCase()){n=t.header[r];break}"GET"!==t.method&&0===n.indexOf("application/json")&&Object(i["k"])(t.data)&&(t.data=JSON.stringify(t.data));var a=Object(o["c"])("createRequestTask",t),s=a.requestTaskId;return u[s]={args:t,callbackId:e},new h(s)}},8390:function(t,e){(function(){"use strict";for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=new Uint8Array(256),i=0;i<t.length;i++)n[t.charCodeAt(i)]=i;e.encode=function(e){var n,i=new Uint8Array(e),r=i.length,o="";for(n=0;n<r;n+=3)o+=t[i[n]>>2],o+=t[(3&i[n])<<4|i[n+1]>>4],o+=t[(15&i[n+1])<<2|i[n+2]>>6],o+=t[63&i[n+2]];return r%3===2?o=o.substring(0,o.length-1)+"=":r%3===1&&(o=o.substring(0,o.length-2)+"=="),o},e.decode=function(t){var e,i,r,o,a,s=.75*t.length,c=t.length,u=0;"="===t[t.length-1]&&(s--,"="===t[t.length-2]&&s--);var l=new ArrayBuffer(s),h=new Uint8Array(l);for(e=0;e<c;e+=4)i=n[t.charCodeAt(e)],r=n[t.charCodeAt(e+1)],o=n[t.charCodeAt(e+2)],a=n[t.charCodeAt(e+3)],h[u++]=i<<2|r>>4,h[u++]=(15&r)<<4|o>>2,h[u++]=(3&o)<<6|63&a;return l}})()},"83a6":function(t,e,n){"use strict";e["a"]={data:function(){return{hovering:!1}},props:{hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}},methods:{_hoverTouchStart:function(t){var e=this;t._hoverPropagationStopped||this.hoverClass&&"none"!==this.hoverClass&&!this.disabled&&(t.touches.length>1||(this.hoverStopPropagation&&(t._hoverPropagationStopped=!0),this._hoverTouch=!0,this._hoverStartTimer=setTimeout((function(){e.hovering=!0,e._hoverTouch||e._hoverReset()}),this.hoverStartTime)))},_hoverTouchEnd:function(t){this._hoverTouch=!1,this.hovering&&this._hoverReset()},_hoverReset:function(){var t=this;requestAnimationFrame((function(){clearTimeout(t._hoverStayTimer),t._hoverStayTimer=setTimeout((function(){t.hovering=!1}),t.hoverStayTime)}))},_hoverTouchCancel:function(t){this._hoverTouch=!1,this.hovering=!1,clearTimeout(this._hoverStartTimer)}}}},"84e0":function(t,e,n){"use strict";n.r(e),function(t){function i(e){var n=getCurrentPages();return n.length&&t.publishHandler("pageScrollTo",e,n[n.length-1].$page.id),{}}n.d(e,"pageScrollTo",(function(){return i}))}.call(this,n("0dd1"))},8542:function(t,e,n){"use strict";n.d(e,"a",(function(){return y})),n.d(e,"d",(function(){return _})),n.d(e,"e",(function(){return x})),n.d(e,"b",(function(){return O})),n.d(e,"c",(function(){return E}));var i=n("f2b3");function r(t){return c(t)||s(t)||a(t)||o()}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function a(t,e){if(t){if("string"===typeof t)return u(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?u(t,e):void 0}}function s(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function c(t){if(Array.isArray(t))return u(t)}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function l(t){return l="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}var h=["invoke","success","fail","complete","returnValue"],f={},d={};function p(t,e){var n=e?t?t.concat(e):Array.isArray(e)?e:[e]:t;return n?g(n):n}function g(t){for(var e=[],n=0;n<t.length;n++)-1===e.indexOf(t[n])&&e.push(t[n]);return e}function v(t,e){var n=t.indexOf(e);-1!==n&&t.splice(n,1)}function m(t,e){Object.keys(e).forEach((function(n){-1!==h.indexOf(n)&&Object(i["j"])(e[n])&&(t[n]=p(t[n],e[n]))}))}function b(t,e){t&&e&&Object.keys(e).forEach((function(n){-1!==h.indexOf(n)&&Object(i["j"])(e[n])&&v(t[n],e[n])}))}function y(t,e){"string"===typeof t&&Object(i["k"])(e)?m(d[t]||(d[t]={}),e):Object(i["k"])(t)&&m(f,t)}function _(t,e){"string"===typeof t?Object(i["k"])(e)?b(d[t],e):delete d[t]:Object(i["k"])(t)&&b(f,t)}function w(t){return function(e){return t(e)||e}}function S(t){return!!t&&("object"===l(t)||"function"===typeof t)&&"function"===typeof t.then}function k(t,e){for(var n=!1,i=0;i<t.length;i++){var r=t[i];if(n)n=Promise.then(w(r));else{var o=r(e);if(S(o)&&(n=Promise.resolve(o)),!1===o)return{then:function(){}}}}return n||{then:function(t){return t(e)}}}function T(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return["success","fail","complete"].forEach((function(n){if(Array.isArray(t[n])){var r=e[n];e[n]=function(e){k(t[n],e).then((function(t){return Object(i["j"])(r)&&r(t)||t}))}}})),e}function x(t,e){var n=[];Array.isArray(f.returnValue)&&n.push.apply(n,r(f.returnValue));var i=d[t];return i&&Array.isArray(i.returnValue)&&n.push.apply(n,r(i.returnValue)),n.forEach((function(t){e=t(e)||e})),e}function C(t){var e=Object.create(null);Object.keys(f).forEach((function(t){"returnValue"!==t&&(e[t]=f[t].slice())}));var n=d[t];return n&&Object.keys(n).forEach((function(t){"returnValue"!==t&&(e[t]=(e[t]||[]).concat(n[t]))})),e}function O(t,e,n){for(var i=arguments.length,r=new Array(i>3?i-3:0),o=3;o<i;o++)r[o-3]=arguments[o];var a=C(t);if(a&&Object.keys(a).length){if(Array.isArray(a.invoke)){var s=k(a.invoke,n);return s.then((function(t){return e.apply(void 0,[T(a,t)].concat(r))}))}return e.apply(void 0,[T(a,n)].concat(r))}return e.apply(void 0,[n].concat(r))}var E={returnValue:function(t){return S(t)?t.then((function(t){return t[1]})).catch((function(t){return t[0]})):t}}},"854d":function(t,e,n){"use strict";var i=n("2883"),r=n.n(i);r.a},"856e":function(t,e,n){"use strict";var i=n("01d0"),r=n.n(i);r.a},"85b6":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"a",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"d",(function(){return s}));var i=["SystemAsyncLoading","SystemAsyncError"];function r(t){return!(!t.$parent||"PageBody"!==t.$parent.$options.name)&&-1===i.indexOf(t.$options.name)}function o(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;return Array.isArray(t[e])&&t[e].length}function a(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=JSON.parse(JSON.stringify(t)),n=Object.keys(e),i=n.length;if(i)for(var r=0;r<i;r++){var o=n[r],a=o.length;if("v"===o.substr(0,1)&&(9===a||10===a)){delete e[o];break}}return e}function s(t){return t+="",-1!==t.indexOf("upx")?uni.upx2px(parseInt(t)||0):parseInt(t)||0}},8793:function(t,e,n){var i={"./action-sheet.js":"626d","./index.js":"f1ea","./modal.js":"ee4f","./toast.js":"77e0","./transition.js":"5a56"};function r(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}r.keys=function(){return Object.keys(i)},r.resolve=o,t.exports=r,r.id="8793"},8842:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-movable-view",t._g({},t.$listeners),[n("v-uni-resize-sensor",{on:{resize:t.setParent}}),t._t("default")],2)},r=[],o=n("ba15");function a(t,e,n){return t>e-n&&t<e+n}function s(t,e){return a(t,0,e)}function c(){}function u(t,e){this._m=t,this._f=1e3*e,this._startTime=0,this._v=0}function l(t,e,n){this._m=t,this._k=e,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function h(t,e,n){this._springX=new l(t,e,n),this._springY=new l(t,e,n),this._springScale=new l(t,e,n),this._startTime=0}c.prototype.x=function(t){return Math.sqrt(t)},u.prototype.setV=function(t,e){var n=Math.pow(Math.pow(t,2)+Math.pow(e,2),.5);this._x_v=t,this._y_v=e,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(t/this._x_a)||Math.abs(e/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},u.prototype.setS=function(t,e){this._x_s=t,this._y_s=e},u.prototype.s=function(t){void 0===t&&(t=((new Date).getTime()-this._startTime)/1e3),t>this._t&&(t=this._t,this._lastDt=t);var e=this._x_v*t+.5*this._x_a*Math.pow(t,2)+this._x_s,n=this._y_v*t+.5*this._y_a*Math.pow(t,2)+this._y_s;return(this._x_a>0&&e<this._endPositionX||this._x_a<0&&e>this._endPositionX)&&(e=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:e,y:n}},u.prototype.ds=function(t){return void 0===t&&(t=((new Date).getTime()-this._startTime)/1e3),t>this._t&&(t=this._t),{dx:this._x_v+this._x_a*t,dy:this._y_v+this._y_a*t}},u.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},u.prototype.dt=function(){return-this._x_v/this._x_a},u.prototype.done=function(){var t=a(this.s().x,this._endPositionX)||a(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,t},u.prototype.setEnd=function(t,e){this._endPositionX=t,this._endPositionY=e},u.prototype.reconfigure=function(t,e){this._m=t,this._f=1e3*e},l.prototype._solve=function(t,e){var n=this._c,i=this._m,r=this._k,o=n*n-4*i*r;if(0===o){var a=-n/(2*i),s=t,c=e/(a*t);return{x:function(t){return(s+c*t)*Math.pow(Math.E,a*t)},dx:function(t){var e=Math.pow(Math.E,a*t);return a*(s+c*t)*e+c*e}}}if(o>0){var u=(-n-Math.sqrt(o))/(2*i),l=(-n+Math.sqrt(o))/(2*i),h=(e-u*t)/(l-u),f=t-h;return{x:function(t){var e,n;return t===this._t&&(e=this._powER1T,n=this._powER2T),this._t=t,e||(e=this._powER1T=Math.pow(Math.E,u*t)),n||(n=this._powER2T=Math.pow(Math.E,l*t)),f*e+h*n},dx:function(t){var e,n;return t===this._t&&(e=this._powER1T,n=this._powER2T),this._t=t,e||(e=this._powER1T=Math.pow(Math.E,u*t)),n||(n=this._powER2T=Math.pow(Math.E,l*t)),f*u*e+h*l*n}}}var d=Math.sqrt(4*i*r-n*n)/(2*i),p=-n/2*i,g=t,v=(e-p*t)/d;return{x:function(t){return Math.pow(Math.E,p*t)*(g*Math.cos(d*t)+v*Math.sin(d*t))},dx:function(t){var e=Math.pow(Math.E,p*t),n=Math.cos(d*t),i=Math.sin(d*t);return e*(v*d*n-g*d*i)+p*e*(v*i+g*n)}}},l.prototype.x=function(t){return void 0===t&&(t=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(t):0},l.prototype.dx=function(t){return void 0===t&&(t=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(t):0},l.prototype.setEnd=function(t,e,n){if(n||(n=(new Date).getTime()),t!==this._endPosition||!s(e,.1)){e=e||0;var i=this._endPosition;this._solution&&(s(e,.1)&&(e=this._solution.dx((n-this._startTime)/1e3)),i=this._solution.x((n-this._startTime)/1e3),s(e,.1)&&(e=0),s(i,.1)&&(i=0),i+=this._endPosition),this._solution&&s(i-t,.1)&&s(e,.1)||(this._endPosition=t,this._solution=this._solve(i-this._endPosition,e),this._startTime=n)}},l.prototype.snap=function(t){this._startTime=(new Date).getTime(),this._endPosition=t,this._solution={x:function(){return 0},dx:function(){return 0}}},l.prototype.done=function(t){return t||(t=(new Date).getTime()),a(this.x(),this._endPosition,.1)&&s(this.dx(),.1)},l.prototype.reconfigure=function(t,e,n){this._m=t,this._k=e,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},l.prototype.springConstant=function(){return this._k},l.prototype.damping=function(){return this._c},l.prototype.configuration=function(){function t(t,e){t.reconfigure(1,e,t.damping())}function e(t,e){t.reconfigure(1,t.springConstant(),e)}return[{label:"Spring Constant",read:this.springConstant.bind(this),write:t.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:e.bind(this,this),min:1,max:500}]},h.prototype.setEnd=function(t,e,n,i){var r=(new Date).getTime();this._springX.setEnd(t,i,r),this._springY.setEnd(e,i,r),this._springScale.setEnd(n,i,r),this._startTime=r},h.prototype.x=function(){var t=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(t),y:this._springY.x(t),scale:this._springScale.x(t)}},h.prototype.done=function(){var t=(new Date).getTime();return this._springX.done(t)&&this._springY.done(t)&&this._springScale.done(t)},h.prototype.reconfigure=function(t,e,n){this._springX.reconfigure(t,e,n),this._springY.reconfigure(t,e,n),this._springScale.reconfigure(t,e,n)};var f=n("f2b3"),d=!1;function p(t){d||(d=!0,requestAnimationFrame((function(){t(),d=!1})))}function g(t,e){if(t===e)return 0;var n=t.offsetLeft;return t.offsetParent?n+=g(t.offsetParent,e):0}function v(t,e){if(t===e)return 0;var n=t.offsetTop;return t.offsetParent?n+=v(t.offsetParent,e):0}function m(t,e){return+((1e3*t-1e3*e)/1e3).toFixed(1)}function b(t,e,n){var i=function(t){t&&t.id&&cancelAnimationFrame(t.id),t&&(t.cancelled=!0)},r={id:0,cancelled:!1};function o(e,n,i,r){if(!e||!e.cancelled){i(n);var a=t.done();a||e.cancelled||(e.id=requestAnimationFrame(o.bind(null,e,n,i,r))),a&&r&&r(n)}}return o(r,t,e,n),{cancel:i.bind(null,r),model:t}}var y={name:"MovableView",mixins:[o["a"]],props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},data:function(){return{xSync:this._getPx(this.x),ySync:this._getPx(this.y),scaleValueSync:Number(this.scaleValue)||1,width:0,height:0,minX:0,minY:0,maxX:0,maxY:0}},computed:{dampingNumber:function(){var t=Number(this.damping);return isNaN(t)?20:t},frictionNumber:function(){var t=Number(this.friction);return isNaN(t)||t<=0?2:t},scaleMinNumber:function(){var t=Number(this.scaleMin);return isNaN(t)?.5:t},scaleMaxNumber:function(){var t=Number(this.scaleMax);return isNaN(t)?10:t},xMove:function(){return"all"===this.direction||"horizontal"===this.direction},yMove:function(){return"all"===this.direction||"vertical"===this.direction}},watch:{x:function(t){this.xSync=this._getPx(t)},xSync:function(t){this._setX(t)},y:function(t){this.ySync=this._getPx(t)},ySync:function(t){this._setY(t)},scaleValue:function(t){this.scaleValueSync=Number(t)||0},scaleValueSync:function(t){this._setScaleValue(t)},scaleMinNumber:function(){this._setScaleMinOrMax()},scaleMaxNumber:function(){this._setScaleMinOrMax()}},created:function(){this._offset={x:0,y:0},this._scaleOffset={x:0,y:0},this._translateX=0,this._translateY=0,this._scale=1,this._oldScale=1,this._STD=new h(1,9*Math.pow(this.dampingNumber,2)/40,this.dampingNumber),this._friction=new u(1,this.frictionNumber),this._declineX=new c,this._declineY=new c,this.__touchInfo={historyX:[0,0],historyY:[0,0],historyT:[0,0]}},mounted:function(){this.touchtrack(this.$el,"_onTrack"),this.setParent(),this._friction.reconfigure(1,this.frictionNumber),this._STD.reconfigure(1,9*Math.pow(this.dampingNumber,2)/40,this.dampingNumber),this.$el.style.transformOrigin="center"},methods:{_getPx:function(t){return/\d+[ur]px$/i.test(t)?uni.upx2px(parseFloat(t)):Number(t)||0},_setX:function(t){if(this.xMove){if(t+this._scaleOffset.x===this._translateX)return this._translateX;this._SFA&&this._SFA.cancel(),this._animationTo(t+this._scaleOffset.x,this.ySync+this._scaleOffset.y,this._scale)}return t},_setY:function(t){if(this.yMove){if(t+this._scaleOffset.y===this._translateY)return this._translateY;this._SFA&&this._SFA.cancel(),this._animationTo(this.xSync+this._scaleOffset.x,t+this._scaleOffset.y,this._scale)}return t},_setScaleMinOrMax:function(){if(!this.scale)return!1;this._updateScale(this._scale,!0),this._updateOldScale(this._scale)},_setScaleValue:function(t){return!!this.scale&&(t=this._adjustScale(t),this._updateScale(t,!0),this._updateOldScale(t),t)},__handleTouchStart:function(){this._isScaling||this.disabled||(Object(f["e"])({disable:!0}),this._FA&&this._FA.cancel(),this._SFA&&this._SFA.cancel(),this.__touchInfo.historyX=[0,0],this.__touchInfo.historyY=[0,0],this.__touchInfo.historyT=[0,0],this.xMove&&(this.__baseX=this._translateX),this.yMove&&(this.__baseY=this._translateY),this.$el.style.willChange="transform",this._checkCanMove=null,this._firstMoveDirection=null,this._isTouching=!0)},__handleTouchMove:function(t){var e=this;if(!this._isScaling&&!this.disabled&&this._isTouching){var n=this._translateX,i=this._translateY;if(null===this._firstMoveDirection&&(this._firstMoveDirection=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),this.xMove&&(n=t.detail.dx+this.__baseX,this.__touchInfo.historyX.shift(),this.__touchInfo.historyX.push(n),this.yMove||null!==this._checkCanMove||(this._checkCanMove=Math.abs(t.detail.dx/t.detail.dy)<1)),this.yMove&&(i=t.detail.dy+this.__baseY,this.__touchInfo.historyY.shift(),this.__touchInfo.historyY.push(i),this.xMove||null!==this._checkCanMove||(this._checkCanMove=Math.abs(t.detail.dy/t.detail.dx)<1)),this.__touchInfo.historyT.shift(),this.__touchInfo.historyT.push(t.detail.timeStamp),!this._checkCanMove){t.preventDefault();var r="touch";n<this.minX?this.outOfBounds?(r="touch-out-of-bounds",n=this.minX-this._declineX.x(this.minX-n)):n=this.minX:n>this.maxX&&(this.outOfBounds?(r="touch-out-of-bounds",n=this.maxX+this._declineX.x(n-this.maxX)):n=this.maxX),i<this.minY?this.outOfBounds?(r="touch-out-of-bounds",i=this.minY-this._declineY.x(this.minY-i)):i=this.minY:i>this.maxY&&(this.outOfBounds?(r="touch-out-of-bounds",i=this.maxY+this._declineY.x(i-this.maxY)):i=this.maxY),p((function(){e._setTransform(n,i,e._scale,r)}))}}},__handleTouchEnd:function(){var t=this;if(!this._isScaling&&!this.disabled&&this._isTouching&&(Object(f["e"])({disable:!1}),this.$el.style.willChange="auto",this._isTouching=!1,!this._checkCanMove&&!this._revise("out-of-bounds")&&this.inertia)){var e=1e3*(this.__touchInfo.historyX[1]-this.__touchInfo.historyX[0])/(this.__touchInfo.historyT[1]-this.__touchInfo.historyT[0]),n=1e3*(this.__touchInfo.historyY[1]-this.__touchInfo.historyY[0])/(this.__touchInfo.historyT[1]-this.__touchInfo.historyT[0]);this._friction.setV(e,n),this._friction.setS(this._translateX,this._translateY);var i=this._friction.delta().x,r=this._friction.delta().y,o=i+this._translateX,a=r+this._translateY;o<this.minX?(o=this.minX,a=this._translateY+(this.minX-this._translateX)*r/i):o>this.maxX&&(o=this.maxX,a=this._translateY+(this.maxX-this._translateX)*r/i),a<this.minY?(a=this.minY,o=this._translateX+(this.minY-this._translateY)*i/r):a>this.maxY&&(a=this.maxY,o=this._translateX+(this.maxY-this._translateY)*i/r),this._friction.setEnd(o,a),this._FA=b(this._friction,(function(){var e=t._friction.s(),n=e.x,i=e.y;t._setTransform(n,i,t._scale,"friction")}),(function(){t._FA.cancel()}))}},_onTrack:function(t){switch(t.detail.state){case"start":this.__handleTouchStart();break;case"move":this.__handleTouchMove(t);break;case"end":this.__handleTouchEnd()}},_getLimitXY:function(t,e){var n=!1;return t>this.maxX?(t=this.maxX,n=!0):t<this.minX&&(t=this.minX,n=!0),e>this.maxY?(e=this.maxY,n=!0):e<this.minY&&(e=this.minY,n=!0),{x:t,y:e,outOfBounds:n}},setParent:function(){if(this.$parent._isMounted){this._FA&&this._FA.cancel(),this._SFA&&this._SFA.cancel();var t=this.scale?this.scaleValueSync:1;this._updateOffset(),this._updateWH(t),this._updateBoundary(),this._translateX=this.xSync+this._scaleOffset.x,this._translateY=this.ySync+this._scaleOffset.y;var e=this._getLimitXY(this._translateX,this._translateY),n=e.x,i=e.y;this._setTransform(n,i,t,"",!0),this._updateOldScale(t)}},_updateOffset:function(){this._offset.x=g(this.$el,this.$parent.$el),this._offset.y=v(this.$el,this.$parent.$el)},_updateWH:function(t){t=t||this._scale,t=this._adjustScale(t);var e=this.$el.getBoundingClientRect();this.height=e.height/this._scale,this.width=e.width/this._scale;var n=this.height*t,i=this.width*t;this._scaleOffset.x=(i-this.width)/2,this._scaleOffset.y=(n-this.height)/2},_updateBoundary:function(){var t=0-this._offset.x+this._scaleOffset.x,e=this.$parent.width-this.width-this._offset.x-this._scaleOffset.x;this.minX=Math.min(t,e),this.maxX=Math.max(t,e);var n=0-this._offset.y+this._scaleOffset.y,i=this.$parent.height-this.height-this._offset.y-this._scaleOffset.y;this.minY=Math.min(n,i),this.maxY=Math.max(n,i)},_beginScale:function(){this._isScaling=!0},_endScale:function(){this._isScaling=!1,this._updateOldScale(this._scale)},_setScale:function(t){this.scale&&(t=this._adjustScale(t),t=this._oldScale*t,this._beginScale(),this._updateScale(t))},_updateScale:function(t,e){var n=this;if(this.scale){t=this._adjustScale(t),this._updateWH(t),this._updateBoundary();var i=this._getLimitXY(this._translateX,this._translateY),r=i.x,o=i.y;e?this._animationTo(r,o,t,"",!0,!0):p((function(){n._setTransform(r,o,t,"",!0,!0)}))}},_updateOldScale:function(t){this._oldScale=t},_adjustScale:function(t){return t=Math.max(.5,this.scaleMinNumber,t),t=Math.min(10,this.scaleMaxNumber,t),t},_animationTo:function(t,e,n,i,r,o){var a=this;this._FA&&this._FA.cancel(),this._SFA&&this._SFA.cancel(),this.xMove||(t=this._translateX),this.yMove||(e=this._translateY),this.scale||(n=this._scale);var s=this._getLimitXY(t,e);t=s.x,e=s.y,this.animation?(this._STD._springX._solution=null,this._STD._springY._solution=null,this._STD._springScale._solution=null,this._STD._springX._endPosition=this._translateX,this._STD._springY._endPosition=this._translateY,this._STD._springScale._endPosition=this._scale,this._STD.setEnd(t,e,n,1),this._SFA=b(this._STD,(function(){var t=a._STD.x(),e=t.x,n=t.y,s=t.scale;a._setTransform(e,n,s,i,r,o)}),(function(){a._SFA.cancel()}))):this._setTransform(t,e,n,i,r,o)},_revise:function(t){var e=this._getLimitXY(this._translateX,this._translateY),n=e.x,i=e.y,r=e.outOfBounds;return r&&this._animationTo(n,i,this._scale,t),r},_setTransform:function(t,e,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",r=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0;null!==t&&"NaN"!==t.toString()&&"number"===typeof t||(t=this._translateX||0),null!==e&&"NaN"!==e.toString()&&"number"===typeof e||(e=this._translateY||0),t=Number(t.toFixed(1)),e=Number(e.toFixed(1)),n=Number(n.toFixed(1)),this._translateX===t&&this._translateY===e||r||this.$trigger("change",{},{x:m(t,this._scaleOffset.x),y:m(e,this._scaleOffset.y),source:i}),this.scale||(n=this._scale),n=this._adjustScale(n),n=+n.toFixed(3),o&&n!==this._scale&&this.$trigger("scale",{},{x:t,y:e,scale:n});var a="translateX("+t+"px) translateY("+e+"px) translateZ(0px) scale("+n+")";this.$el.style.transform=a,this.$el.style.webkitTransform=a,this._translateX=t,this._translateY=e,this._scale=n}}},_=y,w=(n("7c2b"),n("2877")),S=Object(w["a"])(_,i,r,!1,null,null,null);e["default"]=S.exports},8875:function(t,e,n){var i,r,o;(function(n,a){r=[],i=a,o="function"===typeof i?i.apply(e,r):i,void 0===o||(t.exports=o)})("undefined"!==typeof self&&self,(function(){function t(){if(document.currentScript)return document.currentScript;try{throw new Error}catch(h){var t,e,n,i=/.*at [^(]*\((.*):(.+):(.+)\)$/gi,r=/@([^@]*):(\d+):(\d+)\s*$/gi,o=i.exec(h.stack)||r.exec(h.stack),a=o&&o[1]||!1,s=o&&o[2]||!1,c=document.location.href.replace(document.location.hash,""),u=document.getElementsByTagName("script");a===c&&(t=document.documentElement.outerHTML,e=new RegExp("(?:[^\\n]+?\\n){0,"+(s-2)+"}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*","i"),n=t.replace(e,"$1").trim());for(var l=0;l<u.length;l++){if("interactive"===u[l].readyState)return u[l];if(u[l].src===a)return u[l];if(a===c&&u[l].innerHTML&&u[l].innerHTML.trim()===n)return u[l]}return null}}return t}))},"893e":function(t,e,n){"use strict";n.r(e),function(t,i){function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}n.d(e,"connectSocket",(function(){return p})),n.d(e,"sendSocketMessage",(function(){return g})),n.d(e,"closeSocket",(function(){return v})),n.d(e,"onSocketOpen",(function(){return b})),n.d(e,"onSocketError",(function(){return y})),n.d(e,"onSocketMessage",(function(){return _})),n.d(e,"onSocketClose",(function(){return w}));var c=t,u=c.invokeCallbackHandler,l=["open","close","error","message"],h={},f=[],d=function(){function t(e,n,o){var a,c=this;r(this,t),s(this,"_webSocket",void 0);try{var d=this._webSocket=new WebSocket(e,n);d.binaryType="arraybuffer",this._callbacks={},l.forEach((function(t){c._callbacks[t]=[],d.addEventListener(t,(function(e){var n="message"===t?{data:e.data}:{};if(c._callbacks[t].forEach((function(e){try{e(n)}catch(r){i.error("thirdScriptError\n".concat(r,";at socketTask.on").concat(t[0].toUpperCase()+t.substr(1)," callback function\n"),r)}})),c===f[0]&&h[t]&&u(h[t],n),"error"===t||"close"===t){var r=f.indexOf(c);r>=0&&f.splice(r,1)}}))}));var p=["CLOSED","CLOSING","CONNECTING","OPEN","readyState"];p.forEach((function(t){Object.defineProperty(c,t,{get:function(){return d[t]}})}))}catch(g){a=g}o(a,this)}return a(t,[{key:"send",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.data,n=this._webSocket;try{if(n.readyState!==n.OPEN)throw new Error("SocketTask.readyState is not OPEN");n.send(e),this._callback(t,"sendSocketMessage:ok")}catch(i){this._callback(t,"sendSocketMessage:fail ".concat(i))}}},{key:"close",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=this._webSocket,n=[];n.push(t.code||1e3),"string"===typeof t.reason&&n.push(t.reason);try{e.close.apply(e,n),this._callback(t,"sendSocketMessage:ok")}catch(i){this._callback(t,"sendSocketMessage:fail ".concat(i))}}},{key:"_callback",value:function(t,e){var n=t.success,i=t.fail,r=t.complete,o={errMsg:e};/:ok$/.test(e)?"function"===typeof n&&n(o):"function"===typeof i&&i(o),"function"===typeof r&&r(o)}}]),t}();function p(t,e){var n=t.url,i=t.protocols;return new d(n,i,(function(t,n){t||f.push(n),u(e,{errMsg:"connectSocket:"+(t?"fail ".concat(t):"ok")})}))}function g(t,e){var n=f[0];n&&n.readyState===n.OPEN?n.send(Object.assign({},t,{complete:function(t){u(e,t)}})):u(e,{errMsg:"sendSocketMessage:fail WebSocket is not connected "})}function v(t,e){var n=f[0];n?n.close(Object.assign({},t,{complete:function(t){u(e,t)}})):u(e,{errMsg:"closeSocket:fail WebSocket is not connected"})}function m(t){return function(e){h[t]=e}}l.forEach((function(t){var e=t[0].toUpperCase()+t.substr(1);d.prototype["on".concat(e)]=function(e){this._callbacks[t].push(e)}}));var b=m("open"),y=m("error"),_=m("message"),w=m("close")}.call(this,n("0dd1"),n("3ad9")["default"])},"898f":function(t,e,n){"use strict";n.r(e),n.d(e,"previewImage",(function(){return a}));var i=n("db70"),r="longPressActionsCallback",o={};function a(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o=t.longPressActions||{},(o.success||o.fail||o.complete)&&(o.callbackId=r),Object(i["c"])("previewImagePlus",t)}Object(i["d"])(r,(function(t){var e=t.errMsg||"";new RegExp("\\:\\s*fail").test(e)?o.fail&&o.fail(t):o.success&&o.success(t),o.complete&&o.complete(t)}))},"8a36":function(t,e,n){"use strict";(function(t){var i=n("f2b3");e["a"]={props:{id:{type:String,default:""}},created:function(){var t=this;this._addListeners(this.id),this.$watch("id",(function(e,n){t._removeListeners(n,!0),t._addListeners(e,!0)}))},beforeDestroy:function(){this._removeListeners(this.id)},methods:{_addListeners:function(e,n){var r=this;if(!n||e){var o=this.$options.listeners;Object(i["k"])(o)&&Object.keys(o).forEach((function(i){n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&t.on("uni-".concat(i,"-").concat(r.$page.id,"-").concat(e),r[o[i]]):0===i.indexOf("@")?r.$on("uni-".concat(i.substr(1)),r[o[i]]):0===i.indexOf("uni-")?t.on(i,r[o[i]]):e&&t.on("uni-".concat(i,"-").concat(r.$page.id,"-").concat(e),r[o[i]])}))}},_removeListeners:function(e,n){var r=this;if(!n||e){var o=this.$options.listeners;Object(i["k"])(o)&&Object.keys(o).forEach((function(i){n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&t.off("uni-".concat(i,"-").concat(r.$page.id,"-").concat(e),r[o[i]]):0===i.indexOf("@")?r.$off("uni-".concat(i.substr(1)),r[o[i]]):0===i.indexOf("uni-")?t.off(i,r[o[i]]):e&&t.off("uni-".concat(i,"-").concat(r.$page.id,"-").concat(e),r[o[i]])}))}}}}}).call(this,n("501c"))},"8aec":function(t,e,n){"use strict";var i=n("5363"),r=n("72b3");function o(t,e,n){this._extent=t,this._friction=e||new i["a"](.01),this._spring=n||new r["a"](1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}function a(t,e,n){function i(t,e,n,r){if(!t||!t.cancelled){n(e);var o=e.done();o||t.cancelled||(t.id=requestAnimationFrame(i.bind(null,t,e,n,r))),o&&r&&r(e)}}function r(t){t&&t.id&&cancelAnimationFrame(t.id),t&&(t.cancelled=!0)}var o={id:0,cancelled:!1};return i(o,t,e,n),{cancel:r.bind(null,o),model:t}}function s(t,e){e=e||{},this._element=t,this._options=e,this._enableSnap=e.enableSnap||!1,this._itemSize=e.itemSize||0,this._enableX=e.enableX||!1,this._enableY=e.enableY||!1,this._shouldDispatchScrollEvent=!!e.onScroll,this._enableX?(this._extent=(e.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=e.scrollWidth):(this._extent=(e.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=e.scrollHeight),this._position=0,this._scroll=new o(this._extent,e.friction,e.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}o.prototype.snap=function(t,e){this._springOffset=0,this._springing=!0,this._spring.snap(t),this._spring.setEnd(e)},o.prototype.set=function(t,e){this._friction.set(t,e),t>0&&e>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(t),this._spring.setEnd(0)):t<-this._extent&&e<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(t),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()},o.prototype.x=function(t){if(!this._startTime)return 0;if(t||(t=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;var e=this._friction.x(t),n=this.dx(t);return(e>0&&n>=0||e<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),e<-this._extent?this._springOffset=-this._extent:this._springOffset=0,e=this._spring.x()+this._springOffset),e},o.prototype.dx=function(t){var e=0;return e=this._lastTime===t?this._lastDx:this._springing?this._spring.dx(t):this._friction.dx(t),this._lastTime=t,this._lastDx=e,e},o.prototype.done=function(){return this._springing?this._spring.done():this._friction.done()},o.prototype.setVelocityByEnd=function(t){this._friction.setVelocityByEnd(t)},o.prototype.configuration=function(){var t=this._friction.configuration();return t.push.apply(t,this._spring.configuration()),t},s.prototype.onTouchStart=function(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()},s.prototype.onTouchMove=function(t,e){var n=this._startPosition;this._enableX?n+=t:this._enableY&&(n+=e),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()},s.prototype.onTouchEnd=function(t,e,n){var i=this;if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(e)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(t)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){var r=this._scroll._friction.x(100),o=r%this._itemSize,s=Math.abs(o)>this._itemSize/2?r-(this._itemSize-Math.abs(o)):r-o;s<=0&&s>=-this._extent&&this._scroll.setVelocityByEnd(s)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=a(this._scroll,(function(){var t=Date.now(),e=(t-i._scroll._startTime)/1e3,n=i._scroll.x(e);i._position=n,i.updatePosition();var r=i._scroll.dx(e);i._shouldDispatchScrollEvent&&t-i._lastTime>i._lastDelay&&(i.dispatchScroll(),i._lastDelay=Math.abs(2e3/r),i._lastTime=t)}),(function(){i._enableSnap&&(s<=0&&s>=-i._extent&&(i._position=s,i.updatePosition()),"function"===typeof i._options.onSnap&&i._options.onSnap(Math.floor(Math.abs(i._position)/i._itemSize))),i._shouldDispatchScrollEvent&&i.dispatchScroll(),i._scrolling=!1}))},s.prototype.onTransitionEnd=function(){this._element.style.transition="",this._element.style.webkitTransition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._element.removeEventListener("webkitTransitionEnd",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()},s.prototype.snap=function(){var t=this._itemSize,e=this._position%t,n=Math.abs(e)>this._itemSize/2?this._position-(t-Math.abs(e)):this._position-e;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),"function"===typeof this._options.onSnap&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))},s.prototype.scrollTo=function(t,e){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"===typeof t&&(this._position=-t),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0),this._element.style.transition="transform "+(e||.2)+"s ease-out",this._element.style.webkitTransition="-webkit-transform "+(e||.2)+"s ease-out",this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd),this._element.addEventListener("webkitTransitionEnd",this._onTransitionEnd)},s.prototype.dispatchScroll=function(){if("function"===typeof this._options.onScroll&&Math.round(this._lastPos)!==Math.round(this._position)){this._lastPos=this._position;var t={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(t)}},s.prototype.update=function(t,e,n){var i=0,r=this._position;this._enableX?(i=this._element.childNodes.length?(e||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=e):(i=this._element.childNodes.length?(e||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=e),"number"===typeof t&&(this._position=-t),this._position<-i?this._position=-i:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),"function"===typeof this._options.onSnap&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=i,this._scroll._extent=i},s.prototype.updatePosition=function(){var t="";this._enableX?t="translateX("+this._position+"px) translateZ(0)":this._enableY&&(t="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=t,this._element.style.transform=t},s.prototype.isScrolling=function(){return this._scrolling||this._snapping};e["a"]={methods:{initScroller:function(t,e){this._touchInfo={trackingID:-1,maxDy:0,maxDx:0},this._scroller=new s(t,e),this.__handleTouchStart=this._handleTouchStart.bind(this),this.__handleTouchMove=this._handleTouchMove.bind(this),this.__handleTouchEnd=this._handleTouchEnd.bind(this),this._initedScroller=!0},_findDelta:function(t){var e=this._touchInfo;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:t.screenX-e.x,y:t.screenY-e.y}},_handleTouchStart:function(t){var e=this._touchInfo,n=this._scroller;n&&("start"===t.detail.state?(e.trackingID="touch",e.x=t.detail.x,e.y=t.detail.y):(e.trackingID="mouse",e.x=t.screenX,e.y=t.screenY),e.maxDx=0,e.maxDy=0,e.historyX=[0],e.historyY=[0],e.historyTime=[t.detail.timeStamp],e.listener=n,n.onTouchStart&&n.onTouchStart(),event.preventDefault())},_handleTouchMove:function(t){var e=this._touchInfo;if(-1!==e.trackingID){t.preventDefault();var n=this._findDelta(t);if(n){for(e.maxDy=Math.max(e.maxDy,Math.abs(n.y)),e.maxDx=Math.max(e.maxDx,Math.abs(n.x)),e.historyX.push(n.x),e.historyY.push(n.y),e.historyTime.push(t.detail.timeStamp);e.historyTime.length>10;)e.historyTime.shift(),e.historyX.shift(),e.historyY.shift();e.listener&&e.listener.onTouchMove&&e.listener.onTouchMove(n.x,n.y,t.detail.timeStamp)}}},_handleTouchEnd:function(t){var e=this._touchInfo;if(-1!==e.trackingID){t.preventDefault();var n=this._findDelta(t);if(n){var i=e.listener;e.trackingID=-1,e.listener=null;var r=e.historyTime.length,o={x:0,y:0};if(r>2)for(var a=e.historyTime.length-1,s=e.historyTime[a],c=e.historyX[a],u=e.historyY[a];a>0;){a--;var l=e.historyTime[a],h=s-l;if(h>30&&h<50){o.x=(c-e.historyX[a])/(h/1e3),o.y=(u-e.historyY[a])/(h/1e3);break}}e.historyTime=[],e.historyX=[],e.historyY=[],i&&i.onTouchEnd&&i.onTouchEnd(n.x,n.y,o)}}}}}},"8af1":function(t,e,n){"use strict";function i(t,e){for(var n=this.$children,r=n.length,o=arguments.length,a=new Array(o>2?o-2:0),s=2;s<o;s++)a[s-2]=arguments[s];for(var c=0;c<r;c++){var u=n[c],l=u.$options.name&&u.$options.name.substr(4);if(~t.indexOf(l))return u.$emit.apply(u,[e].concat(a)),!1;if(!1===i.apply(u,[t,e].concat([a])))return!1}}n.d(e,"b",(function(){return o})),n.d(e,"f",(function(){return a["a"]})),n.d(e,"c",(function(){return s["a"]})),n.d(e,"g",(function(){return c["a"]})),n.d(e,"e",(function(){return u["a"]})),n.d(e,"a",(function(){return h})),n.d(e,"d",(function(){return m}));var r,o={methods:{$dispatch:function(t,e){"string"===typeof t&&(t=[t]);var n=this.$parent||this.$root,i=n.$options.name&&n.$options.name.substr(4);while(n&&(!i||!~t.indexOf(i)))n=n.$parent,n&&(i=n.$options.name&&n.$options.name.substr(4));if(n){for(var r=arguments.length,o=new Array(r>2?r-2:0),a=2;a<r;a++)o[a-2]=arguments[a];n.$emit.apply(n,[e].concat(o))}},$broadcast:function(t,e){"string"===typeof t&&(t=[t]);for(var n=arguments.length,r=new Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];i.apply(this,[t,e].concat(r))}}},a=n("8a36"),s=n("83a6"),c=n("1b6f"),u=n("5a23"),l=n("f2b3"),h={name:"BaseInput",mixins:[o,u["a"]],model:{prop:"value",event:"update:value"},props:{value:{type:[String,Number],default:""}},data:function(){return{valueSync:this._getValueString(this.value)}},created:function(){var t=this,e=this.__valueChange=Object(l["b"])((function(e){t.valueSync=t._getValueString(e)}),100);this.$watch("value",e),this.__triggerInput=Object(l["p"])((function(e,n){t.$emit("update:value",n.value),t.$trigger("input",e,n)}),100),this.$triggerInput=function(e,n){t.__valueChange.cancel(),t.__triggerInput(e,n)}},beforeDestroy:function(){this.__valueChange.cancel(),this.__triggerInput.cancel()},methods:{_getValueString:function(t){return null===t?"":String(t)}}},f=!l["o"]||{passive:!0,capture:!0},d=[],p=0;function g(t){if(!r){var e=["touchstart","touchmove","touchend","mousedown","mouseup"];e.forEach((function(t){document.addEventListener(t,(function(){d.forEach((function(t){t.userInteract=!0,p++,setTimeout((function(){p--,p||(t.userInteract=!1)}),0)}))}),f)}))}d.push(t)}function v(t){var e=d.indexOf(t);e>=0&&d.splice(e,1)}var m={data:function(){return{userInteract:!1}},mounted:function(){g(this)},beforeDestroy:function(){v(this)}}},"8b18":function(t,e,n){},"8b3f":function(t,e,n){"use strict";n.r(e),n.d(e,"onNetworkStatusChange",(function(){return a}));var i=n("a118"),r=n("db70"),o=[];function a(t){o.push(t)}Object(r["d"])("onNetworkStatusChange",(function(t){o.forEach((function(e){Object(i["a"])(e,t)}))}))},"8b61":function(t,e,n){},"8bbf":function(t,n){t.exports=e},"8c15":function(t,e,n){"use strict";n.r(e),function(t){var i=n("85b6"),r=n("d4b6"),o=n("61c2"),a=n("c4c5");function s(){t.publishHandler("onPageReady",{},this.$page.id)}e["default"]={install:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e.routes;Object(r["a"])();var n=function(t,e){for(var n=t.target;n&&n!==e;n=n.parentNode)if(n.tagName&&0===n.tagName.indexOf("UNI-"))break;return n};t.prototype.$handleEvent=function(t){if(t instanceof Event){var e=n(t,this.$el);t=r["b"].call(this,t.type,t,{},e||t.target,t.currentTarget)}return t},t.prototype.$getComponentDescriptor=function(t,e){return Object(a["a"])(t||this,e)},t.prototype.$handleWxsEvent=function(t){if(t instanceof Event){var e=t.currentTarget,i=e&&e.__vue__&&e.__vue__.$getComponentDescriptor(e.__vue__,!1);t=r["b"].call(this,t.type,t,{},n(t,this.$el)||t.target,t.currentTarget),t.instance=i}return t},t.mixin({beforeCreate:function(){var t=this,e=this.$options,n=e.wxs;n&&Object.keys(n).forEach((function(e){t[e]=n[e]})),e.behaviors&&e.behaviors.length&&Object(o["a"])(e,this),Object(i["b"])(this)&&(e.mounted=e.mounted?[].concat(s,e.mounted):[s])}})}}}.call(this,n("501c"))},"8ce3":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"chooseVideo",(function(){return u}));var i=n("e2e2"),r=n("f2b3"),o=t,a=o.invokeCallbackHandler,s=null,c=function(t){var e=document.createElement("input");return e.type="file",Object(r["r"])(e,{position:"absolute",visibility:"hidden","z-index":-999,width:0,height:0,top:0,left:0}),e.accept="video/*",1===t.sourceType.length&&"camera"===t.sourceType[0]&&(e.capture="camera"),e};function u(t,e){var n=t.sourceType;s&&(document.body.removeChild(s),s=null),s=c({sourceType:n}),document.body.appendChild(s),s.addEventListener("change",(function(t){var n,r=t.target.files[0],o={errMsg:"chooseVideo:ok",tempFile:r,size:r.size,duration:0,width:0,height:0,name:r.name};Object.defineProperty(o,"tempFilePath",{get:function(){return n=n||Object(i["a"])(this.tempFile),n}});var s=document.createElement("video");if(void 0!==s.onloadedmetadata){var c=Object(i["a"])(r);s.onloadedmetadata=function(){Object(i["b"])(c),a(e,Object.assign(o,{duration:s.duration||0,width:s.videoWidth||0,height:s.videoHeight||0}))},setTimeout((function(){s.onloadedmetadata=null,Object(i["b"])(c),a(e,o)}),300),s.src=c}else a(e,o)})),s.click()}}.call(this,n("0dd1"))},"8e16":function(t,e,n){"use strict";var i=n("ea49"),r=n.n(i);r.a},"8f7e":function(t,e,n){"use strict";n.r(e);var i=n("8bbf"),r=n.n(i),o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-app",{class:{"uni-app--showtabbar":t.showTabBar}},[n("keep-alive",{attrs:{include:t.keepAliveInclude}},[n("router-view",{key:t.key})],1),t.hasTabBar?n("tab-bar",t._b({directives:[{name:"show",rawName:"v-show",value:t.showTabBar,expression:"showTabBar"}]},"tab-bar",t.tabBar,!1)):t._e(),t.$options.components.Toast?n("toast",t._b({},"toast",t.showToast,!1)):t._e(),t.$options.components.ActionSheet?n("action-sheet",t._b({on:{close:t._onActionSheetClose}},"action-sheet",t.showActionSheet,!1)):t._e(),t.$options.components.Modal?n("modal",t._b({on:{close:t._onModalClose}},"modal",t.showModal,!1)):t._e()],1)},a=[],s=n("cdc1"),c=s["a"],u=(n("854d"),n("2877")),l=Object(u["a"])(c,o,a,!1,null,null,null),h=l.exports,f=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-page",{attrs:{"data-page":t.$route.meta.pagePath}},["none"!==t.navigationBar.type?n("page-head",t._b({},"page-head",t.navigationBar,!1)):t._e(),t.enablePullDownRefresh?n("page-refresh",{ref:"refresh",attrs:{color:t.refreshOptions.color,offset:t.refreshOptions.offset}}):t._e(),t.enablePullDownRefresh?n("page-body",{nativeOn:{touchstart:function(e){return t._touchstart(e)},touchmove:function(e){return t._touchmove(e)},touchend:function(e){return t._touchend(e)},touchcancel:function(e){return t._touchend(e)}}},[t._t("page")],2):n("page-body",[t._t("page")],2)],1)},d=[],p=n("27d0"),g=p["a"],v=(n("6226"),Object(u["a"])(g,f,d,!1,null,null,null)),m=v.exports,b=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"uni-async-error",on:{click:t._onClick}},[t._v(" 连接服务器超时，点击屏幕重试 ")])},y=[],_={name:"AsyncError",methods:{_onClick:function(){window.location.reload()}}},w=_,S=(n("b628"),Object(u["a"])(w,b,y,!1,null,null,null)),k=S.exports,T=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},x=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"uni-async-loading"},[n("i",{staticClass:"uni-loading"})])}],C={name:"AsyncLoading"},O=C,E=(n("5727"),Object(u["a"])(O,T,x,!1,null,null,null)),M=E.exports,j=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"uni-system-choose-location"},[n("system-header",{attrs:{confirm:!!t.data},on:{back:t._back,confirm:t._choose}},[t._v(" 选择位置 ")]),n("div",{staticClass:"map-content"},[n("iframe",{attrs:{src:t.src,allow:"geolocation",seamless:"",sandbox:"allow-scripts allow-same-origin allow-forms",frameborder:"0"}})])],1)},A=[],I=n("fda5"),$=I["a"],P=(n("9470"),Object(u["a"])($,j,A,!1,null,null,null)),B=P.exports,L=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"uni-system-open-location"},[n("system-header",{on:{back:t._back}},[t._v(" 位置 ")]),n("div",{staticClass:"map-content"},[n("iframe",{ref:"map",attrs:{src:t.src,allow:"geolocation",sandbox:"allow-scripts allow-same-origin allow-forms allow-top-navigation allow-modals allow-popups",frameborder:"0"},on:{load:t._load}}),t.isPoimarkerSrc?n("div",{staticClass:"actTonav",on:{click:t._nav}}):t._e()])],1)},N=[],D=n("bab8"),R=__uniConfig.qqMapKey,z="uniapp",F="https://apis.map.qq.com/tools/poimarker",q={name:"SystemOpenLocation",components:{SystemHeader:D["a"]},data:function(){var t=this.$route.query,e=t.latitude,n=t.longitude,i=t.scale,r=void 0===i?18:i,o=t.name,a=void 0===o?"":o,s=t.address,c=void 0===s?"":s;return{latitude:e,longitude:n,scale:r,name:a,address:c,src:"",isPoimarkerSrc:!1}},mounted:function(){this.latitude&&this.longitude&&(this.src="".concat(F,"?type=0&marker=coord:").concat(this.latitude,",").concat(this.longitude,";title:").concat(this.name,";addr:").concat(this.address,";&key=").concat(R,"&referer=").concat(z))},methods:{_back:function(){0!==this.$refs.map.src.indexOf(F)?this.$refs.map.src=this.src:getApp().$router.back()},_load:function(){0===this.$refs.map.src.indexOf(F)?this.isPoimarkerSrc=!0:this.isPoimarkerSrc=!1},_nav:function(){var t="https://map.qq.com/nav/drive#routes/page?transport=2&epointy=".concat(this.latitude,"&epointx=").concat(this.longitude,"&eword=").concat(encodeURIComponent(this.name||"目的地"),"&referer=").concat(z);this.$refs.map.src=t}}},H=q,V=(n("3da9"),Object(u["a"])(H,L,N,!1,null,null,null)),U=V.exports,Y=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"uni-system-preview-image",on:{click:t._click}},[n("v-uni-swiper",{staticClass:"uni-swiper",attrs:{current:t.index,"indicator-dots":!1,autoplay:!1},on:{"update:current":function(e){t.index=e}}},t._l(t.urls,(function(t,e){return n("v-uni-swiper-item",{key:e},[n("img",{staticClass:"uni-preview-image",attrs:{src:t}})])})),1)],1)},X=[],W={name:"SystemPreviewImage",data:function(){var t=this.$route.params,e=t.urls,n=t.current;return{urls:e||[],current:n,index:0}},created:function(){var t="number"===typeof this.current?this.current:this.urls.indexOf(this.current);this.index=t<0?0:t},mounted:function(){var t=this,e=20,n=0,i=0;this.$el.addEventListener("mousedown",(function(e){t.preventDefault=!1,n=e.clientX,i=e.clientY})),this.$el.addEventListener("mouseup",(function(r){(Math.abs(r.clientX-n)>e||Math.abs(r.clientY-i)>e)&&(t.preventDefault=!0)}))},methods:{_click:function(){this.preventDefault||getApp().$router.back()}}},G=W,K=(n("f10e"),Object(u["a"])(G,Y,X,!1,null,null,null)),Q=K.exports,Z={ChooseLocation:B,OpenLocation:U,PreviewImage:Q};r.a.component(h.name,h),r.a.component(m.name,m),r.a.component(k.name,k),r.a.component(M.name,M),Object.keys(Z).forEach((function(t){var e=Z[t];r.a.component(e.name,e)}))},"8fa5":function(t,e,n){},"90f7":function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-page-refresh",[n("div",{staticClass:"uni-page-refresh",style:{"margin-top":t.offset+"px"}},[n("div",{staticClass:"uni-page-refresh-inner"},[n("svg",{staticClass:"uni-page-refresh__icon",attrs:{fill:t.color,width:"24",height:"24",viewBox:"0 0 24 24"}},[n("path",{attrs:{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"}}),n("path",{attrs:{d:"M0 0h24v24H0z",fill:"none"}})]),n("svg",{staticClass:"uni-page-refresh__spinner",attrs:{width:"24",height:"24",viewBox:"25 25 50 50"}},[n("circle",{staticClass:"uni-page-refresh__path",attrs:{stroke:t.color,cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"4","stroke-miterlimit":"10"}})])])])])},r=[],o={name:"PageRefresh",props:{color:{type:String,default:"#2BD009"},offset:{type:Number,default:0}}},a=o,s=(n("9b5b"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["a"]=c.exports},9213:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-swiper-item",t._g({},t.$listeners),[t._t("default")],2)},r=[],o={name:"SwiperItem",props:{itemId:{type:String,default:""}},mounted:function(){var t=this.$el;t.style.position="absolute",t.style.width="100%",t.style.height="100%";var e=this.$vnode._callbacks;e&&e.forEach((function(t){t()}))}},a=o,s=(n("bfea"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},"924c":function(t,e,n){"use strict";n.r(e),function(t){function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function o(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t}function a(e,n,i,r){t.publishHandler(n+"-audio-"+e,{audioId:e,type:i,data:r},n)}n.d(e,"createAudioContext",(function(){return c}));var s=function(){function t(e,n){i(this,t),this.id=e,this.pageId=n}return o(t,[{key:"setSrc",value:function(t){a(this.id,this.pageId,"setSrc",{src:t})}},{key:"play",value:function(){a(this.id,this.pageId,"play")}},{key:"pause",value:function(){a(this.id,this.pageId,"pause")}},{key:"stop",value:function(){a(this.id,this.pageId,"stop")}},{key:"seek",value:function(t){a(this.id,this.pageId,"seek",{position:t})}}]),t}();function c(e,n){if(n)return new s(e,n.$page.id);var i=getApp();if(i.$route&&i.$route.params.__id__)return new s(e,i.$route.params.__id__);t.emit("onError","createAudioContext:fail")}}.call(this,n("0dd1"))},9250:function(t,e){var n=["base64ToArrayBuffer","arrayBufferToBase64","addInterceptor","removeInterceptor"],i=["request","uploadFile","downloadFile","connectSocket","onSocketOpen","onSocketError","sendSocketMessage","onSocketMessage","closeSocket","onSocketClose"],r=["navigateTo","redirectTo","reLaunch","switchTab","navigateBack"],o=["setStorage","setStorageSync","getStorage","getStorageSync","getStorageInfo","getStorageInfoSync","removeStorage","removeStorageSync","clearStorage","clearStorageSync"],a=["getLocation","chooseLocation","openLocation","createMapContext"],s=["chooseImage","previewImage","getImageInfo","saveImageToPhotosAlbum","compressImage","getRecorderManager","getBackgroundAudioManager","createInnerAudioContext","chooseVideo","saveVideoToPhotosAlbum","createVideoContext","createCameraContext","createLivePlayerContext","createLivePusherContext"],c=["getSystemInfo","getSystemInfoSync","canIUse","onMemoryWarning","getNetworkType","onNetworkStatusChange","onAccelerometerChange","startAccelerometer","stopAccelerometer","onCompassChange","startCompass","stopCompass","onGyroscopeChange","startGyroscope","stopGyroscope","makePhoneCall","scanCode","setClipboardData","getClipboardData","setScreenBrightness","getScreenBrightness","setKeepScreenOn","onUserCaptureScreen","vibrateLong","vibrateShort","addPhoneContact","openBluetoothAdapter","startBluetoothDevicesDiscovery","onBluetoothDeviceFound","stopBluetoothDevicesDiscovery","onBluetoothAdapterStateChange","getConnectedBluetoothDevices","getBluetoothDevices","getBluetoothAdapterState","closeBluetoothAdapter","writeBLECharacteristicValue","readBLECharacteristicValue","onBLEConnectionStateChange","onBLECharacteristicValueChange","notifyBLECharacteristicValueChange","getBLEDeviceServices","getBLEDeviceCharacteristics","createBLEConnection","closeBLEConnection","onBeaconServiceChange","onBeaconUpdate","getBeacons","startBeaconDiscovery","stopBeaconDiscovery","checkIsSupportSoterAuthentication","checkIsSoterEnrolledInDevice","startSoterAuthentication","onUIStyleChange"],u=["hideKeyboard","onKeyboardHeightChange"],l=["showToast","hideToast","showLoading","hideLoading","showModal","showActionSheet","setNavigationBarTitle","setNavigationBarColor","showNavigationBarLoading","hideNavigationBarLoading","setTabBarItem","setTabBarStyle","hideTabBar","showTabBar","setTabBarBadge","removeTabBarBadge","showTabBarRedDot","hideTabBarRedDot","onTabBarMidButtonTap","setBackgroundColor","setBackgroundTextStyle","createAnimation","pageScrollTo","onWindowResize","offWindowResize","loadFontFace","startPullDownRefresh","stopPullDownRefresh","createSelectorQuery","createIntersectionObserver","getMenuButtonBoundingClientRect"],h=["$emit","$on","$once","$off"],f=["saveFile","getSavedFileList","getSavedFileInfo","removeSavedFile","getFileInfo","openDocument","getFileSystemManager"],d=["createOffscreenCanvas","createCanvasContext","canvasToTempFilePath","canvasPutImageData","canvasGetImageData"],p=["getProvider","login","checkSession","getUserInfo","share","shareWithSystem","showShareMenu","hideShareMenu","requestPayment","subscribePush","unsubscribePush","onPush","offPush","requireNativePlugin","upx2px","restoreGlobal","getSubNVueById","getCurrentSubNVue","setPageMeta","onNativeEventReceive","sendNativeEvent"],g=["createRewardedVideoAd"],v=[].concat(n,i,r,o,a,s,c,u,l,h,f,d,p,g);t.exports=v},"927d":function(t,e,n){},"93a5":function(t,e,n){var i={"./audio/index.vue":"e0b6","./cover-image/index.vue":"d677","./cover-view/index.vue":"c41f","./map/index.vue":"594d","./picker/index.vue":"6fa7","./video/index.vue":"31e2","./web-view/index.vue":"9980"};function r(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}r.keys=function(){return Object.keys(i)},r.resolve=o,t.exports=r,r.id="93a5"},9400:function(t,e,n){"use strict";var i=n("cc89"),r=n.n(i);r.a},"944e":function(t,e,n){"use strict";var i=n("a6bb"),r=n.n(i);r.a},9470:function(t,e,n){"use strict";var i=n("69c3"),r=n.n(i);r.a},9481:function(t,e,n){"use strict";n.r(e),n.d(e,"onBluetoothDeviceFound",(function(){return a})),n.d(e,"onBluetoothAdapterStateChange",(function(){return s})),n.d(e,"onBLEConnectionStateChange",(function(){return c})),n.d(e,"onBLECharacteristicValueChange",(function(){return u}));var i=n("a118"),r=n("db70");function o(t){var e=[];return Object(r["d"])(t,(function(t){e.forEach((function(e){Object(i["a"])(e,t)}))})),function(t){e.push(t)}}var a=o("onBluetoothDeviceFound"),s=o("onBluetoothAdapterStateChange"),c=o("onBLEConnectionStateChange"),u=o("onBLECharacteristicValueChange")},"957a":function(t,e,n){"use strict";n.r(e),n.d(e,"canIUse",(function(){return i}));var i=[{name:"schema",type:String,required:!0}]},"98be":function(t,e,n){"use strict";n.d(e,"a",(function(){return f}));var i=n("9250"),r=n.n(i),o=n("27a7"),a=n("ed1a"),s=Object.create(null),c=n("bdb1");c.keys().forEach((function(t){Object.assign(s,c(t))}));var u=s,l=n("3b67"),h=Object.assign(Object.create(null),u,l["a"]),f=Object.create(null);r.a.forEach((function(t){h[t]?f[t]=Object(a["d"])(t,Object(o["b"])(t,h[t])):f[t]=Object(o["c"])(t)}))},9980:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-web-view",t._g({},t.$listeners),[n("v-uni-resize-sensor",{ref:"sensor",on:{resize:t._resize}})],1)},r=[],o={name:"WebView",props:{src:{type:String,default:""}},watch:{src:function(t,e){this.iframe&&(this.iframe.src=this.$getRealPath(this.src))}},mounted:function(){this.iframe=document.createElement("iframe"),this.iframe.src=this.$getRealPath(this.src),document.body.appendChild(this.iframe),this._resize()},activated:function(){this.iframe.style.display="block"},deactivated:function(){this.iframe.style.display="none"},beforeDestroy:function(){document.body.removeChild(this.iframe)},methods:{_resize:function(){var t=this.$el.getBoundingClientRect(),e=t.top,n=t.bottom,i=t.width,r=t.height;this.iframe.style.position="absolute",this.iframe.style.display="block",this.iframe.style.border=0,this.iframe.style.top=e+"px",this.iframe.style.bottom=n+"px",this.iframe.style.width=i+"px",this.iframe.style.height=r+"px"}}},a=o,s=(n("c33f"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},"9a3e":function(t,e,n){"use strict";n.r(e),n.d(e,"uploadFile",(function(){return r}));var i=n("cb0f"),r={url:{type:String,required:!0},files:{type:Array},filePath:{type:String,validator:function(t,e){t&&(e.type=Object(i["a"])(t))}},name:{type:String},header:{type:Object,validator:function(t,e){e.header=t||{}}},formData:{type:Object,validator:function(t,e){e.formData=t||{}}}}},"9a8b":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-icon",t._g({},t.$listeners),[n("i",{class:"uni-icon-"+t.type,style:{"font-size":t._converPx(t.size),color:t.color},attrs:{role:"img"}})])},r=[],o={name:"Icon",props:{type:{type:String,required:!0,default:""},size:{type:[String,Number],default:23},color:{type:String,default:""}},methods:{_converPx:function(t){if(/\d+[ur]px$/i.test(t))t.replace(/\d+[ur]px$/i,(function(t){return"".concat(uni.upx2px(parseFloat(t)),"px")}));else if(/^-?[\d\.]+$/.test(t))return"".concat(t,"px");return t||""}}},a=o,s=(n("7e6a"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},"9b1b":function(t,e,n){"use strict";n.r(e),n.d(e,"onWindowResize",(function(){return a})),n.d(e,"offWindowResize",(function(){return s}));var i=n("a118"),r=n("db70"),o=[];function a(t){o.push(t)}function s(t){o.splice(o.indexOf(t),1)}Object(r["d"])("onViewDidResize",(function(t){o.forEach((function(e){Object(i["a"])(e,t)}))}))},"9b1f":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-progress",t._g({staticClass:"uni-progress"},t.$listeners),[n("div",{staticClass:"uni-progress-bar",style:t.outerBarStyle},[n("div",{staticClass:"uni-progress-inner-bar",style:t.innerBarStyle})]),t.showInfo?[n("p",{staticClass:"uni-progress-info"},[t._v(" "+t._s(t.currentPercent)+"% ")])]:t._e()],2)},r=[],o={activeColor:"#007AFF",backgroundColor:"#EBEBEB",activeMode:"backwards"},a={name:"Progress",props:{percent:{type:[Number,String],default:0,validator:function(t){return!isNaN(parseFloat(t,10))}},showInfo:{type:[Boolean,String],default:!1},strokeWidth:{type:[Number,String],default:6,validator:function(t){return!isNaN(parseFloat(t,10))}},color:{type:String,default:o.activeColor},activeColor:{type:String,default:o.activeColor},backgroundColor:{type:String,default:o.backgroundColor},active:{type:[Boolean,String],default:!1},activeMode:{type:String,default:o.activeMode}},data:function(){return{currentPercent:0,strokeTimer:0,lastPercent:0}},computed:{outerBarStyle:function(){return"background-color: ".concat(this.backgroundColor,"; height: ").concat(this.strokeWidth,"px;")},innerBarStyle:function(){var t="";return t=this.color!==o.activeColor&&this.activeColor===o.activeColor?this.color:this.activeColor,"width: ".concat(this.currentPercent,"%;background-color: ").concat(t)},realPercent:function(){var t=parseFloat(this.percent,10);return t<0&&(t=0),t>100&&(t=100),t}},watch:{realPercent:function(t,e){this.strokeTimer&&clearInterval(this.strokeTimer),this.lastPercent=e||0,this._activeAnimation()}},created:function(){this._activeAnimation()},methods:{_activeAnimation:function(){var t=this;this.active?(this.currentPercent=this.activeMode===o.activeMode?0:this.lastPercent,this.strokeTimer=setInterval((function(){t.currentPercent+1>t.realPercent?(t.currentPercent=t.realPercent,t.strokeTimer&&clearInterval(t.strokeTimer)):t.currentPercent+=1}),30)):this.currentPercent=this.realPercent}}},s=a,c=(n("944e"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},"9b5b":function(t,e,n){"use strict";var i=n("8b61"),r=n.n(i);r.a},"9e56":function(t,e,n){"use strict";n.r(e),function(t){function i(e,n){var i=e.urls,r=e.current,o=t,a=o.invokeCallbackHandler;getApp().$router.push({type:"navigateTo",path:"/preview-image",params:{urls:i,current:r}},(function(){a(n,{errMsg:"previewImage:ok"})}),(function(){a(n,{errMsg:"previewImage:fail"})}))}n.d(e,"previewImage",(function(){return i}))}.call(this,n("0dd1"))},"9f96":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-slider",t._g({ref:"uni-slider",on:{click:t._onClick}},t.$listeners),[n("div",{staticClass:"uni-slider-wrapper"},[n("div",{staticClass:"uni-slider-tap-area"},[n("div",{staticClass:"uni-slider-handle-wrapper",style:t.setBgColor},[n("div",{ref:"uni-slider-handle",staticClass:"uni-slider-handle",style:t.setBlockBg}),n("div",{staticClass:"uni-slider-thumb",style:t.setBlockStyle}),n("div",{staticClass:"uni-slider-track",style:t.setActiveColor})])]),n("span",{directives:[{name:"show",rawName:"v-show",value:t.showValue,expression:"showValue"}],staticClass:"uni-slider-value"},[t._v(t._s(t.sliderValue))])]),t._t("default")],2)},r=[],o=n("8af1"),a=n("ba15"),s={name:"Slider",mixins:[o["b"],o["f"],a["a"]],props:{name:{type:String,default:""},min:{type:[Number,String],default:0},max:{type:[Number,String],default:100},value:{type:[Number,String],default:0},step:{type:[Number,String],default:1},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#e9e9e9"},backgroundColor:{type:String,default:"#e9e9e9"},activeColor:{type:String,default:"#007aff"},selectedColor:{type:String,default:"#007aff"},blockColor:{type:String,default:"#ffffff"},blockSize:{type:[Number,String],default:28},showValue:{type:[Boolean,String],default:!1}},data:function(){return{sliderValue:Number(this.value)}},computed:{setBlockStyle:function(){return{width:this.blockSize+"px",height:this.blockSize+"px",marginLeft:-this.blockSize/2+"px",marginTop:-this.blockSize/2+"px",left:this._getValueWidth(),backgroundColor:this.blockColor}},setBgColor:function(){return{backgroundColor:this._getBgColor()}},setBlockBg:function(){return{left:this._getValueWidth()}},setActiveColor:function(){return{backgroundColor:this._getActiveColor(),width:this._getValueWidth()}}},watch:{value:function(t){this.sliderValue=Number(t)}},mounted:function(){this.touchtrack(this.$refs["uni-slider-handle"],"_onTrack")},created:function(){this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this})},beforeDestroy:function(){this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})},methods:{_onUserChangedValue:function(t){var e=this.$refs["uni-slider"],n=e.offsetWidth,i=e.getBoundingClientRect().left,r=(t.x-i)*(this.max-this.min)/n+Number(this.min);this.sliderValue=this._filterValue(r)},_filterValue:function(t){return t<this.min?this.min:t>this.max?this.max:Math.round((t-this.min)/this.step)*this.step+Number(this.min)},_getValueWidth:function(){return 100*(this.sliderValue-this.min)/(this.max-this.min)+"%"},_getBgColor:function(){return"#e9e9e9"!==this.backgroundColor?this.backgroundColor:"#007aff"!==this.color?this.color:"#007aff"},_getActiveColor:function(){return"#007aff"!==this.activeColor?this.activeColor:"#e9e9e9"!==this.selectedColor?this.selectedColor:"#e9e9e9"},_onTrack:function(t){if(!this.disabled)return"move"===t.detail.state?(this._onUserChangedValue({x:t.detail.x0}),this.$trigger("changing",t,{value:this.sliderValue}),!1):"end"===t.detail.state&&this.$trigger("change",t,{value:this.sliderValue})},_onClick:function(t){this.disabled||(this._onUserChangedValue(t),this.$trigger("change",t,{value:this.sliderValue}))},_resetFormData:function(){this.sliderValue=this.min},_getFormData:function(){var t={};return""!==this.name&&(t.value=this.sliderValue,t.key=this.name),t}}},c=s,u=(n("6428"),n("2877")),l=Object(u["a"])(c,i,r,!1,null,null,null);e["default"]=l.exports},a118:function(t,e,n){"use strict";(function(t){function i(){var e;return(e=t).invokeCallbackHandler.apply(e,arguments)}n.d(e,"a",(function(){return i}))}).call(this,n("0dd1"))},a201:function(t,e,n){"use strict";n.r(e),n.d(e,"request",(function(){return u}));var i=n("f2b3"),r={OPTIONS:"OPTIONS",GET:"GET",HEAD:"HEAD",POST:"POST",PUT:"PUT",DELETE:"DELETE",TRACE:"TRACE",CONNECT:"CONNECT"},o={JSON:"json"},a={TEXT:"text",ARRAYBUFFER:"arraybuffer"},s=encodeURIComponent;function c(t,e){var n=t.split("#"),r=n[1]||"";n=n[0].split("?");var o=n[1]||"";t=n[0];var a=o.split("&").filter((function(t){return t}));for(var c in o={},a.forEach((function(t){t=t.split("="),o[t[0]]=t[1]})),e)if(Object(i["h"])(e,c)){var u=e[c];"undefined"===typeof u||null===u?u="":Object(i["k"])(u)&&(u=JSON.stringify(u)),o[s(c)]=s(u)}return o=Object.keys(o).map((function(t){return"".concat(t,"=").concat(o[t])})).join("&"),t+(o?"?"+o:"")+(r?"#"+r:"")}var u={method:{type:String,validator:function(t,e){t=(t||"").toUpperCase(),e.method=Object.values(r).indexOf(t)<0?r.GET:t}},data:{type:[Object,String,Array,ArrayBuffer],validator:function(t,e){e.data=t||""}},url:{type:String,required:!0,validator:function(t,e){e.method===r.GET&&Object(i["k"])(e.data)&&Object.keys(e.data).length&&(e.url=c(t,e.data))}},header:{type:Object,validator:function(t,e){var n=e.header=t||{};e.method!==r.GET&&(Object.keys(n).find((function(t){return"content-type"===t.toLowerCase()}))||(n["Content-Type"]="application/json"))}},dataType:{type:String,validator:function(t,e){e.dataType=(t||o.JSON).toLowerCase()}},responseType:{type:String,validator:function(t,e){t=(t||"").toLowerCase(),e.responseType=Object.values(a).indexOf(t)<0?a.TEXT:t}},withCredentials:{type:Boolean}}},a20f:function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return c}));var i=n("f2b3"),r=function(){var t=document.createElement("canvas");t.height=t.width=0;var e=t.getContext("2d"),n=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}(),o=function(t,e){for(var n in t)Object(i["h"])(t,n)&&e(t[n],n)},a={fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",setTransform:[4,5]},s=CanvasRenderingContext2D.prototype;function c(t){t.width=t.offsetWidth*r,t.height=t.offsetHeight*r,t.getContext("2d").__hidpi__=!0}s.drawImageByCanvas=function(t){return function(e,n,i,o,a,s,c,u,l,h){if(!this.__hidpi__)return t.apply(this,arguments);n*=r,i*=r,o*=r,a*=r,s*=r,c*=r,u=h?u*r:u,l=h?l*r:l,t.call(this,e,n,i,o,a,s,c,u,l)}}(s.drawImage),1!==r&&(o(a,(function(t,e){s[e]=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var n=Array.prototype.slice.call(arguments);if("all"===t)n=n.map((function(t){return t*r}));else if(Array.isArray(t))for(var i=0;i<t.length;i++)n[t[i]]*=r;return e.apply(this,n)}}(s[e])})),s.stroke=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);this.lineWidth*=r,t.apply(this,arguments),this.lineWidth/=r}}(s.stroke),s.fillText=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);var e=Array.prototype.slice.call(arguments);e[1]*=r,e[2]*=r,this.font=this.font.replace(/(\d+)(px|em|rem|pt)/g,(function(t,e,n){return e*r+n})),t.apply(this,e),this.font=this.font.replace(/(\d+)(px|em|rem|pt)/g,(function(t,e,n){return e/r+n}))}}(s.fillText),s.strokeText=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);var e=Array.prototype.slice.call(arguments);e[1]*=r,e[2]*=r,this.font=this.font.replace(/(\d+)(px|em|rem|pt)/g,(function(t,e,n){return e*r+n})),t.apply(this,e),this.font=this.font.replace(/(\d+)(px|em|rem|pt)/g,(function(t,e,n){return e/r+n}))}}(s.strokeText),s.drawImage=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);this.scale(r,r),t.apply(this,arguments),this.scale(1/r,1/r)}}(s.drawImage))},a3e5:function(t,e,n){"use strict";var i=n("df1e"),r=n.n(i);r.a},a402:function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-page-wrapper",[n("uni-page-body",[t._t("default")],2)],1)},r=[],o={name:"PageBody"},a=o,s=(n("167a"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["a"]=c.exports},a470:function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var i=n("65a8"),r=n("d8c8"),o=n.n(r);function a(){if(uni.canIUse("css.var")){var t=document.documentElement.style,e=parseInt((t.getPropertyValue("--window-top").match(/\d+/)||["0"])[0]),n=parseInt((t.getPropertyValue("--window-bottom").match(/\d+/)||["0"])[0]);return{top:e?e+o.a.top:0,bottom:n?n+o.a.bottom:0}}var r=0,a=0,s=getCurrentPages();if(s.length){var c=s[s.length-1].$parent.$parent,u=c.navigationBar.type;r="default"===u||"float"===u?i["a"]:0}var l=getApp();return l&&(a=l.$children[0]&&l.$children[0].showTabBar?i["b"]:0),{top:r,bottom:a}}},a5ec:function(t,e,n){"use strict";var i=n("54bc"),r=n.n(i);r.a},a6bb:function(t,e,n){},a741:function(t,e,n){"use strict";(function(t,i){function r(t,e,n){return t=t.$vm||t,t.__call_hook&&t.__call_hook(e,n)}function o(e,n,i){return"onError"!==n&&t.debug("App：".concat(n," have been invoked")+(i?" ".concat(JSON.stringify(i)):"")),e=e.$vm||e,e.__call_hook&&e.__call_hook(n,i)}function a(e,n,o){return"onLoad"===n&&(e.$mp.query=o,i.publishHandler("onPageLoad",e,e.$page.id)),"onShow"===n&&(e.$route.meta.isTabBar&&e.$route.params.detail&&i.emit("onTabItemTap",e.$route.params.detail),i.publishHandler("onPageShow",e,e.$page.id)),"onPageScroll"!==n&&t.debug("".concat(e.$page.route,"[").concat(e.$page.id,"]：").concat(n," have been invoked")),r(e,n,o)}n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return a}))}).call(this,n("3ad9")["default"],n("0dd1"))},a919:function(t,e,n){"use strict";(function(t){var i=n("cb0f");e["a"]={name:"TabBar",props:{position:{default:"bottom",validator:function(t){return-1!==["bottom","top"].indexOf(t)}},color:{type:String,default:"#999"},selectedColor:{type:String,default:"#007aff"},backgroundColor:{type:String,default:"#f7f7fa"},borderStyle:{default:"black",validator:function(t){return-1!==["black","white"].indexOf(t)}},list:{type:Array,default:function(){return[]}}},computed:{borderColor:function(){return"white"===this.borderStyle?"rgba(255, 255, 255, 0.33)":"rgba(0, 0, 0, 0.33)"}},watch:{$route:function(t,e){t.meta.isTabBar&&(this.__path__=t.path)}},beforeCreate:function(){this.__path__=this.$route.path},methods:{_getRealPath:function(t){return 0!==t.indexOf("/")&&(t="/"+t),Object(i["a"])(t)},_switchTab:function(e,n){var i=e.text,r=e.pagePath,o="/"+r;o===__uniRoutes[0].alias&&(o="/");var a={index:n,text:i,pagePath:r};this.$route.path!==o?(this.__path__=this.$route.path,uni.switchTab({from:"tabBar",url:o,detail:a})):t.emit("onTabItemTap",a)}}}}).call(this,n("0dd1"))},a954:function(t,e,n){"use strict";n.r(e),n.d(e,"addInterceptor",(function(){return i})),n.d(e,"removeInterceptor",(function(){return r}));var i=[{name:"method",type:[String,Object],required:!0}],r=i},aa92:function(t,e,n){"use strict";function i(t){return s(t)||a(t)||o(t)||r()}function r(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function o(t,e){if(t){if("string"===typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(t,e):void 0}}function a(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function s(t){if(Array.isArray(t))return c(t)}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}n.d(e,"a",(function(){return p}));var u=["onLaunch","onShow","onHide","onUniNViewMessage","onError","onLoad","onReady","onUnload","onPullDownRefresh","onReachBottom","onTabItemTap","onShareAppMessage","onResize","onPageScroll","onNavigationBarButtonTap","onBackPress","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputClicked","onNavigationBarSearchInputFocusChanged","onPageShow","onPageHide","onPageResize","onServiceCreated","onServiceAttached"],l=["data","properties","options","relations"];function h(t,e,n){e[n]&&Object.assign(t[n]||(t[n]={}),e[n])}function f(t,e){t.push.apply(t,i(e))}function d(t,e){l.forEach((function(n){h(t,e,n)})),e.externalClasses&&f(t.externalClasses||(t.externalClasses=[]),e.externalClasses),e.path&&(t.path=e.path)}function p(t){var e=t.extend;t.extend=function(t){t=t||{};var n=t.methods;return n&&Object.keys(n).forEach((function(e){-1!==u.indexOf(e)&&(t[e]=n[e],delete n[e])})),e.call(this,t)};var n=t.config.optionMergeStrategies,i=n.created;u.forEach((function(t){n[t]=i})),n.mpOptions=function(t,e){if(!t)return e;var n=Object.create(null);return d(n,t),e&&d(n,e),n}}},abb2:function(t,e,n){"use strict";n.r(e),n.d(e,"connectSocket",(function(){return r})),n.d(e,"sendSocketMessage",(function(){return o})),n.d(e,"closeSocket",(function(){return a}));var i={OPTIONS:"OPTIONS",GET:"GET",HEAD:"HEAD",POST:"POST",PUT:"PUT",DELETE:"DELETE",TRACE:"TRACE",CONNECT:"CONNECT"},r={url:{type:String,required:!0},header:{type:Object,validator:function(t,e){e.header=t||{}}},method:{type:String,validator:function(t,e){t=(t||"").toUpperCase(),e.method=Object.values(i).indexOf(t)<0?i.GET:t}},protocols:{type:[Array,String],validator:function(t,e){"string"===typeof t&&(t=[t]),e.protocols=(t||[]).filter((function(t){return"string"===typeof t}))}}},o={data:{type:[String,ArrayBuffer]}},a={code:{type:Number},reason:{type:String}}},abbf:function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return o}));var i=n("a741"),r=n("1164");function o(e,n){return{created:function(){Object(r["a"])(this,e),n.meta.name||t.emit("onPageNotFound",{path:n.path,query:n.query,isEntryPage:!0})},beforeMount:function(){this.$el=document.getElementById("app")},mounted:function(){var t={path:this.$route.meta&&this.$route.meta.pagePath,query:this.$route.query,scene:1001};Object(i["a"])(this,"onLaunch",t),Object(i["a"])(this,"onShow",t)}}}n.d(e,"b",(function(){return r["b"]})),n.d(e,"c",(function(){return r["c"]}))}).call(this,n("0dd1"))},abea:function(t,e,n){"use strict";function i(t){var e=t.service;return{service:e,provider:[]}}n.r(e),n.d(e,"getProvider",(function(){return i}))},add1:function(t,e,n){},af33:function(t,e,n){"use strict";n.r(e),n.d(e,"createSelectorQuery",(function(){return v}));var i=n("f2b3"),r=n("db70"),o=n("303f"),a=n("bfa6"),s=n("ee03"),c=n("60db");function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function h(t,e,n){return e&&l(t.prototype,e),n&&l(t,n),t}var f={canvas:o["CanvasContext"],map:a["MapContext"],video:s["VideoContext"],editor:c["EditorContext"]};function d(t){if(t&&t.context){var e=t.context,n=e.id,i=e.name,r=e.page,o=f[i];t.context=o&&new o(n,r)}}var p=function(){function t(e,n,i,r){u(this,t),this._selectorQuery=e,this._component=n,this._selector=i,this._single=r}return h(t,[{key:"boundingClientRect",value:function(t){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},t),this._selectorQuery}},{key:"fields",value:function(t,e){return this._selectorQuery._push(this._selector,this._component,this._single,t,e),this._selectorQuery}},{key:"scrollOffset",value:function(t){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},t),this._selectorQuery}},{key:"context",value:function(t){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},t),this._selectorQuery}}]),t}(),g=function(){function t(e){u(this,t),this._page=e,this._queue=[],this._queueCb=[]}return h(t,[{key:"exec",value:function(t){var e=this;Object(r["c"])("requestComponentInfo",this._page,this._queue,(function(n){var r=e._queueCb;n.forEach((function(t,n){Array.isArray(t)?t.forEach(d):d(t);var o=r[n];Object(i["j"])(o)&&o.call(e,t)})),Object(i["j"])(t)&&t.call(e,n)}))}},{key:"in",value:function(t){return this._component=t._$id||t,this}},{key:"select",value:function(t){return new p(this,this._component,t,!0)}},{key:"selectAll",value:function(t){return new p(this,this._component,t,!1)}},{key:"selectViewport",value:function(){return new p(this,0,"",!0)}},{key:"_push",value:function(t,e,n,i,r){this._queue.push({component:e,selector:t,single:n,fields:i}),this._queueCb.push(r)}}]),t}();function v(t){return new g(t||Object(r["b"])("createSelectorQuery"))}},b0ef:function(t,e,n){"use strict";n.r(e),n.d(e,"$on",(function(){return i})),n.d(e,"$once",(function(){return r})),n.d(e,"$off",(function(){return o})),n.d(e,"$emit",(function(){return a}));var i=[{name:"event",type:[String,Array],required:!0},{name:"callback",type:Function,required:!0}],r=i,o=[{name:"event",type:[String,Array]},{name:"callback",type:Function}],a=[{name:"event",type:String,required:!0}]},b10a:function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return a}));var i=n("18fd");function r(t){return t.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}function o(t){return t.reduce((function(t,e){var n=e.value,i=e.name;return n.match(/ /)&&"style"!==i&&(n=n.split(" ")),t[i]?Array.isArray(t[i])?t[i].push(n):t[i]=[t[i],n]:t[i]=n,t}),{})}function a(e){e=r(e);var n=[],a={node:"root",children:[]};return Object(i["a"])(e,{start:function(t,e,i){var r={name:t};if(0!==e.length&&(r.attrs=o(e)),i){var s=n[0]||a;s.children||(s.children=[]),s.children.push(r)}else n.unshift(r)},end:function(e){var i=n.shift();if(i.name!==e&&t.error("invalid state: mismatch end tag"),0===n.length)a.children.push(i);else{var r=n[0];r.children||(r.children=[]),r.children.push(i)}},chars:function(t){var e={type:"text",text:t};if(0===n.length)a.children.push(e);else{var i=n[0];i.children||(i.children=[]),i.children.push(e)}},comment:function(t){var e={node:"comment",text:t},i=n[0];i.children||(i.children=[]),i.children.push(e)}}),a.children}}).call(this,n("3ad9")["default"])},b253:function(t,e,n){"use strict";function i(t){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&a(t,e)}function a(t,e){return a=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},a(t,e)}function s(t){return function(){var e,n=h(t);if(l()){var i=h(this).constructor;e=Reflect.construct(n,arguments,i)}else e=n.apply(this,arguments);return c(this,e)}}function c(t,e){return!e||"object"!==i(e)&&"function"!==typeof e?u(t):e}function u(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function l(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function h(t){return h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},h(t)}n.d(e,"a",(function(){return Y}));var f=function(t){var e=t.import("blots/block/embed"),n=function(t){o(n,t);var e=s(n);function n(){return r(this,n),e.apply(this,arguments)}return n}(e);return n.blotName="divider",n.tagName="HR",{"formats/divider":n}};function d(t){return d="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function p(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function g(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&v(t,e)}function v(t,e){return v=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},v(t,e)}function m(t){return function(){var e,n=w(t);if(_()){var i=w(this).constructor;e=Reflect.construct(n,arguments,i)}else e=n.apply(this,arguments);return b(this,e)}}function b(t,e){return!e||"object"!==d(e)&&"function"!==typeof e?y(t):e}function y(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function w(t){return w=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},w(t)}var S=function(t){var e=t.import("blots/inline"),n=function(t){g(n,t);var e=m(n);function n(){return p(this,n),e.apply(this,arguments)}return n}(e);return n.blotName="ins",n.tagName="INS",{"formats/ins":n}},k=function(t){var e=t.import("parchment"),n=e.Scope,i=e.Attributor,r={scope:n.BLOCK,whitelist:["left","right","center","justify"]},o=new i.Style("align","text-align",r);return{"formats/align":o}},T=function(t){var e=t.import("parchment"),n=e.Scope,i=e.Attributor,r={scope:n.BLOCK,whitelist:["rtl"]},o=new i.Style("direction","direction",r);return{"formats/direction":o}};function x(t){return x="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(t)}function C(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function O(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function E(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function M(t,e,n){return e&&E(t.prototype,e),n&&E(t,n),t}function j(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&A(t,e)}function A(t,e){return A=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},A(t,e)}function I(t){return function(){var e,n=D(t);if(B()){var i=D(this).constructor;e=Reflect.construct(n,arguments,i)}else e=n.apply(this,arguments);return $(this,e)}}function $(t,e){return!e||"object"!==x(e)&&"function"!==typeof e?P(t):e}function P(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function B(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function L(t,e,n){return L="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var i=N(t,e);if(i){var r=Object.getOwnPropertyDescriptor(i,e);return r.get?r.get.call(n):r.value}},L(t,e,n||t)}function N(t,e){while(!Object.prototype.hasOwnProperty.call(t,e))if(t=D(t),null===t)break;return t}function D(t){return D=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},D(t)}var R=function(t){var e=t.import("parchment"),n=t.import("blots/container"),i=t.import("formats/list/item"),r=function(t){j(r,t);var n=I(r);function r(t){var i;O(this,r),i=n.call(this,t);var o=function(n){if(n.target.parentNode===t){var r=i.statics.formats(t),o=e.find(n.target);"checked"===r?o.format("list","unchecked"):"unchecked"===r&&o.format("list","checked")}};return t.addEventListener("click",o),i}return M(r,null,[{key:"create",value:function(t){var e="ordered"===t?"OL":"UL",n=L(D(r),"create",this).call(this,e);return"checked"!==t&&"unchecked"!==t||n.setAttribute("data-checked","checked"===t),n}},{key:"formats",value:function(t){return"OL"===t.tagName?"ordered":"UL"===t.tagName?t.hasAttribute("data-checked")?"true"===t.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}}]),M(r,[{key:"format",value:function(t,e){this.children.length>0&&this.children.tail.format(t,e)}},{key:"formats",value:function(){return C({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(t,e){if(t instanceof i)L(D(r.prototype),"insertBefore",this).call(this,t,e);else{var n=null==e?this.length():e.offset(this),o=this.split(n);o.parent.insertBefore(t,o)}}},{key:"optimize",value:function(t){L(D(r.prototype),"optimize",this).call(this,t);var e=this.next;null!=e&&e.prev===this&&e.statics.blotName===this.statics.blotName&&e.domNode.tagName===this.domNode.tagName&&e.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(e.moveChildren(this),e.remove())}},{key:"replace",value:function(t){if(t.statics.blotName!==this.statics.blotName){var n=e.create(this.statics.defaultChild);t.moveChildren(n),this.appendChild(n)}L(D(r.prototype),"replace",this).call(this,t)}}]),r}(n);return r.blotName="list",r.scope=e.Scope.BLOCK_BLOT,r.tagName=["OL","UL"],r.defaultChild="list-item",r.allowedChildren=[i],{"formats/list":r}},z=function(t){var e=t.import("parchment"),n=e.Scope,i=t.import("formats/background"),r=new i.constructor("backgroundColor","background-color",{scope:n.INLINE});return{"formats/backgroundColor":r}},F=n("f2b3"),q=function(t){var e=t.import("parchment"),n=e.Scope,i=e.Attributor,r={scope:n.BLOCK},o=["margin","marginTop","marginBottom","marginLeft","marginRight"],a=["padding","paddingTop","paddingBottom","paddingLeft","paddingRight"],s={};return o.concat(a).forEach((function(t){s["formats/".concat(t)]=new i.Style(t,Object(F["l"])(t),r)})),s},H=function(t){var e=t.import("parchment"),n=e.Scope,i=e.Attributor,r={scope:n.INLINE},o=["font","fontSize","fontStyle","fontVariant","fontWeight","fontFamily"],a={};return o.forEach((function(t){a["formats/".concat(t)]=new i.Style(t,Object(F["l"])(t),r)})),a},V=function(t){var e=t.import("parchment"),n=e.Scope,i=e.Attributor,r=[{name:"lineHeight",scope:n.BLOCK},{name:"letterSpacing",scope:n.INLINE},{name:"textDecoration",scope:n.INLINE},{name:"textIndent",scope:n.BLOCK}],o={};return r.forEach((function(t){var e=t.name,n=t.scope;o["formats/".concat(e)]=new i.Style(e,Object(F["l"])(e),{scope:n})})),o},U=function(t){var e=t.import("formats/image"),n=["alt","height","width","data-custom","class","data-local"];e.sanitize=function(t){return t},e.formats=function(t){return n.reduce((function(e,n){return t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e}),{})};var i=e.prototype.format;e.prototype.format=function(t,e){n.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):i.call(this,t,e)}};function Y(t){var e={divider:f,ins:S,align:k,direction:T,list:R,background:z,box:q,font:H,text:V,image:U},n={};Object.values(e).forEach((function(e){return Object.assign(n,e(t))})),t.register(n,!0)}},b2bb:function(t,e,n){},b34d:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-form",t._g({},t.$listeners),[n("span",[t._t("default")],2)])},r=[],o=n("8af1"),a={name:"Form",mixins:[o["f"]],data:function(){return{childrenList:[]}},listeners:{"@form-submit":"_onSubmit","@form-reset":"_onReset","@form-group-update":"_formGroupUpdateHandler"},methods:{_onSubmit:function(t){var e={};this.childrenList.forEach((function(t){t._getFormData&&t._getFormData().key&&(e[t._getFormData().key]=t._getFormData().value)})),this.$trigger("submit",t,{value:e})},_onReset:function(t){this.$trigger("reset",t,{}),this.childrenList.forEach((function(t){t._resetFormData&&t._resetFormData()}))},_formGroupUpdateHandler:function(t){if("add"===t.type)this.childrenList.push(t.vm);else{var e=this.childrenList.indexOf(t.vm);this.childrenList.splice(e,1)}}}},s=a,c=n("2877"),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},b501:function(t,e,n){"use strict";n.r(e),n.d(e,"setClipboardData",(function(){return i}));var i={beforeSuccess:function(){uni.showToast({title:"内容已复制",icon:"success",mask:!1})}}},b628:function(t,e,n){"use strict";var i=n("8b18"),r=n.n(i);r.a},b705:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-rich-text",t._g({},t.$listeners),[n("div")])},r=[],o=n("b10a"),a=n("f2b3"),s={a:"",abbr:"",b:"",blockquote:"",br:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",ol:["start","type"],p:"",q:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","rowspan","height","width"],tfoot:"",th:["colspan","rowspan","height","width"],thead:"",tr:"",ul:""},c={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'"};function u(t){return t.replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(t,e){if(Object(a["h"])(c,e)&&c[e])return c[e];if(/^#[0-9]{1,4}$/.test(e))return String.fromCharCode(e.slice(1));if(/^#x[0-9a-f]{1,4}$/i.test(e))return String.fromCharCode("0"+e.slice(1));var n=document.createElement("div");return n.innerHTML=t,n.innerText||n.textContent}))}function l(t,e){return t.forEach((function(t){if(Object(a["k"])(t))if(Object(a["h"])(t,"type")&&"node"!==t.type)"text"===t.type&&"string"===typeof t.text&&""!==t.text&&e.appendChild(document.createTextNode(u(t.text)));else{if("string"!==typeof t.name||!t.name)return;var n=t.name.toLowerCase();if(!Object(a["h"])(s,n))return;var i=document.createElement(n);if(!i)return;var r=t.attrs;if(Object(a["k"])(r)){var o=s[n]||[];Object.keys(r).forEach((function(t){var e=r[t];switch(t){case"class":Array.isArray(e)&&(e=e.join(" "));case"style":i.setAttribute(t,e);break;default:-1!==o.indexOf(t)&&i.setAttribute(t,e)}}))}var c=t.children;Array.isArray(c)&&c.length&&l(t.children,i),e.appendChild(i)}})),e}var h={name:"RichText",props:{nodes:{type:[Array,String],default:function(){return[]}}},watch:{nodes:function(t){this._renderNodes(t)}},mounted:function(){this._renderNodes(this.nodes)},methods:{_renderNodes:function(t){"string"===typeof t&&(t=Object(o["a"])(t));var e=l(t,document.createDocumentFragment());this.$el.firstChild.innerHTML="",this.$el.firstChild.appendChild(e)}}},f=h,d=n("2877"),p=Object(d["a"])(f,i,r,!1,null,null,null);e["default"]=p.exports},b865:function(t,e,n){"use strict";(function(t,i){function r(e,n){return t.emit("api."+e,n)}function o(t,e,n){i.UniViewJSBridge.subscribeHandler(t,e,n)}n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return o}))}).call(this,n("0dd1"),n("c8ba"))},b866:function(t,e,n){"use strict";n.r(e),n.d(e,"getImageInfo",(function(){return r}));var i=n("cb0f"),r={src:{type:String,required:!0,validator:function(t,e){e.src=Object(i["a"])(t)}}}},ba15:function(t,e,n){"use strict";var i=function(t,e,n,i){t.addEventListener(e,(function(t){"function"===typeof n&&!1===n(t)&&(t.preventDefault(),t.stopPropagation())}),{passive:!1})};e["a"]={beforeDestroy:function(){document.removeEventListener("mousemove",this.__mouseMoveEventListener),document.removeEventListener("mouseup",this.__mouseUpEventListener)},methods:{touchtrack:function(t,e,n){var r,o,a=this,s=0,c=0,u=0,l=0,h=function(t,n,i,r){if(!1===a[e]({target:t.target,currentTarget:t.currentTarget,preventDefault:t.preventDefault.bind(t),stopPropagation:t.stopPropagation.bind(t),touches:t.touches,changedTouches:t.changedTouches,detail:{state:n,x0:i,y0:r,dx:i-s,dy:r-c,ddx:i-u,ddy:r-l,timeStamp:t.timeStamp}}))return!1},f=null;i(t,"touchstart",(function(t){if(r=!0,1===t.touches.length&&!f)return f=t,s=u=t.touches[0].pageX,c=l=t.touches[0].pageY,h(t,"start",s,c)})),i(t,"mousedown",(function(t){if(o=!0,!r&&!f)return f=t,s=u=t.pageX,c=l=t.pageY,h(t,"start",s,c)})),i(t,"touchmove",(function(t){if(1===t.touches.length&&f){var e=h(t,"move",t.touches[0].pageX,t.touches[0].pageY);return u=t.touches[0].pageX,l=t.touches[0].pageY,e}}));var d=this.__mouseMoveEventListener=function(t){if(!r&&o&&f){var e=h(t,"move",t.pageX,t.pageY);return u=t.pageX,l=t.pageY,e}};document.addEventListener("mousemove",d),i(t,"touchend",(function(t){if(0===t.touches.length&&f)return r=!1,f=null,h(t,"end",t.changedTouches[0].pageX,t.changedTouches[0].pageY)}));var p=this.__mouseUpEventListener=function(t){if(o=!1,!r&&f)return f=null,h(t,"end",t.pageX,t.pageY)};document.addEventListener("mouseup",p),i(t,"touchcancel",(function(t){if(f){r=!1;var e=f;return f=null,h(t,n?"cancel":"end",e.touches[0].pageX,e.touches[0].pageY)}}))}}}},bab8:function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"system-header"},[n("div",{staticClass:"header-text"},[t._t("default")],2),n("div",{staticClass:"header-btn header-back uni-btn-icon header-btn-icon",on:{click:t._back}},[t._v("  ")]),t.confirm?n("div",{staticClass:"header-btn header-confirm",on:{click:t._confirm}},[n("svg",{staticClass:"header-btn-img",attrs:{width:"200px",height:"200.00px",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"}},[n("path",{attrs:{d:"M939.6960642844446 226.08613831111114c-14.635971697777777-13.725872355555557-37.719236835555556-13.070208568888889-51.445109191111115 1.6029502577777779L402.69993870222225 744.6571451733333 137.46159843555557 483.31364238222227c-14.344349013333334-14.12709944888889-37.392384-13.98030904888889-51.51948344888889 0.3640399644444444-14.12709944888889 14.30911886222222-13.945078897777778 37.392384 0.40122709333333334 51.482296319999996l291.8171704888889 287.48392106666665c0.10960327111111111 0.10960327111111111 0.2544366933333333 0.1448334222222222 0.3640399644444444 0.2544366933333333s0.1448334222222222 0.2544366933333333 0.2544366933333333 0.3640399644444444c2.293843057777778 2.1842397866666667 5.061329351111111 3.4231500799999997 7.719212373333333 4.879309937777777 1.3113264355555554 0.7652670577777777 2.43867648 1.8926159644444445 3.822419057777778 2.43867648 4.2960634311111106 1.6753664 8.846562417777779 2.548279751111111 13.361832391111111 2.548279751111111 4.769706666666666 0 9.539412195555554-0.9472864711111111 13.98030904888889-2.839903573333333 1.4933469866666664-0.6184766577777778 2.6578830222222223-1.8926159644444445 4.0416267377777775-2.6950701511111115 2.7302991644444448-1.6029502577777779 5.5702027377777785-2.9495068444444446 7.901232924444444-5.315766044444445 0.10960327111111111-0.10960327111111111 0.1448334222222222-0.2916238222222222 0.2544366933333333-0.40122709333333334 0.07241614222222222-0.10960327111111111 0.21920654222222222-0.1448334222222222 0.3268528355555555-0.2544366933333333L941.2579134577779 277.5273335466667C955.0953460622222 262.9305059555556 954.3320359822221 239.8844279466666 939.6960642844446 226.08613831111114z"}})])]):t._e()])},r=[],o=n("65f0"),a=o["a"],s=(n("0a32"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["a"]=c.exports},bacd:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-canvas",t._g({attrs:{"canvas-id":t.canvasId,"disable-scroll":t.disableScroll}},t._listeners),[n("canvas",{ref:"canvas",attrs:{width:"300",height:"150"}}),n("div",{staticStyle:{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",overflow:"hidden"}},[t._t("default")],2),n("v-uni-resize-sensor",{ref:"sensor",on:{resize:t._resize}})],1)},r=[],o=n("d8ca"),a=o["a"],s=(n("0741"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},bdb1:function(t,e,n){var i={"./base/base64.js":"1ca3","./base/can-i-use.js":"3648","./base/interceptor.js":"2eae","./base/upx2px.js":"45d2","./context/audio.js":"2c67","./context/background-audio.js":"c3f2","./context/canvas.js":"303f","./context/create-map-context.js":"bfa6","./context/create-video-context.js":"ee03","./context/editor.js":"60db","./device/accelerometer.js":"7d13","./device/bluetooth.js":"9481","./device/compass.js":"e4ee","./device/network.js":"8b3f","./device/theme.js":"d001","./media/preview-image.js":"898f","./media/recorder.js":"3676","./network/download-file.js":"f0c3","./network/request.js":"82c2","./network/socket.js":"811a","./network/upload-file.js":"1ff3","./ui/create-animation.js":"1e4d","./ui/create-intersection-observer.js":"091a","./ui/create-selector-query.js":"af33","./ui/keyboard.js":"78a1","./ui/load-font-face.js":"0001","./ui/page-scroll-to.js":"84e0","./ui/set-page-meta.js":"2ec6","./ui/tab-bar.js":"454d","./ui/window.js":"9b1b"};function r(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}r.keys=function(){return Object.keys(i)},r.resolve=o,t.exports=r,r.id="bdb1"},be12:function(t,e,n){"use strict";(function(t){function n(t,e,n){var i=Array.prototype.slice.call(t.changedTouches).filter((function(t){return t.identifier===e}))[0];return!!i&&(t.deltaY=i.pageY-n,!0)}var i="pulling",r="reached",o="aborting",a="refreshing",s="restoring";e["a"]={mounted:function(){var e=this;this.enablePullDownRefresh&&(this.refreshContainerElem=this.$refs.refresh.$el,this.refreshControllerElem=this.refreshContainerElem.querySelector(".uni-page-refresh"),this.refreshInnerElemStyle=this.refreshControllerElem.querySelector(".uni-page-refresh-inner").style,t.on(this.$route.params.__id__+".startPullDownRefresh",(function(){e.state||(e.state=a,e._addClass(),setTimeout((function(){e._refreshing()}),50))})),t.on(this.$route.params.__id__+".stopPullDownRefresh",(function(){e.state===a&&(e._removeClass(),e.state=s,e._addClass(),e._restoring((function(){e._removeClass(),e.state=e.distance=e.offset=null})))})))},methods:{_touchstart:function(t){var e=t.changedTouches[0];this.touchId=e.identifier,this.startY=e.pageY,[o,a,s].indexOf(this.state)>=0?this.canRefresh=!1:this.canRefresh=!0},_touchmove:function(t){if(this.canRefresh&&n(t,this.touchId,this.startY)){var e=t.deltaY;if(0===(document.documentElement.scrollTop||document.body.scrollTop)){if(!(e<0)||this.state){t.preventDefault(),null==this.distance&&(this.offset=e,this.state=i,this._addClass()),e-=this.offset,e<0&&(e=0),this.distance=e;var o=e>=this.refreshOptions.range&&this.state!==r,a=e<this.refreshOptions.range&&this.state!==i;(o||a)&&(this._removeClass(),this.state=this.state===r?i:r,this._addClass()),this._pulling(e)}}else this.touchId=null}},_touchend:function(t){var e=this;n(t,this.touchId,this.startY)&&null!==this.state&&(this.state===i?(this._removeClass(),this.state=o,this._addClass(),this._aborting((function(){e._removeClass(),e.state=e.distance=e.offset=null}))):this.state===r&&(this._removeClass(),this.state=a,this._addClass(),this._refreshing()))},_toggleClass:function(t){if(this.state){var e=this.refreshContainerElem;e&&e.classList[t]("uni-page-refresh--"+this.state)}},_addClass:function(){this._toggleClass("add")},_removeClass:function(){this._toggleClass("remove")},_pulling:function(t){var e=this.refreshControllerElem;if(e){var n=e.style,i=t/this.refreshOptions.range;i>1?i=1:i*=i*i;var r=Math.round(t/(this.refreshOptions.range/this.refreshOptions.height)),o=r?"translate3d(-50%, "+r+"px, 0)":0;n.webkitTransform=o,n.clip="rect("+(45-r)+"px,45px,45px,-5px)",this.refreshInnerElemStyle.webkitTransform="rotate("+360*i+"deg)"}},_aborting:function(t){var e=this.refreshControllerElem;if(e){var n=e.style;if(n.webkitTransform){n.webkitTransition="-webkit-transform 0.3s",n.webkitTransform="translate3d(-50%, 0, 0)";var i=function i(){r&&clearTimeout(r),e.removeEventListener("webkitTransitionEnd",i),n.webkitTransition="",t()};e.addEventListener("webkitTransitionEnd",i);var r=setTimeout(i,350)}else t()}},_refreshing:function(){var e=this.refreshControllerElem;if(e){var n=e.style;n.webkitTransition="-webkit-transform 0.2s",n.webkitTransform="translate3d(-50%, "+this.refreshOptions.height+"px, 0)",t.emit("onPullDownRefresh",{},this.$route.params.__id__)}},_restoring:function(t){var e=this.refreshControllerElem;if(e){var n=e.style;n.webkitTransition="-webkit-transform 0.3s",n.webkitTransform+=" scale(0.01)";var i=function i(){r&&clearTimeout(r),e.removeEventListener("webkitTransitionEnd",i),n.webkitTransition="",n.webkitTransform="translate3d(-50%, 0, 0)",t()};e.addEventListener("webkitTransitionEnd",i);var r=setTimeout(i,350)}}}}}).call(this,n("0dd1"))},be14:function(t,e,n){"use strict";n.r(e),function(t){function i(e,n){var i=t,r=i.invokeCallbackHandler;getApp().$router.push({type:"navigateTo",path:"/choose-location"},(function(){var e=function e(i){t.unsubscribe("onChooseLocation",e),r(n,i?Object.assign(i,{errMsg:"chooseLocation:ok"}):{errMsg:"chooseLocation:fail"})};t.subscribe("onChooseLocation",e)}),(function(){r(n,{errMsg:"chooseLocation:fail"})}))}n.d(e,"chooseLocation",(function(){return i}))}.call(this,n("0dd1"))},bfa6:function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"MapContext",(function(){return c})),n.d(e,"createMapContext",(function(){return u}));var i=n("db70"),r=n("f2b3");function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e,n,r){Object(i["c"])("operateMapPlayer",t,e,n,r)}t.subscribe("onMapMethodCallback",(function(t){var e=t.callbackId,n=t.data;r["a"].invoke(e,n)}));var s=["getCenterLocation","moveToLocation","getScale","getRegion","includePoints","translateMarker"],c=function t(e,n){o(this,t),this.id=e,this.pageVm=n};function u(t,e){return new c(t,e||Object(i["b"])("createMapContext"))}c.prototype.$getAppMap=function(){return plus.maps.getMapById(this.pageVm.$page.id+"-map-"+this.id)},s.forEach((function(t){c.prototype[t]=r["a"].warp((function(e,n){e.callbackId=n,a(this.id,this.pageVm,t,e)}))}))}.call(this,n("0dd1"))},bfbd:function(t,e,n){},bfea:function(t,e,n){"use strict";var i=n("4e0b"),r=n.n(i);r.a},c0e5:function(t,e,n){},c195:function(t,e,n){},c2aa:function(t,e,n){},c33a:function(t,e,n){},c33f:function(t,e,n){"use strict";var i=n("c195"),r=n.n(i);r.a},c3f2:function(t,e,n){"use strict";n.r(e),n.d(e,"getBackgroundAudioManager",(function(){return f}));var i=n("db70");function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}var s=["canplay","play","pause","stop","ended","timeUpdate","prev","next","error","waiting"],c={};s.forEach((function(t){c[t]=[]}));var u,l=[{name:"duration",readonly:!0},{name:"currentTime",readonly:!0},{name:"paused",readonly:!0},{name:"src",cache:!0},{name:"startTime",default:0,cache:!0},{name:"buffered",readonly:!0},{name:"title",cache:!0},{name:"epname",cache:!0},{name:"singer",cache:!0},{name:"coverImgUrl",cache:!0},{name:"webUrl",cache:!0},{name:"protocol",readonly:!0,default:"http"}],h=function(){function t(){var e=this;r(this,t),this._options={},Object(i["d"])("onBackgroundAudioStateChange",(function(t){var e=t.state,n=t.errMsg,i=t.errCode;c[e].forEach((function(t){"function"===typeof t&&t("error"===e?{errMsg:n,errCode:i}:{})}))})),l.forEach((function(t){var n=t.name,r={get:function(){var e=t.cache?this._options:Object(i["c"])("getBackgroundAudioState");return n in e?e[n]:t.default}};t.readonly||(r.set=function(t){this._options[n]=t,Object(i["c"])("setBackgroundAudioState",Object.assign({},this._options,{audioId:this.id}))}),Object.defineProperty(e,n,r)}))}return a(t,[{key:"play",value:function(){this._operate("play")}},{key:"pause",value:function(){this._operate("pause")}},{key:"stop",value:function(){this._operate("stop")}},{key:"seek",value:function(t){this._operate("seek",{currentTime:t})}},{key:"_operate",value:function(t,e){Object(i["c"])("operateBackgroundAudio",Object.assign({},e,{operationType:t}))}}]),t}();function f(){return u||(u=new h)}s.forEach((function(t){var e=t[0].toUpperCase()+t.substr(1);h.prototype["on".concat(e)]=function(e){c[t].push(e)}}))},c418:function(t,e,n){},c41f:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-cover-view",t._g({attrs:{"scroll-top":t.scrollTop}},t.$listeners),[n("div",{ref:"content",staticClass:"uni-cover-view"},[t._t("default")],2)])},r=[],o={name:"CoverView",props:{scrollTop:{type:[String,Number],default:0}},watch:{scrollTop:function(t){this.setScrollTop(t)}},mounted:function(){this.setScrollTop(this.scrollTop)},methods:{setScrollTop:function(t){var e=this.$refs.content;"scroll"===getComputedStyle(e).overflowY&&(e.scrollTop=this._upx2pxNum(t))},_upx2pxNum:function(t){return/\d+[ur]px$/i.test(t)&&t.replace(/\d+[ur]px$/i,(function(t){return uni.upx2px(parseFloat(t))})),parseFloat(t)||0}}},a=o,s=(n("cc5f"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},c4c5:function(t,e,n){"use strict";(function(t,i){n.d(e,"a",(function(){return d}));var r=n("f2b3");function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function s(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t}var c=/^\s+|\s+$/g,u=/\s+/;function l(t,e,n){var i=[],r=function(t){return r=n?function(t){return!e.contains(t)}:function(t){return e.contains(t)},r(t)};return t.forEach((function(t){t=t.replace(c,""),r(t)&&i.push(t)})),i}function h(t){var e={},n=/;(?![^(]*\))/g,i=/:(.+)/;return t.split(n).forEach((function(t){if(t){var n=t.split(i);n.length>1&&(e[n[0].trim()]=n[1].trim())}})),e}var f=function(){function e(t){o(this,e),this.$vm=t,this.$el=t.$el}return s(e,[{key:"selectComponent",value:function(t){if(this.$el&&t){var e=this.$el.querySelector(t);return e&&e.__vue__&&d(e.__vue__,!1)}}},{key:"selectAllComponents",value:function(t){if(!this.$el||!t)return[];for(var e=[],n=this.$el.querySelectorAll(t),i=0;i<n.length;i++){var r=n[i];r.__vue__&&e.push(d(r.__vue__,!1))}return e}},{key:"setStyle",value:function(t){return this.$el&&t?("string"===typeof t&&(t=h(t)),Object(r["k"])(t)&&(this.$el.__wxsStyle=t,this.$vm.$forceUpdate()),this):this}},{key:"addClass",value:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(!this.$el||!e.length)return this;var i=l(e,this.$el.classList,!0);if(i.length){var r=this.$el.__wxsAddClass||"";this.$el.__wxsAddClass=r+(r?" ":"")+i.join(" "),this.$vm.$forceUpdate()}return this}},{key:"removeClass",value:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(!this.$el||!e.length)return this;var i=this.$el.classList,r=this.$el.__wxsAddClass?this.$el.__wxsAddClass.split(u):[],o=l(e,i,!1);if(o.length){var a=[];o.forEach((function(t){var e=r.findIndex((function(e){return e===t}));-1!==e&&r.splice(e,1),a.push(t)})),this.$el.__wxsRemoveClass=a,this.$el.__wxsAddClass=r.join(" "),this.$vm.$forceUpdate()}return this}},{key:"hasClass",value:function(t){return this.$el&&this.$el.classList.contains(t)}},{key:"getDataset",value:function(){return this.$el&&this.$el.dataset}},{key:"callMethod",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.$vm[e]?this.$vm[e](JSON.parse(JSON.stringify(n))):this.$vm._$id&&t.publishHandler("onWxsInvokeCallMethod",{cid:this.$vm._$id,method:e,args:n})}},{key:"requestAnimationFrame",value:function(t){return i.requestAnimationFrame(t),this}},{key:"getState",value:function(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}},{key:"triggerEvent",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.$vm.$emit(t,e),this}}]),e}();function d(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(e&&t&&t.$options.name&&0===t.$options.name.indexOf("VUni")&&(t=t.$parent),t&&t.$el)return t.$el.__wxsComponentDescriptor||(t.$el.__wxsComponentDescriptor=new f(t)),t.$el.__wxsComponentDescriptor}}).call(this,n("501c"),n("c8ba"))},c61c:function(t,e,n){"use strict";n.r(e);var i=n("f2b3");function r(t){return Math.sqrt(t.x*t.x+t.y*t.y)}var o,a,s={name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},data:function(){return{width:0,height:0,items:[]}},created:function(){this.gapV={x:null,y:null},this.pinchStartLen=null},mounted:function(){this._resize()},methods:{_resize:function(){this._getWH(),this.items.forEach((function(t,e){t.componentInstance.setParent()}))},_find:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.items,n=this.$el;function i(t){for(var r=0;r<e.length;r++){var o=e[r];if(t===o.componentInstance.$el)return o}return t===n||t===document.body||t===document?null:i(t.parentNode)}return i(t)},_touchstart:function(t){Object(i["e"])({disable:!0});var e=t.touches;if(e&&e.length>1){var n={x:e[1].pageX-e[0].pageX,y:e[1].pageY-e[0].pageY};if(this.pinchStartLen=r(n),this.gapV=n,!this.scaleArea){var o=this._find(e[0].target),a=this._find(e[1].target);this._scaleMovableView=o&&o===a?o:null}}},_touchmove:function(t){var e=t.touches;if(e&&e.length>1){t.preventDefault();var n={x:e[1].pageX-e[0].pageX,y:e[1].pageY-e[0].pageY};if(null!==this.gapV.x&&this.pinchStartLen>0){var i=r(n)/this.pinchStartLen;this._updateScale(i)}this.gapV=n}},_touchend:function(t){Object(i["e"])({disable:!1});var e=t.touches;e&&e.length||t.changedTouches&&(this.gapV.x=0,this.gapV.y=0,this.pinchStartLen=null,this.scaleArea?this.items.forEach((function(t){t.componentInstance._endScale()})):this._scaleMovableView&&this._scaleMovableView.componentInstance._endScale())},_updateScale:function(t){t&&1!==t&&(this.scaleArea?this.items.forEach((function(e){e.componentInstance._setScale(t)})):this._scaleMovableView&&this._scaleMovableView.componentInstance._setScale(t))},_getWH:function(){var t=window.getComputedStyle(this.$el),e=this.$el.getBoundingClientRect();this.width=e.width-["Left","Right"].reduce((function(e,n){return e+parseFloat(t["border"+n+"Width"])+parseFloat(t["padding"+n])}),0),this.height=e.height-["Top","Bottom"].reduce((function(e,n){return e+parseFloat(t["border"+n+"Width"])+parseFloat(t["padding"+n])}),0)}},render:function(t){var e=this,n=[],r=this.$slots.default&&Object(i["d"])(this.$slots.default,t);r&&r.forEach((function(t){t.componentOptions&&"v-uni-movable-view"===t.componentOptions.tag&&n.push(t)})),this.items=n;var o=Object.assign({},this.$listeners),a=["touchstart","touchmove","touchend"];return a.forEach((function(t){var n=o[t],i=e["_".concat(t)];o[t]=n?[].concat(n,i):i})),t("uni-movable-area",{on:o},[t("v-uni-resize-sensor",{on:{resize:this._resize}}),r])}},c=s,u=(n("a3e5"),n("2877")),l=Object(u["a"])(c,o,a,!1,null,null,null);e["default"]=l.exports},c8ba:function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(i){"object"===typeof window&&(n=window)}t.exports=n},c8ed:function(t,e,n){"use strict";var i=n("72ad"),r=n.n(i);r.a},c96e:function(t,e,n){"use strict";var i=n("1307"),r=n.n(i);r.a},cb0f:function(t,e,n){"use strict";n.d(e,"a",(function(){return s}));var i=n("0f74"),r=/^([a-z-]+:)?\/\//i,o=/^data:.*,.*/;function a(t){var e=__uniConfig.router.base;return e?"/"!==e&&0===("/"+t).indexOf(e)?"/"+t:e+t:t}function s(t){if(0===t.indexOf("/")){if(0!==t.indexOf("//"))return a(t.substr(1));t="https:"+t}if(r.test(t)||o.test(t)||0===t.indexOf("blob:"))return t;var e=getCurrentPages();return e.length?a(Object(i["a"])(e[e.length-1].$page.route,t).substr(1)):t}},cc5f:function(t,e,n){"use strict";var i=n("324c"),r=n.n(i);r.a},cc76:function(t,e,n){"use strict";var i=Object.create(null),r=n("19c4");r.keys().forEach((function(t){Object.assign(i,r(t))})),e["a"]=i},cc89:function(t,e,n){},cdc1:function(t,e,n){"use strict";(function(t,i){var r=n("f2b3"),o=n("65a8"),a=n("81ea"),s=n("f1ea");e["a"]={name:"App",components:a["a"],mixins:s["default"],props:{keepAliveInclude:{type:Array,default:function(){return[]}}},data:function(){return{transitionName:"fade",hideTabBar:!1,tabBar:__uniConfig.tabBar||{}}},computed:{key:function(){return this.$route.meta.name+"-"+this.$route.params.__id__+"-"+(__uniConfig.reLaunch||1)},hasTabBar:function(){return __uniConfig.tabBar&&__uniConfig.tabBar.list&&__uniConfig.tabBar.list.length},showTabBar:function(){return this.$route.meta.isTabBar&&!this.hideTabBar}},watch:{$route:function(e,n){t.emit("onHidePopup")},hideTabBar:function(t,e){if(uni.canIUse("css.var")){var n=t?0:o["b"],r=uni.canIUse("css.env")?"env":uni.canIUse("css.constant")?"constant":"",a=n&&r?"calc(".concat(n,"px + ").concat(r,"(safe-area-inset-bottom))"):"".concat(n,"px");document.documentElement.style.setProperty("--window-bottom",a),i.debug("uni.".concat(a?"showTabBar":"hideTabBar","：--window-bottom=").concat(a))}window.dispatchEvent(new CustomEvent("resize"))}},created:function(){uni.canIUse("css.var")&&document.documentElement.style.setProperty("--status-bar-height","0px")},mounted:function(){window.addEventListener("message",(function(e){Object(r["k"])(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&t.emit("onWebInvokeAppService",e.data.data,e.data.pageId)})),document.addEventListener("visibilitychange",(function(){"visible"===document.visibilityState?t.emit("onAppEnterForeground"):t.emit("onAppEnterBackground")}))}}}).call(this,n("0dd1"),n("3ad9")["default"])},d001:function(t,e,n){"use strict";n.r(e),n.d(e,"onUIStyleChange",(function(){return a}));var i=n("a118"),r=n("db70"),o=[];function a(t){o.push(t)}Object(r["d"])("onUIStyleChange",(function(t){o.forEach((function(e){Object(i["a"])(e,t)}))}))},d218:function(t,e){},d29c:function(t,e,n){},d3bd:function(t,e,n){"use strict";n.r(e);var i,r,o=n("8af1"),a={name:"Button",mixins:[o["c"],o["b"],o["f"]],props:{hoverClass:{type:String,default:"button-hover"},disabled:{type:[Boolean,String],default:!1},id:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},formType:{type:String,default:"",validator:function(t){return~["","submit","reset"].indexOf(t)}},openType:{type:String,default:""}},data:function(){return{clickFunction:null}},methods:{_onClick:function(t,e){if(!this.disabled)if(e&&this.$el.click(),this.formType)this.$dispatch("Form","submit"===this.formType?"uni-form-submit":"uni-form-reset",{type:this.formType});else if("feedback"===this.openType){var n=plus.webview.create("https://service.dcloud.net.cn/uniapp/feedback.html","feedback",{titleNView:{titleText:"问题反馈",autoBackButton:!0,backgroundColor:"#F7F7F7",titleColor:"#007aff",buttons:[{text:"发送",color:"#007aff",fontSize:"16px",fontWeight:"bold",onclick:function(t){n.evalJS('mui&&mui.trigger(document.getElementById("submit"),"tap")')}}]}});n.show("slide-in-right")}},_bindObjectListeners:function(t,e){if(e)for(var n in e){var i=t.on[n],r=e[n];t.on[n]=i?[].concat(i,r):r}return t}},render:function(t){var e=this,n=Object.create(null);return this.$listeners&&Object.keys(this.$listeners).forEach((function(t){(!e.disabled||"click"!==t&&"tap"!==t)&&(n[t]=e.$listeners[t])})),this.hoverClass&&"none"!==this.hoverClass?t("uni-button",this._bindObjectListeners({class:[this.hovering?this.hoverClass:""],attrs:{disabled:this.disabled},on:{touchstart:this._hoverTouchStart,touchend:this._hoverTouchEnd,touchcancel:this._hoverTouchCancel,click:this._onClick}},n),this.$slots.default):t("uni-button",this._bindObjectListeners({class:[this.hovering?this.hoverClass:""],attrs:{disabled:this.disabled},on:{click:this._onClick}},n),this.$slots.default)},listeners:{"label-click":"_onClick","@label-click":"_onClick"}},s=a,c=(n("5676"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},d4b6:function(t,e,n){"use strict";n.d(e,"b",(function(){return u})),n.d(e,"a",(function(){return y}));var i=n("f2b3"),r=n("85b6"),o=n("24d9"),a=n("a470");function s(t,e){arguments.length>2&&void 0!==arguments[2]&&arguments[2];var n={id:t.id,offsetLeft:t.offsetLeft,offsetTop:t.offsetTop,dataset:Object(r["c"])(t.dataset)};return e&&Object.assign(n,e),n}function c(t){if(t){for(var e=[],n=Object(a["a"])(),i=n.top,r=0;r<t.length;r++){var o=t[r];e.push({identifier:o.identifier,pageX:o.pageX,pageY:o.pageY-i,clientX:o.clientX,clientY:o.clientY-i,force:o.force||0})}return e}return[]}function u(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};if(e._processed)return e.type=n.type||t,e;if("click"===t){var u=Object(a["a"])(),l=u.top;n={x:e.x,y:e.y-l},e.touches=e.changedTouches=[{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY,pageX:e.pageX,pageY:e.pageY}]}var h=Object(o["b"])({type:n.type||t,timeStamp:e.timeStamp||0,detail:n,target:s(i,n),currentTarget:s(r,!1,!0),touches:e instanceof Event||e instanceof CustomEvent?c(e.touches):e.touches,changedTouches:e instanceof Event||e instanceof CustomEvent?c(e.changedTouches):e.changedTouches,preventDefault:function(){},stopPropagation:function(){}});return h}var l=350,h=10,f=!!i["o"]&&{passive:!0},d=!1;function p(){d&&(clearTimeout(d),d=!1)}var g=0,v=0;function m(t){if(p(),1===t.touches.length){var e=t.touches[0],n=e.pageX,i=e.pageY;g=n,v=i,d=setTimeout((function(){var e=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:t.target,currentTarget:t.currentTarget});e.touches=t.touches,e.changedTouches=t.changedTouches,t.target.dispatchEvent(e)}),l)}}function b(t){if(d){if(1!==t.touches.length)return p();var e=t.touches[0],n=e.pageX,i=e.pageY;return Math.abs(n-g)>h||Math.abs(i-v)>h?p():void 0}}function y(){window.addEventListener("touchstart",m,f),window.addEventListener("touchmove",b,f),window.addEventListener("touchend",p,f),window.addEventListener("touchcancel",p,f)}},d5be:function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"chooseImage",(function(){return u}));var i=n("e2e2"),r=n("f2b3"),o=t,a=o.invokeCallbackHandler,s=null,c=function(t){var e=document.createElement("input");return e.type="file",Object(r["r"])(e,{position:"absolute",visibility:"hidden","z-index":-999,width:0,height:0,top:0,left:0}),e.accept="image/*",t.count>1&&(e.multiple="multiple"),1===t.sourceType.length&&"camera"===t.sourceType[0]&&(e.capture="camera"),e};function u(t,e){var n=t.count,r=t.sourceType;s&&(document.body.removeChild(s),s=null),s=c({count:n,sourceType:r}),document.body.appendChild(s),s.addEventListener("change",(function(t){for(var n=[],r=t.target.files.length,o=function(e){var r=t.target.files[e],o=void 0;Object.defineProperty(r,"path",{get:function(){return o=o||Object(i["a"])(r),o}}),n.push(r)},s=0;s<r;s++)o(s);var c={errMsg:"chooseImage:ok",get tempFilePaths(){return n.map((function(t){var e=t.path;return e}))},tempFiles:n};a(e,c)})),s.click()}}.call(this,n("0dd1"))},d5ec:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-radio-group",t._g({},t.$listeners),[t._t("default")],2)},r=[],o=n("8af1"),a={name:"RadioGroup",mixins:[o["b"],o["f"]],props:{name:{type:String,default:""}},data:function(){return{radioList:[]}},listeners:{"@radio-change":"_changeHandler","@radio-group-update":"_radioGroupUpdateHandler"},mounted:function(){this._resetRadioGroupValue(this.radioList.length-1)},created:function(){this.$dispatch("Form","uni-form-group-update",{type:"add",vm:this})},beforeDestroy:function(){this.$dispatch("Form","uni-form-group-update",{type:"remove",vm:this})},methods:{_changeHandler:function(t,e){var n=this.radioList.indexOf(e);this._resetRadioGroupValue(n,!0),this.$trigger("change",t,{value:e.radioValue})},_radioGroupUpdateHandler:function(t){if("add"===t.type)this.radioList.push(t.vm);else{var e=this.radioList.indexOf(t.vm);this.radioList.splice(e,1)}},_resetRadioGroupValue:function(t,e){var n=this;this.radioList.forEach((function(i,r){r!==t&&(e?n.radioList[r].radioChecked=!1:n.radioList.forEach((function(t,e){r>=e||n.radioList[e].radioChecked&&(n.radioList[r].radioChecked=!1)})))}))},_getFormData:function(){var t={};if(""!==this.name){var e="";this.radioList.forEach((function(t){t.radioChecked&&(e=t.value)})),t.value=e,t.key=this.name}return t}}},s=a,c=(n("fb61"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},d677:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-cover-image",t._g({attrs:{src:t.src}},t.$listeners),[n("div",{staticClass:"uni-cover-image"},[t.src?n("img",{attrs:{src:t.$getRealPath(t.src)},on:{load:t._load,error:t._error}}):t._e()])])},r=[],o={name:"CoverImage",props:{src:{type:String,default:""}},methods:{_load:function(t){this.$trigger("load",t)},_error:function(t){this.$trigger("error",t)}}},a=o,s=(n("5d1d"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},d8c8:function(t,e,n){"use strict";var i,r,o=["top","left","right","bottom"],a={};function s(){return r="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":"",r}function c(){if(r="string"===typeof r?r:s(),r){var t=[],e=!1;try{var n=Object.defineProperty({},"passive",{get:function(){e={passive:!0}}});window.addEventListener("test",null,n)}catch(d){}var c=document.createElement("div");u(c,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),o.forEach((function(t){f(c,t)})),document.body.appendChild(c),l(),i=!0}else o.forEach((function(t){a[t]=0}));function u(t,e){var n=t.style;Object.keys(e).forEach((function(t){var i=e[t];n[t]=i}))}function l(e){e?t.push(e):t.forEach((function(t){t()}))}function f(t,n){var i=document.createElement("div"),o=document.createElement("div"),s=document.createElement("div"),c=document.createElement("div"),f=100,d=1e4,p={position:"absolute",width:f+"px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:r+"(safe-area-inset-"+n+")"};u(i,p),u(o,p),u(s,{transition:"0s",animation:"none",width:"400px",height:"400px"}),u(c,{transition:"0s",animation:"none",width:"250%",height:"250%"}),i.appendChild(s),o.appendChild(c),t.appendChild(i),t.appendChild(o),l((function(){i.scrollTop=o.scrollTop=d;var t=i.scrollTop,r=o.scrollTop;function a(){this.scrollTop!==(this===i?t:r)&&(i.scrollTop=o.scrollTop=d,t=i.scrollTop,r=o.scrollTop,h(n))}i.addEventListener("scroll",a,e),o.addEventListener("scroll",a,e)}));var g=getComputedStyle(i);Object.defineProperty(a,n,{configurable:!0,get:function(){return parseFloat(g.paddingBottom)}})}}function u(t){return i||c(),a[t]}var l=[];function h(t){l.length||setTimeout((function(){var t={};l.forEach((function(e){t[e]=a[e]})),l.length=0,f.forEach((function(e){e(t)}))}),0),l.push(t)}var f=[];function d(t){s()&&(i||c(),"function"===typeof t&&f.push(t))}function p(t){var e=f.indexOf(t);e>=0&&f.splice(e,1)}var g={get support(){return 0!=("string"===typeof r?r:s()).length},get top(){return u("top")},get left(){return u("left")},get right(){return u("right")},get bottom(){return u("bottom")},onChange:d,offChange:p};t.exports=g},d8ca:function(t,e,n){"use strict";(function(t,i){var r,o=n("8af1"),a=n("a20f");function s(t){return h(t)||l(t)||u(t)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"===typeof t)return f(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(t,e):void 0}}function l(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function h(t){if(Array.isArray(t))return f(t)}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function d(t){return t=t.slice(0),t[3]=t[3]/255,"rgba("+t.join(",")+")"}function p(t,e){return[].map.call(e,(function(e){var n=t.getBoundingClientRect();return{identifier:e.identifier,x:e.clientX-n.left,y:e.clientY-n.top}}))}function g(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return r||(r=document.createElement("canvas")),r.width=t,r.height=e,r}e["a"]={name:"Canvas",mixins:[o["g"]],props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1}},data:function(){return{actionsWaiting:!1}},computed:{id:function(){return this.canvasId},_listeners:function(){var t=this,e=Object.assign({},this.$listeners),n=["touchstart","touchmove","touchend"];return n.forEach((function(n){var i=e[n],r=[];i&&r.push((function(e){t.$trigger(n,Object.assign({},e,{touches:p(e.currentTarget,e.touches),changedTouches:p(e.currentTarget,e.changedTouches)}))})),t.disableScroll&&"touchmove"===n&&r.push(t._touchmove),e[n]=r})),e}},created:function(){this._actionsDefer=[],this._images={}},mounted:function(){this._resize({width:this.$refs.sensor.$el.offsetWidth,height:this.$refs.sensor.$el.offsetHeight})},beforeDestroy:function(){var t=this.$refs.canvas;t.height=t.width=0},methods:{_handleSubscribe:function(t){var e=t.type,n=t.data,i=void 0===n?{}:n,r=this[e];0!==e.indexOf("_")&&"function"===typeof r&&r(i)},_resize:function(){var t=this.$refs.canvas;if(t.width>0&&t.height>0){var e=t.getContext("2d"),n=e.getImageData(0,0,t.width,t.height);Object(a["b"])(this.$refs.canvas),e.putImageData(n,0,0)}else Object(a["b"])(this.$refs.canvas)},_touchmove:function(t){t.preventDefault()},actionsChanged:function(e){var n=this,i=e.actions,r=e.reserve,o=e.callbackId,a=this;if(i)if(this.actionsWaiting)this._actionsDefer.push([i,r,o]);else{var c=this.$refs.canvas,u=c.getContext("2d");r||(u.fillStyle="#000000",u.strokeStyle="#000000",u.shadowColor="#000000",u.shadowBlur=0,u.shadowOffsetX=0,u.shadowOffsetY=0,u.setTransform(1,0,0,1,0,0),u.clearRect(0,0,c.width,c.height)),this.preloadImage(i);var l=function(t){var e=i[t],r=e.method,c=e.data;if(/^set/.test(r)&&"setTransform"!==r){var l,h=r[3].toLowerCase()+r.slice(4);if("fillStyle"===h||"strokeStyle"===h){if("normal"===c[0])l=d(c[1]);else if("linear"===c[0]){var g=u.createLinearGradient.apply(u,s(c[1]));c[2].forEach((function(t){var e=t[0],n=d(t[1]);g.addColorStop(e,n)})),l=g}else if("radial"===c[0]){var v=c[1][0],m=c[1][1],b=c[1][2],y=u.createRadialGradient(v,m,0,v,m,b);c[2].forEach((function(t){var e=t[0],n=d(t[1]);y.addColorStop(e,n)})),l=y}else if("pattern"===c[0]){var _=n.checkImageLoaded(c[1],i.slice(t+1),o,(function(t){t&&(u[h]=u.createPattern(t,c[2]))}));return _?"continue":"break"}u[h]=l}else"globalAlpha"===h?u[h]=c[0]/255:"shadow"===h?(f=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"],c.forEach((function(t,e){u[f[e]]="shadowColor"===f[e]?d(t):t}))):"fontSize"===h?u.font=u.font.replace(/\d+\.?\d*px/,c[0]+"px"):"lineDash"===h?(u.setLineDash(c[0]),u.lineDashOffset=c[1]||0):"textBaseline"===h?("normal"===c[0]&&(c[0]="alphabetic"),u[h]=c[0]):u[h]=c[0]}else if("fillPath"===r||"strokePath"===r)r=r.replace(/Path/,""),u.beginPath(),c.forEach((function(t){u[t.method].apply(u,t.data)})),u[r]();else if("fillText"===r)u.fillText.apply(u,c);else if("drawImage"===r){if(p=function(){var e=s(c),n=e[0],r=e.slice(1);if(a._images=a._images||{},!a.checkImageLoaded(n,i.slice(t+1),o,(function(t){t&&u.drawImage.apply(u,[t].concat(s(r.slice(4,8)),s(r.slice(0,4))))})))return"break"}(),"break"===p)return"break"}else"clip"===r?(c.forEach((function(t){u[t.method].apply(u,t.data)})),u.clip()):u[r].apply(u,c)};t:for(var h=0;h<i.length;h++){var f,p,g=l(h);switch(g){case"break":break t;case"continue":continue}}!this.actionsWaiting&&o&&t.publishHandler("onDrawCanvas",{callbackId:o,data:{errMsg:"drawCanvas:ok"}},this.$page.id)}},preloadImage:function(t){var e=this;t.forEach((function(t){var n=t.method,r=t.data,o="";function a(){function t(t){e._images[o].src=(window.URL||window.webkitURL).createObjectURL(t)}function n(t){var n=new plus.nativeObj.Bitmap("bitmap"+Date.now());n.load(t,(function(){e._images[o].src=n.toBase64Data(),n.clear()}),(function(){n.clear(),i.error("preloadImage error")}))}function r(i){function r(){plus.downloader.createDownload(i,{filename:"_doc/uniapp_temp/download/"},(function(t,i){200===i?n(t.filename):e._images[o].src=o})).start()}var a=new XMLHttpRequest;a.open("GET",i,!0),a.responseType="blob",a.onload=function(){200===this.status&&t(this.response)},a.onerror=window.plus?r:function(){e._images[o].src=o},a.send()}e._images[o]=new Image,e._images[o].onload=function(){e._images[o].ready=!0},!window.plus||window.webkit&&window.webkit.messageHandlers?window.plus&&0!==o.indexOf("http://")&&0!==o.indexOf("https://")?n(o):/^data:.*,.*/.test(o)?e._images[o].src=o:r(o):e._images[o].src=o}"drawImage"===n?(o=r[0],o=e.$getRealPath(o),r[0]=o):"setFillStyle"===n&&"pattern"===r[0]&&(o=r[1],o=e.$getRealPath(o),r[1]=o),o&&!e._images[o]&&a()}))},checkImageLoaded:function(t,e,n,i){var r=this,o=this._images[t];return o.ready?(i(o),!0):(this._actionsDefer.unshift([e,!0]),this.actionsWaiting=!0,o.onload=function(){o.ready=!0,i(o),r.actionsWaiting=!1;var t=r._actionsDefer.slice(0);r._actionsDefer=[];for(var e=t.shift();e;)r.actionsChanged({actions:e[0],reserve:e[1],callbackId:n}),e=t.shift()},!1)},getImageData:function(e){var n,i=e.x,r=void 0===i?0:i,o=e.y,c=void 0===o?0:o,u=e.width,l=e.height,h=e.destWidth,f=e.destHeight,d=e.hidpi,p=void 0===d||d,v=e.callbackId,m=this.$refs.canvas;u||(u=m.offsetWidth-r),l||(l=m.offsetHeight-c);try{p?(h=u,f=l):h||f?h?f||(f=Math.round(l/u*h)):h=Math.round(u/l*f):(h=Math.round(u*a["a"]),f=Math.round(l*a["a"]));var b=g(h,f),y=b.getContext("2d");y.__hidpi__=!0,y.drawImageByCanvas(m,r,c,u,l,0,0,h,f,!1),n=y.getImageData(0,0,h,f),b.height=b.width=0,y.__hidpi__=!1}catch(_){if(!v)return;return void t.publishHandler("onCanvasMethodCallback",{callbackId:v,data:{errMsg:"canvasGetImageData:fail"}},this.$page.id)}if(!v)return{data:Array.prototype.slice.call(n.data),width:h,height:f};t.publishHandler("onCanvasMethodCallback",{callbackId:v,data:{errMsg:"canvasGetImageData:ok",data:s(n.data),width:h,height:f}},this.$page.id)},putImageData:function(e){var n=e.data,i=e.x,r=e.y,o=e.width,a=e.height,s=e.callbackId;try{a||(a=Math.round(n.length/4/o));var c=g(o,a),u=c.getContext("2d");u.putImageData(new ImageData(new Uint8ClampedArray(n),o,a),0,0),this.$refs.canvas.getContext("2d").drawImage(c,i,r,o,a),c.height=c.width=0}catch(l){return void t.publishHandler("onCanvasMethodCallback",{callbackId:s,data:{errMsg:"canvasPutImageData:fail"}},this.$page.id)}t.publishHandler("onCanvasMethodCallback",{callbackId:s,data:{errMsg:"canvasPutImageData:ok"}},this.$page.id)},getDataUrl:function(e){var n=this,i=e.x,r=void 0===i?0:i,o=e.y,a=void 0===o?0:o,s=e.width,c=e.height,u=e.destWidth,l=e.destHeight,h=e.hidpi,f=void 0===h||h,d=e.fileType,p=e.qualit,v=e.callbackId,m=this.getImageData({x:r,y:a,width:s,height:c,destWidth:u,destHeight:l,hidpi:f});if(m.data&&m.data.length){var b;try{b=new ImageData(new Uint8ClampedArray(m.data),m.width,m.height)}catch(k){return void t.publishHandler("onCanvasMethodCallback",{callbackId:v,data:{errMsg:"canvasGetDataUrl:fail"}},this.$page.id)}u=m.width,l=m.height;var y=g(u,l),_=y.getContext("2d");_.putImageData(b,0,0);var w=y.toDataURL("image/png");y.height=y.width=0;var S=new Image;S.onload=function(){var e=g(u,l);"jpeg"!==d&&"jpg"!==d||(d="jpeg",_.fillStyle="#fff",_.fillRect(0,0,u,l)),_.drawImage(S,0,0),w=e.toDataURL("image/".concat(d),p),e.height=e.width=0,t.publishHandler("onCanvasMethodCallback",{callbackId:v,data:{errMsg:"canvasGetDataUrl:ok",base64:w}},n.$page.id)},S.src=w}else t.publishHandler("onCanvasMethodCallback",{callbackId:v,data:{errMsg:"canvasGetDataUrl:fail"}},this.$page.id)}}}}).call(this,n("501c"),n("3ad9")["default"])},db18:function(t,e,n){"use strict";var i=n("db76"),r=n.n(i);r.a},db70:function(t,e,n){"use strict";(function(t){n.d(e,"c",(function(){return r})),n.d(e,"d",(function(){return o})),n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return s}));var i=n("3b67");function r(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return i["a"][t].apply(null,n)}function o(e,n){return t.on("api."+e,n)}function a(e){var n=getCurrentPages(),i=n.length;i||t.emit("onError","".concat(e,":fail"));var r=n[i-1];return r.$vm}function s(){var t=getCurrentPages(),e=t[t.length-1];return e&&e.$page.id}}).call(this,n("0dd1"))},db76:function(t,e,n){},db8e:function(t,e,n){"use strict";(function(t){function i(e,n){return n?e?e.$el:n.$el:t.error("page is not ready")}n.d(e,"a",(function(){return i}))}).call(this,n("3ad9")["default"])},dd35:function(t,e,n){"use strict";(function(t){var i=n("e949"),r=n("cb0f"),o=n("15bb"),a={forward:"&#xe600;",back:"&#xe601;",share:"&#xe602;",favorite:"&#xe604;",home:"&#xe605;",menu:"&#xe606;",close:"&#xe650;"};e["a"]={name:"PageHead",mixins:[o["a"]],props:{backButton:{type:Boolean,default:!0},backgroundColor:{type:String,default:function(){return"transparent"===this.type?"#000":"#F8F8F8"}},textColor:{type:String,default:"#fff"},titleText:{type:String,default:""},duration:{type:String,default:"0"},timingFunc:{type:String,default:""},loading:{type:Boolean,default:!1},titleSize:{type:String,default:"16px"},type:{default:"default",validator:function(t){return-1!==["default","transparent","float"].indexOf(t)}},coverage:{type:String,default:"132px"},buttons:{type:Array,default:function(){return[]}},searchInput:{type:[Object,Boolean],default:function(){return!1}},titleImage:{type:String,default:""},titlePenetrate:{type:Boolean,default:!1},shadow:{type:Object,default:function(){return{}}}},data:function(){return{focus:!1,text:"",composing:!1}},computed:{btns:function(){var t=this,e=[],n={};return this.buttons.length&&this.buttons.forEach((function(o){var a=Object.assign({},o);if(a.fontSrc&&!a.fontFamily){var s,c=a.fontSrc=Object(r["a"])(a.fontSrc);if(c in n)s=n[c];else{s="font".concat(Date.now()),n[c]=s;var u='@font-face{font-family: "'.concat(s,'";src: url("').concat(c,'") format("truetype")}');Object(i["a"])(u,"uni-btn-font-"+s)}a.fontFamily=s}a.color="transparent"===t.type?"#fff":a.color||t.textColor;var l=a.fontSize||("transparent"===t.type||/\\u/.test(a.text)?"22px":"27px");/\d$/.test(l)&&(l+="px"),a.fontSize=l,a.fontWeight=a.fontWeight||"normal",e.push(a)})),e},headClass:function(){var t=this.shadow.colorType,e={"uni-page-head-transparent":"transparent"===this.type,"uni-page-head-titlePenetrate":this.titlePenetrate,"uni-page-head-shadow":t};return t&&(e["uni-page-head-shadow-".concat(t)]=t),e}},mounted:function(){var e=this;if(this.searchInput){var n=this.$refs.input;n.$watch("composing",(function(t){e.composing=t})),this.searchInput.disabled?n.$el.addEventListener("click",(function(){t.emit("onNavigationBarSearchInputClicked","")})):(n.$refs.input.addEventListener("keyup",(function(n){"ENTER"===n.key.toUpperCase()&&t.emit("onNavigationBarSearchInputConfirmed",{text:e.text})})),n.$refs.input.addEventListener("focus",(function(){t.emit("onNavigationBarSearchInputFocusChanged",{focus:!0})})),n.$refs.input.addEventListener("blur",(function(){t.emit("onNavigationBarSearchInputFocusChanged",{focus:!1})})))}},methods:{_back:function(){1===getCurrentPages().length?uni.reLaunch({url:"/"}):uni.navigateBack({from:"backbutton"})},_onBtnClick:function(e){t.emit("onNavigationBarButtonTap",Object.assign({},this.btns[e],{index:e}))},_formatBtnFontText:function(t){return t.fontSrc&&t.fontFamily?t.text.replace("\\u","&#x"):a[t.type]?a[t.type]:t.text||""},_formatBtnStyle:function(t){var e={color:t.color,fontSize:t.fontSize,fontWeight:t.fontWeight};return t.fontFamily&&(e.fontFamily=t.fontFamily),e},_focus:function(){this.focus=!0},_blur:function(){this.focus=!1},_input:function(e){t.emit("onNavigationBarSearchInputChanged",{text:e})}}}}).call(this,n("0dd1"))},de29:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var i=n("f2b3");function r(t){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function o(t,e,n){var r=e[t],o=!Object(i["h"])(n,t),s=n[t],c=h(Boolean,r.type);if(c>-1&&o&&!Object(i["h"])(r,"default")&&(s=!1),void 0===s&&Object(i["h"])(r,"default")){var u=r.default;s=Object(i["j"])(u)?u():u,n[t]=s}return a(r,t,s,o,n)}function a(t,e,n,i,r){if(t.required&&i)return"Missing required parameter `".concat(e,"`");if(null==n&&!t.required){var o=t.validator;return o?o(n,r):void 0}var a=t.type,s=!a||!0===a,u=[];if(a){Array.isArray(a)||(a=[a]);for(var l=0;l<a.length&&!s;l++){var h=c(n,a[l]);u.push(h.expectedType||""),s=h.valid}}if(!s)return f(e,n,u);var d=t.validator;return d?d(n,r):void 0}var s=/^(String|Number|Boolean|Function|Symbol)$/;function c(t,e){var n,o=u(e);if(s.test(o)){var a=r(t);n=a===o.toLowerCase(),n||"object"!==a||(n=t instanceof e)}else n=t.byteLength>=0||("Object"===o?Object(i["k"])(t):"Array"===o?Array.isArray(t):t instanceof e||Object(i["q"])(t)===u(e));return{valid:n,expectedType:o}}function u(t){var e=t&&t.toString().match(/^\s*function (\w+)/);return e?e[1]:""}function l(t,e){return u(t)===u(e)}function h(t,e){if(!Array.isArray(e))return l(e,t)?0:-1;for(var n=0,i=e.length;n<i;n++)if(l(e[n],t))return n;return-1}function f(t,e,n){var r="parameter `".concat(t,"`.")+" Expected ".concat(n.join(", ")),o=n[0],a=Object(i["q"])(e),s=d(e,o),c=d(e,a);return 1===n.length&&g(o)&&!v(o,a)&&(r+=" with value ".concat(s)),r+=", got ".concat(a," "),g(a)&&(r+="with value ".concat(c,".")),r}function d(t,e){return"String"===e?'"'.concat(t,'"'):"".concat("Number"===e?Number(t):t)}var p=["string","number","boolean"];function g(t){return p.some((function(e){return t.toLowerCase()===e}))}function v(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.some((function(t){return"boolean"===t.toLowerCase()}))}},df1e:function(t,e,n){},e0b6:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-audio",t._g({attrs:{id:t.id,controls:!!t.controls}},t.$listeners),[n("audio",{ref:"audio",staticStyle:{display:"none"},attrs:{loop:t.loop}}),n("div",{staticClass:"uni-audio-default"},[n("div",{staticClass:"uni-audio-left",style:"background-image: url("+t.$getRealPath(t.poster)+");"},[n("div",{staticClass:"uni-audio-button",class:{play:!t.playing,pause:t.playing},on:{click:t.trigger}})]),n("div",{staticClass:"uni-audio-right"},[n("div",{staticClass:"uni-audio-time"},[t._v(" "+t._s(t.currentTime)+" ")]),n("div",{staticClass:"uni-audio-info"},[n("div",{staticClass:"uni-audio-name"},[t._v(" "+t._s(t.name)+" ")]),n("div",{staticClass:"uni-audio-author"},[t._v(" "+t._s(t.author)+" ")])])])])])},r=[],o=n("8af1"),a={name:"Audio",mixins:[o["g"]],props:{id:{type:String,default:""},src:{type:String,default:""},loop:{type:[Boolean,String],default:!1},controls:{type:[Boolean,String],default:!1},poster:{type:String,default:""},name:{type:String,default:""},author:{type:String,default:""}},data:function(){return{playing:!1,currentTime:this.getTime(0)}},watch:{src:function(t){this.$refs.audio&&(this.$refs.audio.src=this.$getRealPath(t))}},mounted:function(){var t=this,e=this.$refs.audio;e.addEventListener("error",(function(e){t.playing=!1,t.$trigger("error",e,{})})),e.addEventListener("play",(function(e){t.playing=!0,t.$trigger("play",e,{})})),e.addEventListener("pause",(function(e){t.playing=!1,t.$trigger("pause",e,{})})),e.addEventListener("ended",(function(e){t.playing=!1,t.$trigger("ended",e,{})})),e.addEventListener("timeupdate",(function(n){var i=e.currentTime;t.currentTime=t.getTime(i);var r=e.duration;t.$trigger("timeupdate",n,{currentTime:i,duration:r})})),e.src=this.$getRealPath(this.src)},methods:{_handleSubscribe:function(t){var e=t.type,n=t.data,i=void 0===n?{}:n,r=this.$refs.audio;switch(e){case"setSrc":r.src=this.$getRealPath(i.src),this.$emit("update:src",i.src);break;case"play":r.play();break;case"pause":r.pause();break;case"seek":r.currentTime=i.position;break}},trigger:function(){this.playing?this.$refs.audio.pause():this.$refs.audio.play()},getTime:function(t){var e=Math.floor(t/3600),n=Math.floor(t%3600/60),i=Math.floor(t%3600%60);e=(e<10?"0":"")+e,n=(n<10?"0":"")+n,i=(i<10?"0":"")+i;var r=n+":"+i;return"00"!==e&&(r=e+":"+r),r}}},s=a,c=(n("e38a"),n("2877")),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},e298:function(t,e,n){"use strict";var i=n("add1"),r=n.n(i);r.a},e2d4:function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"base64ToTempFilePath",(function(){return o}));var i=t,r=i.invokeCallbackHandler;function o(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.base64Data,n=(t.x,t.y,t.width,t.height,t.destWidth,t.destHeight,t.canvasId,t.fileType,t.quality,arguments.length>1?arguments[1]:void 0);r(n,{errMsg:"canvasToTempFilePath:ok",tempFilePath:e})}}.call(this,n("0dd1"))},e2e2:function(t,e,n){"use strict";n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return s})),n.d(e,"b",(function(){return c}));var i=n("f2b3"),r={};function o(t){var e=r[t];return e?Promise.resolve(e):/^data:[a-z-]+\/[a-z-]+;base64,/.test(t)?Promise.resolve(a(t)):new Promise((function(e,n){var i=new XMLHttpRequest;i.open("GET",t,!0),i.responseType="blob",i.onload=function(){e(this.response)},i.onerror=n,i.send()}))}function a(t){t=t.split(",");var e=t[0].match(/:(.*?);/)[1],n=atob(t[1]),i=n.length,r=new Uint8Array(i);while(i--)r[i]=n.charCodeAt(i);var o="".concat(Date.now(),".").concat(e.split("/")[1]);return new File([r],o,{type:e})}function s(t){for(var e in r)if(Object(i["h"])(r,e)){var n=r[e];if(n===t)return e}var o=(window.URL||window.webkitURL).createObjectURL(t);return r[o]=t,o}function c(t){(window.URL||window.webkitURL).revokeObjectURL(t),delete r[t]}},e38a:function(t,e,n){"use strict";var i=n("8fa5"),r=n.n(i);r.a},e3a7:function(t,e,n){var i={"./base/event-bus.js":"6e0c","./context/audio.js":"924c","./context/canvas.js":"e2d4","./context/inner-audio.js":"f9d2","./context/operate-map-player.js":"0758","./context/operate-video-player.js":"f941","./device/accelerometer.js":"2bdd","./device/compass.js":"f7b4","./device/get-system-info.js":"78c8","./device/hide-keyboard.js":"fa1e","./device/make-phone-call.js":"7f4e","./device/network-info.js":"3d64","./device/vibrate.js":"44de","./file/file.js":"3b54","./file/open-document.js":"e826","./index.js":"d218","./location/choose-location.js":"be14","./location/get-location.js":"0554","./location/open-location.js":"6575","./media/choose-image.js":"d5be","./media/choose-video.js":"8ce3","./media/get-image-info.js":"34b2","./media/preview-image.js":"9e56","./network/download-file.js":"4f43","./network/request.js":"1a12","./network/socket.js":"893e","./network/upload-file.js":"7d18","./plugin/get-provider.js":"abea","./route/route.js":"1a8c","./storage/storage.js":"e649","./ui/navigation-bar.js":"5964","./ui/popup.js":"56e9","./ui/pull-down-refresh.js":"45db","./ui/request-component-info.js":"09e5","./ui/tab-bar.js":"fcd1","./ui/window.js":"e8b5"};function r(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}r.keys=function(){return Object.keys(i)},r.resolve=o,t.exports=r,r.id="e3a7"},e4ee:function(t,e,n){"use strict";n.r(e),n.d(e,"onCompassChange",(function(){return s})),n.d(e,"startCompass",(function(){return c})),n.d(e,"stopCompass",(function(){return u}));var i=n("a118"),r=n("db70"),o=[];Object(r["d"])("onCompassChange",(function(t){o.forEach((function(e){Object(i["a"])(e,t)}))}));var a=!1;function s(t){o.push(t),a||c()}function c(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};t.interval;if(!a)return a=!0,Object(r["c"])("enableCompass",{enable:!0})}function u(){return a=!1,Object(r["c"])("enableCompass",{enable:!1})}},e4f1:function(t,e,n){},e5bb:function(t,e,n){"use strict";n.r(e),n.d(e,"chooseLocation",(function(){return i}));var i={keyword:{type:String}}},e649:function(t,e,n){"use strict";function i(t){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}n.r(e),n.d(e,"setStorage",(function(){return a})),n.d(e,"setStorageSync",(function(){return s})),n.d(e,"getStorage",(function(){return c})),n.d(e,"getStorageSync",(function(){return u})),n.d(e,"removeStorage",(function(){return l})),n.d(e,"removeStorageSync",(function(){return h})),n.d(e,"clearStorage",(function(){return f})),n.d(e,"clearStorageSync",(function(){return d})),n.d(e,"getStorageInfo",(function(){return p})),n.d(e,"getStorageInfoSync",(function(){return g}));var r="uni-storage-keys";function o(t){var e=["object","string","number","boolean","undefined"];try{var n="string"===typeof t?JSON.parse(t):t,r=n.type;if(e.indexOf(r)>=0){var o=Object.keys(n);if(2===o.length&&"data"in n){if(i(n.data)===r)return n.data;if("object"===r&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===o.length)return""}}catch(a){}}function a(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.key,n=t.data,r=i(n),o="string"===r?n:JSON.stringify({type:r,data:n});try{localStorage.setItem(e,o)}catch(a){return{errMsg:"setStorage:fail ".concat(a)}}return{errMsg:"setStorage:ok"}}function s(t,e){a({key:t,data:e})}function c(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.key,n=localStorage&&localStorage.getItem(e);if("string"!==typeof n)return{data:"",errMsg:"getStorage:fail"};var i=n;try{var r=JSON.parse(n),a=o(r);void 0!==a&&(i=a)}catch(s){}return{data:i,errMsg:"getStorage:ok"}}function u(t){var e=c({key:t});return e.data}function l(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.key;return localStorage&&localStorage.removeItem(e),{errMsg:"removeStorage:ok"}}function h(t){l({key:t})}function f(){return localStorage&&localStorage.clear(),{errMsg:"clearStorage:ok"}}function d(){f()}function p(){for(var t=localStorage&&localStorage.length||0,e=[],n=0,i=0;i<t;i++){var o=localStorage.key(i),a=localStorage.getItem(o);n+=o.length+a.length,o!==r&&e.push(o)}return{keys:e,currentSize:Math.ceil(2*n/1024),limitSize:Number.MAX_VALUE,errMsg:"getStorageInfo:ok"}}function g(){var t=p();return delete t.errMsg,t}},e692:function(t,e,n){},e826:function(t,e,n){"use strict";n.r(e),function(t){function i(e,n){var i=e.filePath,r=t,o=r.invokeCallbackHandler;window.open(i),o(n,{errMsg:"openDocument:ok"})}n.d(e,"openDocument",(function(){return i}))}.call(this,n("0dd1"))},e865:function(t,e,n){"use strict";var i=n("5dc4"),r=n.n(i);r.a},e8b5:function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"onWindowResize",(function(){return a})),n.d(e,"offWindowResize",(function(){return s}));var i=[],r=[];function o(){r.push(setTimeout((function(){r.forEach((function(t){return clearTimeout(t)})),r.length=0;var e=t,n=e.invokeCallbackHandler,o=uni.getSystemInfoSync(),a=o.windowWidth,s=o.windowHeight,c=o.screenWidth,u=o.screenHeight,l=90===Math.abs(window.orientation),h=l?"landscape":"portrait";i.forEach((function(t){n(t,{deviceOrientation:h,size:{windowWidth:a,windowHeight:s,screenWidth:c,screenHeight:u}})}))}),20))}function a(t){i.length||window.addEventListener("resize",o),i.push(t)}function s(t){i.splice(i.indexOf(t),1),i.length||window.removeEventListener("resize",o)}}.call(this,n("0dd1"))},e949:function(t,e,n){"use strict";function i(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=document.getElementById(e);i&&n&&(i.parentNode.removeChild(i),i=null),i||(i=document.createElement("style"),i.type="text/css",e&&(i.id=e),document.getElementsByTagName("head")[0].appendChild(i)),i.appendChild(document.createTextNode(t))}n.d(e,"a",(function(){return i}))},ea49:function(t,e,n){},ec33:function(t,e,n){"use strict";n.r(e),n.d(e,"getStorage",(function(){return i})),n.d(e,"getStorageSync",(function(){return r})),n.d(e,"setStorage",(function(){return o})),n.d(e,"setStorageSync",(function(){return a})),n.d(e,"removeStorage",(function(){return s})),n.d(e,"removeStorageSync",(function(){return c}));var i={key:{type:String,required:!0}},r=[{name:"key",type:String,required:!0}],o={key:{type:String,required:!0},data:{required:!0}},a=[{name:"key",type:String,required:!0},{name:"data",required:!0}],s=i,c=r},ed1a:function(t,e,n){"use strict";n.d(e,"b",(function(){return f})),n.d(e,"a",(function(){return d})),n.d(e,"c",(function(){return p})),n.d(e,"d",(function(){return m}));var i=n("f2b3"),r=n("8542"),o=/^\$|sendNativeEvent|restoreGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64/,a=/^create|Manager$/,s=["createBLEConnection"],c=["request","downloadFile","uploadFile","connectSocket"],u=["createBLEConnection"],l=/^on|^off/;function h(t){return a.test(t)&&-1===s.indexOf(t)}function f(t){return o.test(t)&&-1===u.indexOf(t)}function d(t){return l.test(t)&&"onPush"!==t}function p(t){return-1!==c.indexOf(t)}function g(t){return t.then((function(t){return[null,t]})).catch((function(t){return[t]}))}function v(t){return!(h(t)||f(t)||d(t))}function m(t,e){return v(t)?function(){for(var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length,a=new Array(o>1?o-1:0),s=1;s<o;s++)a[s-1]=arguments[s];return Object(i["j"])(n.success)||Object(i["j"])(n.fail)||Object(i["j"])(n.complete)?Object(r["e"])(t,r["b"].apply(void 0,[t,e,n].concat(a))):Object(r["e"])(t,g(new Promise((function(i,o){r["b"].apply(void 0,[t,e,Object.assign({},n,{success:i,fail:o})].concat(a))}))))}:e}Promise.prototype.finally||(Promise.prototype.finally=function(t){var e=this.constructor;return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){throw n}))}))})},ed78:function(t,e,n){"use strict";(function(t){var i=n("8aec"),r=n("f2b3"),o=!!r["o"]&&{passive:!0};e["a"]={name:"ScrollView",mixins:[i["a"]],props:{scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"back"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},data:function(){return{lastScrollTop:this.scrollTopNumber,lastScrollLeft:this.scrollLeftNumber,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshRotate:0,refreshState:""}},computed:{upperThresholdNumber:function(){var t=Number(this.upperThreshold);return isNaN(t)?50:t},lowerThresholdNumber:function(){var t=Number(this.lowerThreshold);return isNaN(t)?50:t},scrollTopNumber:function(){return Number(this.scrollTop)||0},scrollLeftNumber:function(){return Number(this.scrollLeft)||0}},watch:{scrollTopNumber:function(t){this._scrollTopChanged(t)},scrollLeftNumber:function(t){this._scrollLeftChanged(t)},scrollIntoView:function(t){this._scrollIntoViewChanged(t)},refresherTriggered:function(t){!0===t?this._setRefreshState("refreshing"):!1===t&&this._setRefreshState("restore")}},mounted:function(){var t=this;this._attached=!0,this._scrollTopChanged(this.scrollTopNumber),this._scrollLeftChanged(this.scrollLeftNumber),this._scrollIntoViewChanged(this.scrollIntoView),this.__handleScroll=function(e){event.preventDefault(),event.stopPropagation(),t._handleScroll.bind(t,event)()};var e=null,n=null;this.__handleTouchMove=function(i){var r=i.touches[0].pageX,o=i.touches[0].pageY,a=t.$refs.main;if(null===n)if(Math.abs(r-e.x)>Math.abs(o-e.y))if(t.scrollX){if(0===a.scrollLeft&&r>e.x)return void(n=!1);if(a.scrollWidth===a.offsetWidth+a.scrollLeft&&r<e.x)return void(n=!1);n=!0}else n=!1;else if(t.scrollY){if(0===a.scrollTop&&o>e.y)return void(n=!1);if(a.scrollHeight===a.offsetHeight+a.scrollTop&&o<e.y)return void(n=!1);n=!0}else n=!1;if(n&&i.stopPropagation(),t.refresherEnabled&&"pulling"===t.refreshState){var s=o-e.y;t.refresherHeight=s;var c=s/t.refresherThreshold;c>1?c=1:c*=360,t.refreshRotate=c,t.$trigger("refresherpulling",i,{deltaY:s})}},this.__handleTouchStart=function(i){1===i.touches.length&&(Object(r["e"])({disable:!0}),n=null,e={x:i.touches[0].pageX,y:i.touches[0].pageY},t.refresherEnabled&&"refreshing"!==t.refreshState&&0===t.$refs.main.scrollTop&&(t.refreshState="pulling"))},this.__handleTouchEnd=function(n){e=null,Object(r["e"])({disable:!1}),t.refresherHeight>=t.refresherThreshold?t._setRefreshState("refreshing"):(t.refresherHeight=0,t.$trigger("refresherabort",n,{}))},this.$refs.main.addEventListener("touchstart",this.__handleTouchStart,o),this.$refs.main.addEventListener("touchmove",this.__handleTouchMove,o),this.$refs.main.addEventListener("scroll",this.__handleScroll,!!r["o"]&&{passive:!1}),this.$refs.main.addEventListener("touchend",this.__handleTouchEnd,o)},activated:function(){this.scrollY&&(this.$refs.main.scrollTop=this.lastScrollTop),this.scrollX&&(this.$refs.main.scrollLeft=this.lastScrollLeft)},beforeDestroy:function(){this.$refs.main.removeEventListener("touchstart",this.__handleTouchStart,o),this.$refs.main.removeEventListener("touchmove",this.__handleTouchMove,o),this.$refs.main.removeEventListener("scroll",this.__handleScroll,!!r["o"]&&{passive:!1}),this.$refs.main.removeEventListener("touchend",this.__handleTouchEnd,o)},methods:{scrollTo:function(t,e){var n=this.$refs.main;t<0?t=0:"x"===e&&t>n.scrollWidth-n.offsetWidth?t=n.scrollWidth-n.offsetWidth:"y"===e&&t>n.scrollHeight-n.offsetHeight&&(t=n.scrollHeight-n.offsetHeight);var i=0,r="";"x"===e?i=n.scrollLeft-t:"y"===e&&(i=n.scrollTop-t),0!==i&&(this.$refs.content.style.transition="transform .3s ease-out",this.$refs.content.style.webkitTransition="-webkit-transform .3s ease-out","x"===e?r="translateX("+i+"px) translateZ(0)":"y"===e&&(r="translateY("+i+"px) translateZ(0)"),this.$refs.content.removeEventListener("transitionend",this.__transitionEnd),this.$refs.content.removeEventListener("webkitTransitionEnd",this.__transitionEnd),this.__transitionEnd=this._transitionEnd.bind(this,t,e),this.$refs.content.addEventListener("transitionend",this.__transitionEnd),this.$refs.content.addEventListener("webkitTransitionEnd",this.__transitionEnd),"x"===e?n.style.overflowX="hidden":"y"===e&&(n.style.overflowY="hidden"),this.$refs.content.style.transform=r,this.$refs.content.style.webkitTransform=r)},_handleTrack:function(t){if("start"===t.detail.state)return this._x=t.detail.x,this._y=t.detail.y,void(this._noBubble=null);"end"===t.detail.state&&(this._noBubble=!1),null===this._noBubble&&this.scrollY&&(Math.abs(this._y-t.detail.y)/Math.abs(this._x-t.detail.x)>1?this._noBubble=!0:this._noBubble=!1),null===this._noBubble&&this.scrollX&&(Math.abs(this._x-t.detail.x)/Math.abs(this._y-t.detail.y)>1?this._noBubble=!0:this._noBubble=!1),this._x=t.detail.x,this._y=t.detail.y,this._noBubble&&t.stopPropagation()},_handleScroll:function(t){if(!(t.timeStamp-this._lastScrollTime<20)){this._lastScrollTime=t.timeStamp;var e=t.target;this.$trigger("scroll",t,{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop,scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,deltaX:this.lastScrollLeft-e.scrollLeft,deltaY:this.lastScrollTop-e.scrollTop}),this.scrollY&&(e.scrollTop<=this.upperThresholdNumber&&this.lastScrollTop-e.scrollTop>0&&t.timeStamp-this.lastScrollToUpperTime>200&&(this.$trigger("scrolltoupper",t,{direction:"top"}),this.lastScrollToUpperTime=t.timeStamp),e.scrollTop+e.offsetHeight+this.lowerThresholdNumber>=e.scrollHeight&&this.lastScrollTop-e.scrollTop<0&&t.timeStamp-this.lastScrollToLowerTime>200&&(this.$trigger("scrolltolower",t,{direction:"bottom"}),this.lastScrollToLowerTime=t.timeStamp)),this.scrollX&&(e.scrollLeft<=this.upperThresholdNumber&&this.lastScrollLeft-e.scrollLeft>0&&t.timeStamp-this.lastScrollToUpperTime>200&&(this.$trigger("scrolltoupper",t,{direction:"left"}),this.lastScrollToUpperTime=t.timeStamp),e.scrollLeft+e.offsetWidth+this.lowerThresholdNumber>=e.scrollWidth&&this.lastScrollLeft-e.scrollLeft<0&&t.timeStamp-this.lastScrollToLowerTime>200&&(this.$trigger("scrolltolower",t,{direction:"right"}),this.lastScrollToLowerTime=t.timeStamp)),this.lastScrollTop=e.scrollTop,this.lastScrollLeft=e.scrollLeft}},_scrollTopChanged:function(t){this.scrollY&&(this._innerSetScrollTop?this._innerSetScrollTop=!1:this.scrollWithAnimation?this.scrollTo(t,"y"):this.$refs.main.scrollTop=t)},_scrollLeftChanged:function(t){this.scrollX&&(this._innerSetScrollLeft?this._innerSetScrollLeft=!1:this.scrollWithAnimation?this.scrollTo(t,"x"):this.$refs.main.scrollLeft=t)},_scrollIntoViewChanged:function(e){if(e){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(e))return t.group('scroll-into-view="'+e+'" 有误'),t.error("id 属性值格式错误。如不能以数字开头。"),void t.groupEnd();var n=this.$el.querySelector("#"+e);if(n){var i=this.$refs.main.getBoundingClientRect(),r=n.getBoundingClientRect();if(this.scrollX){var o=r.left-i.left,a=this.$refs.main.scrollLeft,s=a+o;this.scrollWithAnimation?this.scrollTo(s,"x"):this.$refs.main.scrollLeft=s}if(this.scrollY){var c=r.top-i.top,u=this.$refs.main.scrollTop,l=u+c;this.scrollWithAnimation?this.scrollTo(l,"y"):this.$refs.main.scrollTop=l}}}},_transitionEnd:function(t,e){this.$refs.content.style.transition="",this.$refs.content.style.webkitTransition="",this.$refs.content.style.transform="",this.$refs.content.style.webkitTransform="";var n=this.$refs.main;"x"===e?(n.style.overflowX=this.scrollX?"auto":"hidden",n.scrollLeft=t):"y"===e&&(n.style.overflowY=this.scrollY?"auto":"hidden",n.scrollTop=t),this.$refs.content.removeEventListener("transitionend",this.__transitionEnd),this.$refs.content.removeEventListener("webkitTransitionEnd",this.__transitionEnd)},_setRefreshState:function(t){switch(t){case"refreshing":this.refresherHeight=this.refresherThreshold,this.$trigger("refresherrefresh",event,{});break;case"restore":this.refresherHeight=0,this.$trigger("refresherrestore",{},{});break}this.refreshState=t},getScrollPosition:function(){var t=this.$refs.main;return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}}}}).call(this,n("3ad9")["default"])},ed9f:function(t,e,n){"use strict";n.r(e),n.d(e,"chooseVideo",(function(){return r}));var i=["album","camera"],r={sourceType:{type:Array,required:!1,default:i,validator:function(t,e){var n=t.length;if(n){for(var r=0;r<n;r++)if("string"!==typeof t[r]||!~i.indexOf(t[r])){e.sourceType=i;break}}else e.sourceType=i}}}},edfa:function(t,e,n){"use strict";var i=n("c418"),r=n.n(i);r.a},ee03:function(t,e,n){"use strict";n.r(e),n.d(e,"VideoContext",(function(){return u})),n.d(e,"createVideoContext",(function(){return l}));var i=n("db70");function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}var s=[.5,.8,1,1.25,1.5,2];function c(t,e,n,r){Object(i["c"])("operateVideoPlayer",t,e,n,r)}var u=function(){function t(e,n){r(this,t),this.id=e,this.pageVm=n}return a(t,[{key:"play",value:function(){c(this.id,this.pageVm,"play")}},{key:"pause",value:function(){c(this.id,this.pageVm,"pause")}},{key:"stop",value:function(){c(this.id,this.pageVm,"stop")}},{key:"seek",value:function(t){c(this.id,this.pageVm,"seek",{position:t})}},{key:"sendDanmu",value:function(t){c(this.id,this.pageVm,"sendDanmu",t)}},{key:"playbackRate",value:function(t){~s.indexOf(t)||(t=1),c(this.id,this.pageVm,"playbackRate",{rate:t})}},{key:"requestFullScreen",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};c(this.id,this.pageVm,"requestFullScreen",t)}},{key:"exitFullScreen",value:function(){c(this.id,this.pageVm,"exitFullScreen")}},{key:"showStatusBar",value:function(){c(this.id,this.pageVm,"showStatusBar")}},{key:"hideStatusBar",value:function(){c(this.id,this.pageVm,"hideStatusBar")}}]),t}();function l(t,e){return new u(t,e||Object(i["b"])("createVideoContext"))}},ee4f:function(t,e,n){"use strict";n.r(e),function(t){var i=n("f2b3");e["default"]={data:function(){return{showModal:{visible:!1}}},created:function(){var e=this;t.on("onShowModal",(function(t,n){e.showModal=t,e.onModalCloseCallback=n})),t.on("onHidePopup",(function(t){e.showModal.visible=!1}))},methods:{_onModalClose:function(t){this.showModal.visible=!1,Object(i["j"])(this.onModalCloseCallback)&&this.onModalCloseCallback(t)}}}}.call(this,n("0dd1"))},ef36:function(t,e,n){},f0c3:function(t,e,n){"use strict";n.r(e),n.d(e,"downloadFile",(function(){return l}));var i=n("a118"),r=n("db70");function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function s(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t}var c=function(){function t(e,n){o(this,t),this.id=e,this._callbackId=n,this._callbacks=[]}return s(t,[{key:"abort",value:function(){Object(r["c"])("operateDownloadTask",{downloadTaskId:this.id,operationType:"abort"})}},{key:"onProgressUpdate",value:function(t){"function"===typeof t&&this._callbacks.push(t)}},{key:"onHeadersReceived",value:function(){}},{key:"offProgressUpdate",value:function(t){var e=this._callbacks.indexOf(t);e>=0&&this._callbacks.splice(e,1)}},{key:"offHeadersReceived",value:function(){}}]),t}(),u=Object.create(null);function l(t,e){var n=Object(r["c"])("createDownloadTask",t),i=n.downloadTaskId,o=new c(i,e);return u[i]=o,o}Object(r["d"])("onDownloadTaskStateChange",(function(t){var e=t.downloadTaskId,n=t.state,r=t.tempFilePath,o=t.statusCode,a=t.progress,s=t.totalBytesWritten,c=t.totalBytesExpectedToWrite,l=t.errMsg,h=u[e],f=h._callbackId;switch(n){case"progressUpdate":h._callbacks.forEach((function(t){t({progress:a,totalBytesWritten:s,totalBytesExpectedToWrite:c})}));break;case"success":Object(i["a"])(f,{tempFilePath:r,statusCode:o,errMsg:"request:ok"});case"fail":Object(i["a"])(f,{errMsg:"request:fail "+l});default:setTimeout((function(){delete u[e]}),100);break}}))},f102:function(t,e,n){"use strict";n.r(e),n.d(e,"makePhoneCall",(function(){return i}));var i={phoneNumber:{type:String,required:!0,validator:function(t){if(!t)return"makePhoneCall:fail parameter error: parameter.phoneNumber should not be empty String;"}}}},f10e:function(t,e,n){"use strict";var i=n("29a2"),r=n.n(i);r.a},f1b2:function(t,e,n){"use strict";n.r(e),n.d(e,"chooseImage",(function(){return o}));var i=["original","compressed"],r=["album","camera"],o={count:{type:Number,required:!1,default:9,validator:function(t,e){t<=0&&(e.count=9)}},sizeType:{type:[Array,String],required:!1,default:i,validator:function(t,e){var n=t.length;if(n){if("string"===typeof t)~i.indexOf(t)||(e.sizeType=i);else for(var r=0;r<n;r++)if("string"!==typeof t[r]||!~i.indexOf(t[r])){e.sizeType=i;break}}else e.sizeType=i}},sourceType:{type:Array,required:!1,default:r,validator:function(t,e){var n=t.length;if(n){for(var i=0;i<n;i++)if("string"!==typeof t[i]||!~r.indexOf(t[i])){e.sourceType=r;break}}else e.sourceType=r}}}},f1ea:function(t,e,n){"use strict";n.r(e);var i=[],r=n("8793");r.keys().forEach((function(t){"./index.js"!==t&&i.push(r(t).default)})),e["default"]=i},f1ef:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uni-scroll-view",t._g({},t.$listeners),[n("div",{ref:"wrap",staticClass:"uni-scroll-view"},[n("div",{ref:"main",staticClass:"uni-scroll-view",style:{"overflow-x":t.scrollX?"auto":"hidden","overflow-y":t.scrollY?"auto":"hidden"}},[n("div",{ref:"content",staticClass:"uni-scroll-view-content"},[t.refresherEnabled?n("div",{ref:"refresherinner",staticClass:"uni-scroll-view-refresher",style:{"background-color":t.refresherBackground,height:t.refresherHeight+"px"}},["none"!==t.refresherDefaultStyle?n("div",{staticClass:"uni-scroll-view-refresh"},[n("div",{staticClass:"uni-scroll-view-refresh-inner"},["pulling"==t.refreshState?n("svg",{staticClass:"uni-scroll-view-refresh__icon",style:{transform:"rotate("+t.refreshRotate+"deg)"},attrs:{fill:"#2BD009",width:"24",height:"24",viewBox:"0 0 24 24"}},[n("path",{attrs:{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"}}),n("path",{attrs:{d:"M0 0h24v24H0z",fill:"none"}})]):t._e(),"refreshing"==t.refreshState?n("svg",{staticClass:"uni-scroll-view-refresh__spinner",attrs:{width:"24",height:"24",viewBox:"25 25 50 50"}},[n("circle",{staticStyle:{color:"#2BD009"},attrs:{cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"3"}})]):t._e()])]):t._e(),"none"==t.refresherDefaultStyle?t._t("refresher"):t._e()],2):t._e(),t._t("default")],2)])])])},r=[],o=n("ed78"),a=o["a"],s=(n("5ab3"),n("2877")),c=Object(s["a"])(a,i,r,!1,null,null,null);e["default"]=c.exports},f2b3:function(t,e,n){"use strict";n.d(e,"o",(function(){return i})),n.d(e,"j",(function(){return c})),n.d(e,"k",(function(){return u})),n.d(e,"h",(function(){return l})),n.d(e,"q",(function(){return h})),n.d(e,"n",(function(){return p})),n.d(e,"g",(function(){return g})),n.d(e,"f",(function(){return v})),n.d(e,"r",(function(){return m})),n.d(e,"b",(function(){return b})),n.d(e,"p",(function(){return y})),n.d(e,"l",(function(){return _})),n.d(e,"d",(function(){return w})),n.d(e,"i",(function(){return S})),n.d(e,"c",(function(){return O})),n.d(e,"e",(function(){return j})),n.d(e,"m",(function(){return E})),n.d(e,"a",(function(){return B}));var i=!1;try{var r={};Object.defineProperty(r,"passive",{get:function(){i=!0}}),window.addEventListener("test-passive",null,r)}catch(L){}var o=Object.prototype.toString,a=Object.prototype.hasOwnProperty,s=function(t){return t>9?t:"0"+t};function c(t){return"function"===typeof t}function u(t){return"[object Object]"===o.call(t)}function l(t,e){return a.call(t,e)}function h(t){return o.call(t).slice(8,-1)}function f(t){var e=Object.create(null);return function(n){var i=e[n];return i||(e[n]=t(n))}}var d=/-(\w)/g;f((function(t){return t.replace(d,(function(t,e){return e?e.toUpperCase():""}))}));function p(t,e,n){e.forEach((function(e){l(n,e)&&(t[e]=n[e])}))}function g(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return(""+t).replace(/[^\x00-\xff]/g,"**").length}function v(t){var e=t.date,n=void 0===e?new Date:e,i=t.mode,r=void 0===i?"date":i;return"time"===r?s(n.getHours())+":"+s(n.getMinutes()):n.getFullYear()+"-"+s(n.getMonth()+1)+"-"+s(n.getDate())}function m(t,e){for(var n in e)t.style[n]=e[n]}function b(t,e){var n,i=function(){var i=arguments,r=this;clearTimeout(n);var o=function(){return t.apply(r,i)};n=setTimeout(o,e)};return i.cancel=function(){clearTimeout(n)},i}function y(t,e){var n,i=0,r=function(){for(var r=this,o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];var c=Date.now();clearTimeout(n);var u=function(){i=c,t.apply(r,a)};c-i<e?n=setTimeout(u,e-(c-i)):u()};return r.cancel=function(){clearTimeout(n)},r}function _(t){return t.replace(/[A-Z]/g,(function(t){return"-"+t.toLowerCase()}))}function w(t,e){function n(t){var i=t.children&&t.children.map(n),r=e(t.tag,t.data,i);return r.text=t.text,r.isComment=t.isComment,r.componentOptions=t.componentOptions,r.elm=t.elm,r.context=t.context,r.ns=t.ns,r.isStatic=t.isStatic,r.key=t.key,r}return t.map(n)}function S(t){var e,n,i;if(t=t.replace("#",""),6===t.length)e=t.substring(0,2),n=t.substring(2,4),i=t.substring(4,6);else{if(3!==t.length)return!1;e=t.substring(0,1),n=t.substring(1,2),i=t.substring(2,3)}return 1===e.length&&(e+=e),1===n.length&&(n+=n),1===i.length&&(i+=i),e=parseInt(e,16),n=parseInt(n,16),i=parseInt(i,16),{r:e,g:n,b:i}}var k,T,x,C=decodeURIComponent;function O(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={};return Object.keys(t).forEach((function(n){try{e[n]=C(t[n])}catch(L){e[n]=t[n]}})),e}function E(t){if("function"===typeof t)return window.plus?t():void document.addEventListener("plusready",t)}var M={};function j(t){var e=t.disable;function n(){k||(k=plus.webview.currentWebview()),x||(T=(k.getStyle()||{}).pullToRefresh||{}),x=e,T.support&&k.setPullToRefresh(Object.assign({},T,{support:!e}))}var i=Date.now();e===M.disable&&i-M.time<20||(M.disable=e,M.time=i,E((function(){"iOS"===plus.os.name?setTimeout(n,20):n()})))}var A=0,I={};function $(t){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=String(A++);I[n]={success:e.success,fail:e.fail,complete:e.complete};var i=Object.assign({},e),r=t.bind(this)(i,n);r&&P(n,r)}}function P(t,e){var n=I[t]||{};delete I[t];var i=e.errMsg||"";new RegExp("\\:\\s*fail").test(i)?n.fail&&n.fail(e):n.success&&n.success(e),n.complete&&n.complete(e)}var B={warp:$,invoke:P}},f2ce:function(t,e,n){"use strict";(function(t){var i=n("8af1");e["a"]={name:"Label",mixins:[i["b"]],props:{for:{type:String,default:""}},computed:{pointer:function(){return this.for||this.$slots.default&&this.$slots.default.length}},methods:{_onClick:function(e){var n=/^uni-(checkbox|radio|switch)-/.test(e.target.className);n||(n=/^uni-(checkbox|radio|switch|button)$/i.test(e.target.tagName)),n||(this.for?t.emit("uni-label-click-"+this.$page.id+"-"+this.for,e,!0):this.$broadcast(["Checkbox","Radio","Switch","Button"],"uni-label-click",e,!0))}}}}).call(this,n("501c"))},f4e0:function(t,e,n){"use strict";var i=n("c2aa"),r=n.n(i);r.a},f53a:function(t,e,n){"use strict";var i=n("f735"),r=n.n(i);r.a},f735:function(t,e,n){},f756:function(t,e,n){},f7b4:function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"onCompassChange",(function(){return o})),n.d(e,"startCompass",(function(){return a})),n.d(e,"stopCompass",(function(){return s}));var i,r=[];function o(t){r.push(t),i||a()}function a(){var e=t,n=e.invokeCallbackHandler;if(window.DeviceOrientationEvent)return i=function(t){var e=360-t.alpha;r.forEach((function(t){n(t,{errMsg:"onCompassChange:ok",direction:e||0})}))},window.addEventListener("deviceorientation",i,!1),{};throw new Error("device nonsupport deviceorientation")}function s(){return i&&(window.removeEventListener("deviceorientation",i,!1),i=null),{}}}.call(this,n("0dd1"))},f7fd:function(t,e,n){"use strict";var i=n("33b4"),r=n.n(i);r.a},f941:function(t,e,n){"use strict";n.r(e),function(t){function i(e,n,i,r){var o=n.$page.id;t.publishHandler(o+"-video-"+e,{videoId:e,type:i,data:r},o)}n.d(e,"operateVideoPlayer",(function(){return i}))}.call(this,n("0dd1"))},f9d2:function(t,e,n){"use strict";n.r(e),n.d(e,"createInnerAudioContext",(function(){return h}));var i=n("cb0f");function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var c=["onCanplay","onPlay","onPause","onStop","onEnded","onTimeUpdate","onError","onWaiting","onSeeking","onSeeked"],u=["offCanplay","offPlay","offPause","offStop","offEnded","offTimeUpdate","offError","offWaiting","offSeeking","offSeeked"],l=function(){function t(){var e=this;r(this,t),s(this,"_audio",void 0),s(this,"_stoping",void 0),s(this,"startTime",void 0),s(this,"_events",void 0),s(this,"_src",void 0);var n=this._audio=new Audio;this._stoping=!1;var o=["src","autoplay","loop","duration","currentTime","paused","volume"];o.forEach((function(t){Object.defineProperty(e,t,{set:"src"===t?function(t){return n.src=Object(i["a"])(t),e._src=t,t}:function(e){return n[t]=e,e},get:"src"===t?function(){return e._src}:function(){return n[t]}})})),this.startTime=0,Object.defineProperty(this,"obeyMuteSwitch",{set:function(t){return!1},get:function(){return!1}}),Object.defineProperty(this,"buffered",{get:function(){var t=n.buffered;return t.length?t.end(t.length-1):0}}),this._events={},c.forEach((function(t){e._events[t]=[]})),n.addEventListener("loadedmetadata",(function(){var t=Number(e.startTime)||0;t>0&&(n.currentTime=t)}));var a=["canplay","play","pause","ended","timeUpdate","error","waiting","seeking","seeked"],u=["pause","seeking","seeked","timeUpdate"];a.forEach((function(t){n.addEventListener(t.toLowerCase(),(function(){e._stoping&&u.indexOf(t)>=0||e._events["on".concat(t.substr(0,1).toUpperCase()).concat(t.substr(1))].forEach((function(t){t()}))}),!1)}))}return a(t,[{key:"play",value:function(){this._stoping=!1,this._audio.play()}},{key:"pause",value:function(){this._audio.pause()}},{key:"stop",value:function(){this._stoping=!0,this._audio.pause(),this._audio.currentTime=0,this._events.onStop.forEach((function(t){t()}))}},{key:"seek",value:function(t){this._stoping=!1,t=Number(t),"number"!==typeof t||isNaN(t)||(this._audio.currentTime=t)}},{key:"destroy",value:function(){this.stop()}}]),t}();function h(){return new l}c.forEach((function(t){l.prototype[t]=function(e){"function"===typeof e&&this._events[t].push(e)}})),u.forEach((function(t){l.prototype[t]=function(e){var n=this._events[t.replace("off","on")],i=n.indexOf(e);i>=0&&n.splice(i,1)}}))},fa1e:function(t,e,n){"use strict";function i(){var t=document.activeElement;!t||"TEXTAREA"!==t.tagName&&"INPUT"!==t.tagName||t.blur()}n.r(e),n.d(e,"hideKeyboard",(function(){return i}))},fae3:function(t,e,n){"use strict";if(n.r(e),"undefined"!==typeof window){var i=window.document.currentScript,r=n("8875");i=r(),"currentScript"in document||Object.defineProperty(document,"currentScript",{get:r});var o=i&&i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);o&&(n.p=o[1])}n("2ef3")},fb61:function(t,e,n){"use strict";var i=n("7df2"),r=n.n(i);r.a},fcd1:function(t,e,n){"use strict";n.r(e),n.d(e,"setTabBarItem",(function(){return c})),n.d(e,"setTabBarStyle",(function(){return u})),n.d(e,"hideTabBar",(function(){return l})),n.d(e,"showTabBar",(function(){return h})),n.d(e,"hideTabBarRedDot",(function(){return f})),n.d(e,"showTabBarRedDot",(function(){return d})),n.d(e,"removeTabBarBadge",(function(){return p})),n.d(e,"setTabBarBadge",(function(){return g}));var i=n("f2b3"),r=["text","iconPath","selectedIconPath"],o=["color","selectedColor","backgroundColor","borderStyle"],a=["badge","redDot"];function s(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=getApp();if(n){var s=!1,c=getCurrentPages();if(c.length?c[c.length-1].$page.meta.isTabBar&&(s=!0):n.$children[0].hasTabBar&&(s=!0),!s)return{errMsg:"".concat(t,":fail not TabBar page")};var u=e.index,l=n.$children[0].tabBar;if(u>=__uniConfig.tabBar.list.length)return{errMsg:"".concat(t,":fail tabbar item not found")};switch(t){case"showTabBar":n.$children[0].hideTabBar=!1;break;case"hideTabBar":n.$children[0].hideTabBar=!0;break;case"setTabBarItem":Object(i["n"])(l.list[u],r,e);break;case"setTabBarStyle":Object(i["n"])(l,o,e);break;case"showTabBarRedDot":Object(i["n"])(l.list[u],a,{badge:"",redDot:!0});break;case"setTabBarBadge":Object(i["n"])(l.list[u],a,{badge:e.text,redDot:!0});break;case"hideTabBarRedDot":case"removeTabBarBadge":Object(i["n"])(l.list[u],a,{badge:"",redDot:!1});break}}return{}}function c(t){return s("setTabBarItem",t)}function u(t){return s("setTabBarStyle",t)}function l(t){return s("hideTabBar",t)}function h(t){return s("showTabBar",t)}function f(t){return s("hideTabBarRedDot",t)}function d(t){return s("showTabBarRedDot",t)}function p(t){return s("removeTabBarBadge",t)}function g(t){return s("setTabBarBadge",t)}},fda5:function(t,e,n){"use strict";(function(t){var i=n("bab8");e["a"]={name:"SystemChooseLocation",components:{SystemHeader:i["a"]},data:function(){return{src:"",data:null}},mounted:function(){var t=this,e=__uniConfig.qqMapKey;this.src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=".concat(e,"&referer=uniapp"),window.addEventListener("message",(function(e){var n=e.data;n&&"locationPicker"===n.module&&(t.data={name:n.poiname,address:n.poiaddress,latitude:n.latlng.lat,longitude:n.latlng.lng})}),!1)},methods:{_choose:function(){this.data&&(t.publishHandler("onChooseLocation",this.data),getApp().$router.back())},_back:function(){t.publishHandler("onChooseLocation",null),getApp().$router.back()}}}}).call(this,n("501c"))},ff28:function(t,e,n){"use strict";var i=n("2399"),r=n.n(i);r.a},ffdc:function(t,e,n){"use strict";function i(t,e,n,i){var r,o=document.createElement("script"),a=e.callback||"callback",s="__callback"+Date.now(),c=e.timeout||3e4;function u(){clearTimeout(r),delete window[s],o.remove()}window[s]=function(t){"function"===typeof n&&n(t),u()},o.onerror=function(){"function"===typeof i&&i(),u()},r=setTimeout((function(){"function"===typeof i&&i(),u()}),c),o.src=t+(t.indexOf("?")>=0?"&":"?")+a+"="+s,document.body.appendChild(o)}n.d(e,"a",(function(){return i}))}})}));