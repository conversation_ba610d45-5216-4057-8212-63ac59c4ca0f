# base64-arraybuffer

[![Build Status](https://travis-ci.org/niklasvh/base64-arraybuffer.png)](https://travis-ci.org/niklasvh/base64-arraybuffer)
[![NPM Downloads](https://img.shields.io/npm/dm/base64-arraybuffer.svg)](https://www.npmjs.org/package/base64-arraybuffer)
[![NPM Version](https://img.shields.io/npm/v/base64-arraybuffer.svg)](https://www.npmjs.org/package/base64-arraybuffer)

Encode/decode base64 data into ArrayBuffers

## Getting Started
Install the module with: `npm install base64-arraybuffer`

## API
The library encodes and decodes base64 to and from ArrayBuffers

 - __encode(buffer)__ - Encodes `<PERSON><PERSON>yBuffer` into base64 string
 - __decode(str)__ - Decodes base64 string to `<PERSON>rrayBuffer`

## License
Copyright (c) 2012 <PERSON><PERSON>
Licensed under the MIT license.
