{"name": "base64-arraybuffer", "description": "Encode/decode base64 data into ArrayBuffers", "version": "0.2.0", "homepage": "https://github.com/niklasvh/base64-arraybuffer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://hertzen.com"}, "repository": {"type": "git", "url": "https://github.com/niklasvh/base64-arraybuffer"}, "bugs": {"url": "https://github.com/niklasvh/base64-arraybuffer/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/niklasvh/base64-arraybuffer/blob/master/LICENSE-MIT"}], "main": "lib/base64-arraybuffer", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "mocha test"}, "devDependencies": {"mocha": "^6.1.4"}, "keywords": []}