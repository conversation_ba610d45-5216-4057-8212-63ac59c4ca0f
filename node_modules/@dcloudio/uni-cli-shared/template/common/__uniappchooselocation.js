!function(e){var t={};function A(a){if(t[a])return t[a].exports;var i=t[a]={i:a,l:!1,exports:{}};return e[a].call(i.exports,i,i.exports,A),i.l=!0,i.exports}A.m=e,A.c=t,A.d=function(e,t,a){A.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},A.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},A.t=function(e,t){if(1&t&&(e=A(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(A.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)A.d(a,i,function(t){return e[t]}.bind(null,i));return a},A.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return A.d(t,"a",t),t},A.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},A.p="",A(A.s=40)}([function(e,t){e.exports={}},function(e,t,A){"use strict";function a(e,t,A,a,i,n,o,s){var r,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=A,c._compiled=!0),a&&(c.functional=!0),n&&(c._scopeId="data-v-"+n),o?(r=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(o)},c._ssrRegister=r):i&&(r=s?function(){i.call(this,this.$root.$options.shadowRoot)}:i),r)if(c.functional){c._injectStyles=r;var l=c.render;c.render=function(e,t){return r.call(t),l(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,r):[r]}return{exports:e,options:c}}A.d(t,"a",function(){return a})},function(e,t,A){"use strict";var a;Object.defineProperty(t,"__esModule",{value:!0}),t.weexPlus=t.default=void 0,a="function"==typeof getUni?getUni:function(){var e=function(e){return"function"==typeof e},t=/^\$|^on|^create|Sync$|Manager$|^pause/,A=["os","getCurrentSubNVue","getSubNVueById","stopRecord","stopVoice","stopBackgroundAudio","stopPullDownRefresh","hideKeyboard","hideToast","hideLoading","showNavigationBarLoading","hideNavigationBarLoading","canIUse","navigateBack","closeSocket","pageScrollTo","drawCanvas"],a=function(e){return!(t.test(e)&&"createBLEConnection"!==e||~A.indexOf(e))},n=function(t){return function(){for(var A=arguments.length,a=Array(A>1?A-1:0),i=1;i<A;i++)a[i-1]=arguments[i];var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e(n.success)||e(n.fail)||e(n.complete)?t.apply(void 0,[n].concat(a)):new Promise(function(e,A){t.apply(void 0,[Object.assign({},n,{success:e,fail:A})].concat(a)),Promise.prototype.finally=function(e){var t=this.constructor;return this.then(function(A){return t.resolve(e()).then(function(){return A})},function(A){return t.resolve(e()).then(function(){throw A})})}}).then(function(e){return[null,e]}).catch(function(e){return[e]})}},o=[],s=void 0;function r(e){o.forEach(function(t){return t({origin:s,data:e})})}var c=i.webview.currentWebview().id,l=new BroadcastChannel("UNI-APP-SUBNVUE");function d(e){e.$processed=!0;var t=i.webview.currentWebview().id===e.id,A="uniNView"===e.__uniapp_origin_type&&e.__uniapp_origin_id,a=e.id;if(e.postMessage=function(e){A?l.postMessage({data:e,to:t?A:a}):m({type:"UniAppSubNVue",data:e})},e.onMessage=function(e){o.push(e)},e.__uniapp_mask_id){s=e.__uniapp_host;var n=e.__uniapp_mask,r=i.webview.getWebviewById(e.__uniapp_mask_id);r=r.parent()||r;var c=e.show,d=e.hide,u=e.close,g=function(){r.setStyle({mask:"none"})};e.show=function(){r.setStyle({mask:n});for(var t=arguments.length,A=Array(t),a=0;a<t;a++)A[a]=arguments[a];return c.apply(e,A)},e.hide=function(){g();for(var t=arguments.length,A=Array(t),a=0;a<t;a++)A[a]=arguments[a];return d.apply(e,A)},e.close=function(){g();for(var t=arguments.length,A=Array(t),a=0;a<t;a++)A[a]=arguments[a];return u.apply(e,A)}}}function u(e){var t=i.webview.getWebviewById(e);return t&&!t.$processed&&d(t),t}l.onmessage=function(e){e.data.to===c&&r(e.data.data)};var g=weex.requireModule("plus"),f=weex.requireModule("globalEvent"),p=0,h={},v="__uniapp__service";f.addEventListener("plusMessage",function(e){"UniAppJsApi"===e.data.type?w(e.data.id,e.data.data):"UniAppSubNVue"===e.data.type?r(e.data.data,e.data.options):"onNavigationBarButtonTap"===e.data.type?"function"==typeof B&&B(e.data.data):"onNavigationBarSearchInputChanged"===e.data.type?"function"==typeof _&&_(e.data.data):"onNavigationBarSearchInputConfirmed"===e.data.type?"function"==typeof S&&S(e.data.data):"onNavigationBarSearchInputClicked"===e.data.type&&"function"==typeof x&&x(e.data.data)});var w=function(e,t){var A=h[e];A?(A(t),A.keepAlive||delete h[e]):console.error("callback["+e+"] is undefined")},y=function(t){var A,a,i=t.id,n=t.type,o=t.params;h[i]=(a=function(t){e(A)?A(t):A&&(~t.errMsg.indexOf(":ok")?e(A.success)&&A.success(t):~t.errMsg.indexOf(":fail")&&e(A.fail)&&A.fail(t),e(A.complete)&&A.complete(t))},(e(A=o)||A&&e(A.callback))&&(a.keepAlive=!0),a),g.postMessage({id:i,type:n,params:o},v)};function m(e){g.postMessage(e,v)}var b=function(e){return function(t){y({id:p++,type:e,params:t})}},B=void 0,_=void 0,S=void 0,x=void 0;function C(e){B=e}function L(e){_=e}function I(e){S=e}function E(e){x=e}function k(e){return weex.requireModule(e)}var P=weex.requireModule("dom"),M=weex.requireModule("globalEvent"),j=[];function O(e){"function"==typeof e&&(this.isUniAppReady?e():j.push(e))}M.addEventListener("plusMessage",function(e){"UniAppReady"===e.data.type&&(O.isUniAppReady=!0,j.length&&(j.forEach(function(e){return e()}),j=[]))});var T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},N=weex.requireModule("stream"),Q="GET",D=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Q,A=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"application/x-www-form-urlencoded";return"object"===(void 0===e?"undefined":T(e))?"POST"===t.toUpperCase()&&"application/json"===A.toLowerCase()?JSON.stringify(e):Object.keys(e).map(function(t){return encodeURIComponent(t)+"="+encodeURIComponent(e[t])}).join("&"):e},G=weex.requireModule("plusstorage"),U="__TYPE",R=weex.requireModule("clipboard"),V=function(){if("function"==typeof getUniEmitter)return getUniEmitter;var e={$on:function(){console.warn("uni.$on failed")},$off:function(){console.warn("uni.$off failed")},$once:function(){console.warn("uni.$once failed")},$emit:function(){console.warn("uni.$emit failed")}};return function(){return e}}();function F(e,t,A){return e[t].apply(e,A)}var Y=Object.freeze({loadFontFace:function(t){var A=t.family,a=t.source,i=(t.desc,t.success),n=(t.fail,t.complete);P.addRule("fontFace",{fontFamily:A,src:a.replace(/"/g,"'")});var o={errMsg:"loadFontFace:ok",status:"loaded"};e(i)&&i(o),e(n)&&n(o)},ready:O,request:function(t){var A=t.url,a=t.data,i=t.header,n=t.method,o=void 0===n?"GET":n,s=t.dataType,r=void 0===s?"json":s,c=(t.responseType,t.success),l=t.fail,d=t.complete,u=!1,g=!1,f={};if(i)for(var p in i)g||"content-type"!==p.toLowerCase()?f[p]=i[p]:(g=!0,f["Content-Type"]=i[p]);return o===Q&&a&&(A=A+(~A.indexOf("?")?"&"===A.substr(-1)||"?"===A.substr(-1)?"":"&":"?")+D(a)),N.fetch({url:A,method:o,headers:f,type:"json"===r?"json":"text",body:o!==Q?D(a,o,f["Content-Type"]):""},function(t){var A=t.status,a=(t.ok,t.statusText,t.data),i=t.headers,n={};!A||-1===A||u?(n.errMsg="request:fail",e(l)&&l(n)):(n.data=a,n.statusCode=A,n.header=i,e(c)&&c(n)),e(d)&&d(n)}),{abort:function(){u=!0}}},getStorage:function(t){var A=t.key,a=(t.data,t.success),i=t.fail,n=t.complete;G.getItem(A+U,function(t){if("success"===t.result){var o=t.data;G.getItem(A,function(t){if("success"===t.result){var A=t.data;o&&A?("String"!==o&&(A=JSON.parse(A)),e(a)&&a({errMsg:"getStorage:ok",data:A})):(t.errMsg="setStorage:fail",e(i)&&i(t))}else t.errMsg="setStorage:fail",e(i)&&i(t);e(n)&&n(t)})}else t.errMsg="setStorage:fail",e(i)&&i(t),e(n)&&n(t)})},setStorage:function(t){var A=t.key,a=t.data,i=t.success,n=t.fail,o=t.complete,s="String";"object"===(void 0===a?"undefined":T(a))&&(s="Object",a=JSON.stringify(a)),G.setItem(A,a,function(t){"success"===t.result?G.setItem(A+U,s,function(t){"success"===t.result?e(i)&&i({errMsg:"setStorage:ok"}):(t.errMsg="setStorage:fail",e(n)&&n(t))}):(t.errMsg="setStorage:fail",e(n)&&n(t)),e(o)&&o(t)})},removeStorage:function(t){var A=t.key,a=(t.data,t.success),i=t.fail,n=t.complete;G.removeItem(A,function(t){"success"===t.result?e(a)&&a({errMsg:"removeStorage:ok"}):(t.errMsg="removeStorage:fail",e(i)&&i(t)),e(n)&&n(t)}),G.removeItem(A+U)},clearStorage:function(e){e.key,e.data,e.success,e.fail,e.complete},getClipboardData:function(t){var A=t.success,a=(t.fail,t.complete);R.getString(function(t){var i={errMsg:"getClipboardData:ok",data:t.data};e(A)&&A(i),e(a)&&a(i)})},setClipboardData:function(t){var A=t.data,a=t.success,i=(t.fail,t.complete),n={errMsg:"setClipboardData:ok"};R.setString(A),e(a)&&a(n),e(i)&&i(n)},onSubNVueMessage:r,getSubNVueById:u,getCurrentSubNVue:function(){return u(i.webview.currentWebview().id)},$on:function(){return F(V(),"$on",[].concat(Array.prototype.slice.call(arguments)))},$off:function(){return F(V(),"$off",[].concat(Array.prototype.slice.call(arguments)))},$once:function(){return F(V(),"$once",[].concat(Array.prototype.slice.call(arguments)))},$emit:function(){return F(V(),"$emit",[].concat(Array.prototype.slice.call(arguments)))}}),H={os:{nvue:!0}},Z={};return"undefined"!=typeof Proxy?Z=new Proxy({},{get:function(e,t){if("os"===t)return{nvue:!0};if("postMessage"===t)return m;if("requireNativePlugin"===t)return k;if("onNavigationBarButtonTap"===t)return C;if("onNavigationBarSearchInputChanged"===t)return L;if("onNavigationBarSearchInputConfirmed"===t)return I;if("onNavigationBarSearchInputClicked"===t)return E;var A=Y[t];return A||(A=b(t)),a(t)?n(A):A}}):(Object.keys(H).forEach(function(e){Z[e]=H[e]}),Z.postMessage=m,Z.requireNativePlugin=k,Z.onNavigationBarButtonTap=C,Z.onNavigationBarSearchInputChanged=L,Z.onNavigationBarSearchInputConfirmed=I,Z.onNavigationBarSearchInputClicked=E,Object.keys({uploadFile:!0,downloadFile:!0,chooseImage:!0,previewImage:!0,getImageInfo:!0,saveImageToPhotosAlbum:!0,chooseVideo:!0,saveVideoToPhotosAlbum:!0,saveFile:!0,getSavedFileList:!0,getSavedFileInfo:!0,removeSavedFile:!0,openDocument:!0,setStorage:!0,getStorage:!0,getStorageInfo:!0,removeStorage:!0,clearStorage:!0,getLocation:!0,chooseLocation:!0,openLocation:!0,getSystemInfo:!0,getNetworkType:!0,makePhoneCall:!0,scanCode:!0,setScreenBrightness:!0,getScreenBrightness:!0,setKeepScreenOn:!0,vibrateLong:!0,vibrateShort:!0,addPhoneContact:!0,showToast:!0,showLoading:!0,hideToast:!0,hideLoading:!0,showModal:!0,showActionSheet:!0,setNavigationBarTitle:!0,setNavigationBarColor:!0,navigateTo:!0,redirectTo:!0,reLaunch:!0,switchTab:!0,navigateBack:!0,getProvider:!0,login:!0,getUserInfo:!0,share:!0,requestPayment:!0,subscribePush:!0,unsubscribePush:!0,onPush:!0,offPush:!0}).forEach(function(e){var t=Y[e];t||(t=b(e)),a(e)?Z[e]=n(t):Z[e]=t})),Z};var i=new WeexPlus(weex);t.weexPlus=i;var n=a(weex,i,BroadcastChannel);t.default=n},function(e,t,A){Vue.prototype.__$appStyle__={},Vue.prototype.__merge_style&&Vue.prototype.__merge_style(A(4).default,Vue.prototype.__$appStyle__)},function(e,t,A){"use strict";A.r(t);var a=A(0),i=A.n(a);for(var n in a)"default"!==n&&function(e){A.d(t,e,function(){return a[e]})}(n);t.default=i.a},function(e,t,A){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var A={onLoad:function(){var t=this,A=e.webview.currentWebview().extras||{},a=A.from,i=(A.callback,A.runtime),n=A.data,o=void 0===n?{}:n,s=A.useGlobalEvent;this.__from=a,this.__runtime=i,this.__page=e.webview.currentWebview().id,this.__useGlobalEvent=s,this.data=JSON.parse(JSON.stringify(o)),e.key.addEventListener("backbutton",function(){"function"==typeof t.onClose?t.onClose():e.webview.currentWebview().close("auto")});var r=this,c=function(e){var t=e.data&&e.data.__message;t&&r.__onMessageCallback&&r.__onMessageCallback(t.data)};this.__useGlobalEvent?weex.requireModule("globalEvent").addEventListener("plusMessage",c):new BroadcastChannel(this.__page).onmessage=c},methods:{postMessage:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},A=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a={__message:{__page:this.__page,data:t,keep:A}},i=this.__from;if("v8"===this.__runtime){if(this.__useGlobalEvent)e.webview.postMessageToUniNView(a,i);else new BroadcastChannel(i).postMessage(a)}else{var n=e.webview.getWebviewById(i);n&&n.evalJS("__plusMessage&&__plusMessage(".concat(JSON.stringify({data:a}),")"))}},onMessage:function(e){this.__onMessageCallback=e}}};t.default=A}).call(this,A(2).weexPlus)},function(e,t,A){"use strict";var a=A(29),i=A(11),n=A(1);var o=Object(n.a)(i.default,a.a,a.b,!1,null,null,"8d0eedfe");(function(){this.options.style||(this.options.style={}),Vue.prototype.__merge_style&&Vue.prototype.__$appStyle__&&Vue.prototype.__merge_style(Vue.prototype.__$appStyle__,this.options.style),Vue.prototype.__merge_style?Vue.prototype.__merge_style(A(35).default,this.options.style):Object.assign(this.options.style,A(35).default)}).call(o),t.default=o.exports},,,,,function(e,t,A){"use strict";var a=A(12),i=A.n(a);t.default=i.a},function(e,t,A){"use strict";(function(e,a){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i,n=(i=A(5))&&i.__esModule?i:{default:i};weex.requireModule("dom").addRule("fontFace",{fontFamily:"unichooselocation",src:"url('data:font/truetype;charset=utf-8;base64,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')"});var o={en:{ok:"OK",cancel:"Cancel",search_tips:"Search for a place",no_found:"No results found"},cn:{ok:"完成",cancel:"取消",search_tips:"搜索地点",no_found:"对不起，没有搜索到相关数据"}},s=weex.requireModule("mapSearch"),r={mixins:[n.default],data:function(){return{resource:{},userKeyword:"",showLocation:!0,latitude:39.908692,longitude:116.397477,nearList:[],nearSelectedIndex:-1,nearLoading:!1,nearLoadingEnd:!1,noNearData:!1,isUserLocation:!1,statusBarHeight:20,mapHeight:250,markers:[{id:"location",latitude:39.908692,longitude:116.397477,zIndex:"1",iconPath:"data:image/png;base64,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",width:26,height:36}],showSearch:!1,searchList:[],searchSelectedIndex:-1,searchLoading:!1,searchEnd:!1,noSearchData:!1}},computed:{disableOK:function(){return this.nearSelectedIndex<0&&this.searchSelectedIndex<0}},onLoad:function(){this.resource=e.os.language.toLowerCase().indexOf("zh")>=0?o.cn:o.en,this.statusBarHeight=e.navigator.getStatusbarHeight(),this.mapHeight=e.screen.resolutionHeight/2;var t=this.data;this.userKeyword=t.keyword||"",this._searchInputTimer=null,this._searchPageIndex=1,this._searchKeyword="",this._nearPageIndex=1,this._hasUserLocation=!1,this._userLatitude=0,this._userLongitude=0},onReady:function(){this.mapContext=this.$refs.map1,this.data.latitude&&this.data.longitude?(this._hasUserLocation=!0,this.moveToCenter({latitude:this.data.latitude,longitude:this.data.longitude})):this.getUserLocation()},onUnload:function(){this.clearSearchTimer()},methods:{cancelClick:function(){this.postMessage({event:"cancel"})},doneClick:function(){if(!this.disableOK){var e=this.showSearch&&this.searchSelectedIndex>=0?this.searchList[this.searchSelectedIndex]:this.nearList[this.nearSelectedIndex],t={name:e.name,address:e.address,latitude:e.location.latitude,longitude:e.location.longitude};this.postMessage({event:"selected",detail:t})}},getUserLocation:function(){var t=this;e.geolocation.getCurrentPosition(function(e){var A=e.coordsType,a=e.coords;"wgs84"===A.toLowerCase()?t.wgs84togcjo2(a,function(e){t.getUserLocationSuccess(e)}):t.getUserLocationSuccess(a)},function(e){t._hasUserLocation=!0,console.log(a("Gelocation Error: code - "+e.code+"; message - "+e.message," at template\\__uniappchooselocation.nvue:235"))},{geocode:!1})},getUserLocationSuccess:function(e){this._userLatitude=e.latitude,this._userLongitude=e.longitude,this._hasUserLocation=!0,this.moveToCenter({latitude:e.latitude,longitude:e.longitude})},searchclick:function(t){this.showSearch=t,!1===t&&e.key.hideSoftKeybord()},showSearchView:function(){this.searchList=[],this.showSearch=!0},hideSearchView:function(){this.showSearch=!1,e.key.hideSoftKeybord(),this.noSearchData=!1,this.searchSelectedIndex=-1},onregionchange:function(e){var t=this,A=e.detail,a=A.type||e.type;"drag"===(A.causedBy||e.causedBy)&&"end"===a&&this.mapContext.getCenterLocation(function(e){t.moveToCenter({latitude:e.latitude,longitude:e.longitude})})},onItemClick:function(e,t){t.stopPropagation(),this.nearSelectedIndex!==e&&(this.nearSelectedIndex=e),this.updateMarker(this.nearList[e].location)},moveToCenter:function(e){this.latitude===e.latitude&&this.longitude===e.longitude||(this.latitude=e.latitude,this.longitude=e.longitude,this.updateCenter(e),this.isUserLocation=this._userLatitude===e.latitude&&this._userLongitude===e.longitude)},updateCenter:function(e){this.nearSelectedIndex=-1,this.nearList=[],this.updateMarker(e),this._hasUserLocation&&(this._nearPageIndex=1,this.nearLoadingEnd=!1,this.reverseGeocode(e),this.searchNearByPoint(e))},searchNear:function(){this.nearLoadingEnd||this.searchNearByPoint({latitude:this.latitude,longitude:this.longitude})},searchNearByPoint:function(e){var t=this;this.noNearData=!1,this.nearLoading=!0,s.poiSearchNearBy({point:{latitude:e.latitude,longitude:e.longitude},key:this.userKeyword,index:this._nearPageIndex,radius:1e3},function(e){t.nearLoading=!1,t._nearPageIndex=e.pageIndex+1,t.nearLoadingEnd=e.pageIndex===e.pageNumber,e.poiList&&e.poiList.length?(t.replaceEscape(e.poiList),t.nearList=t.nearList.concat(e.poiList)):t.noNearData=0===t.nearList.length})},updateMarker:function(e){this.mapContext.translateMarker({markerId:"location",destination:{latitude:e.latitude,longitude:e.longitude},duration:0},function(e){})},reverseGeocode:function(e){var t=this;s.reverseGeocode({point:e},function(A){"success"===A.type&&t._nearPageIndex<=2&&t.nearList.splice(0,0,{code:A.code,location:e,name:"地图位置",address:A.address||""})})},onsearchinput:function(e){var t=this,A=e.detail.value.replace(/^\s+|\s+$/g,"");this.clearSearchTimer(),this._searchInputTimer=setTimeout(function(){clearTimeout(t._searchInputTimer),t._searchPageIndex=1,t.searchEnd=!1,t._searchKeyword=A,t.searchList=[],t.search()},300)},clearSearchTimer:function(){this._searchInputTimer&&clearTimeout(this._searchInputTimer)},search:function(){var e=this;0===this._searchKeyword.length||this._searchEnd||this.searchLoading||(this.searchLoading=!0,this.noSearchData=!1,s.poiSearchNearBy({point:{latitude:this.latitude,longitude:this.longitude},key:this._searchKeyword,index:this._searchPageIndex,radius:5e4},function(t){e.searchLoading=!1,e._searchPageIndex=t.pageIndex+1,e.searchEnd=t.pageIndex===t.pageNumber,t.poiList&&t.poiList.length?(e.replaceEscape(t.poiList),e.searchList=e.searchList.concat(t.poiList)):e.noSearchData=0===e.searchList.length}))},onSearchListTouchStart:function(){e.key.hideSoftKeybord()},onSearchItemClick:function(e,t){t.stopPropagation(),this.searchSelectedIndex!==e&&(this.searchSelectedIndex=e)},replaceEscape:function(e){for(var t=0;t<e.length;t++){var A=e[t];A.name=A.name.replace(/\\/g,""),A.address=A.address.replace(/\\/g,"")}},wgs84togcjo2:function(e,t){var A=weex.requireModule("stream"),a="https://apis.map.qq.com/jsapi?qt=translate&type=1&points=".concat(e.longitude,",").concat(e.latitude,"&key=MAP_KEY&output=json&pf=jsapi&ref=jsapi");A.fetch({method:"GET",url:a,type:"json"},function(e){if(e.ok){var A=e.data.detail.points[0];t({latitude:A.lat,longitude:A.lng})}})}}};t.default=r}).call(this,A(2).weexPlus,A(28).default)},function(e,t){e.exports={"unichooselocation-icons":{fontFamily:"unichooselocation",textDecoration:"none",textAlign:"center"},page:{flex:1,position:"relative"},"flex-r":{flexDirection:"row",flexWrap:"nowrap"},"flex-c":{flexDirection:"column",flexWrap:"nowrap"},"flex-fill":{flex:1},"a-i-c":{alignItems:"center"},"j-c-c":{justifyContent:"center"},"nav-cover":{position:"absolute",left:0,top:0,right:0,height:"100",backgroundImage:"linear-gradient(to bottom, rgba(0, 0, 0, .3), rgba(0, 0, 0, 0))"},statusbar:{height:"22"},"title-view":{paddingTop:"5",paddingRight:"15",paddingBottom:"5",paddingLeft:"15"},"btn-cancel":{paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0},"btn-cancel-text":{fontSize:"30",color:"#ffffff"},"btn-done":{backgroundColor:"#007AFF",borderRadius:"3",paddingTop:"5",paddingRight:"12",paddingBottom:"5",paddingLeft:"12"},"btn-done-disabled":{backgroundColor:"#62abfb"},"text-done":{color:"#ffffff",fontSize:"15",fontWeight:"bold",lineHeight:"15",height:"15"},"text-done-disabled":{color:"#c0ddfe"},"map-view":{flex:2,position:"relative"},map:{width:"750rpx"},"map-location":{position:"absolute",right:"20",bottom:"25",width:"44",height:"44",backgroundColor:"#ffffff",borderRadius:"40",boxShadow:"0 2px 4px rgba(100, 100, 100, 0.2)"},"map-location-text":{fontSize:"20"},"map-location-text-active":{color:"#007AFF"},"result-area":{flex:2,position:"relative"},"search-bar":{paddingTop:"12",paddingRight:"15",paddingBottom:"12",paddingLeft:"15",backgroundColor:"#ffffff"},"search-area":{backgroundColor:"#ebebeb",borderRadius:"5",height:"30",paddingLeft:"8"},"search-text":{fontSize:"14",lineHeight:"16",color:"#b4b4b4"},"search-icon":{fontSize:"16",color:"#b4b4b4",marginRight:"4"},"no-data":{color:"#808080"},"no-data-search":{marginTop:"50"},"list-item":{position:"relative",paddingTop:"12",paddingRight:"15",paddingBottom:"12",paddingLeft:"15"},"list-line":{position:"absolute",left:"15",right:0,bottom:0,height:".5",backgroundColor:"#d3d3d3"},"list-name":{fontSize:"14",lines:1,textOverflow:"ellipsis"},"list-address":{fontSize:"12",color:"#808080",lines:1,textOverflow:"ellipsis",marginTop:"5"},"list-icon-area":{paddingLeft:"10",paddingRight:"10"},"list-selected-icon":{fontSize:"20",color:"#007AFF"},"search-view":{position:"absolute",left:0,top:0,right:0,bottom:0,backgroundColor:"#f6f6f6"},"searching-area":{flex:5},"search-input":{fontSize:"14",height:"30",paddingLeft:"6"},"search-cancel":{color:"#0079FF",marginLeft:"10"},"loading-view":{paddingTop:"15",paddingRight:"15",paddingBottom:"15",paddingLeft:"15"},"loading-icon":{width:"28",height:"28",color:"#808080"}}},,,,,,,,,,,,,,,function(e,t,A){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){for(var e=arguments.length,t=new Array(e),A=0;A<e;A++)t[A]=arguments[A];var a=t.map(function(e){var t=Object.prototype.toString.call(e);if("[object object]"===t.toLowerCase())try{e="---BEGIN:JSON---"+JSON.stringify(e)+"---END:JSON---"}catch(t){e="[object object]"}else if(null===e)e="---NULL---";else if(void 0===e)e="---UNDEFINED---";else{var A=function(e){var t=Object.prototype.toString.call(e);return t.substring(8,t.length-1)}(e).toUpperCase();e="NUMBER"===A||"BOOLEAN"===A?"---BEGIN:"+A+"---"+e+"---END:"+A+"---":String(e)}return e}),i="";if(a.length>1){var n=a.pop();i=a.join("---COMMA---"),0===n.indexOf(" at ")?i+=n:i+="---COMMA---"+n}else i=a[0];return i}},function(e,t,A){"use strict";var a=function(){var e=this,t=e.$createElement,A=e._self._c||t;return A("scroll-view",{staticStyle:{flexDirection:"column"},attrs:{scrollY:!0,enableBackToTop:!0,bubble:"true"}},[A("view",{staticClass:["page","flex-c"]},[A("view",{staticClass:["flex-r","map-view"]},[A("map",{ref:"map1",staticClass:["map","flex-fill"],style:"height:"+e.mapHeight+"px",attrs:{showLocation:e.showLocation,longitude:e.longitude,latitude:e.latitude,markers:e.markers},on:{regionchange:e.onregionchange}}),A("view",{staticClass:["map-location","flex-c","a-i-c","j-c-c"],on:{click:function(t){e.getUserLocation()}}},[A("u-text",{staticClass:["unichooselocation-icons","map-location-text"],class:{"map-location-text-active":e.isUserLocation}},[e._v("")])]),A("view",{staticClass:["nav-cover"]},[A("view",{staticClass:["statusbar"],style:"height:"+e.statusBarHeight+"px"}),A("view",{staticClass:["title-view","flex-r"]},[A("view",{staticClass:["btn-cancel"],on:{click:e.cancelClick}},[A("u-text",{staticClass:["unichooselocation-icons","btn-cancel-text"]},[e._v("")])]),A("view",{staticClass:["flex-fill"]}),A("view",{staticClass:["btn-done","flex-r","a-i-c","j-c-c"],class:{"btn-done-disabled":e.disableOK},on:{click:e.doneClick}},[A("u-text",{staticClass:["text-done"],class:{"text-done-disabled":e.disableOK}},[e._v(e._s(e.resource.ok))])])])])],1),A("view",{staticClass:["flex-c","result-area"],class:{"searching-area":e.showSearch}},[A("view",{staticClass:["search-bar"]},[A("view",{staticClass:["search-area","flex-r","a-i-c"],on:{click:e.showSearchView}},[A("u-text",{staticClass:["search-icon","unichooselocation-icons"]},[e._v("")]),A("u-text",{staticClass:["search-text"]},[e._v(e._s(e.resource.search_tips))])])]),e.noNearData?e._e():A("list",{staticClass:["flex-fill","list-view"],attrs:{loadmoreoffset:"5",scrollY:!0},on:{loadmore:function(t){e.searchNear()}}},[e._l(e.nearList,function(t,a){return A("cell",{key:t.uid,appendAsTree:!0,attrs:{append:"tree"}},[A("view",{staticClass:["list-item"],on:{click:function(t){e.onItemClick(a,t)}}},[A("view",{staticClass:["flex-r"]},[A("view",{staticClass:["list-text-area","flex-fill","flex-c"]},[A("u-text",{staticClass:["list-name"]},[e._v(e._s(t.name))]),t.distance?A("u-text",{staticClass:["list-address"]},[e._v(e._s(t.distance<100?"100m 内":t.distance+"m")+" | "+e._s(t.address))]):e._e(),t.distance?e._e():A("u-text",{staticClass:["list-address"]},[e._v(e._s(t.address))])]),a===e.nearSelectedIndex?A("view",{staticClass:["list-icon-area","flex-r","a-i-c","j-c-c"]},[A("u-text",{staticClass:["unichooselocation-icons","list-selected-icon"]},[e._v("")])]):e._e()]),A("view",{staticClass:["list-line"]})])])}),e.nearLoading?A("cell",{appendAsTree:!0,attrs:{append:"tree"}},[A("view",{staticClass:["loading-view","flex-c","a-i-c","j-c-c"]},[A("loading-indicator",{staticClass:["loading-icon"],attrs:{animating:!0,arrow:"false"}})])]):e._e()],2),e.noNearData?A("view",{staticClass:["flex-fill","flex-r","a-i-c","j-c-c"]},[A("u-text",{staticClass:["no-data"]},[e._v(e._s(e.resource.no_found))])]):e._e(),e.showSearch?A("view",{staticClass:["search-view","flex-c"]},[A("view",{staticClass:["search-bar","flex-r","a-i-c"]},[A("view",{staticClass:["search-area","flex-fill","flex-r"]},[A("u-input",{staticClass:["search-input","flex-fill"],attrs:{focus:!0,placeholder:e.resource.search_tips},on:{input:e.onsearchinput}})],1),A("u-text",{staticClass:["search-cancel"],on:{click:e.hideSearchView}},[e._v(e._s(e.resource.cancel))])]),e.noSearchData?e._e():A("list",{staticClass:["flex-fill","list-view"],attrs:{enableBackToTop:!0,scrollY:!0},on:{loadmore:function(t){e.search()},touchstart:e.onSearchListTouchStart}},[e._l(e.searchList,function(t,a){return A("cell",{key:t.uid,appendAsTree:!0,attrs:{append:"tree"}},[A("view",{staticClass:["list-item"],on:{click:function(t){e.onSearchItemClick(a,t)}}},[A("view",{staticClass:["flex-r"]},[A("view",{staticClass:["list-text-area","flex-fill","flex-c"]},[A("u-text",{staticClass:["list-name"]},[e._v(e._s(t.name))]),t.distance?A("u-text",{staticClass:["list-address"]},[e._v(e._s(t.distance<100?"100m 内":t.distance+"m")+" | "+e._s(t.address))]):e._e(),t.distance?e._e():A("u-text",{staticClass:["list-address"]},[e._v(e._s(t.address))])]),a===e.searchSelectedIndex?A("view",{staticClass:["list-icon-area","flex-r","a-i-c","j-c-c"]},[A("u-text",{staticClass:["unichooselocation-icons","list-selected-icon"]},[e._v("")])]):e._e()]),A("view",{staticClass:["list-line"]})])])}),e.searchLoading?A("cell",{appendAsTree:!0,attrs:{append:"tree"}},[A("view",{staticClass:["loading-view","flex-c","a-i-c","j-c-c"]},[A("loading-indicator",{staticClass:["loading-icon"],attrs:{animating:!0}})])]):e._e()],2),e.noSearchData?A("view",{staticClass:["flex-fill","flex-r","j-c-c"]},[A("u-text",{staticClass:["no-data","no-data-search"]},[e._v(e._s(e.resource.no_found))])]):e._e()]):e._e()])])])},i=[];A.d(t,"a",function(){return a}),A.d(t,"b",function(){return i})},,,,,,function(e,t,A){"use strict";A.r(t);var a=A(13),i=A.n(a);for(var n in a)"default"!==n&&function(e){A.d(t,e,function(){return a[e]})}(n);t.default=i.a},,,,,function(e,t,A){"use strict";A.r(t);A(3);var a=A(6);a.default.mpType="page",a.default.route="template/__uniappchooselocation",a.default.el="#root",new Vue(a.default)}]);