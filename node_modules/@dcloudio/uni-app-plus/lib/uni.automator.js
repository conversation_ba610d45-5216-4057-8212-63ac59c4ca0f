"use strict";function t(t){return t&&"object"==typeof t&&"default"in t?t.default:t}var e=t(require("fs")),s=t(require("debug")),i=t(require("postcss-selector-parser")),a=t(require("fs-extra")),r=t(require("licia/dateFormat")),n=require("path"),o=require("util");function c(t){t.walk(t=>{if("tag"===t.type){const e=t.value;t.value="page"===e?"body":"uni-"+e}})}const l=["Page.getElement","Page.getElements","Element.getElement","Element.getElements"];const h=s("automator:launcher"),u=o.promisify(e.readdir),p=o.promisify(e.stat);class d{constructor(t){this.id=t.id,this.app=t.executablePath,this.appid=t.appid||"HBuilder",this.package=t.package||"io.dcloud.HBuilder"}shouldPush(){return this.exists(this.FILE_APP_SERVICE).then(()=>(h(`${r("yyyy-mm-dd HH:MM:ss:l")} ${this.FILE_APP_SERVICE} exists`),!1)).catch(()=>(h(`${r("yyyy-mm-dd HH:MM:ss:l")} ${this.FILE_APP_SERVICE} not exists`),!0))}push(t){return async function t(e){const s=await u(e);return(await Promise.all(s.map(async s=>{const i=n.resolve(e,s);return(await p(i)).isDirectory()?t(i):i}))).reduce((t,e)=>t.concat(e),[])}(t).then(e=>{const s=e.map(e=>{const s=n.join(this.DIR_WWW,n.relative(t,e));return h(`${r("yyyy-mm-dd HH:MM:ss:l")} push ${e} ${s}`),this.pushFile(e,s)});return Promise.all(s)}).then(t=>!0)}get FILE_APP_SERVICE(){return this.DIR_WWW+"/app-service.js"}}const m=s("automator:simctl");class y extends d{async init(){const t=require("node-simctl").Simctl;this.tool=new t({udid:this.id});try{await this.tool.bootDevice()}catch(t){}await this.initSDCard(),m(`${r("yyyy-mm-dd HH:MM:ss:l")} init ${this.id}`)}async initSDCard(){const t=(await this.tool.appInfo(this.package)).match(/DataContainer\s+=\s+"(.*)"/);if(!t)return Promise.resolve("");this.sdcard=t[1].replace("file:",""),m(`${r("yyyy-mm-dd HH:MM:ss:l")} install ${this.sdcard}`)}async version(){return Promise.resolve(this.sdcard?"9.6.96":"")}async install(){return m(`${r("yyyy-mm-dd HH:MM:ss:l")} install ${this.app}`),await this.tool.installApp(this.app),await this.initSDCard(),Promise.resolve(!0)}async start(){try{await this.tool.terminateApp(this.package),await this.tool.launchApp(this.package)}catch(t){}return Promise.resolve(!0)}async exit(){return await this.tool.terminateApp(this.package),await this.tool.shutdownDevice(),Promise.resolve(!0)}async captureScreenshot(){return Promise.resolve(await this.tool.getScreenshot())}exists(t){return a.existsSync(t)?Promise.resolve(!0):Promise.reject(Error(t+" not exists"))}pushFile(t,e){return Promise.resolve(a.copySync(t,e))}get DIR_WWW(){return`${this.sdcard}/Documents/Pandora/apps/${this.appid}/www/`}}const f=require("adbkit"),w=s("automator:adb");class M extends d{async init(){if(this.tool=f.createClient(),!this.id){const t=await this.tool.listDevices();if(!t.length)throw Error("Device not found");this.id=t[0].id}this.sdcard=(await this.shell(this.COMMAND_EXTERNAL)).trim(),w(`${r("yyyy-mm-dd HH:MM:ss:l")} init ${this.id} ${this.sdcard}`)}version(){return this.shell(this.COMMAND_VERSION).then(t=>{const e=t.match(/versionName=(.*)/);return e&&e.length>1?e[1]:""})}install(){return w(`${r("yyyy-mm-dd HH:MM:ss:l")} install ${this.app}`),this.tool.install(this.id,this.app).then(()=>this.init())}start(){return this.exit().then(()=>this.shell(this.COMMAND_START))}exit(){return this.shell(this.COMMAND_STOP)}captureScreenshot(){return this.tool.screencap(this.id).then(t=>new Promise(e=>{const s=[];t.on("data",(function(t){s.push(t)})),t.on("end",(function(){e(Buffer.concat(s).toString("base64"))}))}))}exists(t){return this.tool.stat(this.id,t).then(()=>!1).catch(()=>!0)}pushFile(t,e){return this.tool.push(this.id,t,e)}shell(t){return w(`${r("yyyy-mm-dd HH:MM:ss:l")} SEND ► ${t}`),this.tool.shell(this.id,t).then(f.util.readAll).then(t=>{const e=t.toString();return w(`${r("yyyy-mm-dd HH:MM:ss:l")} ◀ RECV ${e}`),e})}get DIR_WWW(){return`${this.sdcard}/Android/data/${this.package}/apps/${this.appid}/www`}get COMMAND_EXTERNAL(){return"echo $EXTERNAL_STORAGE"}get COMMAND_VERSION(){return"dumpsys package "+this.package}get COMMAND_STOP(){return"am force-stop "+this.package}get COMMAND_START(){return`am start -n ${this.package}/io.dcloud.PandoraEntry --es ${this.appid} --ez needUpdateApp false --ez reload true`}}const E=s("automator:devtool");let g,P=!1;const A={"Tool.close":{reflect:async()=>{}},"App.exit":{reflect:async()=>g.exit()},"App.enableLog":{reflect:()=>Promise.resolve()},"App.captureScreenshot":{reflect:async(t,e)=>{const s=await g.captureScreenshot(e);return E("App.captureScreenshot "+s.length),{data:s}}}};!function(t){l.forEach(e=>{t[e]=function(t){return{reflect:async(e,s)=>e(t,s,!1),params:t=>(t.selector&&(t.selector=i(c).processSync(t.selector)),t)}}(e)})}(A);const $={devtools:{name:"App",paths:[],required:["manifest.json","app-service.js"],validate:async function(t,s){if(t.platform=(t.platform||process.env.UNI_OS_NAME).toLocaleLowerCase(),Object.assign(t,t[t.platform]),g=function(t,e){return"ios"===t?new y(e):new M(e)}(t.platform,t),await g.init(),!await g.version()){if(!t.executablePath)throw Error(`app-plus->${t.platform}->executablePath is not provided`);if(!e.existsSync(t.executablePath))throw Error(t.executablePath+" not exists");P=!0}return t},create:async function(t,e,s){P&&await g.install(),(s.compiled||await g.shouldPush())&&await g.push(t),await g.start()}},adapter:A};module.exports=$;
