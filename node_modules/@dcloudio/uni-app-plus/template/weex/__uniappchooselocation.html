<!DOCTYPE html>
<html lang="zh-CN">

    <head>
        <meta charset="UTF-8" />
        <script>
            var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
            document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')
        </script>
        <title></title>
        <style>
            html,
            body {
                margin: 0;
                padding: 0;
                position: absolute;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                background: #ffffff;
            }

            * {
                margin: 0;
                padding: 0;
            }

            button,
            input,
            input[type=button],
            input[type=reset],
            input[type=search],
            input[type=submit],
            select {
                outline: none;
                -webkit-tap-highlight-color: transparent;
                -webkit-appearance: none;
                font-family: inherit;
                border: none;
            }

            .hide {
                display: none;
            }

            .search-wrap {
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                padding: 8px 60px 8px 15px;
                background-color: #fff;
                border-bottom: 1px solid #d3d3d3;
                z-index: 100;
            }

            .search-wrap.init-status {
                padding-right: 15px;
            }

            .search-wrap .search-bar {
                display: block;
                height: 30px;
                background-color: #ebebeb;
                padding-left: 10px;
                padding-right: 10px;
                color: #b4b4b4;
                font-size: 15px;
                line-height: 31px;
                border-radius: 5px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .search-wrap .cancel,
            .search-wrap .submit {
                position: absolute;
                top: 0;
                right: 0;
                width: 55px;
                height: 46px;
                line-height: 46px;
                text-align: center;
                font-size: 16px;
                color: #0079ff;
                background-color: #fff;
            }

            .search-wrap .clear-input {
                top: 11px;
                right: 60px;
                width: 34px;
                height: 26px;
                line-height: 26px;
                background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAPFBMVEUAAACZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZlr8OySAAAAE3RSTlMANO/H574Jw5g/soF2LxKbQCgmvcJ4rgAAAKFJREFUOMu9k1sSgyAMRcNLwbfe/e+1HZLa0DKTP8+PztwTDRDoWa4lRSCm5erGZcTNWP7iIaMhD22+BfwQtqae89bQ39jRYVf9ocu3U+5/cp4D7yZeCwkrKo7Y8O8XVFYRZilkgx+VWYQEZagcSYQIZagcUQRAGZwLpmD+wmzSXKa5UdZWW4d10E1Gh2wPjD1y9tBqSlDlB/U4PxfnpEd5AeZAIGebfob2AAAAAElFTkSuQmCC) no-repeat center center;
                background-color: #ededed;
                background-size: 18px 18px;
                position: absolute;
                padding: 0;
                border: 0;
                font-size: 14px;
                border-radius: 4px;
                text-align: center;
                z-index: 2;
            }

            .search-wrap.init-status .cancel,
            .search-wrap.init-status .clear-input,
            .search-wrap.init-status .submit {
                display: none;
            }

            .search-wrap .search-bar .keyword {
                display: block;
                width: 100%;
                padding-top: 6px;
                padding-bottom: 6px;
                border: 0;
                font-size: 14px;
                line-height: 18px;
                background: 0 0;
            }

            .search-wrap.init-status .search-bar .keyword {
                text-align: left;
            }

            .search-wrap .search-bar .keyword::-webkit-input-placeholder {
                background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKAAAAAgCAMAAACioYPHAAAAaVBMVEUAAACAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICxWbJ4AAAAInRSTlMAv39AEB/f758wYFDQj3CvDPflh9qpJBYEyEc3sn1om1t4LqRQpwAABAhJREFUWMPtlumS0zAQhD26TzvXsiwsC/T7PySK5Fi+Q2Cpoij6R2w5lvSpPTNS86+Jc1kbLbfrb7WxXT4U44Y1jK/1tI3cmPnwzYTT6dK1chcQCLVB2ABk8GtdXW1IKLHWk9Ct452PuOlJ7gLexk1shGYLcGFPfbujLs0Aut+z6vMJuJw/2Q+8JeD4tg0YyizCE0gQGs+TvCjIRIZlEW43RAvAmJx8ENAfofwQBQScNwEJZQ4gdDI1kNXHJ6AoSyFQFoAFYGLYAzTspki29++I10NT9QVo9wEZ2r4h82XPh0cBAw3qRzkovH6cJiFg7wDy2vgtQPsTPc9Qh9mjJ7yswUXGFBgzloEvHYyR3USjD2XcNWn7lLJyBCi8UbgPeNDLmvB8XLMARKRBpDhDEl2npBqDBLpJ0aAAluftxQZARYDSPwH4DZcly1d8vRODnPtrw3LOw/xLO7Ba2fM9d9fXeLpcO7MYAN21dtSnMzPvzS1IzVrOfoJ6IAZngBHKiTFg7So86xSArgVNF0WYqu4IL2tV/wAcfhFQ0A3JRzEHtABULgFLwEVA9TdqNWM15CZgmzwKVAKyXIpxlKm04gU0Ako2kvExQvR5dX8SUBJc7NhVCv12MbzLCZ0ocahB/m6ZIboPGPBpyfEREBuAWrdbg0qNKkoY7wL4hO8rmzNOq3xOwyRyMR1UDL7TtRZ6pqFcalr/HoBva0X5jKflQ9kqACspx6ZHRsp4+TX3DoDPwOe94l35rigB2UimFe/loEfRIJzKeKUR4OaAYgwoQJtlpv5l8PJxYeBleFQVOjks1NfqUddiXQdow3nLskwA3ATQEauAjnQ1P4BPVSuxPOI8L9Np0qXEtBqXV1qYuoFUBUrqGNPaVsCogbYCCmgzmE803/QSex34y4TvuIzAZahQntohDHNIYi3ndrKj8v5oL/yVrnOi/zMbZJsdCTY5AL7KevoHcPpwF1AE7ZrY81Wx7sZgcyw6eUVXAEIrapJIxqYVIBdNx/NWFEvkmTrq2xFH458THT9rwIRKuASshAhzPkmA6Rm8wlDBg4py/cBCUZSYprSkkjkKmTOMj82WkHTUSLr45jkTbgPWmHPTk66GkQODYEMFkmOPx50kIPocyb+2hEVm0pOVfI4XJJ2efMrfQrgP6Agwk/3MKSg+MclWE6ssgpc9nlMwJdW6UcYZuOKqFtP9TcrDcGS9rBIKjTywT2ggmZ1X0dsCgyhmX1EQ9CIVIkYikZkLioQuF9XUMNyQvEDNFs+INFQOkiRTGFwAyjix2Cc6otHHMWZlbM+yPOclxYd9QYGXTrIsTza7hGFmH6BMqSKdq+bbGMo0ohqUUvkR8VtJ8fKBXvJly2Cx3882//UX6QfMclqph21gcgAAAABJRU5ErkJggg==);
                background-repeat: no-repeat;
                background-position: left center;
                background-size: 80px 16px;
                color: gray
            }

            .search-wrap.init-status .search-bar .keyword::-webkit-input-placeholder {
                background-position: center center;
            }

            .suggestion-wrap {
                position: absolute;
                top: 46px;
                bottom: 0;
                z-index: 7;
                width: 100%;
                background: #f6f6f6;
                overflow: scroll;
                -webkit-overflow-scrolling: touch;
            }

            .map-wrap {
                height: 220px;
                position: relative;
                top: 46px;
                width: 100%;
            }

            .list-wrap {
                width: 100%;
                position: absolute;
                background-color: #fff;
                z-index: 1;
                bottom: 0;
                left: 0;
                overflow: hidden;
                border-top: 1px solid #d3d3d3;
                overflow-y: scroll;
                padding-bottom: 0;
                padding-bottom: constant(safe-area-inset-bottom);
                padding-bottom: env(safe-area-inset-bottom);
                -webkit-overflow-scrolling: touch;
            }

            .poi-list ul {
                list-style: none;
                width: 100%;
                text-align: left;
                padding: 0 0 65px;
            }

            .suggestion-wrap li,
            .active-pos,
            .poi-list li {
                position: relative;
                display: block;
                padding-left: 15px;
                border: none;
                height: 58px;
                padding-top: 6px;
                margin: 0;
            }

            .poi-list li * {
                pointer-events: none;
            }

            .poi-list li p,
            .active-pos div p {
                padding-bottom: 10px;
                border-bottom: 1px solid #d3d3d3;
            }

            .poi-title {
                font-size: 14px;
                line-height: 27px;
                color: #333;
                font-weight: 400;
                display: block;
                width: 92%;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
            }

            .poi-address {
                font-size: 12px;
                color: gray;
                height: 20px;
                line-height: 20px;
                margin: 0;
                width: 92%;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                display: block;
            }

            .poi-city,
            .poi-latlng {
                line-height: 22px;
                display: none;
            }

            .no-more-results,
            .no-results {
                color: gray;
                padding: 20px 0;
                line-height: 24px;
                text-align: center;
            }

            .no-more-results p,
            .no-results p {
                padding: 0;
                margin: 0;
            }

            .list-wrap i {
                width: 16px;
                height: 16px;
                display: none;
                background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAaVBMVEUAAAAAef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef8Aef84EEEWAAAAInRSTlMA+QQY6izG8NO6TphC3JReWSMoDLOfgXajf29lZEs7Hcep1l5DEAAAAPxJREFUOMuFU9cSgyAQBDSUgCVgSS/8/0cGc8E5FQdeOGaXud0rBB9mzdFYRtKnGDkVkktB+VgkYE27poCw6aheUxpxPqHn6SyaRe66NKsfpqyRll6+Njlfsp/jS1WkRFeXf/QUh6Stg3iCAHndMX6VPxltlaiMNdPXqp1iftviR+qHcN34ZJm6FO4/4XY0FOeuknhpp0jdCamHJA7GhjqQ2n2ctCpofPw8K+22OHm8I+HjvXIYj4SYYvSBwTAeU0SROjA6hnEQCTYjg2McbEKhZgbGoVC41HqJQ6lxs1iP8blZuN3WrdudG5jcyGWHNj/2+cXJr15+ebPr/wWbNBG/n/A0GgAAAABJRU5ErkJggg==) no-repeat right center;
                background-size: 16px 16px;
                position: absolute;
                right: 15px;
                top: 50%;
                margin-top: -8px;
            }

            .list-wrap i.active {
                right: 30px;
                display: inline-block;
            }
        </style>
    </head>

    <body>
        <div class="search-wrap init-status">
            <div class="search-bar">
                <form name="poi-search" id="poiSearch" onsubmit="return false;">
                    <input type="search" class="keyword" id="searchword" placeholder=" ">
                    <button type="reset" class="clear-input hide"></button>
                    <button type="submit" class="submit hide">搜索</button>
                </form>
                <a class="cancel">取消</a>
            </div>
        </div>
        <div class="suggestion-wrap hide">
            <div class="poi-list">
                <ul>

                </ul>
            </div>
            <div class="no-results hide">
                <p>对不起，没有搜索到相关数据！</p>
            </div>
            <div class="no-more-results hide">
                <p>亲，没有更多了~</p>
            </div>
        </div>
        <div id="map" class="map-wrap"></div>
        <div class="list-wrap">
            <div class="active-pos">

            </div>
            <div class="poi-list">
                <ul>

                </ul>
                <div class="no-results hide">
                    <p>对不起，没有搜索到相关数据！</p>
                </div>
            </div>
        </div>
        <script>
            var loc;
            var serviceWebview;
            var back = function (cancel) {
                if (cancel) {
                    plus.webview.postMessageToUniNView({
                        type: 'chooseLocation',
                        args: {
                            errMsg: 'cancel'
                        }
                    }, '__uniapp__service');
                }
                var webview = plus.webview.currentWebview()
                if (webview.__uniapp_statusbar_style === 'dark') {
                    plus.navigator.setStatusBarStyle('dark')
                }
                webview.close('auto');
            };
            window.__chooseLocationConfirm__ = function () {
                if (!loc) {
                    plus.nativeUI.alert('您尚未选择位置!');
                    return;
                }
                plus.webview.postMessageToUniNView({
                    type: 'chooseLocation',
                    args: loc
                }, '__uniapp__service');
                back();
            }
            var searchWrapElem = document.querySelector('.search-wrap');
            var searchWordElem = document.getElementById('searchword');
            var resetElem = document.querySelector('[type="reset"]');
            var cancelElem = document.querySelector('.cancel');

            var suggestionWrapElem = document.querySelector('.suggestion-wrap');
            var mapElem = document.getElementById('map');
            var listWrapElem = document.querySelector('.list-wrap');

            var suggestionPoiListElem = suggestionWrapElem.querySelector('.poi-list ul');
            var suggestionPoiNoResultsElem = suggestionWrapElem.querySelector('.no-results');
            var suggestionPoiNoMoreResultsElem = suggestionWrapElem.querySelector(
                '.no-more-results');

            var activePosElem = document.querySelector('.active-pos');
            var nearbyPoiListElem = listWrapElem.querySelector('.poi-list ul');
            var nearbyPoiNoResultsElem = listWrapElem.querySelector('.no-results');

            listWrapElem.style.top = mapElem.offsetHeight + 47 + 'px';


            var debounce = function (func, wait, immediate) {
                var timeout, result;
                return function () {
                    var context = this,
                        args = arguments,
                        later, callNow;
                    later = function () {
                        timeout = null;
                        if (!immediate) {
                            result = func.apply(context, args);
                        }
                    };
                    callNow = immediate && !timeout;
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                    if (callNow) {
                        result = func.apply(context, args);
                    }
                    return result;
                };
            };

            var plusReady = function (callback) {
                if (window.plus) {
                    callback()
                } else {
                    document.addEventListener('plusready', callback)
                }
            }
            var ZOOM = 13
            window.__chooseLocation__ = function (params) {
                if (params && params.keyword) {
                    searchWordElem.value = params.keyword;
                }
                plusReady(function () {
                    serviceWebview = plus.webview.getWebviewById('__W2A_CONTEXT_') ||
                        plus.webview
                            .getLaunchWebview();
                    plus.key.addEventListener('backbutton', function () {
                        back(true);
                    });

                    var nativeMarker = false;

                    var nativeMap = new plus.maps.Map('map', {
                        zoom: ZOOM
                    });


                    var centerPoint = false

                    var reverseGeocode = debounce(function (point) {
                        plus.maps.Map.reverseGeocode(point, {}, function (res) {
                            activePosElem.innerHTML =
                                '<div data-name="' + (res.address || '') +
                                '" data-address="' + (res.address || '') +
                                '" data-lat="' + res.coord.getLat() +
                                '" data-lng="' + res.coord.getLng() +
                                '"><h2 class="poi-title">地图位置</h2><p><span class="poi-address">' +
                                (res.address ||
                                    '') +
                                '</span><span class="poi-latlng">' +
                                res.coord
                                    .getLat() +
                                ',' + res.coord.getLng() +
                                '</span><i></i></p></div>';
                        }, function (err) {
                            console.log(err);
                        });
                        searchNearbyPoi.poiSearchNearBy(searchWordElem.value || '', point, 1000);
                    }, 10)


                    nativeMap.getUserLocation(function (state, point) { //获取当前用户坐标
                        if (state === 0) {
                            nativeMarker = new plus.maps.Marker(point);
                            nativeMarker.setIcon('<EMAIL>');
                            nativeMap.addOverlay(nativeMarker);
                            //centerPoint = point;
                            nativeMap.showUserLocation(true);
                            if (plus.os.name !== 'Android') {
                                nativeMap.setCenter(point);
                            }
                            // reverseGeocode(centerPoint);
                        } else {
                            console.log('获取用户坐标失败');
                        }

                    });



                    nativeMap.onstatuschanged = function (evt) { //切换坐标中心
                        // if (!centerPoint) {
                        //     return;
                        // }
                        if (centerPoint && evt.center) {
                            if (centerPoint.latitude === evt.center.latitude &&
                                centerPoint.longitude === evt.center.longitude) {
                                return;
                            }
                        }
                        centerPoint = evt.center
                        if (nativeMarker) {
                            nativeMarker.setPoint(centerPoint);
                        }
                        reverseGeocode(centerPoint);
                    };

                    var searchPoi = new plus.maps.Search(nativeMap);
                    var searchNearbyPoi = new plus.maps.Search(nativeMap);

                    var showMap = function (centerPoint) {
                        nativeMap.show();
                        if (centerPoint) {
                            if (nativeMarker) {
                                nativeMarker.setPoint(centerPoint);
                            }
                            nativeMap.setCenter(centerPoint);
                            //               setTimeout(function() {
                            //                 nativeMap.setZoom(ZOOM);
                            //               }, 150);
                            // reverseGeocode(centerPoint);
                        }
                        searchWordElem.value = '';
                        cancelElem.className = 'cancel hide';
                        searchWrapElem.className = 'search-wrap init-status';
                        suggestionWrapElem.className = 'suggestion-wrap hide';
                        resetSuggestionPoiList();
                    }

                    var resetSuggestionPoiList = function () {
                        suggestionPoiListElem.innerHTML = '';
                        suggestionPoiNoResultsElem.className = 'no-results hide';
                        suggestionPoiNoMoreResultsElem.className =
                            'no-more-results hide';
                    }

                    var showSuggestionPoiList = function () {
                        nativeMap.hide();
                        resetSuggestionPoiList();
                    }

                    var showSuggestionPoiNoResults = function () {
                        suggestionPoiListElem.innerHTML = '';
                        suggestionPoiNoResultsElem.className = 'no-results';
                        suggestionPoiNoMoreResultsElem.className =
                            'no-more-results hide';
                    }

                    var renderPoiResults = function (poiList) {
                        var html = [];
                        for (var i = 0; i < poiList.length; i++) {
                            var poi = poiList[i];
                            if (poi.point) {
                                html.push('<li data-name="' + (poi.name || '') +
                                    '" data-address="' + (poi.address || '') +
                                    '" data-lat="' + poi.point.getLat() +
                                    '" data-lng="' + poi.point.getLng() +
                                    '"><p><span class="poi-title">' + poi.name +
                                    '</span><span class="poi-address">' + (poi.address ||
                                        '') +
                                    '</span><span class="poi-latlng">' + poi.point
                                        .getLat() +
                                    ',' + poi.point.getLng() +
                                    '</span><span class="poi-city">' + poi.city +
                                    '</span></p><i></i></li>');
                            }
                        }
                        return html.join('');
                    }
                    var showNearbyPoiNoResults = function () {
                        nearbyPoiListElem.innerHTML = '';
                        nearbyPoiNoResultsElem.className = 'no-results';
                    }
                    var showNearbyPoiResults = function (poiList) {
                        var html = renderPoiResults(poiList);
                        if (html) {
                            nearbyPoiListElem.innerHTML = html;
                            nearbyPoiNoResultsElem.className = 'no-results hide';
                        } else {
                            showNearbyPoiNoResults();
                        }
                    }
                    var showSuggestionPoiResults = function (poiList) {
                        var html = renderPoiResults(poiList);
                        if (html) {
                            suggestionPoiListElem.innerHTML = html;
                            suggestionPoiNoResultsElem.className =
                                'no-results hide';
                            suggestionPoiNoMoreResultsElem.className =
                                'no-more-results';
                        } else {
                            showSuggestionPoiNoResults();
                        }
                    }

                    searchPoi.onPoiSearchComplete = function (state, res) {
                        if (state === 0) {
                            if (res.poiList.length) {
                                showSuggestionPoiResults(res.poiList);
                            } else {
                                showSuggestionPoiNoResults();
                            }
                        } else {
                            showSuggestionPoiNoResults();
                        }
                    };

                    searchNearbyPoi.onPoiSearchComplete = function (state, res) {
                        if (state === 0) {
                            if (res.poiList.length) {
                                showNearbyPoiResults(res.poiList);
                            } else {
                                showNearbyPoiNoResults();
                            }
                        } else {
                            showNearbyPoiNoResults();
                        }
                    };

                    var getSuggestion = debounce(function (q) {
                        if (q.trim()) {
                            searchPoi.poiSearchNearBy(q, centerPoint, 50000)
                        } else { }
                    }, 10);

                    suggestionPoiListElem.addEventListener('click', function (evt) {
                        var target = evt.target;
                        for (; target && target !== suggestionPoiListElem; target =
                            target.parentNode) {
                            if (target && target.tagName === 'LI') {
                                var laglng = target.querySelector('.poi-latlng')
                                    .innerText
                                    .split(',');
                                centerPoint = new plus.maps.Point(Number(laglng[1]), Number(laglng[
                                    0]));
                                showMap(centerPoint);
                                reverseGeocode(centerPoint);
                                break;
                            }
                        }
                    });

                    searchWordElem.addEventListener('click', function () {
                        searchWrapElem.className = 'search-wrap';
                        suggestionWrapElem.className = 'suggestion-wrap';
                        cancelElem.className = 'cancel';
                        showSuggestionPoiList();
                    });

                    var oldHasValue = false;

                    var setInputState = function (hasValue) {
                        if (hasValue !== oldHasValue) {
                            if (hasValue) {
                                resetElem.className = 'clear-input'
                            } else {
                                resetElem.className = 'clear-input hide'
                            }
                            oldHasValue = hasValue;
                        }
                    }

                    searchWordElem.addEventListener('input', function () {
                        var value = this.value.replace(/^\s+|\s+$/g, '');
                        setInputState(!!value);
                        getSuggestion(value);
                    });

                    resetElem.addEventListener('click', function (e) {
                        setInputState(false);
                        setTimeout(function () {
                            searchWordElem.focus();
                        }, 0);
                    });
                    cancelElem.addEventListener('click', function () {
                        showMap();
                    });

                    listWrapElem.addEventListener('click', function (e) {
                        var target = e.target;
                        for (; target && target !== listWrapElem; target =
                            target.parentNode) {
                            if (target.className === 'active-pos' || target.tagName ===
                                'LI') {
                                var lastActiveElem = listWrapElem.querySelector(
                                    'i.active');
                                if (lastActiveElem) {
                                    lastActiveElem.className = '';
                                }
                                if (target.className === 'active-pos') {
                                    target = target.children[0]
                                }
                                target.querySelector('i').className = 'active';
                                loc = {
                                    poiname: target.getAttribute('data-name'),
                                    poiaddress: target.getAttribute(
                                        'data-address'),
                                    latlng: {
                                        lat: target.getAttribute(
                                            'data-lat'),
                                        lng: target.getAttribute(
                                            'data-lng'),
                                    }
                                }
                                if (nativeMarker) {
                                    nativeMarker.setPoint(new plus.maps.Point(
                                        loc.latlng.lng, loc.latlng.lat));
                                }
                                break;
                            }
                        }
                    })


                });
            };
        </script>
    </body>

</html>