"use strict";function t(t){return t&&"object"==typeof t&&"default"in t?t.default:t}var e=t(require("fs")),n=t(require("path")),s=t(require("debug")),i=t(require("licia/isRelative")),o=t(require("ws")),r=require("events"),a=t(require("licia/uuid")),c=t(require("licia/stringify")),p=t(require("licia/dateFormat")),l=t(require("licia/waitUntil")),u=t(require("licia/fs")),h=t(require("licia/isFn")),d=t(require("licia/trim")),m=t(require("licia/isStr")),g=t(require("licia/startWith")),y=t(require("licia/isNum")),v=t(require("licia/sleep")),w=t(require("licia/isUndef")),f=t(require("address")),P=t(require("default-gateway")),M=t(require("licia/getPort")),k=require("child_process"),E=t(require("licia/toStr"));class b extends r.EventEmitter{constructor(t){super(),this.ws=t,this.ws.addEventListener("message",t=>{this.emit("message",t.data)}),this.ws.addEventListener("close",()=>{this.emit("close")})}send(t){this.ws.send(t)}close(){this.ws.close()}}class I extends r.EventEmitter{constructor(t,e,n){super(),this.puppet=e,this.namespace=n,this.callbacks=new Map,this.transport=t,this.debug=s("automator:protocol:"+this.namespace),this.onMessage=t=>{this.debug(`${p("yyyy-mm-dd HH:MM:ss:l")} ◀ RECV ${t}`);const{id:e,method:n,error:s,result:i,params:o}=JSON.parse(t);if(!e)return this.puppet.emit(n,o);const{callbacks:r}=this;if(e&&r.has(e)){const t=r.get(e);r.delete(e),s?t.reject(Error(s.message)):t.resolve(i)}},this.onClose=()=>{this.callbacks.forEach(t=>{t.reject(Error("Connection closed"))})},this.transport.on("message",this.onMessage),this.transport.on("close",this.onClose)}send(t,e={},n=!0){if(n&&this.puppet.adapter.has(t))return this.puppet.adapter.send(this,t,e);const s=a(),i=c({id:s,method:t,params:e});return this.debug(`${p("yyyy-mm-dd HH:MM:ss:l")} SEND ► ${i}`),new Promise((t,e)=>{try{this.transport.send(i)}catch(t){e(Error("Connection closed"))}this.callbacks.set(s,{resolve:t,reject:e})})}dispose(){this.transport.close()}static createDevtoolConnection(t,e){return new Promise((n,s)=>{const i=new o(t);i.addEventListener("open",()=>{n(new I(new b(i),e,"devtool"))}),i.addEventListener("error",s)})}static createRuntimeConnection(t,e,n){return new Promise((i,r)=>{s("automator:runtime")(`${p("yyyy-mm-dd HH:MM:ss:l")} port=${t}`);const a=new o.Server({port:t});l(async()=>{if(e.runtimeConnection)return!0},n,1e3).catch(t=>{throw Error("Failed to connect to runtime, please make sure the project is running")}),a.on("connection",(function(t){s("automator:runtime")(p("yyyy-mm-dd HH:MM:ss:l")+" connected");const n=new I(new b(t),e,"runtime");e.setRuntimeConnection(n),i(n)})),e.setRuntimeServer(a)})}}const C=require("qrcode-terminal");require("qrcode-reader");async function T(t,e){const[n,s]=function(t){return m(t)?[!0,[t]]:[!1,t]}(e),i=await t(s);return n?i[0]:i}
/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */
function D(t,e,n,s){var i,o=arguments.length,r=o<3?e:null===s?s=Object.getOwnPropertyDescriptor(e,n):s;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,n,s);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(r=(o<3?i(r):o>3?i(e,n,r):i(e,n))||r);return o>3&&r&&Object.defineProperty(e,n,r),r}var j;function A(t,e){const n=e.value;return e.value=async function(e){return(await(null==n?void 0:n.call(this,e)))(t)},e}function R(t,e,n){return A(j.RUNTIME,n)}function x(t,e,n){return A(j.DEVTOOL,n)}!function(t){t.RUNTIME="runtime",t.DEVTOOL="devtool"}(j||(j={}));class q{constructor(t){this.puppet=t}invoke(t,e){return async n=>this.puppet.devtoolConnection?(n===j.DEVTOOL?this.puppet.devtoolConnection:this.puppet.runtimeConnection).send(t,e):this.puppet.runtimeConnection.send(t,e)}on(t,e){this.puppet.on(t,e)}}class U extends q{constructor(t,e){super(t),this.id=e.elementId,this.pageId=e.pageId,this.nodeId=e.nodeId,this.videoId=e.videoId}async getData(t){return this.invokeMethod("Element.getData",t)}async setData(t){return this.invokeMethod("Element.setData",t)}async callMethod(t){return this.invokeMethod("Element.callMethod",t)}async getElement(t){return this.invokeMethod("Element.getElement",t)}async getElements(t){return this.invokeMethod("Element.getElements",t)}async getOffset(){return this.invokeMethod("Element.getOffset")}async getHTML(t){return this.invokeMethod("Element.getHTML",t)}async getAttributes(t){return this.invokeMethod("Element.getAttributes",t)}async getStyles(t){return this.invokeMethod("Element.getStyles",t)}async getDOMProperties(t){return this.invokeMethod("Element.getDOMProperties",t)}async getProperties(t){return this.invokeMethod("Element.getProperties",t)}async tap(){return this.invokeMethod("Element.tap")}async longpress(){return this.invokeMethod("Element.longpress")}async touchstart(t){return this.invokeMethod("Element.touchstart",t)}async touchmove(t){return this.invokeMethod("Element.touchmove",t)}async touchend(t){return this.invokeMethod("Element.touchend",t)}async triggerEvent(t){return this.invokeMethod("Element.triggerEvent",t)}async callFunction(t){return this.invokeMethod("Element.callFunction",t)}async callContextMethod(t){return this.invokeMethod("Element.callContextMethod",t)}invokeMethod(t,e={}){return e.elementId=this.id,e.pageId=this.pageId,this.nodeId&&(e.nodeId=this.nodeId),this.videoId&&(e.videoId=this.videoId),this.invoke(t,e)}}D([R],U.prototype,"getData",null),D([R],U.prototype,"setData",null),D([R],U.prototype,"callMethod",null),D([x],U.prototype,"getElement",null),D([x],U.prototype,"getElements",null),D([x],U.prototype,"getOffset",null),D([x],U.prototype,"getHTML",null),D([x],U.prototype,"getAttributes",null),D([x],U.prototype,"getStyles",null),D([x],U.prototype,"getDOMProperties",null),D([x],U.prototype,"getProperties",null),D([x],U.prototype,"tap",null),D([x],U.prototype,"longpress",null),D([x],U.prototype,"touchstart",null),D([x],U.prototype,"touchmove",null),D([x],U.prototype,"touchend",null),D([x],U.prototype,"triggerEvent",null),D([x],U.prototype,"callFunction",null),D([x],U.prototype,"callContextMethod",null);class ${constructor(t,e,n){this.puppet=t,this.id=e.elementId,this.pageId=e.pageId,this.nodeId=e.nodeId||null,this.videoId=e.videoId||null,this.tagName=e.tagName,this.nvue=e.nvue,this.elementMap=n,"body"!==this.tagName&&"page-body"!==this.tagName||(this.tagName="page"),this.api=new U(t,e)}async $(t){try{const e=await this.api.getElement({selector:t});return $.create(this.puppet,Object.assign({},e,{pageId:this.pageId}),this.elementMap)}catch(t){return null}}async $$(t){const{elements:e}=await this.api.getElements({selector:t});return e.map(t=>$.create(this.puppet,Object.assign({},t,{pageId:this.pageId}),this.elementMap))}async size(){const[t,e]=await this.domProperty(["offsetWidth","offsetHeight"]);return{width:t,height:e}}async offset(){return this.api.getOffset()}async text(){return this.domProperty("innerText")}async attribute(t){if(!m(t))throw Error("name must be a string");return(await this.api.getAttributes({names:[t]})).attributes[0]}async value(){return this.property("value")}async property(t){if(!m(t))throw Error("name must be a string");if(this.puppet.checkProperty){let e=this.publicProps;if(e||(this.publicProps=e=await this._property("__propPublic")),!e[t])throw Error(`${this.tagName}.${t} not exists`)}return this._property(t)}async html(){return(await this.api.getHTML({type:"inner"})).html}async outerHtml(){return(await this.api.getHTML({type:"outer"})).html}async style(t){if(!m(t))throw Error("name must be a string");return(await this.api.getStyles({names:[t]})).styles[0]}async tap(){return this.api.tap()}async longpress(){return this.nvue?this.api.longpress():(await this.touchstart(),await v(350),this.touchend())}async trigger(t,e){const n={type:t};return w(e)||(n.detail=e),this.api.triggerEvent(n)}async touchstart(t){return this.api.touchstart(t)}async touchmove(t){return this.api.touchmove(t)}async touchend(t){return this.api.touchend(t)}async domProperty(t){return T(async t=>(await this.api.getDOMProperties({names:t})).properties,t)}_property(t){return T(async t=>(await this.api.getProperties({names:t})).properties,t)}send(t,e){return e.elementId=this.id,e.pageId=this.pageId,this.nodeId&&(e.nodeId=this.nodeId),this.videoId&&(e.videoId=this.videoId),this.puppet.send(t,e)}async callFunction(t,...e){return(await this.api.callFunction({functionName:t,args:e})).result}static create(t,e,n){let s,i=n.get(e.elementId);if(i)return i;if(e.nodeId)s=S;else switch(e.tagName){case"input":s=N;break;case"textarea":s=O;break;case"scroll-view":s=_;break;case"swiper":s=F;break;case"movable-view":s=L;break;case"switch":s=H;break;case"slider":s=B;break;case"video":s=W;break;default:s=$}return i=new s(t,e,n),n.set(e.elementId,i),i}}class S extends ${async setData(t){return this.api.setData({data:t})}async data(t){const e={};return t&&(e.path=t),(await this.api.getData(e)).data}async callMethod(t,...e){return(await this.api.callMethod({method:t,args:e})).result}}class N extends ${async input(t){return this.callFunction("input.input",t)}}class O extends ${async input(t){return this.callFunction("textarea.input",t)}}class _ extends ${async scrollTo(t,e){return this.callFunction("scroll-view.scrollTo",t,e)}async property(t){return"scrollTop"===t?this.callFunction("scroll-view.scrollTop"):"scrollLeft"===t?this.callFunction("scroll-view.scrollLeft"):super.property(t)}async scrollWidth(){return this.callFunction("scroll-view.scrollWidth")}async scrollHeight(){return this.callFunction("scroll-view.scrollHeight")}}class F extends ${async swipeTo(t){return this.callFunction("swiper.swipeTo",t)}}class L extends ${async moveTo(t,e){return this.callFunction("movable-view.moveTo",t,e)}async property(t){return"x"===t?this._property("_translateX"):"y"===t?this._property("_translateY"):super.property(t)}}class H extends ${async tap(){return this.callFunction("switch.tap")}}class B extends ${async slideTo(t){return this.callFunction("slider.slideTo",t)}}class W extends ${async callContextMethod(t,...e){return this.api.callContextMethod({method:t,args:e})}}class V extends q{constructor(t,e){super(t),this.id=e.id}async getData(t){return this.invokeMethod("Page.getData",t)}async setData(t){return this.invokeMethod("Page.setData",t)}async callMethod(t){return this.invokeMethod("Page.callMethod",t)}async getElement(t){return this.invokeMethod("Page.getElement",t)}async getElements(t){return this.invokeMethod("Page.getElements",t)}async getWindowProperties(t){return this.invokeMethod("Page.getWindowProperties",t)}invokeMethod(t,e={}){return e.pageId=this.id,this.invoke(t,e)}}D([R],V.prototype,"getData",null),D([R],V.prototype,"setData",null),D([R],V.prototype,"callMethod",null),D([x],V.prototype,"getElement",null),D([x],V.prototype,"getElements",null),D([x],V.prototype,"getWindowProperties",null);class z{constructor(t,e){this.puppet=t,this.id=e.id,this.path=e.path,this.query=e.query,this.elementMap=new Map,this.api=new V(t,e)}async waitFor(t){return y(t)?await v(t):h(t)?l(t):m(t)?l(async()=>(await this.$$(t)).length>0):void 0}async $(t){try{const e=await this.api.getElement({selector:t});return $.create(this.puppet,Object.assign({selector:t},e,{pageId:this.id}),this.elementMap)}catch(t){return null}}async $$(t){const{elements:e}=await this.api.getElements({selector:t});return e.map(e=>$.create(this.puppet,Object.assign({selector:t},e,{pageId:this.id}),this.elementMap))}async data(t){const e={};return t&&(e.path=t),(await this.api.getData(e)).data}async setData(t){return this.api.setData({data:t})}async size(){const[t,e]=await this.windowProperty(["document.documentElement.scrollWidth","document.documentElement.scrollHeight"]);return{width:t,height:e}}async callMethod(t,...e){return(await this.api.callMethod({method:t,args:e})).result}async scrollTop(){return this.windowProperty("document.documentElement.scrollTop")}async windowProperty(t){const e=m(t);e&&(t=[t]);const{properties:n}=await this.api.getWindowProperties({names:t});return e?n[0]:n}static create(t,e,n){let s=n.get(e.id);return s?(s.query=e.query,s):(s=new z(t,e),n.set(e.id,s),s)}}class J extends q{async getPageStack(){return this.invoke("App.getPageStack")}async callUniMethod(t){return this.invoke("App.callUniMethod",t)}async getCurrentPage(){return this.invoke("App.getCurrentPage")}async mockUniMethod(t){return this.invoke("App.mockUniMethod",t)}async callFunction(t){return this.invoke("App.callFunction",t)}async captureScreenshot(t){return this.invoke("App.captureScreenshot",t)}async exit(){return this.invoke("App.exit")}async addBinding(t){return this.invoke("App.addBinding",t)}async enableLog(){return this.invoke("App.enableLog")}onLogAdded(t){return this.on("App.logAdded",t)}onBindingCalled(t){return this.on("App.bindingCalled",t)}onExceptionThrown(t){return this.on("App.exceptionThrown",t)}}D([R],J.prototype,"getPageStack",null),D([R],J.prototype,"callUniMethod",null),D([R],J.prototype,"getCurrentPage",null),D([R],J.prototype,"mockUniMethod",null),D([x],J.prototype,"callFunction",null),D([x],J.prototype,"captureScreenshot",null),D([x],J.prototype,"exit",null),D([x],J.prototype,"addBinding",null),D([x],J.prototype,"enableLog",null);class G extends q{async getInfo(){return this.invoke("Tool.getInfo")}async enableRemoteDebug(t){return this.invoke("Tool.enableRemoteDebug")}async close(){return this.invoke("Tool.close")}async getTestAccounts(){return this.invoke("Tool.getTestAccounts")}onRemoteDebugConnected(t){this.puppet.once("Tool.onRemoteDebugConnected",t),this.puppet.once("Tool.onPreviewConnected",t)}}function X(t){return new Promise(e=>setTimeout(e,t))}D([x],G.prototype,"getInfo",null),D([x],G.prototype,"enableRemoteDebug",null),D([x],G.prototype,"close",null),D([x],G.prototype,"getTestAccounts",null);class Y extends r.EventEmitter{constructor(t,e){super(),this.puppet=t,this.options=e,this.pageMap=new Map,this.appBindings=new Map,this.appApi=new J(t),this.toolApi=new G(t),this.appApi.onLogAdded(t=>{this.emit("console",t)}),this.appApi.onBindingCalled(({name:t,args:e})=>{try{const n=this.appBindings.get(t);n&&n(...e)}catch(t){}}),this.appApi.onExceptionThrown(t=>{this.emit("exception",t)})}async pageStack(){return(await this.appApi.getPageStack()).pageStack.map(t=>z.create(this.puppet,t,this.pageMap))}async navigateTo(t){return this.changeRoute("navigateTo",t)}async redirectTo(t){return this.changeRoute("redirectTo",t)}async navigateBack(){return this.changeRoute("navigateBack")}async reLaunch(t){return this.changeRoute("reLaunch",t)}async switchTab(t){return this.changeRoute("switchTab",t)}async currentPage(){const{id:t,path:e,query:n}=await this.appApi.getCurrentPage();return z.create(this.puppet,{id:t,path:e,query:n},this.pageMap)}async systemInfo(){return this.callUniMethod("getSystemInfoSync")}async callUniMethod(t,...e){return(await this.appApi.callUniMethod({method:t,args:e})).result}async mockUniMethod(t,e,...n){return h(e)||m(s=e)&&(s=d(s),g(s,"function")||g(s,"() =>"))?this.appApi.mockUniMethod({method:t,functionDeclaration:e.toString(),args:n}):this.appApi.mockUniMethod({method:t,result:e});var s}async restoreUniMethod(t){return this.appApi.mockUniMethod({method:t})}async evaluate(t,...e){return(await this.appApi.callFunction({functionDeclaration:t.toString(),args:e})).result}async pageScrollTo(t){await this.callUniMethod("pageScrollTo",{scrollTop:t,duration:0})}async close(){try{await this.appApi.exit()}catch(t){}await X(1e3),this.puppet.disposeRuntimeServer(),await this.toolApi.close(),this.disconnect()}async teardown(){return this["disconnect"===this.options.teardown?"disconnect":"close"]()}async remote(t){if(!this.puppet.devtools.remote)return console.warn(`Failed to enable remote, ${this.puppet.devtools.name} is unimplemented`);const{qrCode:e}=await this.toolApi.enableRemoteDebug({auto:t});var n;e&&await(n=e,new Promise(t=>{C.generate(n,{small:!0},e=>{process.stdout.write(e),t()})}));const s=new Promise(t=>{this.toolApi.onRemoteDebugConnected(async()=>{await X(1e3),t()})}),i=new Promise(t=>{this.puppet.setRemoteRuntimeConnectionCallback(()=>{t()})});return Promise.all([s,i])}disconnect(){this.puppet.dispose()}on(t,e){return"console"===t&&this.appApi.enableLog(),super.on(t,e),this}async exposeFunction(t,e){if(this.appBindings.has(t))throw Error(`Failed to expose function with name ${t}: already exists!`);this.appBindings.set(t,e),await this.appApi.addBinding({name:t})}async checkVersion(){}async screenshot(t){const{data:e}=await this.appApi.captureScreenshot({fullPage:null==t?void 0:t.fullPage});if(!(null==t?void 0:t.path))return e;await u.writeFile(t.path,e,"base64")}async testAccounts(){return(await this.toolApi.getTestAccounts()).accounts}async changeRoute(t,e){return await this.callUniMethod(t,{url:e}),await X(3e3),this.currentPage()}}class K{constructor(t){this.options=t}has(t){return!!this.options[t]}send(t,e,n){const s=this.options[e];if(!s)return Promise.reject(Error(`adapter for ${e} not found`));const i=s.reflect;return i?(s.params&&(n=s.params(n)),"function"==typeof i?i(t.send.bind(t),n):(e=i,t.send(e,n))):Promise.reject(Error(e+"'s reflect is required"))}}const Q=s("automator:puppet");function Z(t){try{return require(t)}catch(t){}}function tt(t,e,s,i){const o=function(t,e,s){let i,o;return process.env.UNI_OUTPUT_DIR?(o=n.join(process.env.UNI_OUTPUT_DIR,"../.automator/"+e,".automator.json"),i=Z(o)):(o=n.join(t,`dist/${s}/.automator/${e}`,".automator.json"),i=Z(o),i||(o=n.join(t,`unpackage/dist/${s}/.automator/${e}`,".automator.json"),i=Z(o))),Q(`${o}=>${JSON.stringify(i)}`),i}(t,s,i);if(!o||!o.wsEndpoint)return!1;const r=require("../package.json").version;if(o.version!==r)return Q(`unmet=>${o.version}!==${r}`),!1;const a=function(t){let e;try{const t=P.v4.sync();e=f.ip(t&&t.interface),e&&(/^10[.]|^172[.](1[6-9]|2[0-9]|3[0-1])[.]|^192[.]168[.]/.test(e)||(e=void 0))}catch(t){}return"ws://"+(e||"localhost")+":"+t}(e);return Q("wsEndpoint=>"+a),o.wsEndpoint===a}class et extends r.EventEmitter{constructor(t,e){if(super(),this.target=e||("h5"===t?require("@dcloudio/uni-h5/lib/h5/uni.automator.js"):require(`@dcloudio/uni-${t}/lib/uni.automator.js`)),!this.target)throw Error("puppet is not provided");this.platform=t,this.adapter=new K(this.target.adapter||{})}setCompiler(t){this.compiler=t}setRuntimeServer(t){this.wss=t}setRemoteRuntimeConnectionCallback(t){this.remoteRuntimeConnectionCallback=t}setRuntimeConnection(t){this.runtimeConnection=t,this.remoteRuntimeConnectionCallback&&(this.remoteRuntimeConnectionCallback(),this.remoteRuntimeConnectionCallback=null)}setDevtoolConnection(t){this.devtoolConnection=t}disposeRuntimeServer(){this.wss&&this.wss.close()}disposeRuntime(){this.runtimeConnection.dispose()}disposeDevtool(){this.compiler&&this.compiler.stop(),this.devtoolConnection&&this.devtoolConnection.dispose()}dispose(){this.disposeRuntime(),this.disposeDevtool(),this.disposeRuntimeServer()}send(t,e){return this.runtimeConnection.send(t,e)}validateProject(t){const s=this.target.devtools.required;return!s||!s.find(s=>!e.existsSync(n.join(t,s)))}validateDevtools(t){const e=this.target.devtools.validate;return e?e(t,this):Promise.resolve(t)}createDevtools(t,e,n){const s=this.target.devtools.create;return s?(e.timeout=n,s(t,e,this)):Promise.resolve()}shouldCompile(t,e,n,s){this.compiled=!0;const i=this.target.shouldCompile;return i?this.compiled=i(n,s):!0===n.compile?this.compiled=!0:this.compiled=!tt(t,e,this.platform,this.mode),this.compiled}get checkProperty(){return"mp-weixin"===this.platform}get devtools(){return this.target.devtools}get mode(){const t=this.target.mode;return t||("production"===process.env.NODE_ENV?"build":"dev")}}const nt=s("automator:compiler"),st=/The\s+(.*)\s+directory is ready/;class it{constructor(t){this.puppet=t,this.puppet.setCompiler(this)}compile(t){const e=this.puppet.mode,s=this.puppet.platform;let i=t.silent;const o=t.port,r=t.host,a=`${e}:${s}`,c=t.projectPath,[p,l]=this.getSpawnArgs(t,a);l.push("--auto-port"),l.push(E(o)),r&&(l.push("--auto-host"),l.push(r));const u={cwd:t.cliPath,env:process.env};return new Promise((t,o)=>{const r=o=>{const r=o.toString().trim();if(!i&&console.log(r),r.includes("- Network")){const e=r.match(/Network:(.*)/)[1].trim();nt("url: "+e),t({path:e})}else if(r.includes("DONE  Build complete")){let o=`unpackage/dist/${e}/${s}`;const a=r.match(st);a&&a.length>1&&(o=a[1]),i=!0,this.stop(),t({path:n.join(c,o)})}};nt(`${p} ${l.join(" ")} %o`,u),this.cliProcess=k.spawn(p,l,u),this.cliProcess.on("error",t=>{o(t)}),this.cliProcess.stdout.on("data",r),this.cliProcess.stderr.on("data",r)})}stop(){this.cliProcess&&this.cliProcess.kill("SIGTERM")}getSpawnArgs(t,e){let s;const i=t.cliPath;try{s=require(n.join(i,"package.json"))}catch(t){}return s&&s.scripts&&s.scripts[e]?[process.env.UNI_NPM_PATH||(/^win/.test(process.platform)?"npm.cmd":"npm"),["run",e,"--"]]:(process.env.UNI_INPUT_DIR=t.projectPath,process.env.UNI_OUTPUT_DIR=n.join(t.projectPath,`unpackage/dist/${this.puppet.mode}/${this.puppet.platform}`),[process.env.UNI_NODE_PATH||"node",[n.join(i,"bin/uniapp-cli.js")]])}}class ot{async launch(t){let e=t[t.platform]||{};this.puppet=new et(t.platform,e.puppet);const{port:i,cliPath:o,timeout:r,projectPath:a}=await this.validate(t);e=await this.puppet.validateDevtools(e);let c=this.puppet.shouldCompile(a,i,t,e),p=process.env.UNI_OUTPUT_DIR||a;if(c||this.puppet.validateProject(p)||(p=n.join(a,"dist/"+this.puppet.mode+"/"+this.puppet.platform),this.puppet.validateProject(p)||(p=n.join(a,"unpackage/dist/"+this.puppet.mode+"/"+this.puppet.platform),this.puppet.validateProject(p)||(c=!0))),c){this.puppet.compiled=t.compile=!0,this.compiler=new it(this.puppet);const e=await this.compiler.compile({host:t.host,port:i,cliPath:o,projectPath:a,silent:!!t.silent});e.path&&(p=e.path)}const l=[];return l.push(this.createRuntimeConnection(i,r)),l.push(this.puppet.createDevtools(p,e,r)),new Promise((t,n)=>{Promise.all(l).then(([n,i])=>{n&&this.puppet.setRuntimeConnection(n),i&&this.puppet.setDevtoolConnection(i),s("automator:program")("ready");const o=e.teardown||"disconnect";t(new Y(this.puppet,{teardown:o}))}).catch(t=>n(t))})}resolveCliPath(t){if(!t)return t;try{const e=require(n.join(t,"package.json"));if(e.dependencies&&e.dependencies["@dcloudio/vue-cli-plugin-uni"]||e.devDependencies&&e.devDependencies["@dcloudio/vue-cli-plugin-uni"])return t}catch(t){}}resolveProjectPath(t,s){return t||(t=process.env.UNI_INPUT_DIR||process.cwd()),i(t)&&(t=n.resolve(t)),e.existsSync(t)||function(t){throw Error(t)}(`Project path ${t} doesn't exist`),t}async validate(t){const e=this.resolveProjectPath(t.projectPath,t);let n=process.env.UNI_CLI_PATH||t.cliPath;if(n=this.resolveCliPath(n||""),!n&&(n=this.resolveCliPath(process.cwd())),!n&&(n=this.resolveCliPath(e)),!n)throw Error("cliPath is not provided");return{port:await async function(t,e){const n=await M(t||e);if(t&&n!==t)throw Error(`Port ${t} is in use, please specify another port`);return n}(t.port||9520),cliPath:n,timeout:t.timeout||1e5,projectPath:e}}async createRuntimeConnection(t,e){return I.createRuntimeConnection(t,this.puppet,e)}}module.exports=class{constructor(){this.launcher=new ot}async launch(t){return this.launcher.launch(t)}};
