{"name": "default-gateway", "version": "6.0.0", "description": "Get the default network gateway, cross-platform.", "author": "silverwind <<EMAIL>>", "repository": "silverwind/default-gateway", "license": "BSD-2-<PERSON><PERSON>", "main": "index.js", "scripts": {"test": "make test"}, "engines": {"node": ">= 10"}, "files": ["index.js", "android.js", "darwin.js", "freebsd.js", "linux.js", "openbsd.js", "sunos.js", "win32.js", "ibmi.js"], "dependencies": {"execa": "^4.0.0"}, "devDependencies": {"eslint": "6.8.0", "eslint-config-silverwind": "10.0.1", "jest": "25.1.0", "updates": "10.2.2", "versions": "8.2.4"}, "keywords": ["default gateway", "network", "default", "gateway", "routing", "route"]}