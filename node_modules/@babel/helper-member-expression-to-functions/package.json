{"name": "@babel/helper-member-expression-to-functions", "version": "7.8.3", "description": "Helper function to replace certain member expressions with function calls", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-member-expression-to-functions", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "author": "<PERSON> <<EMAIL>>", "dependencies": {"@babel/types": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}