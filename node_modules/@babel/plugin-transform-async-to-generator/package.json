{"name": "@babel/plugin-transform-async-to-generator", "version": "7.8.3", "description": "Turn async functions into ES2015 generators", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-module-imports": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-remap-async-to-generator": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}