{"name": "@babel/compat-data", "version": "7.9.6", "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "description": "", "repository": "https://github.com/babel/babel/tree/master/packages/babel-compat-data", "publishConfig": {"access": "public"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh; node ./scripts/build-data.js; node ./scripts/build-modules-support.js; node ./scripts/build-bugfixes-targets.js"}, "keywords": ["babel", "compat-table", "compat-data"], "dependencies": {"browserslist": "^4.11.1", "invariant": "^2.2.4", "semver": "^5.5.0"}, "devDependencies": {"@babel/helper-compilation-targets": "^7.9.6", "caniuse-db": "1.0.30001035", "electron-to-chromium": "1.3.377", "lodash": "^4.17.15"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}