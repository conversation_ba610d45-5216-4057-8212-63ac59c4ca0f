{"name": "@babel/plugin-transform-modules-systemjs", "version": "7.9.6", "description": "This plugin transforms ES2015 modules to SystemJS", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-hoist-variables": "^7.8.3", "@babel/helper-module-transforms": "^7.9.0", "@babel/helper-plugin-utils": "^7.8.3", "babel-plugin-dynamic-import-node": "^2.3.3"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}