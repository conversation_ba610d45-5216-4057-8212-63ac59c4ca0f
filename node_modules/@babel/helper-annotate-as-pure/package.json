{"name": "@babel/helper-annotate-as-pure", "version": "7.8.3", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/types": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}