{"name": "@babel/plugin-proposal-class-properties", "version": "7.8.3", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}