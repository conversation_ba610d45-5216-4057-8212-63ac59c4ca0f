{"name": "@babel/helpers", "version": "7.9.6", "description": "Collection of helper functions used by Babel transforms.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-helpers", "main": "lib/index.js", "dependencies": {"@babel/template": "^7.8.3", "@babel/traverse": "^7.9.6", "@babel/types": "^7.9.6"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}