{"name": "@babel/plugin-transform-typeof-symbol", "version": "7.8.4", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/runtime": "^7.8.4", "@babel/runtime-corejs2": "^7.8.4", "@babel/runtime-corejs3": "^7.8.4", "resolve": "^1.15.0"}, "gitHead": "5c2e6bc07fed3d28801d93168622c99ae622653a"}