{"name": "@babel/register", "version": "7.9.0", "description": "babel require hook", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-register", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "browser": {"./lib/node.js": "./lib/browser.js"}, "dependencies": {"find-cache-dir": "^2.0.0", "lodash": "^4.17.13", "make-dir": "^2.1.0", "pirates": "^4.0.0", "source-map-support": "^0.5.16"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-transform-modules-commonjs": "^7.9.0", "browserify": "16.5.0", "default-require-extensions": "^2.0.0"}, "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02"}