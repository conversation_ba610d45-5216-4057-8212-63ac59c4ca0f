{"name": "@babel/plugin-transform-modules-umd", "version": "7.9.0", "description": "This plugin transforms ES2015 modules to UMD", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-module-transforms": "^7.9.0", "@babel/helper-plugin-utils": "^7.8.3"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02"}