{"name": "@babel/plugin-transform-regenerator", "author": "<PERSON> <<EMAIL>>", "description": "Explode async and generator functions into a state machine.", "version": "7.8.7", "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "main": "lib/index.js", "dependencies": {"regenerator-transform": "^0.14.2"}, "license": "MIT", "publishConfig": {"access": "public"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "595f65f33b8e948e34d12be83f700cf8d070c790"}