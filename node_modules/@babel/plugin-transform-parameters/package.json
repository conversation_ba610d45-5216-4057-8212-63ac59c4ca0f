{"name": "@babel/plugin-transform-parameters", "version": "7.9.5", "description": "Compile ES2015 default and rest parameters to ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-parameters", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-get-function-arity": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "5b97e77e030cf3853a147fdff81844ea4026219d"}