{"name": "@babel/helper-create-class-features-plugin", "version": "7.9.6", "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "description": "Compile class public and private fields, private methods and decorators to ES6", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "main": "lib/index.js", "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/helper-function-name": "^7.9.5", "@babel/helper-member-expression-to-functions": "^7.8.3", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-replace-supers": "^7.9.6", "@babel/helper-split-export-declaration": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}