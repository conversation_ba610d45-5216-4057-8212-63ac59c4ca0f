{"name": "@babel/helper-explode-assignable-expression", "version": "7.8.3", "description": "Helper function to explode an assignable expression", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-explode-assignable-expression", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/traverse": "^7.8.3", "@babel/types": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}