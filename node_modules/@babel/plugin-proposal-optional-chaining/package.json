{"name": "@babel/plugin-proposal-optional-chaining", "version": "7.9.0", "description": "Transform optional chaining operators into a series of nil checks", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-optional-chaining", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/plugin-transform-block-scoping": "^7.8.3"}, "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02"}