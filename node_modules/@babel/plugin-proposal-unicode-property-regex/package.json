{"name": "@babel/plugin-proposal-unicode-property-regex", "version": "7.8.8", "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.8.8", "@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "c831a2450dbf252c75750a455c63e1016c2f2244"}