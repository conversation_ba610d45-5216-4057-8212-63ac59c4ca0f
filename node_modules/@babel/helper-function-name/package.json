{"name": "@babel/helper-function-name", "version": "7.9.5", "description": "Helper function to change the property 'name' of every function", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-function-name", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-get-function-arity": "^7.8.3", "@babel/template": "^7.8.3", "@babel/types": "^7.9.5"}, "gitHead": "5b97e77e030cf3853a147fdff81844ea4026219d"}