{"name": "@babel/highlight", "version": "7.9.0", "description": "Syntax highlight JavaScript strings for output in terminals.", "author": "suchipi <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-highlight", "main": "lib/index.js", "dependencies": {"@babel/helper-validator-identifier": "^7.9.0", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02"}