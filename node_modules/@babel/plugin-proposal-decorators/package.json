{"name": "@babel/plugin-proposal-decorators", "version": "7.8.3", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "publishConfig": {"access": "public"}, "description": "Compile class and object decorators to ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-decorators", "main": "lib/index.js", "keywords": ["babel", "babel-plugin", "decorators"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-decorators": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}