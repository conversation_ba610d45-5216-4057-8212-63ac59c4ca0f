{"name": "@babel/plugin-proposal-object-rest-spread", "version": "7.9.6", "description": "Compile object rest and spread to ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-object-rest-spread", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.0", "@babel/plugin-transform-parameters": "^7.9.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}