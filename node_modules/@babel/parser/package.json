{"name": "@babel/parser", "version": "7.9.6", "description": "A JavaScript parser", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "repository": "https://github.com/babel/babel/tree/master/packages/babel-parser", "main": "lib/index.js", "types": "typings/babel-parser.d.ts", "files": ["bin", "lib", "typings"], "engines": {"node": ">=6.0.0"}, "devDependencies": {"@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6", "@babel/helper-validator-identifier": "^7.9.0", "charcodes": "^0.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}