{"name": "@babel/helper-wrap-function", "version": "7.8.3", "description": "Helper to wrap functions inside a function call.", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-function-name": "^7.8.3", "@babel/template": "^7.8.3", "@babel/traverse": "^7.8.3", "@babel/types": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}