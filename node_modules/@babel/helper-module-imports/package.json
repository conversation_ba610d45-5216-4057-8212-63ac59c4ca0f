{"name": "@babel/helper-module-imports", "version": "7.8.3", "description": "Babel helper functions for inserting module loads", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "main": "lib/index.js", "dependencies": {"@babel/types": "^7.8.3"}, "devDependencies": {"@babel/core": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}