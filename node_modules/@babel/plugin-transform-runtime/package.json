{"name": "@babel/plugin-transform-runtime", "version": "7.9.6", "description": "Externalise references to helpers and builtins, automatically polyfilling your code without polluting globals", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-runtime", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "browser": {"./lib/get-runtime-path/index.js": "./lib/get-runtime-path/browser.js", "./src/get-runtime-path/index.js": "./src/get-runtime-path/browser.js"}, "dependencies": {"@babel/helper-module-imports": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "resolve": "^1.8.1", "semver": "^5.5.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/helpers": "^7.9.6", "@babel/plugin-transform-typeof-symbol": "^7.8.3", "@babel/preset-env": "^7.9.6", "@babel/runtime": "^7.9.6", "@babel/template": "^7.8.3", "@babel/types": "^7.9.6"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}