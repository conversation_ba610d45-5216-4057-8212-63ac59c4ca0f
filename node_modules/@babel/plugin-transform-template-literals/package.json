{"name": "@babel/plugin-transform-template-literals", "version": "7.8.3", "description": "Compile ES2015 template literals to ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-template-literals", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-annotate-as-pure": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}