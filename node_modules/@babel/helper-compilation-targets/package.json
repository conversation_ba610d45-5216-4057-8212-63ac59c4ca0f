{"name": "@babel/helper-compilation-targets", "version": "7.9.6", "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "description": "Engine compat data used in @babel/preset-env", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-compilation-targets", "main": "lib/index.js", "exports": {".": "./lib/index.js"}, "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/compat-data": "^7.9.6", "browserslist": "^4.11.1", "invariant": "^2.2.4", "levenary": "^1.1.1", "semver": "^5.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}