{"name": "@babel/plugin-syntax-decorators", "version": "7.8.3", "description": "Allow parsing of decorators", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-decorators", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}