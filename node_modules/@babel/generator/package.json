{"name": "@babel/generator", "version": "7.9.6", "description": "Turns an AST into code.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-generator", "main": "lib/index.js", "files": ["lib"], "dependencies": {"@babel/types": "^7.9.6", "jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0"}, "devDependencies": {"@babel/helper-fixtures": "^7.8.6", "@babel/parser": "^7.9.6"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}