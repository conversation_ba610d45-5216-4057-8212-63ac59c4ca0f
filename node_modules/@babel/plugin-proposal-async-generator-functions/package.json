{"name": "@babel/plugin-proposal-async-generator-functions", "version": "7.8.3", "description": "Turn async generator functions into ES2015 generators", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-async-generator-functions", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-remap-async-to-generator": "^7.8.3", "@babel/plugin-syntax-async-generators": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}