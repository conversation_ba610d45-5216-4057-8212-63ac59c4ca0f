{"name": "@babel/helper-simple-access", "version": "7.8.3", "description": "Babel helper for ensuring that access to a given value is performed through simple accesses", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-simple-access", "main": "lib/index.js", "dependencies": {"@babel/template": "^7.8.3", "@babel/types": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}