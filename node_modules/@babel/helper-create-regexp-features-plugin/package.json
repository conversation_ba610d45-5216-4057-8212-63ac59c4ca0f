{"name": "@babel/helper-create-regexp-features-plugin", "version": "7.8.8", "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "description": "Compile ESNext Regular Expressions to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "main": "lib/index.js", "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.8.3", "@babel/helper-regex": "^7.8.3", "regexpu-core": "^4.7.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.8.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "c831a2450dbf252c75750a455c63e1016c2f2244"}