{"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.8.3", "description": "Compile regular expressions using named groups to ES5.", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3", "core-js": "^3.2.1", "core-js-pure": "^3.2.1"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}