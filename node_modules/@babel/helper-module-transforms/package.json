{"name": "@babel/helper-module-transforms", "version": "7.9.0", "description": "Babel helper functions for implementing ES6 module transformations", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "main": "lib/index.js", "dependencies": {"@babel/helper-module-imports": "^7.8.3", "@babel/helper-replace-supers": "^7.8.6", "@babel/helper-simple-access": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3", "@babel/template": "^7.8.6", "@babel/types": "^7.9.0", "lodash": "^4.17.13"}, "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02"}