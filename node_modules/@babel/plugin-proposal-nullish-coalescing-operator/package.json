{"name": "@babel/plugin-proposal-nullish-coalescing-operator", "version": "7.8.3", "description": "Remove nullish coalescing operator", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-nullish-coalescing-operator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}