{"name": "@babel/traverse", "version": "7.9.6", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-traverse", "main": "lib/index.js", "dependencies": {"@babel/code-frame": "^7.8.3", "@babel/generator": "^7.9.6", "@babel/helper-function-name": "^7.9.5", "@babel/helper-split-export-declaration": "^7.8.3", "@babel/parser": "^7.9.6", "@babel/types": "^7.9.6", "debug": "^4.1.0", "globals": "^11.1.0", "lodash": "^4.17.13"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}