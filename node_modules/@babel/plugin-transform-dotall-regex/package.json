{"name": "@babel/plugin-transform-dotall-regex", "version": "7.8.3", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}