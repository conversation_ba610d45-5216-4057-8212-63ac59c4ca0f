{"name": "@babel/plugin-transform-object-super", "version": "7.8.3", "description": "Compile ES2015 object super to ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-replace-supers": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}