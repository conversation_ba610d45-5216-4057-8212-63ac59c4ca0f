{"name": "@babel/helper-remap-async-to-generator", "version": "7.8.3", "description": "Helper function to remap async functions to generators", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-remap-async-to-generator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-annotate-as-pure": "^7.8.3", "@babel/helper-wrap-function": "^7.8.3", "@babel/template": "^7.8.3", "@babel/traverse": "^7.8.3", "@babel/types": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}