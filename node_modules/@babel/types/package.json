{"name": "@babel/types", "version": "7.9.6", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-types", "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"@babel/helper-validator-identifier": "^7.9.5", "lodash": "^4.17.13", "to-fast-properties": "^2.0.0"}, "devDependencies": {"@babel/generator": "^7.9.6", "@babel/parser": "^7.9.6"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d"}