{"name": "@babel/plugin-transform-function-name", "version": "7.8.3", "description": "Apply ES2015 function.name semantics to all functions", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}