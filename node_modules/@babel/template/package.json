{"name": "@babel/template", "version": "7.8.6", "description": "Generate an AST from a string template.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-template", "main": "lib/index.js", "dependencies": {"@babel/code-frame": "^7.8.3", "@babel/parser": "^7.8.6", "@babel/types": "^7.8.6"}, "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b"}