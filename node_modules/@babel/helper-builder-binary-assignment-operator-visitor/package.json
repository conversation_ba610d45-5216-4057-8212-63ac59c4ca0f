{"name": "@babel/helper-builder-binary-assignment-operator-visitor", "version": "7.8.3", "description": "Helper function to build binary assignment operator visitors", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-builder-binary-assignment-operator-visitor", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-explode-assignable-expression": "^7.8.3", "@babel/types": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}