{"name": "@babel/code-frame", "version": "7.8.3", "description": "Generate errors that contain a code frame that point to source locations.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "main": "lib/index.js", "dependencies": {"@babel/highlight": "^7.8.3"}, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017"}