{"name": "prompts", "version": "2.3.2", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": "terkelg/prompts", "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib", "dist", "index.js"], "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.4"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.7", "@babel/plugin-proposal-object-rest-spread": "^7.8.3", "@babel/preset-env": "^7.8.7", "tap-spec": "^5.0.0", "tape": "^4.13.2"}, "engines": {"node": ">= 6"}}