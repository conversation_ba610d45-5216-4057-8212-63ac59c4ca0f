<template>
  <view class="k-page" :style="pageStyle">
    <!-- 自定义header插槽 -->
    <view v-if="showHeader" class="k-page-header" :style="headerStyle">
      <div v-if="title">{{ title }}</div>
      <slot v-else name="header-left" />
      <slot name="header-right" />
    </view>
    <!-- 页面内容区域 -->
    <view class="k-page-content">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'k-page',
  props: {
    title: {
      type: Boolean,
      default: '',
    },
    showHeader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      pageStyle: '',
      headerStyle: {},
    };
  },
  mounted() {
    this.initStyle();
  },
  methods: {
    initStyle() {
      // 在 mPaas 中获取系统信息
      const systemInfo = my.getSystemInfoSync();
      const { statusBarHeight } = systemInfo;

      this.pageStyle = `padding-top: ${statusBarHeight};padding-bottom: ${systemInfo.screenHeight - systemInfo.safeArea.bottom};`;

      if (this.showHeader) {
        const { statusBarHeight } = systemInfo;
        this.headerStyle = `padding-left: ${statusBarHeight};padding-right: ${statusBarHeight * 2}; height: ${statusBarHeight};`;
      }
    },
  },
};
</script>

<style scoped>
.k-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.k-page-header {
  display: flex;
  align-items: center;
}

.k-page-content {
  flex: 1;
  height: 0;
  overflow: hidden;
  box-sizing: border-box;
}
</style>
