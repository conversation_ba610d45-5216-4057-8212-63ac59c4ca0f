<template>
  <view class="travel-application">
    <!-- 导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="back-icon">‹</text>
        </view>
        <view class="navbar-title">
          <text class="title-text">出差申请信息</text>
        </view>
        <view class="navbar-right" @click="handleSave">
          <text class="save-text">保存</text>
        </view>
      </view>
    </view>

    <view class="form-container">
      <!-- 起始时间 -->
      <view class="form-item">
        <text class="label">起始时间</text>
        <input
          class="custom-input"
          v-model="formData.startTime"
          placeholder="请选择起始时间"
          @tap="showStartTimePicker = true"
          readonly
        />
      </view>

      <!-- 结束时间 -->
      <view class="form-item">
        <text class="label">结束时间</text>
        <input
          class="custom-input"
          v-model="formData.endTime"
          placeholder="请选择结束时间"
          @tap="showEndTimePicker = true"
          readonly
        />
      </view>

      <!-- 出差目的 -->
      <view class="form-item">
        <text class="label">出差目的</text>
        <view class="custom-input-wrapper" @tap="showPurposePicker = true">
          <input
            class="custom-input"
            v-model="formData.purpose"
            placeholder="请选择出差目的"
            readonly
          />
          <text class="arrow-icon">▼</text>
        </view>
      </view>

      <!-- 出差项目 项目名称 -->
      <view class="form-item">
        <text class="label">出差项目 项目名称</text>
        <input
          class="custom-input"
          v-model="formData.projectName"
          placeholder="请输入项目名称"
        />
      </view>

      <!-- 项目预算 -->
      <view class="form-item">
        <text class="label">项目预算</text>
        <input
          class="custom-input"
          v-model="formData.budget"
          placeholder="请输入项目预算"
          type="number"
        />
      </view>

      <!-- 国内国际 -->
      <view class="form-item">
        <text class="label">国内国际</text>
        <view class="custom-input-wrapper" @tap="showTravelTypePicker = true">
          <input
            class="custom-input"
            v-model="formData.travelType"
            placeholder="请选择国内国际"
            readonly
          />
          <text class="arrow-icon">▼</text>
        </view>
      </view>

      <!-- 是否乘坐飞机 -->
      <view class="form-item radio-item">
        <text class="label">是否乘坐飞机</text>
        <view class="radio-group">
          <radio-group @change="onFlightChange">
            <label class="radio-label">
              <radio value="yes" :checked="formData.isFlight === 'yes'" />
              <text class="radio-text">是</text>
            </label>
            <label class="radio-label">
              <radio value="no" :checked="formData.isFlight === 'no'" />
              <text class="radio-text">否</text>
            </label>
          </radio-group>
        </view>
      </view>

      <!-- 是否长差 -->
      <view class="form-item radio-item">
        <text class="label">是否长差(长差31天及以上)</text>
        <view class="radio-group">
          <radio-group @change="onLongTripChange">
            <label class="radio-label">
              <radio value="no" :checked="formData.isLongTrip === 'no'" />
              <text class="radio-text">否</text>
            </label>
          </radio-group>
        </view>
      </view>

      <!-- 出行人 -->
      <view class="form-item">
        <text class="label">出行人</text>
        <view class="traveler-section">
          <view class="add-traveler" @click="addTraveler">
            <text class="plus-icon">+</text>
          </view>
        </view>
      </view>

      <!-- 详细说明 -->
      <view class="form-item">
        <text class="label">详细说明</text>
        <textarea
          class="custom-textarea"
          v-model="formData.description"
          placeholder="请输入详细说明"
        ></textarea>
      </view>

      <!-- 添加出差城市 -->
      <view class="form-item">
        <view class="add-city-header">
          <text class="plus-icon">+</text>
          <text class="add-city-text">添加出差城市</text>
        </view>
        <view class="city-tags">
          <view
            class="city-tag"
            v-for="(city, index) in formData.cities"
            :key="index"
          >
            <text class="city-name">{{ city }}</text>
            <text class="close-icon" @tap="removeCity(index)">×</text>
          </view>
        </view>
      </view>

      <!-- 附件 -->
      <view class="form-item">
        <text class="label">附件</text>
        <view class="attachment-section">
          <view class="attachment-placeholder" @click="selectAttachment">
            <text class="photo-icon">📷</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间选择器 -->
    <picker
      v-if="showStartTimePicker"
      mode="date"
      @change="confirmStartTime"
      @cancel="showStartTimePicker = false"
    >
      <view></view>
    </picker>

    <picker
      v-if="showEndTimePicker"
      mode="date"
      @change="confirmEndTime"
      @cancel="showEndTimePicker = false"
    >
      <view></view>
    </picker>

    <!-- 出差目的选择器 -->
    <picker
      v-if="showPurposePicker"
      :range="purposeList"
      @change="confirmPurpose"
      @cancel="showPurposePicker = false"
    >
      <view></view>
    </picker>

    <!-- 国内国际选择器 -->
    <picker
      v-if="showTravelTypePicker"
      :range="travelTypeList"
      @change="confirmTravelType"
      @cancel="showTravelTypePicker = false"
    >
      <view></view>
    </picker>
  </view>
</template>

<script>
export default {
  name: "TravelApplication",
  data() {
    return {
      formData: {
        startTime: "",
        endTime: "",
        purpose: "",
        projectName: "",
        budget: "",
        travelType: "",
        isFlight: "no",
        isLongTrip: "no",
        travelers: [],
        description: "",
        cities: ["北京", "上海"],
        attachments: [],
      },
      showStartTimePicker: false,
      showEndTimePicker: false,
      showPurposePicker: false,
      showTravelTypePicker: false,
      startTimeValue: Number(new Date()),
      endTimeValue: Number(new Date()),
      purposeOptions: [
        ["商务洽谈", "技术交流", "培训学习", "会议参加", "项目实施", "其他"],
      ],
      travelTypeOptions: [["国内", "国际"]],
      // 支付宝小程序原生picker数据
      purposeList: [
        "商务洽谈",
        "技术交流",
        "培训学习",
        "会议参加",
        "项目实施",
        "其他",
      ],
      travelTypeList: ["国内", "国际"],
    };
  },
  methods: {
    handleSave() {
      console.log("保存出差申请信息:", this.formData);
      uni.showToast({
        title: "保存成功",
        icon: "success",
      });
    },
    confirmStartTime(e) {
      this.formData.startTime = e.detail.value;
      this.showStartTimePicker = false;
    },
    confirmEndTime(e) {
      this.formData.endTime = e.detail.value;
      this.showEndTimePicker = false;
    },
    confirmPurpose(e) {
      this.formData.purpose = this.purposeList[e.detail.value];
      this.showPurposePicker = false;
    },
    confirmTravelType(e) {
      this.formData.travelType = this.travelTypeList[e.detail.value];
      this.showTravelTypePicker = false;
    },
    addTraveler() {
      console.log("添加出行人");
      // 这里可以跳转到选择人员页面或弹出选择框
    },
    removeCity(index) {
      this.formData.cities.splice(index, 1);
    },
    selectAttachment() {
      console.log("选择附件");
      // 这里可以调用文件选择API
    },
    onFlightChange(e) {
      this.formData.isFlight = e.detail.value;
    },
    onLongTripChange(e) {
      this.formData.isLongTrip = e.detail.value;
    },
    goBack() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
.travel-application {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.save-btn {
  padding: 0 16px;
}

.save-text {
  color: #007aff;
  font-size: 16px;
}

.form-container {
  padding: 12px 16px;
}

.form-item {
  background-color: #ffffff;
  margin-bottom: 12px;
  padding: 16px;
  border-radius: 8px;

  .label {
    display: block;
    color: #333333;
    font-size: 16px;
    margin-bottom: 12px;
    font-weight: 500;
  }

  &.radio-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      margin-bottom: 0;
      flex: 1;
    }

    .radio-group {
      flex-shrink: 0;
    }
  }
}

.traveler-section {
  .add-traveler {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px dashed #cccccc;
    border-radius: 20px;
  }
}

.add-city-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .add-city-text {
    margin-left: 8px;
    color: #007aff;
    font-size: 16px;
  }
}

.city-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.city-tag {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  padding: 6px 12px;
  border-radius: 16px;

  .city-name {
    margin-right: 6px;
    color: #333333;
    font-size: 14px;
  }
}

.attachment-section {
  .attachment-placeholder {
    width: 80px;
    height: 80px;
    border: 1px dashed #cccccc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fafafa;
  }
}

.radio-label {
  display: inline-flex;
  align-items: center;
  margin-right: 32px;

  .radio-text {
    margin-left: 8px;
    color: #333333;
    font-size: 16px;
  }
}

.custom-navbar {
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    padding: 0 16px;

    .navbar-left {
      width: 60px;

      .back-icon {
        font-size: 24px;
        color: #333333;
        font-weight: bold;
      }
    }

    .navbar-title {
      flex: 1;
      text-align: center;

      .title-text {
        color: #333333;
        font-size: 18px;
        font-weight: 500;
      }
    }

    .navbar-right {
      width: 60px;
      text-align: right;
    }
  }
}

// 原生组件样式
.custom-input {
  width: 100%;
  height: 40px;
  padding: 0 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 16px;
  color: #333333;
  background-color: #ffffff;
}

.custom-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;

  .custom-input {
    flex: 1;
    padding-right: 40px;
  }

  .arrow-icon {
    position: absolute;
    right: 12px;
    color: #999999;
    font-size: 12px;
  }
}

.custom-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 16px;
  color: #333333;
  background-color: #ffffff;
  resize: none;
}

.plus-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: #007aff;
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
}

.close-icon {
  margin-left: 6px;
  color: #999999;
  font-size: 16px;
  font-weight: bold;
}

.photo-icon {
  font-size: 32px;
  color: #cccccc;
}
</style>
