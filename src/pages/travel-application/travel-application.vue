<template>
  <view class="travel-application">
    <!-- 导航栏 -->
    <!-- #ifdef MP-ALIPAY -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="back-icon">‹</text>
        </view>
        <view class="navbar-title">
          <text class="title-text">出差申请信息</text>
        </view>
        <view class="navbar-right" @click="handleSave">
          <text class="save-text">保存</text>
        </view>
      </view>
    </view>
    <!-- #endif -->
    <!-- #ifndef MP-ALIPAY -->
    <u-navbar
      :is-back="true"
      title="出差申请信息"
      :background="{ backgroundColor: '#ffffff' }"
      :title-style="{ color: '#333333', fontSize: '18px', fontWeight: '500' }"
    >
      <view slot="right" class="save-btn" @click="handleSave">
        <text class="save-text">保存</text>
      </view>
    </u-navbar>
    <!-- #endif -->

    <view class="form-container">
      <!-- 起始时间 -->
      <view class="form-item">
        <text class="label">起始时间</text>
        <!-- #ifdef MP-ALIPAY -->
        <input
          class="custom-input"
          v-model="formData.startTime"
          placeholder="请选择起始时间"
          @tap="showStartTimePicker = true"
          readonly
        />
        <!-- #endif -->
        <!-- #ifndef MP-ALIPAY -->
        <u-input
          v-model="formData.startTime"
          placeholder="请选择起始时间"
          :border="false"
          @click="showStartTimePicker = true"
          readonly
        />
        <!-- #endif -->
      </view>

      <!-- 结束时间 -->
      <view class="form-item">
        <text class="label">结束时间</text>
        <!-- #ifdef MP-ALIPAY -->
        <input
          class="custom-input"
          v-model="formData.endTime"
          placeholder="请选择结束时间"
          @tap="showEndTimePicker = true"
          readonly
        />
        <!-- #endif -->
        <!-- #ifndef MP-ALIPAY -->
        <u-input
          v-model="formData.endTime"
          placeholder="请选择结束时间"
          :border="false"
          @click="showEndTimePicker = true"
          readonly
        />
        <!-- #endif -->
      </view>

      <!-- 出差目的 -->
      <view class="form-item">
        <text class="label">出差目的</text>
        <!-- #ifdef MP-ALIPAY -->
        <view class="custom-input-wrapper" @tap="showPurposePicker = true">
          <input
            class="custom-input"
            v-model="formData.purpose"
            placeholder="请选择出差目的"
            readonly
          />
          <text class="arrow-icon">▼</text>
        </view>
        <!-- #endif -->
        <!-- #ifndef MP-ALIPAY -->
        <u-input
          v-model="formData.purpose"
          placeholder="请选择出差目的"
          :border="false"
          suffix-icon="arrow-down"
          @click="showPurposePicker = true"
          readonly
        />
        <!-- #endif -->
      </view>

      <!-- 出差项目 项目名称 -->
      <view class="form-item">
        <text class="label">出差项目 项目名称</text>
        <!-- #ifdef MP-ALIPAY -->
        <input
          class="custom-input"
          v-model="formData.projectName"
          placeholder="请输入项目名称"
        />
        <!-- #endif -->
        <!-- #ifndef MP-ALIPAY -->
        <u-input
          v-model="formData.projectName"
          placeholder="请输入项目名称"
          :border="false"
        />
        <!-- #endif -->
      </view>

      <!-- 项目预算 -->
      <view class="form-item">
        <text class="label">项目预算</text>
        <!-- #ifdef MP-ALIPAY -->
        <input
          class="custom-input"
          v-model="formData.budget"
          placeholder="请输入项目预算"
          type="number"
        />
        <!-- #endif -->
        <!-- #ifndef MP-ALIPAY -->
        <u-input
          v-model="formData.budget"
          placeholder="请输入项目预算"
          :border="false"
          type="number"
        />
        <!-- #endif -->
      </view>

      <!-- 国内国际 -->
      <view class="form-item">
        <text class="label">国内国际</text>
        <!-- #ifdef MP-ALIPAY -->
        <view class="custom-input-wrapper" @tap="showTravelTypePicker = true">
          <input
            class="custom-input"
            v-model="formData.travelType"
            placeholder="请选择国内国际"
            readonly
          />
          <text class="arrow-icon">▼</text>
        </view>
        <!-- #endif -->
        <!-- #ifndef MP-ALIPAY -->
        <u-input
          v-model="formData.travelType"
          placeholder="请选择国内国际"
          :border="false"
          suffix-icon="arrow-down"
          @click="showTravelTypePicker = true"
          readonly
        />
        <!-- #endif -->
      </view>

      <!-- 是否乘坐飞机 -->
      <view class="form-item radio-item">
        <text class="label">是否乘坐飞机</text>
        <view class="radio-group">
          <!-- #ifdef MP-ALIPAY -->
          <radio-group @change="onFlightChange">
            <label class="radio-label">
              <radio value="yes" :checked="formData.isFlight === 'yes'" />
              <text class="radio-text">是</text>
            </label>
            <label class="radio-label">
              <radio value="no" :checked="formData.isFlight === 'no'" />
              <text class="radio-text">否</text>
            </label>
          </radio-group>
          <!-- #endif -->
          <!-- #ifndef MP-ALIPAY -->
          <u-radio-group v-model="formData.isFlight" placement="row">
            <u-radio
              :customStyle="{ marginRight: '32px' }"
              label="是"
              name="yes"
            ></u-radio>
            <u-radio label="否" name="no"></u-radio>
          </u-radio-group>
          <!-- #endif -->
        </view>
      </view>

      <!-- 是否长差 -->
      <view class="form-item radio-item">
        <text class="label">是否长差(长差31天及以上)</text>
        <view class="radio-group">
          <!-- #ifdef MP-ALIPAY -->
          <radio-group @change="onLongTripChange">
            <label class="radio-label">
              <radio value="no" :checked="formData.isLongTrip === 'no'" />
              <text class="radio-text">否</text>
            </label>
          </radio-group>
          <!-- #endif -->
          <!-- #ifndef MP-ALIPAY -->
          <u-radio-group v-model="formData.isLongTrip" placement="row">
            <u-radio label="否" name="no"></u-radio>
          </u-radio-group>
          <!-- #endif -->
        </view>
      </view>

      <!-- 出行人 -->
      <view class="form-item">
        <text class="label">出行人</text>
        <view class="traveler-section">
          <view class="add-traveler" @click="addTraveler">
            <!-- #ifdef MP-ALIPAY -->
            <text class="plus-icon">+</text>
            <!-- #endif -->
            <!-- #ifndef MP-ALIPAY -->
            <u-icon name="plus-circle" size="20" color="#007AFF"></u-icon>
            <!-- #endif -->
          </view>
        </view>
      </view>

      <!-- 详细说明 -->
      <view class="form-item">
        <text class="label">详细说明</text>
        <!-- #ifdef MP-ALIPAY -->
        <textarea
          class="custom-textarea"
          v-model="formData.description"
          placeholder="请输入详细说明"
        ></textarea>
        <!-- #endif -->
        <!-- #ifndef MP-ALIPAY -->
        <u-textarea
          v-model="formData.description"
          placeholder="请输入详细说明"
          :border="false"
          height="80"
        />
        <!-- #endif -->
      </view>

      <!-- 添加出差城市 -->
      <view class="form-item">
        <view class="add-city-header">
          <!-- #ifdef MP-ALIPAY -->
          <text class="plus-icon">+</text>
          <!-- #endif -->
          <!-- #ifndef MP-ALIPAY -->
          <u-icon name="plus-circle" size="20" color="#007AFF"></u-icon>
          <!-- #endif -->
          <text class="add-city-text">添加出差城市</text>
        </view>
        <view class="city-tags">
          <view
            class="city-tag"
            v-for="(city, index) in formData.cities"
            :key="index"
          >
            <text class="city-name">{{ city }}</text>
            <!-- #ifdef MP-ALIPAY -->
            <text class="close-icon" @tap="removeCity(index)">×</text>
            <!-- #endif -->
            <!-- #ifndef MP-ALIPAY -->
            <u-icon
              name="close-circle-fill"
              size="16"
              color="#999999"
              @click="removeCity(index)"
            ></u-icon>
            <!-- #endif -->
          </view>
        </view>
      </view>

      <!-- 附件 -->
      <view class="form-item">
        <text class="label">附件</text>
        <view class="attachment-section">
          <view class="attachment-placeholder" @click="selectAttachment">
            <!-- #ifdef MP-ALIPAY -->
            <text class="photo-icon">📷</text>
            <!-- #endif -->
            <!-- #ifndef MP-ALIPAY -->
            <u-icon name="photo" size="40" color="#CCCCCC"></u-icon>
            <!-- #endif -->
          </view>
        </view>
      </view>
    </view>

    <!-- 时间选择器 -->
    <!-- #ifdef MP-ALIPAY -->
    <picker
      v-if="showStartTimePicker"
      mode="date"
      @change="confirmStartTime"
      @cancel="showStartTimePicker = false"
    >
      <view></view>
    </picker>

    <picker
      v-if="showEndTimePicker"
      mode="date"
      @change="confirmEndTime"
      @cancel="showEndTimePicker = false"
    >
      <view></view>
    </picker>

    <!-- 出差目的选择器 -->
    <picker
      v-if="showPurposePicker"
      :range="purposeList"
      @change="confirmPurpose"
      @cancel="showPurposePicker = false"
    >
      <view></view>
    </picker>

    <!-- 国内国际选择器 -->
    <picker
      v-if="showTravelTypePicker"
      :range="travelTypeList"
      @change="confirmTravelType"
      @cancel="showTravelTypePicker = false"
    >
      <view></view>
    </picker>
    <!-- #endif -->

    <!-- #ifndef MP-ALIPAY -->
    <u-datetime-picker
      :show="showStartTimePicker"
      v-model="startTimeValue"
      mode="datetime"
      @confirm="confirmStartTime"
      @cancel="showStartTimePicker = false"
    ></u-datetime-picker>

    <u-datetime-picker
      :show="showEndTimePicker"
      v-model="endTimeValue"
      mode="datetime"
      @confirm="confirmEndTime"
      @cancel="showEndTimePicker = false"
    ></u-datetime-picker>

    <!-- 出差目的选择器 -->
    <u-picker
      :show="showPurposePicker"
      :columns="purposeOptions"
      @confirm="confirmPurpose"
      @cancel="showPurposePicker = false"
    ></u-picker>

    <!-- 国内国际选择器 -->
    <u-picker
      :show="showTravelTypePicker"
      :columns="travelTypeOptions"
      @confirm="confirmTravelType"
      @cancel="showTravelTypePicker = false"
    ></u-picker>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  name: "TravelApplication",
  data() {
    return {
      formData: {
        startTime: "",
        endTime: "",
        purpose: "",
        projectName: "",
        budget: "",
        travelType: "",
        isFlight: "no",
        isLongTrip: "no",
        travelers: [],
        description: "",
        cities: ["北京", "上海"],
        attachments: [],
      },
      showStartTimePicker: false,
      showEndTimePicker: false,
      showPurposePicker: false,
      showTravelTypePicker: false,
      startTimeValue: Number(new Date()),
      endTimeValue: Number(new Date()),
      purposeOptions: [
        ["商务洽谈", "技术交流", "培训学习", "会议参加", "项目实施", "其他"],
      ],
      travelTypeOptions: [["国内", "国际"]],
      // 支付宝小程序原生picker数据
      purposeList: [
        "商务洽谈",
        "技术交流",
        "培训学习",
        "会议参加",
        "项目实施",
        "其他",
      ],
      travelTypeList: ["国内", "国际"],
    };
  },
  methods: {
    handleSave() {
      console.log("保存出差申请信息:", this.formData);
      uni.showToast({
        title: "保存成功",
        icon: "success",
      });
    },
    confirmStartTime(e) {
      // #ifdef MP-ALIPAY
      this.formData.startTime = e.detail.value;
      this.showStartTimePicker = false;
      // #endif
      // #ifndef MP-ALIPAY
      this.formData.startTime = uni.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM");
      this.showStartTimePicker = false;
      // #endif
    },
    confirmEndTime(e) {
      // #ifdef MP-ALIPAY
      this.formData.endTime = e.detail.value;
      this.showEndTimePicker = false;
      // #endif
      // #ifndef MP-ALIPAY
      this.formData.endTime = uni.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM");
      this.showEndTimePicker = false;
      // #endif
    },
    confirmPurpose(e) {
      // #ifdef MP-ALIPAY
      this.formData.purpose = this.purposeList[e.detail.value];
      this.showPurposePicker = false;
      // #endif
      // #ifndef MP-ALIPAY
      this.formData.purpose = e.value[0];
      this.showPurposePicker = false;
      // #endif
    },
    confirmTravelType(e) {
      // #ifdef MP-ALIPAY
      this.formData.travelType = this.travelTypeList[e.detail.value];
      this.showTravelTypePicker = false;
      // #endif
      // #ifndef MP-ALIPAY
      this.formData.travelType = e.value[0];
      this.showTravelTypePicker = false;
      // #endif
    },
    addTraveler() {
      console.log("添加出行人");
      // 这里可以跳转到选择人员页面或弹出选择框
    },
    removeCity(index) {
      this.formData.cities.splice(index, 1);
    },
    selectAttachment() {
      console.log("选择附件");
      // 这里可以调用文件选择API
    },
    onFlightChange(e) {
      this.formData.isFlight = e.detail.value;
    },
    onLongTripChange(e) {
      this.formData.isLongTrip = e.detail.value;
    },
    goBack() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
.travel-application {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.save-btn {
  padding: 0 16px;
}

.save-text {
  color: #007aff;
  font-size: 16px;
}

.form-container {
  padding: 12px 16px;
}

.form-item {
  background-color: #ffffff;
  margin-bottom: 12px;
  padding: 16px;
  border-radius: 8px;

  .label {
    display: block;
    color: #333333;
    font-size: 16px;
    margin-bottom: 12px;
    font-weight: 500;
  }

  &.radio-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      margin-bottom: 0;
      flex: 1;
    }

    .radio-group {
      flex-shrink: 0;
    }
  }
}

.traveler-section {
  .add-traveler {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px dashed #cccccc;
    border-radius: 20px;
  }
}

.add-city-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .add-city-text {
    margin-left: 8px;
    color: #007aff;
    font-size: 16px;
  }
}

.city-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.city-tag {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  padding: 6px 12px;
  border-radius: 16px;

  .city-name {
    margin-right: 6px;
    color: #333333;
    font-size: 14px;
  }
}

.attachment-section {
  .attachment-placeholder {
    width: 80px;
    height: 80px;
    border: 1px dashed #cccccc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fafafa;
  }
}

.radio-label {
  display: inline-flex;
  align-items: center;
  margin-right: 32px;

  .radio-text {
    margin-left: 8px;
    color: #333333;
    font-size: 16px;
  }
}

.custom-navbar {
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    padding: 0 16px;

    .navbar-left {
      width: 60px;

      .back-icon {
        font-size: 24px;
        color: #333333;
        font-weight: bold;
      }
    }

    .navbar-title {
      flex: 1;
      text-align: center;

      .title-text {
        color: #333333;
        font-size: 18px;
        font-weight: 500;
      }
    }

    .navbar-right {
      width: 60px;
      text-align: right;
    }
  }
}
</style>
