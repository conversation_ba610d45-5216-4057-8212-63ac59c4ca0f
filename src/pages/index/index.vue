<template>
  <view class="content">
    <image class="logo" src="/static/logo.png"></image>
    <view>
      <text class="title">{{ title }}</text>
    </view>
    <view class="button-container">
      <u-button
        type="primary"
        text="出差申请信息"
        @click="goToTravelApplication"
        :custom-style="{ marginTop: '40px', width: '300px' }"
      ></u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: "Hello",
    };
  },
  onLoad() {},
  methods: {
    goToTravelApplication() {
      uni.navigateTo({
        url: "/pages/travel-application/travel-application",
      });
    },
  },
};
</script>

<style>
.content {
  text-align: center;
  height: 400upx;
}

.logo {
  height: 200upx;
  width: 200upx;
  margin-top: 200upx;
}

.title {
  font-size: 36upx;
  color: #8f8f94;
}
</style>
