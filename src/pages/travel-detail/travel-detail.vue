<template>
  <view class="travel-detail">
    <!-- 导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="back-icon">‹</text>
        </view>
        <view class="navbar-title">
          <view class="title-main">出差目的</view>
          <view class="title-sub">GHJK456789678 (出差申请单ID)</view>
        </view>
      </view>
    </view>

    <view class="content">
      <!-- 审批信息 -->
      <view class="section">
        <view class="section-title">审批信息</view>

        <view
          class="approval-item"
          v-for="(item, index) in approvalList"
          :key="index"
        >
          <view class="approval-left">
            <view class="approval-dot" :class="item.status"></view>
            <view class="approval-info">
              <text class="approval-name">{{ item.name }}</text>
              <text class="approval-time">{{ item.time }}</text>
            </view>
          </view>
          <view class="approval-right">
            <text class="approval-status">{{ item.statusText }}</text>
          </view>
        </view>
      </view>

      <!-- 出差申请信息 -->
      <view class="section">
        <view class="section-title">出差申请信息</view>

        <view class="info-item">
          <text class="info-label">出差时间范围</text>
          <text class="info-value">{{ travelInfo.dateRange }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">出差项目</text>
          <text class="info-value">{{ travelInfo.project }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">出差预算</text>
          <text class="info-value">{{ travelInfo.budget }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">国内国际</text>
          <text class="info-value">{{ travelInfo.type }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">是否长差</text>
          <text class="info-value">{{ travelInfo.isLongTrip }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">出行人</text>
          <text class="info-value">{{ travelInfo.travelers }}</text>
        </view>
      </view>

      <!-- 出差城市 -->
      <view class="section">
        <view class="section-title">出差城市</view>
        <view class="city-tags">
          <view
            class="city-tag"
            v-for="(city, index) in travelInfo.cities"
            :key="index"
          >
            <text class="city-name">{{ city }}</text>
          </view>
        </view>
      </view>

      <!-- 附件 -->
      <view class="section">
        <view class="section-title">附件</view>
        <view class="attachment-list">
          <view
            class="attachment-item"
            v-for="(file, index) in travelInfo.attachments"
            :key="index"
          >
            <view class="attachment-icon">
              <text class="photo-icon">📷</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-item" @click="handleDelete">
        <view class="action-icon">
          <text class="icon-text">🗑</text>
        </view>
        <text class="action-text">删除</text>
      </view>

      <view class="action-item" @click="handleEdit">
        <view class="action-icon">
          <text class="icon-text">✏️</text>
        </view>
        <text class="action-text">修改</text>
      </view>

      <view class="action-item" @click="handleRevoke">
        <view class="action-icon">
          <text class="icon-text">↩️</text>
        </view>
        <text class="action-text">撤回</text>
      </view>

      <view class="action-item" @click="handleSubmit">
        <view class="action-icon">
          <text class="icon-text">📋</text>
        </view>
        <text class="action-text">提交审批</text>
      </view>

      <view class="action-item" @click="handleCreateReport">
        <view class="action-icon">
          <text class="icon-text">📊</text>
        </view>
        <text class="action-text">创建报销单</text>
      </view>

      <view class="action-item" @click="handleSendEmail">
        <view class="action-icon">
          <text class="icon-text">📧</text>
        </view>
        <text class="action-text">发送邮件</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "TravelDetail",
  data() {
    return {
      approvalList: [
        {
          name: "审批人姓名",
          time: "时间",
          status: "pending",
          statusText: "审批状态",
        },
        {
          name: "审批人姓名",
          time: "时间",
          status: "pending",
          statusText: "审批状态",
        },
      ],
      travelInfo: {
        dateRange: "2025/03/25-2025/06/01",
        project: "测试项目",
        budget: "10000.00",
        type: "国内",
        isLongTrip: "否",
        travelers: "张三",
        cities: ["城市", "城市"],
        attachments: [{}], // 一个附件
      },
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    handleDelete() {
      uni.showModal({
        title: "确认删除",
        content: "确定要删除这个出差申请吗？",
        success: (res) => {
          if (res.confirm) {
            console.log("删除申请");
            uni.showToast({
              title: "删除成功",
              icon: "success",
            });
          }
        },
      });
    },
    handleEdit() {
      console.log("修改申请");
      uni.navigateTo({
        url: "/pages/travel-application/travel-application",
      });
    },
    handleRevoke() {
      uni.showModal({
        title: "确认撤回",
        content: "确定要撤回这个出差申请吗？",
        success: (res) => {
          if (res.confirm) {
            console.log("撤回申请");
            uni.showToast({
              title: "撤回成功",
              icon: "success",
            });
          }
        },
      });
    },
    handleSubmit() {
      console.log("提交审批");
      uni.showToast({
        title: "提交成功",
        icon: "success",
      });
    },
    handleCreateReport() {
      console.log("创建报销单");
      uni.showToast({
        title: "创建报销单",
        icon: "none",
      });
    },
    handleSendEmail() {
      console.log("发送邮件");
      uni.showToast({
        title: "发送邮件",
        icon: "none",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.travel-detail {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120px;
}

.custom-navbar {
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;

  .navbar-content {
    display: flex;
    align-items: center;
    height: 60px;
    padding: 0 16px;

    .navbar-left {
      width: 40px;

      .back-icon {
        font-size: 24px;
        color: #333333;
        font-weight: bold;
      }
    }

    .navbar-title {
      flex: 1;
      margin-left: 12px;

      .title-main {
        color: #333333;
        font-size: 18px;
        font-weight: 500;
        line-height: 24px;
      }

      .title-sub {
        color: #666666;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
}

.content {
  padding: 16px;
}

.section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;

  .section-title {
    color: #333333;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }
}

.approval-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .approval-left {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .approval-dot {
    width: 12px;
    height: 12px;
    border-radius: 6px;
    margin-right: 12px;

    &.pending {
      background-color: #8b5cf6;
    }

    &.approved {
      background-color: #10b981;
    }

    &.rejected {
      background-color: #ef4444;
    }
  }

  .approval-info {
    display: flex;
    flex-direction: column;

    .approval-name {
      color: #333333;
      font-size: 16px;
      line-height: 22px;
    }

    .approval-time {
      color: #666666;
      font-size: 14px;
      line-height: 20px;
    }
  }

  .approval-right {
    .approval-status {
      color: #666666;
      font-size: 14px;
    }
  }
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .info-label {
    color: #333333;
    font-size: 16px;
    flex-shrink: 0;
  }

  .info-value {
    color: #666666;
    font-size: 16px;
    text-align: right;
    flex: 1;
    margin-left: 16px;
  }
}

.city-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.city-tag {
  background-color: #f0f0f0;
  padding: 6px 12px;
  border-radius: 16px;

  .city-name {
    color: #333333;
    font-size: 14px;
  }
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.attachment-item {
  .attachment-icon {
    width: 60px;
    height: 60px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fafafa;

    .photo-icon {
      font-size: 24px;
      color: #cccccc;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  padding: 12px 16px;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;

    .action-icon {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 4px;

      .icon-text {
        font-size: 18px;
      }
    }

    .action-text {
      color: #333333;
      font-size: 12px;
      text-align: center;
    }
  }
}
</style>
