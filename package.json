{"name": "mpaas-uniapp-tutorial", "version": "0.1.0", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production  UNI_PLATFORM=h5 UNI_OUTPUT_DIR=dist/h5 BUILD_ENV=ali-ide vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production  UNI_PLATFORM=mp-alipay UNI_OUTPUT_DIR=dist/alipay BUILD_ENV=ali-ide vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production  UNI_PLATFORM=mp-weixin UNI_OUTPUT_DIR=dist/wechat BUILD_ENV=ali-ide vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 BUILD_ENV=ali-ide vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay UNI_OUTPUT_DIR=dist/alipay BUILD_ENV=ali-ide vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin UNI_OUTPUT_DIR=dist/wechat BUILD_ENV=ali-ide vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i"}, "dependencies": {"@dcloudio/uni-app-plus": "^2.0.0-27520200518001", "@dcloudio/uni-h5": "^2.0.0-27520200518001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-alipay": "^2.0.0-27520200518001", "@dcloudio/uni-mp-baidu": "^2.0.0-27520200518001", "@dcloudio/uni-mp-qq": "^2.0.0-27520200518001", "@dcloudio/uni-mp-toutiao": "^2.0.0-27520200518001", "@dcloudio/uni-mp-weixin": "^2.0.0-27520200518001", "@dcloudio/uni-quickapp-native": "^2.0.0-27520200518001", "@dcloudio/uni-quickapp-webview": "^2.0.0-27520200518001", "@dcloudio/uni-stat": "^2.0.0-27520200518001", "core-js": "^3.6.4", "flyio": "^0.6.2", "regenerator-runtime": "^0.12.1", "uview-ui": "2.0.31", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/types": "*", "@dcloudio/uni-automator": "^2.0.0-27520200518001", "@dcloudio/uni-cli-shared": "^2.0.0-27520200518001", "@dcloudio/uni-migration": "^2.0.0-27520200518001", "@dcloudio/uni-template-compiler": "^2.0.0-27520200518001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-27520200518001", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-27520200518001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-27520200518001", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-27520200518001", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-27520200518001", "@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-service": "~4.3.0", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0", "sass": "1.32.13", "sass-loader": "^10.4.1", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}}