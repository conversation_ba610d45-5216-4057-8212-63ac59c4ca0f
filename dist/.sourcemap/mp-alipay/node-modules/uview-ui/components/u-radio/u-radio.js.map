{"version": 3, "sources": ["webpack:///./node_modules/uview-ui/components/u-radio/u-radio.vue?f694", "webpack:///./node_modules/uview-ui/components/u-radio/u-radio.vue?e56b", "webpack:///./node_modules/uview-ui/components/u-radio/u-radio.vue?42e0", "webpack:///./node_modules/uview-ui/components/u-radio/u-radio.vue?4a0e", "webpack:///./node_modules/uview-ui/components/u-radio/u-radio.vue", "webpack:///./node_modules/uview-ui/components/u-radio/u-radio.vue?ec76", "webpack:///./node_modules/uview-ui/components/u-radio/u-radio.vue?4e6c"], "names": ["name", "mixins", "uni", "$u", "mpMixin", "mixin", "props", "data", "checked", "parentData", "iconSize", "labelDisabled", "disabled", "shape", "activeColor", "inactiveColor", "size", "value", "iconColor", "placement", "borderBottom", "iconPlacement", "computed", "elDisabled", "el<PERSON>abelDisabled", "elSize", "elIconSize", "elActiveColor", "elInactiveColor", "elLabelColor", "labelColor", "elShape", "elLabelSize", "addUnit", "labelSize", "elIconColor", "iconClasses", "classes", "push", "join", "iconWrapStyle", "style", "backgroundColor", "borderColor", "width", "height", "marginRight", "radioStyle", "error", "paddingBottom", "os", "deepMerge", "addStyle", "customStyle", "mounted", "init", "methods", "updateParentData", "parent", "getParentData", "iconClickHandler", "e", "preventEvent", "setRadioCheckedStatus", "wrapperClickHandler", "labelClickHandler", "emitEvent", "$emit", "$nextTick", "formValidate", "unCheckedOther"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsH;AACtH,gBAAgB,mIAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,WAAW,qRAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAAmY,CAAgB,+ZAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmCvZ;;;;AAnCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;;;;;;;;;;;;;;;;;;;;eAqBe;AACdA,MAAI,EAAE,SADQ;AAGdC,QAAM,EAAE,CAACC,GAAG,CAACC,EAAJ,CAAOC,OAAR,EAAiBF,GAAG,CAACC,EAAJ,CAAOE,KAAxB,EAA8BC,cAA9B,CAHM;AAIdC,MAJc,kBAIP;AACN,WAAO;AACNC,aAAO,EAAE,KADH;AAEN;AACA;AACA;AACAC,gBAAU,EAAE;AACXC,gBAAQ,EAAE,EADC;AAEXC,qBAAa,EAAE,IAFJ;AAGXC,gBAAQ,EAAE,IAHC;AAIXC,aAAK,EAAE,IAJI;AAKXC,mBAAW,EAAE,IALF;AAMXC,qBAAa,EAAE,IANJ;AAOXC,YAAI,EAAE,EAPK;AAQXC,aAAK,EAAE,IARI;AASXC,iBAAS,EAAE,IATA;AAUXC,iBAAS,EAAE,KAVA;AAWXC,oBAAY,EAAE,KAXH;AAYXC,qBAAa,EAAE;AAZJ;AALN,KAAP;AAoBA,GAzBa;AA0BdC,UAAQ,EAAE;AACT;AACAC,cAFS,wBAEI;AACZ,aAAO,KAAKX,QAAL,KAAkB,EAAlB,GAAuB,KAAKA,QAA5B,GAAuC,KAAKH,UAAL,CAAgBG,QAAhB,KAA6B,IAA7B,GAAoC,KAAKH,UAAL,CAAgBG,QAApD,GAA+D,KAA7G;AACA,KAJQ;AAKT;AACAY,mBANS,6BAMS;AACjB,aAAO,KAAKb,aAAL,KAAuB,EAAvB,GAA4B,KAAKA,aAAjC,GAAiD,KAAKF,UAAL,CAAgBE,aAAhB,KAAkC,IAAlC,GAAyC,KAAKF,UAAL,CAAgBE,aAAzD,GACvD,KADD;AAEA,KATQ;AAUT;AACAc,UAXS,oBAWA;AACR,aAAO,KAAKT,IAAL,GAAY,KAAKA,IAAjB,GAAyB,KAAKP,UAAL,CAAgBO,IAAhB,GAAuB,KAAKP,UAAL,CAAgBO,IAAvC,GAA8C,EAA9E;AACA,KAbQ;AAcT;AACAU,cAfS,wBAeI;AACZ,aAAO,KAAKhB,QAAL,GAAgB,KAAKA,QAArB,GAAiC,KAAKD,UAAL,CAAgBC,QAAhB,GAA2B,KAAKD,UAAL,CAAgBC,QAA3C,GAAsD,EAA9F;AACA,KAjBQ;AAkBT;AACAiB,iBAnBS,2BAmBO;AACf,aAAO,KAAKb,WAAL,GAAmB,KAAKA,WAAxB,GAAuC,KAAKL,UAAL,CAAgBK,WAAhB,GAA8B,KAAKL,UAAL,CAAgBK,WAA9C,GAA4D,SAA1G;AACA,KArBQ;AAsBT;AACAc,mBAvBS,6BAuBS;AACjB,aAAO,KAAKb,aAAL,GAAqB,KAAKA,aAA1B,GAA2C,KAAKN,UAAL,CAAgBM,aAAhB,GAAgC,KAAKN,UAAL,CAAgBM,aAAhD,GACjD,SADD;AAEA,KA1BQ;AA2BT;AACAc,gBA5BS,0BA4BM;AACd,aAAO,KAAKC,UAAL,GAAkB,KAAKA,UAAvB,GAAqC,KAAKrB,UAAL,CAAgBqB,UAAhB,GAA6B,KAAKrB,UAAL,CAAgBqB,UAA7C,GAA0D,SAAtG;AACA,KA9BQ;AA+BT;AACAC,WAhCS,qBAgCC;AACT,aAAO,KAAKlB,KAAL,GAAa,KAAKA,KAAlB,GAA2B,KAAKJ,UAAL,CAAgBI,KAAhB,GAAwB,KAAKJ,UAAL,CAAgBI,KAAxC,GAAgD,QAAlF;AACA,KAlCQ;AAmCT;AACAmB,eApCS,yBAoCK;AACb,aAAO9B,GAAG,CAACC,EAAJ,CAAO8B,OAAP,CAAe,KAAKC,SAAL,GAAiB,KAAKA,SAAtB,GAAmC,KAAKzB,UAAL,CAAgByB,SAAhB,GAA4B,KAAKzB,UAAL,CAAgByB,SAA5C,GACxD,IADM,CAAP;AAEA,KAvCQ;AAwCTC,eAxCS,yBAwCK;AACb,UAAMjB,SAAS,GAAG,KAAKA,SAAL,GAAiB,KAAKA,SAAtB,GAAmC,KAAKT,UAAL,CAAgBS,SAAhB,GAA4B,KAAKT,UAAL,CAAgBS,SAA5C,GACpD,SADD,CADa,CAGb;;AACA,UAAI,KAAKK,UAAT,EAAqB;AACpB;AACA,eAAO,KAAKf,OAAL,GAAe,KAAKoB,eAApB,GAAsC,aAA7C;AACA,OAHD,MAGO;AACN,eAAO,KAAKpB,OAAL,GAAeU,SAAf,GAA2B,aAAlC;AACA;AACD,KAlDQ;AAmDTkB,eAnDS,yBAmDK;AACb,UAAIC,OAAO,GAAG,EAAd,CADa,CAEb;;AACAA,aAAO,CAACC,IAAR,CAAa,yBAAyB,KAAKP,OAA3C;;AACA,UAAI,KAAKR,UAAT,EAAqB;AACpBc,eAAO,CAACC,IAAR,CAAa,8BAAb;AACA;;AACD,UAAI,KAAK9B,OAAL,IAAgB,KAAKe,UAAzB,EAAqC;AACpCc,eAAO,CAACC,IAAR,CAAa,uCAAb;AACA,OATY,CAUb;;;AAEAD,aAAO,GAAGA,OAAO,CAACE,IAAR,CAAa,GAAb,CAAV;AAEA,aAAOF,OAAP;AACA,KAlEQ;AAmETG,iBAnES,2BAmEO;AACf;AACA,UAAMC,KAAK,GAAG,EAAd;AACAA,WAAK,CAACC,eAAN,GAAwB,KAAKlC,OAAL,IAAgB,CAAC,KAAKe,UAAtB,GAAmC,KAAKI,aAAxC,GAAwD,SAAhF;AACAc,WAAK,CAACE,WAAN,GAAoB,KAAKnC,OAAL,IAAgB,CAAC,KAAKe,UAAtB,GAAmC,KAAKI,aAAxC,GAAwD,KAAKC,eAAjF;AACAa,WAAK,CAACG,KAAN,GAAc1C,GAAG,CAACC,EAAJ,CAAO8B,OAAP,CAAe,KAAKR,MAApB,CAAd;AACAgB,WAAK,CAACI,MAAN,GAAe3C,GAAG,CAACC,EAAJ,CAAO8B,OAAP,CAAe,KAAKR,MAApB,CAAf,CANe,CAOf;;AACA,UAAI,KAAKhB,UAAL,CAAgBY,aAAhB,KAAkC,OAAtC,EAA+C;AAC9CoB,aAAK,CAACK,WAAN,GAAoB,CAApB;AACA;;AACD,aAAOL,KAAP;AACA,KA/EQ;AAgFTM,cAhFS,wBAgFI;AACZ,UAAMN,KAAK,GAAG,EAAd;;AACA,UAAG,KAAKhC,UAAL,CAAgBW,YAAhB,IAAgC,KAAKX,UAAL,CAAgBU,SAAhB,KAA8B,KAAjE,EAAwE;AACvEjB,WAAG,CAACC,EAAJ,CAAO6C,KAAP,CAAa,mEAAb;AACA,OAJW,CAKZ;;;AACA,UAAG,KAAKvC,UAAL,CAAgBW,YAAhB,IAAgC,KAAKX,UAAL,CAAgBU,SAAhB,KAA8B,QAAjE,EAA2E;AAC1E;AACAsB,aAAK,CAACQ,aAAN,GAAsB/C,GAAG,CAACC,EAAJ,CAAO+C,EAAP,OAAgB,KAAhB,GAAwB,MAAxB,GAAiC,KAAvD;AACA;;AACD,aAAOhD,GAAG,CAACC,EAAJ,CAAOgD,SAAP,CAAiBV,KAAjB,EAAwBvC,GAAG,CAACC,EAAJ,CAAOiD,QAAP,CAAgB,KAAKC,WAArB,CAAxB,CAAP;AACA;AA3FQ,GA1BI;AAuHdC,SAvHc,qBAuHJ;AACT,SAAKC,IAAL;AACA,GAzHa;AA0HdC,SAAO,EAAE;AACRD,QADQ,kBACD;AACN;AACA,WAAKE,gBAAL;;AACA,UAAI,CAAC,KAAKC,MAAV,EAAkB;AACjBxD,WAAG,CAACC,EAAJ,CAAO6C,KAAP,CAAa,8BAAb;AACA,OALK,CAMN;;;AACA,WAAKxC,OAAL,GAAe,KAAKR,IAAL,KAAc,KAAKS,UAAL,CAAgBQ,KAA7C;AACA,KATO;AAURwC,oBAVQ,8BAUW;AAClB,WAAKE,aAAL,CAAmB,eAAnB;AACA,KAZO;AAaR;AACAC,oBAdQ,4BAcSC,CAdT,EAcY;AACnB,WAAKC,YAAL,CAAkBD,CAAlB,EADmB,CAEnB;;AACA,UAAI,CAAC,KAAKtC,UAAV,EAAsB;AACrB,aAAKwC,qBAAL;AACA;AACD,KApBO;AAqBR;AACAC,uBAtBQ,+BAsBYH,CAtBZ,EAsBe;AACtB,WAAKpD,UAAL,CAAgBY,aAAhB,KAAkC,OAAlC,IAA6C,KAAKuC,gBAAL,CAAsBC,CAAtB,CAA7C;AACA,KAxBO;AAyBR;AACAI,qBA1BQ,6BA0BUJ,CA1BV,EA0Ba;AACpB,WAAKC,YAAL,CAAkBD,CAAlB,EADoB,CAEpB;;AACA,UAAI,CAAC,KAAKrC,eAAN,IAAyB,CAAC,KAAKD,UAAnC,EAA+C;AAC9C,aAAKwC,qBAAL;AACA;AACD,KAhCO;AAiCRG,aAjCQ,uBAiCI;AAAA;;AACX;AACA,UAAI,CAAC,KAAK1D,OAAV,EAAmB;AAClB,aAAK2D,KAAL,CAAW,QAAX,EAAqB,KAAKnE,IAA1B,EADkB,CAElB;;AACA,aAAKoE,SAAL,CAAe,YAAM;AACpBlE,aAAG,CAACC,EAAJ,CAAOkE,YAAP,CAAoB,KAApB,EAA0B,QAA1B;AACA,SAFD;AAGA;AACD,KA1CO;AA2CR;AACA;AACA;AACAN,yBA9CQ,mCA8CgB;AACvB,WAAKG,SAAL,GADuB,CAEvB;;AACA,WAAK1D,OAAL,GAAe,IAAf;AACA,aAAO,KAAKkD,MAAL,CAAYY,cAAnB,KAAsC,UAAtC,IAAoD,KAAKZ,MAAL,CAAYY,cAAZ,CAA2B,IAA3B,CAApD;AACA;AAnDO;AA1HK,C;;;;;;;;;;;;;;ACzDf;AAAA;AAAA;AAAA;AAA4uB,CAAgB,ytBAAG,EAAC,C;;;;;;;;;;;ACAhwB;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-radio/u-radio.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-radio.vue?vue&type=template&id=643b3322&scoped=true&\"\nvar renderjs\nimport script from \"./u-radio.vue?vue&type=script&lang=js&\"\nexport * from \"./u-radio.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-radio.vue?vue&type=style&index=0&id=643b3322&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"643b3322\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-radio/u-radio.vue\"\nexport default component.exports", "export * from \"-!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--14-0!../../../@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-radio.vue?vue&type=template&id=643b3322&scoped=true&\"", "var components = {\n  uIcon: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n    )\n  }\n}\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.radioStyle])\n\n  var s1 = _vm.__get_style([_vm.iconWrapStyle])\n\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1\n      }\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-radio.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-radio.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport props from './props.js';\n/**\n * radio 单选框\n * @description 单选框用于有一个选择，用户只能选择其中一个的场景。搭配u-radio-group使用\n * @tutorial https://www.uviewui.com/components/radio.html\n * @property {String | Number}\tname\t\t\tradio的名称\n * @property {String}\t\t\tshape\t\t\t形状，square为方形，circle为圆型\n * @property {Boolean}\t\t\tdisabled\t\t是否禁用\n * @property {String | Boolean}\tlabelDisabled\t是否禁止点击提示语选中单选框\n * @property {String}\t\t\tactiveColor\t\t选中时的颜色，如设置parent的active-color将失效\n * @property {String}\t\t\tinactiveColor\t未选中的颜色\n * @property {String | Number}\ticonSize\t\t图标大小，单位px\n * @property {String | Number}\tlabelSize\t\tlabel字体大小，单位px\n * @property {String | Number}\tlabel\t\t\tlabel提示文字，因为nvue下，直接slot进来的文字，由于特殊的结构，无法修改样式\n * @property {String | Number}\tsize\t\t\t整体的大小\n * @property {String}\t\t\ticonColor\t\t图标颜色\n * @property {String}\t\t\tlabelColor\t\tlabel的颜色\n * @property {Object}\t\t\tcustomStyle\t\t组件的样式，对象形式\n * \n * @event {Function} change 某个radio状态发生变化时触发(选中状态)\n * @example <u-radio :labelDisabled=\"false\">门掩黄昏，无计留春住</u-radio>\n */\nexport default {\n\tname: \"u-radio\",\n\t\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\tdata() {\n\t\treturn {\n\t\t\tchecked: false,\n\t\t\t// 当你看到这段代码的时候，\n\t\t\t// 父组件的默认值，因为头条小程序不支持在computed中使用this.parent.shape的形式\n\t\t\t// 故只能使用如此方法\n\t\t\tparentData: {\n\t\t\t\ticonSize: 12,\n\t\t\t\tlabelDisabled: null,\n\t\t\t\tdisabled: null,\n\t\t\t\tshape: null,\n\t\t\t\tactiveColor: null,\n\t\t\t\tinactiveColor: null,\n\t\t\t\tsize: 18,\n\t\t\t\tvalue: null,\n\t\t\t\ticonColor: null,\n\t\t\t\tplacement: 'row',\n\t\t\t\tborderBottom: false,\n\t\t\t\ticonPlacement: 'left'\n\t\t\t}\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 是否禁用，如果父组件u-raios-group禁用的话，将会忽略子组件的配置\n\t\telDisabled() {\n\t\t\treturn this.disabled !== '' ? this.disabled : this.parentData.disabled !== null ? this.parentData.disabled : false;\n\t\t},\n\t\t// 是否禁用label点击\n\t\telLabelDisabled() {\n\t\t\treturn this.labelDisabled !== '' ? this.labelDisabled : this.parentData.labelDisabled !== null ? this.parentData.labelDisabled :\n\t\t\t\tfalse;\n\t\t},\n\t\t// 组件尺寸，对应size的值，默认值为21px\n\t\telSize() {\n\t\t\treturn this.size ? this.size : (this.parentData.size ? this.parentData.size : 21);\n\t\t},\n\t\t// 组件的勾选图标的尺寸，默认12px\n\t\telIconSize() {\n\t\t\treturn this.iconSize ? this.iconSize : (this.parentData.iconSize ? this.parentData.iconSize : 12);\n\t\t},\n\t\t// 组件选中激活时的颜色\n\t\telActiveColor() {\n\t\t\treturn this.activeColor ? this.activeColor : (this.parentData.activeColor ? this.parentData.activeColor : '#2979ff');\n\t\t},\n\t\t// 组件选未中激活时的颜色\n\t\telInactiveColor() {\n\t\t\treturn this.inactiveColor ? this.inactiveColor : (this.parentData.inactiveColor ? this.parentData.inactiveColor :\n\t\t\t\t'#c8c9cc');\n\t\t},\n\t\t// label的颜色\n\t\telLabelColor() {\n\t\t\treturn this.labelColor ? this.labelColor : (this.parentData.labelColor ? this.parentData.labelColor : '#606266')\n\t\t},\n\t\t// 组件的形状\n\t\telShape() {\n\t\t\treturn this.shape ? this.shape : (this.parentData.shape ? this.parentData.shape : 'circle');\n\t\t},\n\t\t// label大小\n\t\telLabelSize() {\n\t\t\treturn uni.$u.addUnit(this.labelSize ? this.labelSize : (this.parentData.labelSize ? this.parentData.labelSize :\n\t\t\t\t'15'))\n\t\t},\n\t\telIconColor() {\n\t\t\tconst iconColor = this.iconColor ? this.iconColor : (this.parentData.iconColor ? this.parentData.iconColor :\n\t\t\t\t'#ffffff');\n\t\t\t// 图标的颜色\n\t\t\tif (this.elDisabled) {\n\t\t\t\t// disabled状态下，已勾选的radio图标改为elInactiveColor\n\t\t\t\treturn this.checked ? this.elInactiveColor : 'transparent'\n\t\t\t} else {\n\t\t\t\treturn this.checked ? iconColor : 'transparent'\n\t\t\t}\n\t\t},\n\t\ticonClasses() {\n\t\t\tlet classes = []\n\t\t\t// 组件的形状\n\t\t\tclasses.push('u-radio__icon-wrap--' + this.elShape)\n\t\t\tif (this.elDisabled) {\n\t\t\t\tclasses.push('u-radio__icon-wrap--disabled')\n\t\t\t}\n\t\t\tif (this.checked && this.elDisabled) {\n\t\t\t\tclasses.push('u-radio__icon-wrap--disabled--checked')\n\t\t\t}\n\t\t\t// 支付宝，头条小程序无法动态绑定一个数组类名，否则解析出来的结果会带有\",\"，而导致失效\n\n\t\t\tclasses = classes.join(' ')\n\n\t\t\treturn classes\n\t\t},\n\t\ticonWrapStyle() {\n\t\t\t// radio的整体样式\n\t\t\tconst style = {}\n\t\t\tstyle.backgroundColor = this.checked && !this.elDisabled ? this.elActiveColor : '#ffffff'\n\t\t\tstyle.borderColor = this.checked && !this.elDisabled ? this.elActiveColor : this.elInactiveColor\n\t\t\tstyle.width = uni.$u.addUnit(this.elSize)\n\t\t\tstyle.height = uni.$u.addUnit(this.elSize)\n\t\t\t// 如果是图标在右边的话，移除它的右边距\n\t\t\tif (this.parentData.iconPlacement === 'right') {\n\t\t\t\tstyle.marginRight = 0\n\t\t\t}\n\t\t\treturn style\n\t\t},\n\t\tradioStyle() {\n\t\t\tconst style = {}\n\t\t\tif(this.parentData.borderBottom && this.parentData.placement === 'row') {\n\t\t\t\tuni.$u.error('检测到您将borderBottom设置为true，需要同时将u-radio-group的placement设置为column才有效')\n\t\t\t}\n\t\t\t// 当父组件设置了显示下边框并且排列形式为纵向时，给内容和边框之间加上一定间隔\n\t\t\tif(this.parentData.borderBottom && this.parentData.placement === 'column') {\n\t\t\t\t// ios像素密度高，需要多一点的距离\n\t\t\t\tstyle.paddingBottom = uni.$u.os() === 'ios' ? '12px' : '8px'\n\t\t\t}\n\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\n\t\t}\n\t},\n\tmounted() {\n\t\tthis.init()\n\t},\n\tmethods: {\n\t\tinit() {\n\t\t\t// 支付宝小程序不支持provide/inject，所以使用这个方法获取整个父组件，在created定义，避免循环引用\n\t\t\tthis.updateParentData()\n\t\t\tif (!this.parent) {\n\t\t\t\tuni.$u.error('u-radio必须搭配u-radio-group组件使用')\n\t\t\t}\n\t\t\t// 设置初始化时，是否默认选中的状态\n\t\t\tthis.checked = this.name === this.parentData.value\n\t\t},\n\t\tupdateParentData() {\n\t\t\tthis.getParentData('u-radio-group')\n\t\t},\n\t\t// 点击图标\n\t\ticonClickHandler(e) {\n\t\t\tthis.preventEvent(e)\n\t\t\t// 如果整体被禁用，不允许被点击\n\t\t\tif (!this.elDisabled) {\n\t\t\t\tthis.setRadioCheckedStatus()\n\t\t\t}\n\t\t},\n\t\t// 横向两端排列时，点击组件即可触发选中事件\n\t\twrapperClickHandler(e) {\n\t\t\tthis.parentData.iconPlacement === 'right' && this.iconClickHandler(e)\n\t\t},\n\t\t// 点击label\n\t\tlabelClickHandler(e) {\n\t\t\tthis.preventEvent(e)\n\t\t\t// 如果按钮整体被禁用或者label被禁用，则不允许点击文字修改状态\n\t\t\tif (!this.elLabelDisabled && !this.elDisabled) {\n\t\t\t\tthis.setRadioCheckedStatus()\n\t\t\t}\n\t\t},\n\t\temitEvent() {\n\t\t\t// u-radio的checked不为true时(意味着未选中)，才发出事件，避免多次点击触发事件\n\t\t\tif (!this.checked) {\n\t\t\t\tthis.$emit('change', this.name)\n\t\t\t\t// 尝试调用u-form的验证方法，进行一定延迟，否则微信小程序更新可能会不及时\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tuni.$u.formValidate(this, 'change')\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t// 改变组件选中状态\n\t\t// 这里的改变的依据是，更改本组件的checked值为true，同时通过父组件遍历所有u-radio实例\n\t\t// 将本组件外的其他u-radio的checked都设置为false(都被取消选中状态)，因而只剩下一个为选中状态\n\t\tsetRadioCheckedStatus() {\n\t\t\tthis.emitEvent()\n\t\t\t// 将本组件标记为选中状态\n\t\t\tthis.checked = true\n\t\t\ttypeof this.parent.unCheckedOther === 'function' && this.parent.unCheckedOther(this)\n\t\t}\n\t}\n}\n", "import mod from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-radio.vue?vue&type=style&index=0&id=643b3322&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-radio.vue?vue&type=style&index=0&id=643b3322&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753514800806\n      var cssReload = require(\"/Users/<USER>/Desktop/workspace/云梦泽项目/mPaaS-uniapp-tutorial/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}