{"version": 3, "sources": ["webpack:///./node_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?2b0d", "webpack:///./node_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?5f86", "webpack:///./node_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?5299", "webpack:///./node_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?33a8", "webpack:///./node_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue", "webpack:///./node_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?1366", "webpack:///./node_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?7a17"], "names": ["name", "mixins", "uni", "$u", "mpMixin", "mixin", "props", "data", "safeAreaBottomHeight", "isNvue", "computed", "style", "deepMerge", "addStyle", "customStyle", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsH;AACtH,gBAAgB,mIAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClBA;AAAA;AAAA;AAAA;AAAyY,CAAgB,qaAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACU7Z;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;;;;;;;;;eAUe;AACdA,MAAI,EAAE,eADQ;AAEdC,QAAM,EAAE,CAACC,GAAG,CAACC,EAAJ,CAAOC,OAAR,EAAiBF,GAAG,CAACC,EAAJ,CAAOE,KAAxB,EAA+BC,cAA/B,CAFM;AAGdC,MAHc,kBAGP;AACN,WAAO;AACNC,0BAAoB,EAAE,CADhB;AAENC,YAAM,EAAE;AAFF,KAAP;AAIA,GARa;AASdC,UAAQ,EAAE;AACTC,SADS,mBACD;AACP,UAAMA,KAAK,GAAG,EAAd;AAKA,aAAOT,GAAG,CAACC,EAAJ,CAAOS,SAAP,CAAiBD,KAAjB,EAAwBT,GAAG,CAACC,EAAJ,CAAOU,QAAP,CAAgB,KAAKC,WAArB,CAAxB,CAAP;AACA;AARQ,GATI;AAmBdC,SAnBc,qBAmBJ,CAKT;AAxBa,C;;;;;;;;;;;;;;ACrBf;AAAA;AAAA;AAAA;AAAkvB,CAAgB,+tBAAG,EAAC,C;;;;;;;;;;;ACAtwB;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-safe-bottom/u-safe-bottom.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-safe-bottom.vue?vue&type=template&id=758fd84f&scoped=true&\"\nvar renderjs\nimport script from \"./u-safe-bottom.vue?vue&type=script&lang=js&\"\nexport * from \"./u-safe-bottom.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-safe-bottom.vue?vue&type=style&index=0&id=758fd84f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"758fd84f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue\"\nexport default component.exports", "export * from \"-!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--14-0!../../../@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=template&id=758fd84f&scoped=true&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.style])\n\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0\n      }\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport props from \"./props.js\";\n/**\n * SafeBottom 底部安全区\n * @description 这个适配，主要是针对IPhone X等一些底部带指示条的机型，指示条的操作区域与页面底部存在重合，容易导致用户误操作，因此我们需要针对这些机型进行底部安全区适配。\n * @tutorial https://www.uviewui.com/components/safeAreaInset.html\n * @property {type}\t\tprop_name\n * @property {Object}\tcustomStyle\t定义需要用到的外部样式\n *\n * @event {Function()}\n * @example <u-status-bar></u-status-bar>\n */\nexport default {\n\tname: \"u-safe-bottom\",\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\tdata() {\n\t\treturn {\n\t\t\tsafeAreaBottomHeight: 0,\n\t\t\tisNvue: false,\n\t\t};\n\t},\n\tcomputed: {\n\t\tstyle() {\n\t\t\tconst style = {};\n\n\n\n\n\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle));\n\t\t},\n\t},\n\tmounted() {\n\n\n\n\n\t},\n};\n", "import mod from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=style&index=0&id=758fd84f&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=style&index=0&id=758fd84f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753514800728\n      var cssReload = require(\"/Users/<USER>/Desktop/workspace/云梦泽项目/mPaaS-uniapp-tutorial/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}