{"version": 3, "sources": ["webpack:///./node_modules/uview-ui/components/u-input/u-input.vue?86ab", "webpack:///./node_modules/uview-ui/components/u-input/u-input.vue?ab58", "webpack:///./node_modules/uview-ui/components/u-input/u-input.vue?ec1a", "webpack:///./node_modules/uview-ui/components/u-input/u-input.vue?f3d1", "webpack:///./node_modules/uview-ui/components/u-input/u-input.vue", "webpack:///./node_modules/uview-ui/components/u-input/u-input.vue?e0be", "webpack:///./node_modules/uview-ui/components/u-input/u-input.vue?477c"], "names": ["name", "mixins", "uni", "$u", "mpMixin", "mixin", "props", "data", "innerValue", "focused", "firstChange", "changeFromInner", "innerFormatter", "value", "watch", "immediate", "handler", "newVal", "oldVal", "computed", "isShowClear", "clearable", "readonly", "inputClass", "classes", "border", "disabled", "shape", "concat", "push", "join", "wrapperStyle", "style", "backgroundColor", "disabledColor", "padding", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "deepMerge", "addStyle", "customStyle", "inputStyle", "color", "fontSize", "addUnit", "textAlign", "inputAlign", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "onInput", "detail", "formatter", "formatValue", "$nextTick", "valueChange", "onBlur", "event", "$emit", "sleep", "then", "formValidate", "onFocus", "onConfirm", "onkeyboardheightchange", "onClear", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsH;AACtH,gBAAgB,mIAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,WAAW,qRAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAAmY,CAAgB,+ZAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACoFvZ;;;;AApFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eA0Ce;AACdA,MAAI,EAAE,SADQ;AAEdC,QAAM,EAAE,CAACC,GAAG,CAACC,EAAJ,CAAOC,OAAR,EAAiBF,GAAG,CAACC,EAAJ,CAAOE,KAAxB,EAA+BC,cAA/B,CAFM;AAGdC,MAHc,kBAGP;AACN,WAAO;AACN;AACAC,gBAAU,EAAE,EAFN;AAGN;AACAC,aAAO,EAAE,KAJH;AAKN;AACAC,iBAAW,EAAE,IANP;AAON;AACAC,qBAAe,EAAE,KARX;AASN;AACAC,oBAAc,EAAE,wBAAAC,KAAK;AAAA,eAAIA,KAAJ;AAAA;AAVf,KAAP;AAYA,GAhBa;AAiBdC,OAAK,EAAE;AACND,SAAK,EAAE;AACNE,eAAS,EAAE,IADL;AAENC,aAFM,mBAEEC,MAFF,EAEUC,MAFV,EAEkB;AACvB,aAAKV,UAAL,GAAkBS,MAAlB;AAUA,aAAKP,WAAL,GAAmB,KAAnB,CAXuB,CAYvB;;AACA,aAAKC,eAAL,GAAuB,KAAvB;AACA;AAhBK;AADD,GAjBO;AAqCdQ,UAAQ,EAAE;AACT;AACAC,eAFS,yBAEK;AAAA,UACLC,SADK,GACwC,IADxC,CACLA,SADK;AAAA,UACMC,QADN,GACwC,IADxC,CACMA,QADN;AAAA,UACgBb,OADhB,GACwC,IADxC,CACgBA,OADhB;AAAA,UACyBD,UADzB,GACwC,IADxC,CACyBA,UADzB;AAEb,aAAO,CAAC,CAACa,SAAF,IAAe,CAACC,QAAhB,IAA4B,CAAC,CAACb,OAA9B,IAAyCD,UAAU,KAAK,EAA/D;AACA,KALQ;AAMT;AACAe,cAPS,wBAOI;AACR,UAAAC,OAAO,GAAG,EAAV;AAAA,UACDC,MADC,GAC2B,IAD3B,CACDA,MADC;AAAA,UACOC,QADP,GAC2B,IAD3B,CACOA,QADP;AAAA,UACiBC,KADjB,GAC2B,IAD3B,CACiBA,KADjB;AAEJF,YAAM,KAAK,UAAX,KACED,OAAO,GAAGA,OAAO,CAACI,MAAR,CAAe,CAAC,UAAD,EAAa,iBAAb,CAAf,CADZ;AAEAJ,aAAO,CAACK,IAAR,oBAAyBF,KAAzB;AACAF,YAAM,KAAK,QAAX,KACED,OAAO,GAAGA,OAAO,CAACI,MAAR,CAAe,CACzB,iBADyB,EAEzB,oBAFyB,CAAf,CADZ;AAKA,aAAOJ,OAAO,CAACM,IAAR,CAAa,GAAb,CAAP;AACA,KAnBQ;AAoBT;AACAC,gBArBS,0BAqBM;AACd,UAAMC,KAAK,GAAG,EAAd,CADc,CAEd;;AACA,UAAI,KAAKN,QAAT,EAAmB;AAClBM,aAAK,CAACC,eAAN,GAAwB,KAAKC,aAA7B;AACA,OALa,CAMd;;;AACA,UAAI,KAAKT,MAAL,KAAgB,MAApB,EAA4B;AAC3BO,aAAK,CAACG,OAAN,GAAgB,GAAhB;AACA,OAFD,MAEO;AACN;AACAH,aAAK,CAACI,UAAN,GAAmB,KAAnB;AACAJ,aAAK,CAACK,aAAN,GAAsB,KAAtB;AACAL,aAAK,CAACM,WAAN,GAAoB,KAApB;AACAN,aAAK,CAACO,YAAN,GAAqB,KAArB;AACA;;AACD,aAAOrC,GAAG,CAACC,EAAJ,CAAOqC,SAAP,CAAiBR,KAAjB,EAAwB9B,GAAG,CAACC,EAAJ,CAAOsC,QAAP,CAAgB,KAAKC,WAArB,CAAxB,CAAP;AACA,KAtCQ;AAuCT;AACAC,cAxCS,wBAwCI;AACZ,UAAMX,KAAK,GAAG;AACbY,aAAK,EAAE,KAAKA,KADC;AAEbC,gBAAQ,EAAE3C,GAAG,CAACC,EAAJ,CAAO2C,OAAP,CAAe,KAAKD,QAApB,CAFG;AAGbE,iBAAS,EAAE,KAAKC;AAHH,OAAd;AAKA,aAAOhB,KAAP;AACA;AA/CQ,GArCI;AAsFdiB,SAAO,EAAE;AACR;AACAC,gBAFQ,wBAEKC,CAFL,EAEQ;AACf,WAAKvC,cAAL,GAAsBuC,CAAtB;AACA,KAJO;AAKR;AACAC,WANQ,mBAMAD,CANA,EAMG;AAAA;;AAAA,iBACWA,CAAC,CAACE,MAAF,IAAY,EADvB;AAAA,4BACJxC,KADI;AAAA,UACJA,KADI,2BACI,EADJ,eAEV;;;AACA,UAAMyC,SAAS,GAAG,KAAKA,SAAL,IAAkB,KAAK1C,cAAzC;AACA,UAAM2C,WAAW,GAAGD,SAAS,CAACzC,KAAD,CAA7B,CAJU,CAKV;;AACA,WAAKL,UAAL,GAAkBK,KAAlB;AACA,WAAK2C,SAAL,CAAe,YAAM;AACpB,aAAI,CAAChD,UAAL,GAAkB+C,WAAlB;;AACA,aAAI,CAACE,WAAL;AACA,OAHD;AAIA,KAjBO;AAkBR;AACAC,UAnBQ,kBAmBDC,KAnBC,EAmBM;AAAA;;AACb,WAAKC,KAAL,CAAW,MAAX,EAAmBD,KAAK,CAACN,MAAN,CAAaxC,KAAhC,EADa,CAEb;AACA;;AACAX,SAAG,CAACC,EAAJ,CAAO0D,KAAP,CAAa,EAAb,EAAiBC,IAAjB,CAAsB,YAAM;AAC3B,cAAI,CAACrD,OAAL,GAAe,KAAf;AACA,OAFD,EAJa,CAOb;;AACAP,SAAG,CAACC,EAAJ,CAAO4D,YAAP,CAAoB,IAApB,EAA0B,MAA1B;AACA,KA5BO;AA6BR;AACAC,WA9BQ,mBA8BAb,CA9BA,EA8BG;AACV,WAAK1C,OAAL,GAAe,IAAf;AACA,WAAKmD,KAAL,CAAW,OAAX,EAAoBT,CAApB;AACA,KAjCO;AAkCR;AACAc,aAnCQ,qBAmCEN,KAnCF,EAmCS;AAChB,WAAKC,KAAL,CAAW,SAAX,EAAsB,KAAKpD,UAA3B;AACA,KArCO;AAsCR;AACA;AACA0D,0BAxCQ,kCAwCef,CAxCf,EAwCkB;AACzB,WAAKS,KAAL,CAAW,sBAAX,EAAmCT,CAAnC;AACA,KA1CO;AA2CR;AACAM,eA5CQ,yBA4CM;AAAA;;AACb,UAAM5C,KAAK,GAAG,KAAKL,UAAnB;AACA,WAAKgD,SAAL,CAAe,YAAM;AACpB,cAAI,CAACI,KAAL,CAAW,OAAX,EAAoB/C,KAApB,EADoB,CAEpB;;;AACA,cAAI,CAACF,eAAL,GAAuB,IAAvB;;AACA,cAAI,CAACiD,KAAL,CAAW,QAAX,EAAqB/C,KAArB,EAJoB,CAKpB;;;AACAX,WAAG,CAACC,EAAJ,CAAO4D,YAAP,CAAoB,MAApB,EAA0B,QAA1B;AACA,OAPD;AAQA,KAtDO;AAuDR;AACAI,WAxDQ,qBAwDE;AAAA;;AACT,WAAK3D,UAAL,GAAkB,EAAlB;AACA,WAAKgD,SAAL,CAAe,YAAM;AACpB,cAAI,CAACC,WAAL;;AACA,cAAI,CAACG,KAAL,CAAW,OAAX;AACA,OAHD;AAIA,KA9DO;;AA+DR;;;;;AAKAQ,gBApEQ,0BAoEO,CASd;AA7EO;AAtFK,C;;;;;;;;;;;;;;AC/Hf;AAAA;AAAA;AAAA;AAA4uB,CAAgB,ytBAAG,EAAC,C;;;;;;;;;;;ACAhwB;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-input/u-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-input.vue?vue&type=template&id=fdbb9fe6&scoped=true&\"\nvar renderjs\nimport script from \"./u-input.vue?vue&type=script&lang=js&\"\nexport * from \"./u-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-input.vue?vue&type=style&index=0&id=fdbb9fe6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fdbb9fe6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-input/u-input.vue\"\nexport default component.exports", "export * from \"-!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--14-0!../../../@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=template&id=fdbb9fe6&scoped=true&\"", "var components = {\n  uIcon: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n    )\n  }\n}\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.wrapperStyle])\n\n  var s1 = _vm.__get_style([_vm.inputStyle])\n\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1\n      }\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport props from \"./props.js\";\n/**\n * Input 输入框\n * @description  此组件为一个输入框，默认没有边框和样式，是专门为配合表单组件u-form而设计的，利用它可以快速实现表单验证，输入内容，下拉选择等功能。\n * @tutorial https://uviewui.com/components/input.html\n * @property {String | Number}\tvalue\t\t\t\t\t输入的值\n * @property {String}\t\t\ttype\t\t\t\t\t输入框类型，见上方说明 （ 默认 'text' ）\n * @property {Boolean}\t\t\tfixed\t\t\t\t\t如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true，兼容性：微信小程序、百度小程序、字节跳动小程序、QQ小程序 （ 默认 false ）\n * @property {Boolean}\t\t\tdisabled\t\t\t\t是否禁用输入框 （ 默认 false ）\n * @property {String}\t\t\tdisabledColor\t\t\t禁用状态时的背景色（ 默认 '#f5f7fa' ）\n * @property {Boolean}\t\t\tclearable\t\t\t\t是否显示清除控件 （ 默认 false ）\n * @property {Boolean}\t\t\tpassword\t\t\t\t是否密码类型 （ 默认 false ）\n * @property {String | Number}\tmaxlength\t\t\t\t最大输入长度，设置为 -1 的时候不限制最大长度 （ 默认 -1 ）\n * @property {String}\t\t\tplaceholder\t\t\t\t输入框为空时的占位符\n * @property {String}\t\t\tplaceholderClass\t\t指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/ （ 默认 'input-placeholder' ）\n * @property {String | Object}\tplaceholderStyle\t\t指定placeholder的样式，字符串/对象形式，如\"color: red;\"\n * @property {Boolean}\t\t\tshowWordLimit\t\t\t是否显示输入字数统计，只在 type =\"text\"或type =\"textarea\"时有效 （ 默认 false ）\n * @property {String}\t\t\tconfirmType\t\t\t\t设置右下角按钮的文字，兼容性详见uni-app文档 （ 默认 'done' ）\n * @property {Boolean}\t\t\tconfirmHold\t\t\t\t点击键盘右下角按钮时是否保持键盘不收起，H5无效 （ 默认 false ）\n * @property {Boolean}\t\t\tholdKeyboard\t\t\tfocus时，点击页面的时候不收起键盘，微信小程序有效 （ 默认 false ）\n * @property {Boolean}\t\t\tfocus\t\t\t\t\t自动获取焦点，在 H5 平台能否聚焦以及软键盘是否跟随弹出，取决于当前浏览器本身的实现。nvue 页面不支持，需使用组件的 focus()、blur() 方法控制焦点 （ 默认 false ）\n * @property {Boolean}\t\t\tautoBlur\t\t\t\t键盘收起时，是否自动失去焦点，目前仅App3.0.0+有效 （ 默认 false ）\n * @property {Boolean}\t\t\tdisableDefaultPadding\t是否去掉 iOS 下的默认内边距，仅微信小程序，且type=textarea时有效 （ 默认 false ）\n * @property {String ｜ Number}\tcursor\t\t\t\t\t指定focus时光标的位置（ 默认 -1 ）\n * @property {String ｜ Number}\tcursorSpacing\t\t\t输入框聚焦时底部与键盘的距离 （ 默认 30 ）\n * @property {String ｜ Number}\tselectionStart\t\t\t光标起始位置，自动聚集时有效，需与selection-end搭配使用 （ 默认 -1 ）\n * @property {String ｜ Number}\tselectionEnd\t\t\t光标结束位置，自动聚集时有效，需与selection-start搭配使用 （ 默认 -1 ）\n * @property {Boolean}\t\t\tadjustPosition\t\t\t键盘弹起时，是否自动上推页面 （ 默认 true ）\n * @property {String}\t\t\tinputAlign\t\t\t\t输入框内容对齐方式（ 默认 'left' ）\n * @property {String | Number}\tfontSize\t\t\t\t输入框字体的大小 （ 默认 '15px' ）\n * @property {String}\t\t\tcolor\t\t\t\t\t输入框字体颜色\t（ 默认 '#303133' ）\n * @property {Function}\t\t\tformatter\t\t\t    内容式化函数\n * @property {String}\t\t\tprefixIcon\t\t\t\t输入框前置图标\n * @property {String | Object}\tprefixIconStyle\t\t\t前置图标样式，对象或字符串\n * @property {String}\t\t\tsuffixIcon\t\t\t\t输入框后置图标\n * @property {String | Object}\tsuffixIconStyle\t\t\t后置图标样式，对象或字符串\n * @property {String}\t\t\tborder\t\t\t\t\t边框类型，surround-四周边框，bottom-底部边框，none-无边框 （ 默认 'surround' ）\n * @property {Boolean}\t\t\treadonly\t\t\t\t是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会 （ 默认 false ）\n * @property {String}\t\t\tshape\t\t\t\t\t输入框形状，circle-圆形，square-方形 （ 默认 'square' ）\n * @property {Object}\t\t\tcustomStyle\t\t\t\t定义需要用到的外部样式\n * @property {Boolean}\t\t\tignoreCompositionEvent\t是否忽略组件内对文本合成系统事件的处理。\n * @example <u-input v-model=\"value\" :password=\"true\" suffix-icon=\"lock-fill\" />\n */\nexport default {\n\tname: \"u-input\",\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\tdata() {\n\t\treturn {\n\t\t\t// 输入框的值\n\t\t\tinnerValue: \"\",\n\t\t\t// 是否处于获得焦点状态\n\t\t\tfocused: false,\n\t\t\t// value是否第一次变化，在watch中，由于加入immediate属性，会在第一次触发，此时不应该认为value发生了变化\n\t\t\tfirstChange: true,\n\t\t\t// value绑定值的变化是由内部还是外部引起的\n\t\t\tchangeFromInner: false,\n\t\t\t// 过滤处理方法\n\t\t\tinnerFormatter: value => value\n\t\t};\n\t},\n\twatch: {\n\t\tvalue: {\n\t\t\timmediate: true,\n\t\t\thandler(newVal, oldVal) {\n\t\t\t\tthis.innerValue = newVal;\n\n\n\n\n\n\n\n\n\n\t\t\t\tthis.firstChange = false;\n\t\t\t\t// 重置changeFromInner的值为false，标识下一次引起默认为外部引起的\n\t\t\t\tthis.changeFromInner = false;\n\t\t\t},\n\t\t},\n\t},\n\tcomputed: {\n\t\t// 是否显示清除控件\n\t\tisShowClear() {\n\t\t\tconst { clearable, readonly, focused, innerValue } = this;\n\t\t\treturn !!clearable && !readonly && !!focused && innerValue !== \"\";\n\t\t},\n\t\t// 组件的类名\n\t\tinputClass() {\n\t\t\tlet classes = [],\n\t\t\t\t{ border, disabled, shape } = this;\n\t\t\tborder === \"surround\" &&\n\t\t\t\t(classes = classes.concat([\"u-border\", \"u-input--radius\"]));\n\t\t\tclasses.push(`u-input--${shape}`);\n\t\t\tborder === \"bottom\" &&\n\t\t\t\t(classes = classes.concat([\n\t\t\t\t\t\"u-border-bottom\",\n\t\t\t\t\t\"u-input--no-radius\",\n\t\t\t\t]));\n\t\t\treturn classes.join(\" \");\n\t\t},\n\t\t// 组件的样式\n\t\twrapperStyle() {\n\t\t\tconst style = {};\n\t\t\t// 禁用状态下，被背景色加上对应的样式\n\t\t\tif (this.disabled) {\n\t\t\t\tstyle.backgroundColor = this.disabledColor;\n\t\t\t}\n\t\t\t// 无边框时，去除内边距\n\t\t\tif (this.border === \"none\") {\n\t\t\t\tstyle.padding = \"0\";\n\t\t\t} else {\n\t\t\t\t// 由于uni-app的iOS开发者能力有限，导致需要分开写才有效\n\t\t\t\tstyle.paddingTop = \"6px\";\n\t\t\t\tstyle.paddingBottom = \"6px\";\n\t\t\t\tstyle.paddingLeft = \"9px\";\n\t\t\t\tstyle.paddingRight = \"9px\";\n\t\t\t}\n\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle));\n\t\t},\n\t\t// 输入框的样式\n\t\tinputStyle() {\n\t\t\tconst style = {\n\t\t\t\tcolor: this.color,\n\t\t\t\tfontSize: uni.$u.addUnit(this.fontSize),\n\t\t\t\ttextAlign: this.inputAlign\n\t\t\t};\n\t\t\treturn style;\n\t\t},\n\t},\n\tmethods: {\n\t\t// 在微信小程序中，不支持将函数当做props参数，故只能通过ref形式调用\n\t\tsetFormatter(e) {\n\t\t\tthis.innerFormatter = e\n\t\t},\n\t\t// 当键盘输入时，触发input事件\n\t\tonInput(e) {\n\t\t\tlet { value = \"\" } = e.detail || {};\n\t\t\t// 格式化过滤方法\n\t\t\tconst formatter = this.formatter || this.innerFormatter\n\t\t\tconst formatValue = formatter(value)\n\t\t\t// 为了避免props的单向数据流特性，需要先将innerValue值设置为当前值，再在$nextTick中重新赋予设置后的值才有效\n\t\t\tthis.innerValue = value\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.innerValue = formatValue;\n\t\t\t\tthis.valueChange();\n\t\t\t})\n\t\t},\n\t\t// 输入框失去焦点时触发\n\t\tonBlur(event) {\n\t\t\tthis.$emit(\"blur\", event.detail.value);\n\t\t\t// H5端的blur会先于点击清除控件的点击click事件触发，导致focused\n\t\t\t// 瞬间为false，从而隐藏了清除控件而无法被点击到\n\t\t\tuni.$u.sleep(50).then(() => {\n\t\t\t\tthis.focused = false;\n\t\t\t});\n\t\t\t// 尝试调用u-form的验证方法\n\t\t\tuni.$u.formValidate(this, \"blur\");\n\t\t},\n\t\t// 输入框聚焦时触发\n\t\tonFocus(e) {\n\t\t\tthis.focused = true;\n\t\t\tthis.$emit(\"focus\", e);\n\t\t},\n\t\t// 点击完成按钮时触发\n\t\tonConfirm(event) {\n\t\t\tthis.$emit(\"confirm\", this.innerValue);\n\t\t},\n\t\t// 键盘高度发生变化的时候触发此事件\n\t\t// 兼容性：微信小程序2.7.0+、App 3.1.0+\n\t\tonkeyboardheightchange(e) {\n\t\t\tthis.$emit(\"keyboardheightchange\", e);\n\t\t},\n\t\t// 内容发生变化，进行处理\n\t\tvalueChange() {\n\t\t\tconst value = this.innerValue;\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.$emit(\"input\", value);\n\t\t\t\t// 标识value值的变化是由内部引起的\n\t\t\t\tthis.changeFromInner = true;\n\t\t\t\tthis.$emit(\"change\", value);\n\t\t\t\t// 尝试调用u-form的验证方法\n\t\t\t\tuni.$u.formValidate(this, \"change\");\n\t\t\t});\n\t\t},\n\t\t// 点击清除控件\n\t\tonClear() {\n\t\t\tthis.innerValue = \"\";\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.valueChange();\n\t\t\t\tthis.$emit(\"clear\");\n\t\t\t});\n\t\t},\n\t\t/**\n\t\t * 在安卓nvue上，事件无法冒泡\n\t\t * 在某些时间，我们希望监听u-from-item的点击事件，此时会导致点击u-form-item内的u-input后\n\t\t * 无法触发u-form-item的点击事件，这里通过手动调用u-form-item的方法进行触发\n\t\t */\n\t\tclickHandler() {\n\n\n\n\n\n\n\n\n\t\t},\n\t},\n};\n", "import mod from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=style&index=0&id=fdbb9fe6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=style&index=0&id=fdbb9fe6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753516332876\n      var cssReload = require(\"/Users/<USER>/Desktop/workspace/云梦泽项目/mPaaS-uniapp-tutorial/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}