{"version": 3, "sources": ["webpack:///./node_modules/uview-ui/components/u-textarea/u-textarea.vue?86b4", "webpack:///./node_modules/uview-ui/components/u-textarea/u-textarea.vue?1aba", "webpack:///./node_modules/uview-ui/components/u-textarea/u-textarea.vue?e4ed", "webpack:///./node_modules/uview-ui/components/u-textarea/u-textarea.vue?e70e", "webpack:///./node_modules/uview-ui/components/u-textarea/u-textarea.vue", "webpack:///./node_modules/uview-ui/components/u-textarea/u-textarea.vue?8e87", "webpack:///./node_modules/uview-ui/components/u-textarea/u-textarea.vue?d900"], "names": ["name", "mixins", "uni", "$u", "mpMixin", "mixin", "props", "data", "innerValue", "focused", "firstChange", "changeFromInner", "innerFormatter", "value", "watch", "immediate", "handler", "newVal", "oldVal", "computed", "textareaClass", "classes", "border", "disabled", "shape", "concat", "push", "join", "textareaStyle", "style", "deepMerge", "addStyle", "customStyle", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "onFocus", "$emit", "onBlur", "formValidate", "onLinechange", "onInput", "detail", "formatter", "formatValue", "$nextTick", "valueChange", "onConfirm", "onKeyboardheightchange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsH;AACtH,gBAAgB,mIAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtBA;AAAA;AAAA;AAAA;AAAsY,CAAgB,kaAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2C1Z;;;;AA3CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAqCe;AACXA,MAAI,EAAE,YADK;AAEXC,QAAM,EAAE,CAACC,GAAG,CAACC,EAAJ,CAAOC,OAAR,EAAiBF,GAAG,CAACC,EAAJ,CAAOE,KAAxB,EAA+BC,cAA/B,CAFG;AAGdC,MAHc,kBAGP;AACN,WAAO;AACN;AACAC,gBAAU,EAAE,EAFN;AAGN;AACAC,aAAO,EAAE,KAJH;AAKN;AACAC,iBAAW,EAAE,IANP;AAON;AACAC,qBAAe,EAAE,KARX;AASN;AACAC,oBAAc,EAAE,wBAAAC,KAAK;AAAA,eAAIA,KAAJ;AAAA;AAVf,KAAP;AAYA,GAhBa;AAiBdC,OAAK,EAAE;AACHD,SAAK,EAAE;AACHE,eAAS,EAAE,IADR;AAEHC,aAFG,mBAEKC,MAFL,EAEaC,MAFb,EAEqB;AACpB,aAAKV,UAAL,GAAkBS,MAAlB;AAUA,aAAKP,WAAL,GAAmB,KAAnB,CAXoB,CAYpB;;AACA,aAAKC,eAAL,GAAuB,KAAvB;AACH;AAhBE;AADJ,GAjBO;AAqCXQ,UAAQ,EAAE;AACN;AACAC,iBAFM,2BAEU;AACR,UAAAC,OAAO,GAAG,EAAV;AAAA,UACEC,MADF,GAC8B,IAD9B,CACEA,MADF;AAAA,UACUC,QADV,GAC8B,IAD9B,CACUA,QADV;AAAA,UACoBC,KADpB,GAC8B,IAD9B,CACoBA,KADpB;AAEJF,YAAM,KAAK,UAAX,KACKD,OAAO,GAAGA,OAAO,CAACI,MAAR,CAAe,CAAC,UAAD,EAAa,oBAAb,CAAf,CADf;AAEAH,YAAM,KAAK,QAAX,KACKD,OAAO,GAAGA,OAAO,CAACI,MAAR,CAAe,CACtB,iBADsB,EAEtB,uBAFsB,CAAf,CADf;AAKAF,cAAQ,IAAIF,OAAO,CAACK,IAAR,CAAa,sBAAb,CAAZ;AACA,aAAOL,OAAO,CAACM,IAAR,CAAa,GAAb,CAAP;AACH,KAdK;AAeN;AACAC,iBAhBM,2BAgBU;AACZ,UAAMC,KAAK,GAAG,EAAd;AAUA,aAAO3B,GAAG,CAACC,EAAJ,CAAO2B,SAAP,CAAiBD,KAAjB,EAAwB3B,GAAG,CAACC,EAAJ,CAAO4B,QAAP,CAAgB,KAAKC,WAArB,CAAxB,CAAP;AACH;AA5BK,GArCC;AAmEXC,SAAO,EAAE;AACX;AACAC,gBAFW,wBAEEC,CAFF,EAEK;AACf,WAAKvB,cAAL,GAAsBuB,CAAtB;AACA,KAJU;AAKLC,WALK,mBAKGD,CALH,EAKM;AACP,WAAKE,KAAL,CAAW,OAAX,EAAoBF,CAApB;AACH,KAPI;AAQLG,UARK,kBAQEH,CARF,EAQK;AACN,WAAKE,KAAL,CAAW,MAAX,EAAmBF,CAAnB,EADM,CAEN;;AACAjC,SAAG,CAACC,EAAJ,CAAOoC,YAAP,CAAoB,IAApB,EAA0B,MAA1B;AACH,KAZI;AAaLC,gBAbK,wBAaQL,CAbR,EAaW;AACZ,WAAKE,KAAL,CAAW,YAAX,EAAyBF,CAAzB;AACH,KAfI;AAgBLM,WAhBK,mBAgBGN,CAhBH,EAgBM;AAAA;;AAAA,iBACKA,CAAC,CAACO,MAAF,IAAY,EADjB;AAAA,4BACV7B,KADU;AAAA,UACVA,KADU,2BACF,EADE,eAEhB;;;AACA,UAAM8B,SAAS,GAAG,KAAKA,SAAL,IAAkB,KAAK/B,cAAzC;AACA,UAAMgC,WAAW,GAAGD,SAAS,CAAC9B,KAAD,CAA7B,CAJgB,CAKhB;;AACA,WAAKL,UAAL,GAAkBK,KAAlB;AACA,WAAKgC,SAAL,CAAe,YAAM;AACpB,aAAI,CAACrC,UAAL,GAAkBoC,WAAlB;;AACA,aAAI,CAACE,WAAL;AACA,OAHD;AAIM,KA3BI;AA4BX;AACAA,eA7BW,yBA6BG;AAAA;;AACV,UAAMjC,KAAK,GAAG,KAAKL,UAAnB;AACA,WAAKqC,SAAL,CAAe,YAAM;AACjB,cAAI,CAACR,KAAL,CAAW,OAAX,EAAoBxB,KAApB,EADiB,CAEjB;;;AACA,cAAI,CAACF,eAAL,GAAuB,IAAvB;;AACA,cAAI,CAAC0B,KAAL,CAAW,QAAX,EAAqBxB,KAArB,EAJiB,CAKjB;;;AACAX,WAAG,CAACC,EAAJ,CAAOoC,YAAP,CAAoB,MAApB,EAA0B,QAA1B;AACH,OAPD;AAQH,KAvCU;AAwCLQ,aAxCK,qBAwCKZ,CAxCL,EAwCQ;AACT,WAAKE,KAAL,CAAW,SAAX,EAAsBF,CAAtB;AACH,KA1CI;AA2CLa,0BA3CK,kCA2CkBb,CA3ClB,EA2CqB;AACtB,WAAKE,KAAL,CAAW,sBAAX,EAAmCF,CAAnC;AACH;AA7CI;AAnEE,C;;;;;;;;;;;;;;ACjFf;AAAA;AAAA;AAAA;AAA+uB,CAAgB,4tBAAG,EAAC,C;;;;;;;;;;;ACAnwB;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-textarea/u-textarea.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-textarea.vue?vue&type=template&id=81cd9d32&scoped=true&\"\nvar renderjs\nimport script from \"./u-textarea.vue?vue&type=script&lang=js&\"\nexport * from \"./u-textarea.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-textarea.vue?vue&type=style&index=0&id=81cd9d32&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"81cd9d32\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-textarea/u-textarea.vue\"\nexport default component.exports", "export * from \"-!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--14-0!../../../@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-textarea.vue?vue&type=template&id=81cd9d32&scoped=true&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.textareaStyle])\n\n  var g0 = _vm.$u.addUnit(_vm.height)\n  var g1 = _vm.$u.addStyle(_vm.placeholderStyle, \"string\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1\n      }\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-textarea.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-textarea.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport props from \"./props.js\";\n/**\n * Textarea 文本域\n * @description 文本域此组件满足了可能出现的表单信息补充，编辑等实际逻辑的功能，内置了字数校验等\n * @tutorial https://www.uviewui.com/components/textarea.html\n *\n * @property {String | Number} \t\tvalue\t\t\t\t\t输入框的内容\n * @property {String | Number}\t\tplaceholder\t\t\t\t输入框为空时占位符\n * @property {String}\t\t\t    placeholderClass\t\t指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/ （ 默认 'input-placeholder' ）\n * @property {String | Object}\t    placeholderStyle\t\t指定placeholder的样式，字符串/对象形式，如\"color: red;\"\n * @property {String | Number}\t\theight\t\t\t\t\t输入框高度（默认 70 ）\n * @property {String}\t\t\t\tconfirmType\t\t\t\t设置键盘右下角按钮的文字，仅微信小程序，App-vue和H5有效（默认 'done' ）\n * @property {Boolean}\t\t\t\tdisabled\t\t\t\t是否禁用（默认 false ）\n * @property {Boolean}\t\t\t\tcount\t\t\t\t\t是否显示统计字数（默认 false ）\n * @property {Boolean}\t\t\t\tfocus\t\t\t\t\t是否自动获取焦点，nvue不支持，H5取决于浏览器的实现（默认 false ）\n * @property {Boolean | Function}\tautoHeight\t\t\t\t是否自动增加高度（默认 false ）\n * @property {Boolean}\t\t\t\tfixed\t\t\t\t\t如果textarea是在一个position:fixed的区域，需要显示指定属性fixed为true（默认 false ）\n * @property {Number}\t\t\t\tcursorSpacing\t\t\t指定光标与键盘的距离（默认 0 ）\n * @property {String | Number}\t\tcursor\t\t\t\t\t指定focus时的光标位置\n * @property {Function}\t\t\t    formatter\t\t\t    内容式化函数\n * @property {Boolean}\t\t\t\tshowConfirmBar\t\t\t是否显示键盘上方带有”完成“按钮那一栏，（默认 true ）\n * @property {Number}\t\t\t\tselectionStart\t\t\t光标起始位置，自动聚焦时有效，需与selection-end搭配使用，（默认 -1 ）\n * @property {Number | Number}\t\tselectionEnd\t\t\t光标结束位置，自动聚焦时有效，需与selection-start搭配使用（默认 -1 ）\n * @property {Boolean}\t\t\t\tadjustPosition\t\t\t键盘弹起时，是否自动上推页面（默认 true ）\n * @property {Boolean | Number}\t\tdisableDefaultPadding\t是否去掉 iOS 下的默认内边距，只微信小程序有效（默认 false ）\n * @property {Boolean}\t\t\t\tholdKeyboard\t\t\tfocus时，点击页面的时候不收起键盘，只微信小程序有效（默认 false ）\n * @property {String | Number}\t\tmaxlength\t\t\t\t最大输入长度，设置为 -1 的时候不限制最大长度（默认 140 ）\n * @property {String}\t\t\t\tborder\t\t\t\t\t边框类型，surround-四周边框，none-无边框，bottom-底部边框（默认 'surround' ）\n * @property {Boolean}\t\t\t\tignoreCompositionEvent\t是否忽略组件内对文本合成系统事件的处理\n *\n * @event {Function(e)} focus\t\t\t\t\t输入框聚焦时触发，event.detail = { value, height }，height 为键盘高度\n * @event {Function(e)} blur\t\t\t\t\t输入框失去焦点时触发，event.detail = {value, cursor}\n * @event {Function(e)} linechange\t\t\t\t输入框行数变化时调用，event.detail = {height: 0, heightRpx: 0, lineCount: 0}\n * @event {Function(e)} input\t\t\t\t\t当键盘输入时，触发 input 事件\n * @event {Function(e)} confirm\t\t\t\t\t点击完成时， 触发 confirm 事件\n * @event {Function(e)} keyboardheightchange\t键盘高度发生变化的时候触发此事件\n * @example <u--textarea v-model=\"value1\" placeholder=\"请输入内容\" ></u--textarea>\n */\nexport default {\n    name: \"u-textarea\",\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\tdata() {\n\t\treturn {\n\t\t\t// 输入框的值\n\t\t\tinnerValue: \"\",\n\t\t\t// 是否处于获得焦点状态\n\t\t\tfocused: false,\n\t\t\t// value是否第一次变化，在watch中，由于加入immediate属性，会在第一次触发，此时不应该认为value发生了变化\n\t\t\tfirstChange: true,\n\t\t\t// value绑定值的变化是由内部还是外部引起的\n\t\t\tchangeFromInner: false,\n\t\t\t// 过滤处理方法\n\t\t\tinnerFormatter: value => value\n\t\t}\n\t},\n\twatch: {\n\t    value: {\n\t        immediate: true,\n\t        handler(newVal, oldVal) {\n\t            this.innerValue = newVal;\n\n\n\n\n\n\n\n\n\n\t            this.firstChange = false;\n\t            // 重置changeFromInner的值为false，标识下一次引起默认为外部引起的\n\t            this.changeFromInner = false;\n\t        },\n\t    },\n\t},\n    computed: {\n        // 组件的类名\n        textareaClass() {\n            let classes = [],\n                { border, disabled, shape } = this;\n            border === \"surround\" &&\n                (classes = classes.concat([\"u-border\", \"u-textarea--radius\"]));\n            border === \"bottom\" &&\n                (classes = classes.concat([\n                    \"u-border-bottom\",\n                    \"u-textarea--no-radius\",\n                ]));\n            disabled && classes.push(\"u-textarea--disabled\");\n            return classes.join(\" \");\n        },\n        // 组件的样式\n        textareaStyle() {\n            const style = {};\n\n\n\n\n\n\n\n\n\n            return uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle));\n        },\n    },\n    methods: {\n\t\t// 在微信小程序中，不支持将函数当做props参数，故只能通过ref形式调用\n\t\tsetFormatter(e) {\n\t\t\tthis.innerFormatter = e\n\t\t},\n        onFocus(e) {\n            this.$emit(\"focus\", e);\n        },\n        onBlur(e) {\n            this.$emit(\"blur\", e);\n            // 尝试调用u-form的验证方法\n            uni.$u.formValidate(this, \"blur\");\n        },\n        onLinechange(e) {\n            this.$emit(\"linechange\", e);\n        },\n        onInput(e) {\n\t\t\tlet { value = \"\" } = e.detail || {};\n\t\t\t// 格式化过滤方法\n\t\t\tconst formatter = this.formatter || this.innerFormatter\n\t\t\tconst formatValue = formatter(value)\n\t\t\t// 为了避免props的单向数据流特性，需要先将innerValue值设置为当前值，再在$nextTick中重新赋予设置后的值才有效\n\t\t\tthis.innerValue = value\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.innerValue = formatValue;\n\t\t\t\tthis.valueChange();\n\t\t\t})\n        },\n\t\t// 内容发生变化，进行处理\n\t\tvalueChange() {\n\t\t    const value = this.innerValue;\n\t\t    this.$nextTick(() => {\n\t\t        this.$emit(\"input\", value);\n\t\t        // 标识value值的变化是由内部引起的\n\t\t        this.changeFromInner = true;\n\t\t        this.$emit(\"change\", value);\n\t\t        // 尝试调用u-form的验证方法\n\t\t        uni.$u.formValidate(this, \"change\");\n\t\t    });\n\t\t},\n        onConfirm(e) {\n            this.$emit(\"confirm\", e);\n        },\n        onKeyboardheightchange(e) {\n            this.$emit(\"keyboardheightchange\", e);\n        },\n    },\n};\n", "import mod from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-textarea.vue?vue&type=style&index=0&id=81cd9d32&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-textarea.vue?vue&type=style&index=0&id=81cd9d32&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753516332867\n      var cssReload = require(\"/Users/<USER>/Desktop/workspace/云梦泽项目/mPaaS-uniapp-tutorial/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}