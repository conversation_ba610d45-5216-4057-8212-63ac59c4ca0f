{"version": 3, "sources": ["webpack:///./node_modules/uview-ui/components/u-status-bar/u-status-bar.vue?9c54", "webpack:///./node_modules/uview-ui/components/u-status-bar/u-status-bar.vue?98b0", "webpack:///./node_modules/uview-ui/components/u-status-bar/u-status-bar.vue?9a81", "webpack:///./node_modules/uview-ui/components/u-status-bar/u-status-bar.vue?e2f1", "webpack:///./node_modules/uview-ui/components/u-status-bar/u-status-bar.vue", "webpack:///./node_modules/uview-ui/components/u-status-bar/u-status-bar.vue?f409", "webpack:///./node_modules/uview-ui/components/u-status-bar/u-status-bar.vue?1a18"], "names": ["name", "mixins", "uni", "$u", "mpMixin", "mixin", "props", "data", "computed", "style", "height", "addUnit", "sys", "statusBarHeight", "backgroundColor", "bgColor", "deepMerge", "addStyle", "customStyle"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsH;AACtH,gBAAgB,mIAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClBA;AAAA;AAAA;AAAA;AAAwY,CAAgB,oaAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACU5Z;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;;;;;;;eAQe;AACdA,MAAI,EAAE,cADQ;AAEdC,QAAM,EAAE,CAACC,GAAG,CAACC,EAAJ,CAAOC,OAAR,EAAiBF,GAAG,CAACC,EAAJ,CAAOE,KAAxB,EAA+BC,cAA/B,CAFM;AAGdC,MAHc,kBAGP;AACN,WAAO,EAAP;AAEA,GANa;AAOdC,UAAQ,EAAE;AACTC,SADS,mBACD;AACP,UAAMA,KAAK,GAAG,EAAd,CADO,CAEP;;AACAA,WAAK,CAACC,MAAN,GAAeR,GAAG,CAACC,EAAJ,CAAOQ,OAAP,CAAeT,GAAG,CAACC,EAAJ,CAAOS,GAAP,GAAaC,eAA5B,EAA6C,IAA7C,CAAf;AACAJ,WAAK,CAACK,eAAN,GAAwB,KAAKC,OAA7B;AACA,aAAOb,GAAG,CAACC,EAAJ,CAAOa,SAAP,CAAiBP,KAAjB,EAAwBP,GAAG,CAACC,EAAJ,CAAOc,QAAP,CAAgB,KAAKC,WAArB,CAAxB,CAAP;AACA;AAPQ;AAPI,C;;;;;;;;;;;;;;ACnBf;AAAA;AAAA;AAAA;AAAivB,CAAgB,8tBAAG,EAAC,C;;;;;;;;;;;ACArwB;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-status-bar/u-status-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-status-bar.vue?vue&type=template&id=3c8c2ae7&scoped=true&\"\nvar renderjs\nimport script from \"./u-status-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-status-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-status-bar.vue?vue&type=style&index=0&id=3c8c2ae7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3c8c2ae7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-status-bar/u-status-bar.vue\"\nexport default component.exports", "export * from \"-!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--14-0!../../../@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-status-bar.vue?vue&type=template&id=3c8c2ae7&scoped=true&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.style])\n\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0\n      }\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-status-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-status-bar.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport props from './props.js';\n/**\n * StatbusBar 状态栏占位\n * @description 本组件主要用于状态填充，比如在自定导航栏的时候，它会自动适配一个恰当的状态栏高度。\n * @tutorial https://uviewui.com/components/statusBar.html\n * @property {String}\t\t\tbgColor\t\t\t背景色 (默认 'transparent' )\n * @property {String | Object}\tcustomStyle\t\t自定义样式 \n * @example <u-status-bar></u-status-bar>\n */\nexport default {\n\tname: 'u-status-bar',\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\tdata() {\n\t\treturn {\n\t\t}\n\t},\n\tcomputed: {\n\t\tstyle() {\n\t\t\tconst style = {}\n\t\t\t// 状态栏高度，由于某些安卓和微信开发工具无法识别css的顶部状态栏变量，所以使用js获取的方式\n\t\t\tstyle.height = uni.$u.addUnit(uni.$u.sys().statusBarHeight, 'px')\n\t\t\tstyle.backgroundColor = this.bgColor\n\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\n\t\t}\n\t},\n}\n", "import mod from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-status-bar.vue?vue&type=style&index=0&id=3c8c2ae7&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-status-bar.vue?vue&type=style&index=0&id=3c8c2ae7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753514800741\n      var cssReload = require(\"/Users/<USER>/Desktop/workspace/云梦泽项目/mPaaS-uniapp-tutorial/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}