{"version": 3, "sources": ["webpack:///./node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?a1d2", "webpack:///./node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?64e4", "webpack:///./node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?a292", "webpack:///./node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?f490", "webpack:///./node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue", "webpack:///./node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?db76", "webpack:///./node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?ae28"], "names": ["name", "mixins", "uni", "$u", "mpMixin", "mixin", "props", "data", "array12", "Array", "from", "length", "aniAngel", "webviewHide", "loading", "computed", "otherBorderColor", "lightColor", "colorGradient", "color", "mode", "inactiveColor", "watch", "show", "n", "mounted", "init", "methods", "setTimeout", "addEventListenerToWebview", "pages", "getCurrentPages", "page", "currentWebview", "$getAppWebview", "addEventListener"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACsH;AACtH,gBAAgB,mIAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA0Y,CAAgB,saAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC4D9Z;;;;AA5DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;;;;;;;;;;;;;;;;;;eAkBe;AACdA,MAAI,EAAE,gBADQ;AAEdC,QAAM,EAAE,CAACC,GAAG,CAACC,EAAJ,CAAOC,OAAR,EAAiBF,GAAG,CAACC,EAAJ,CAAOE,KAAxB,EAA+BC,cAA/B,CAFM;AAGdC,MAHc,kBAGP;AACN,WAAO;AACN;AACA;AACAC,aAAO,EAAEC,KAAK,CAACC,IAAN,CAAW;AACnBC,cAAM,EAAE;AADW,OAAX,CAHH;AAMN;AACA;AACAC,cAAQ,EAAE,GARJ;AAQS;AACfC,iBAAW,EAAE,KATP;AASc;AACpBC,aAAO,EAAE,KAVH,CAUU;;AAVV,KAAP;AAYA,GAhBa;AAiBdC,UAAQ,EAAE;AACT;AACA;AACA;AACAC,oBAJS,8BAIU;AAClB,UAAMC,UAAU,GAAGf,GAAG,CAACC,EAAJ,CAAOe,aAAP,CAAqB,KAAKC,KAA1B,EAAiC,SAAjC,EAA4C,GAA5C,EAAiD,EAAjD,CAAnB;;AACA,UAAI,KAAKC,IAAL,KAAc,QAAlB,EAA4B;AAC3B,eAAO,KAAKC,aAAL,GAAqB,KAAKA,aAA1B,GAA0CJ,UAAjD;AACA,OAFD,MAEO;AACN,eAAO,aAAP;AACA,OANiB,CAOlB;;AACA;AAZQ,GAjBI;AA+BdK,OAAK,EAAE;AACNC,QADM,gBACDC,CADC,EACE,CACP;AAQA;AAVK,GA/BO;AA2CdC,SA3Cc,qBA2CJ;AACT,SAAKC,IAAL;AACA,GA7Ca;AA8CdC,SAAO,EAAE;AACRD,QADQ,kBACD;AACNE,gBAAU,CAAC,YAAM,CAOhB,CAPS,EAOP,EAPO,CAAV;AAQA,KAVO;AAWR;AACAC,6BAZQ,uCAYoB;AAAA;;AAC3B;AACA,UAAMC,KAAK,GAAGC,eAAe,EAA7B,CAF2B,CAG3B;;AACA,UAAMC,IAAI,GAAGF,KAAK,CAACA,KAAK,CAACnB,MAAN,GAAe,CAAhB,CAAlB,CAJ2B,CAK3B;;AACA,UAAMsB,cAAc,GAAGD,IAAI,CAACE,cAAL,EAAvB,CAN2B,CAO3B;;AACAD,oBAAc,CAACE,gBAAf,CAAgC,MAAhC,EAAwC,YAAM;AAC7C,aAAI,CAACtB,WAAL,GAAmB,IAAnB;AACA,OAFD;AAGAoB,oBAAc,CAACE,gBAAf,CAAgC,MAAhC,EAAwC,YAAM;AAC7C,aAAI,CAACtB,WAAL,GAAmB,KAAnB;AACA,OAFD;AAGA;AA1BO;AA9CK,C;;;;;;;;;;;;;;AClFf;AAAA;AAAA;AAAA;AAAmvB,CAAgB,guBAAG,EAAC,C;;;;;;;;;;;ACAvwB;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-loading-icon/u-loading-icon.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-loading-icon.vue?vue&type=template&id=8ae91632&scoped=true&\"\nvar renderjs\nimport script from \"./u-loading-icon.vue?vue&type=script&lang=js&\"\nexport * from \"./u-loading-icon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-loading-icon.vue?vue&type=style&index=0&id=8ae91632&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8ae91632\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\nexport default component.exports", "export * from \"-!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--14-0!../../../@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=template&id=8ae91632&scoped=true&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)])\n\n  var g0 = _vm.$u.addUnit(_vm.size)\n  var g1 = _vm.$u.addUnit(_vm.size)\n  var g2 = _vm.$u.addUnit(_vm.textSize)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n        g2: g2\n      }\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../babel-loader/lib/index.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport props from './props.js';\n\n\n\n/**\n * loading 加载动画\n * @description 警此组件为一个小动画，目前用在uView的loadmore加载更多和switch开关等组件的正在加载状态场景。\n * @tutorial https://www.uviewui.com/components/loading.html\n * @property {Boolean}\t\t\tshow\t\t\t是否显示组件  (默认 true)\n * @property {String}\t\t\tcolor\t\t\t动画活动区域的颜色，只对 mode = flower 模式有效（默认color['u-tips-color']）\n * @property {String}\t\t\ttextColor\t\t提示文本的颜色（默认color['u-tips-color']）\n * @property {Boolean}\t\t\tvertical\t\t文字和图标是否垂直排列 (默认 false )\n * @property {String}\t\t\tmode\t\t\t模式选择，见官网说明（默认 'circle' ）\n * @property {String | Number}\tsize\t\t\t加载图标的大小，单位px （默认 24 ）\n * @property {String | Number}\ttextSize\t\t文字大小（默认 15 ）\n * @property {String | Number}\ttext\t\t\t文字内容 \n * @property {String}\t\t\ttimingFunction\t动画模式 （默认 'ease-in-out' ）\n * @property {String | Number}\tduration\t\t动画执行周期时间（默认 1200）\n * @property {String}\t\t\tinactiveColor\tmode=circle时的暗边颜色 \n * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\n * @example <u-loading mode=\"circle\"></u-loading>\n */\nexport default {\n\tname: 'u-loading-icon',\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\tdata() {\n\t\treturn {\n\t\t\t// Array.form可以通过一个伪数组对象创建指定长度的数组\n\t\t\t// https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/from\n\t\t\tarray12: Array.from({\n\t\t\t\tlength: 12\n\t\t\t}),\n\t\t\t// 这里需要设置默认值为360，否则在安卓nvue上，会延迟一个duration周期后才执行\n\t\t\t// 在iOS nvue上，则会一开始默认执行两个周期的动画\n\t\t\taniAngel: 360, // 动画旋转角度\n\t\t\twebviewHide: false, // 监听webview的状态，如果隐藏了页面，则停止动画，以免性能消耗\n\t\t\tloading: false, // 是否运行中，针对nvue使用\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 当为circle类型时，给其另外三边设置一个更轻一些的颜色\n\t\t// 之所以需要这么做的原因是，比如父组件传了color为红色，那么需要另外的三个边为浅红色\n\t\t// 而不能是固定的某一个其他颜色(因为这个固定的颜色可能浅蓝，导致效果没有那么细腻良好)\n\t\totherBorderColor() {\n\t\t\tconst lightColor = uni.$u.colorGradient(this.color, '#ffffff', 100)[80]\n\t\t\tif (this.mode === 'circle') {\n\t\t\t\treturn this.inactiveColor ? this.inactiveColor : lightColor\n\t\t\t} else {\n\t\t\t\treturn 'transparent'\n\t\t\t}\n\t\t\t// return this.mode === 'circle' ? this.inactiveColor ? this.inactiveColor : lightColor : 'transparent'\n\t\t}\n\t},\n\twatch: {\n\t\tshow(n) {\n\t\t\t// nvue中，show为true，且为非loading状态，就重新执行动画模块\n\n\n\n\n\n\n\n\t\t}\n\t},\n\tmounted() {\n\t\tthis.init()\n\t},\n\tmethods: {\n\t\tinit() {\n\t\t\tsetTimeout(() => {\n\n\n\n\n\n\n\t\t\t}, 20)\n\t\t},\n\t\t// 监听webview的显示与隐藏\n\t\taddEventListenerToWebview() {\n\t\t\t// webview的堆栈\n\t\t\tconst pages = getCurrentPages()\n\t\t\t// 当前页面\n\t\t\tconst page = pages[pages.length - 1]\n\t\t\t// 当前页面的webview实例\n\t\t\tconst currentWebview = page.$getAppWebview()\n\t\t\t// 监听webview的显示与隐藏，从而停止或者开始动画(为了性能)\n\t\t\tcurrentWebview.addEventListener('hide', () => {\n\t\t\t\tthis.webviewHide = true\n\t\t\t})\n\t\t\tcurrentWebview.addEventListener('show', () => {\n\t\t\t\tthis.webviewHide = false\n\t\t\t})\n\t\t},\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\t}\n}\n", "import mod from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=style&index=0&id=8ae91632&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=style&index=0&id=8ae91632&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753516332905\n      var cssReload = require(\"/Users/<USER>/Desktop/workspace/云梦泽项目/mPaaS-uniapp-tutorial/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}