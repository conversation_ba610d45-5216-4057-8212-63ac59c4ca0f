{"version": 3, "sources": ["webpack:///./src/main.js", "webpack:///./src/pages/travel-application/travel-application.vue?cd36", "webpack:///./src/pages/travel-application/travel-application.vue?ccb1", "webpack:///./src/pages/travel-application/travel-application.vue?e774", "webpack:///./src/pages/travel-application/travel-application.vue?a103", "webpack:///./src/pages/travel-application/travel-application.vue", "webpack:///./src/pages/travel-application/travel-application.vue?2248", "webpack:///./src/pages/travel-application/travel-application.vue?5771"], "names": ["createPage", "Page", "name", "data", "formData", "startTime", "endTime", "purpose", "projectName", "budget", "travelType", "isFlight", "isLongTrip", "travelers", "description", "cities", "attachments", "showStartTimePicker", "showEndTimePicker", "showPurposePicker", "showTravelTypePicker", "startTimeValue", "Number", "Date", "endTimeValue", "purposeOptions", "travelTypeOptions", "methods", "handleSave", "console", "log", "uni", "showToast", "title", "icon", "confirmStartTime", "e", "$u", "timeFormat", "value", "confirmEndTime", "confirmPurpose", "confirmTravelType", "addTraveler", "removeCity", "index", "splice", "selectAttachment"], "mappings": ";;;;;;;;;;;;AAAA;;AACA;;AACA;;;;AACAA,UAAU,CAACC,0BAAD,CAAV,C;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AACsE;AACL;AACsC;;;AAGvG;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,WAAW,iSAEN;AACL,GAAG;AACH;AACA,WAAW,2RAEN;AACL,GAAG;AACH;AACA,WAAW,+TAEN;AACL,GAAG;AACH;AACA,WAAW,2RAEN;AACL,GAAG;AACH;AACA,WAAW,qRAEN;AACL,GAAG;AACH;AACA,WAAW,6SAEN;AACL,GAAG;AACH;AACA,WAAW,uVAEN;AACL,GAAG;AACH;AACA,WAAW,iSAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAA+c,CAAgB,2eAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACAne;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;eAEe;AACbC,MAAI,EAAE,mBADO;AAEbC,MAFa,kBAEN;AACL,WAAO;AACLC,cAAQ,EAAE;AACRC,iBAAS,EAAE,EADH;AAERC,eAAO,EAAE,EAFD;AAGRC,eAAO,EAAE,EAHD;AAIRC,mBAAW,EAAE,EAJL;AAKRC,cAAM,EAAE,EALA;AAMRC,kBAAU,EAAE,EANJ;AAORC,gBAAQ,EAAE,IAPF;AAQRC,kBAAU,EAAE,IARJ;AASRC,iBAAS,EAAE,EATH;AAURC,mBAAW,EAAE,EAVL;AAWRC,cAAM,EAAE,CAAC,IAAD,EAAO,IAAP,CAXA;AAYRC,mBAAW,EAAE;AAZL,OADL;AAeLC,yBAAmB,EAAE,KAfhB;AAgBLC,uBAAiB,EAAE,KAhBd;AAiBLC,uBAAiB,EAAE,KAjBd;AAkBLC,0BAAoB,EAAE,KAlBjB;AAmBLC,oBAAc,EAAEC,MAAM,CAAC,IAAIC,IAAJ,EAAD,CAnBjB;AAoBLC,kBAAY,EAAEF,MAAM,CAAC,IAAIC,IAAJ,EAAD,CApBf;AAqBLE,oBAAc,EAAE,CACd,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,EAAyB,MAAzB,EAAiC,MAAjC,EAAyC,IAAzC,CADc,CArBX;AAwBLC,uBAAiB,EAAE,CACjB,CAAC,IAAD,EAAO,IAAP,CADiB;AAxBd,KAAP;AA4BD,GA/BY;AAgCbC,SAAO,EAAE;AACPC,cADO,wBACM;AACXC,aAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAK1B,QAA9B;AACA2B,SAAG,CAACC,SAAJ,CAAc;AACZC,aAAK,EAAE,MADK;AAEZC,YAAI,EAAE;AAFM,OAAd;AAID,KAPM;AAQPC,oBARO,4BAQUC,CARV,EAQa;AAClB,WAAKhC,QAAL,CAAcC,SAAd,GAA0B0B,GAAG,CAACM,EAAJ,CAAOC,UAAP,CAAkBF,CAAC,CAACG,KAApB,EAA2B,kBAA3B,CAA1B;AACA,WAAKtB,mBAAL,GAA2B,KAA3B;AACD,KAXM;AAYPuB,kBAZO,0BAYQJ,CAZR,EAYW;AAChB,WAAKhC,QAAL,CAAcE,OAAd,GAAwByB,GAAG,CAACM,EAAJ,CAAOC,UAAP,CAAkBF,CAAC,CAACG,KAApB,EAA2B,kBAA3B,CAAxB;AACA,WAAKrB,iBAAL,GAAyB,KAAzB;AACD,KAfM;AAgBPuB,kBAhBO,0BAgBQL,CAhBR,EAgBW;AAChB,WAAKhC,QAAL,CAAcG,OAAd,GAAwB6B,CAAC,CAACG,KAAF,CAAQ,CAAR,CAAxB;AACA,WAAKpB,iBAAL,GAAyB,KAAzB;AACD,KAnBM;AAoBPuB,qBApBO,6BAoBWN,CApBX,EAoBc;AACnB,WAAKhC,QAAL,CAAcM,UAAd,GAA2B0B,CAAC,CAACG,KAAF,CAAQ,CAAR,CAA3B;AACA,WAAKnB,oBAAL,GAA4B,KAA5B;AACD,KAvBM;AAwBPuB,eAxBO,yBAwBO;AACZd,aAAO,CAACC,GAAR,CAAY,OAAZ,EADY,CAEZ;AACD,KA3BM;AA4BPc,cA5BO,sBA4BIC,KA5BJ,EA4BW;AAChB,WAAKzC,QAAL,CAAcW,MAAd,CAAqB+B,MAArB,CAA4BD,KAA5B,EAAmC,CAAnC;AACD,KA9BM;AA+BPE,oBA/BO,8BA+BY;AACjBlB,aAAO,CAACC,GAAR,CAAY,MAAZ,EADiB,CAEjB;AACD;AAlCM;AAhCI,C;;;;;;;;;;;;;;AC5Mf;AAAA;AAAA;AAAA;AAA42B,CAAgB,y1BAAG,EAAC,C;;;;;;;;;;;ACAh4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/travel-application/travel-application.js", "sourcesContent": ["import 'uni-pages';\nimport Vue from 'vue'\nimport Page from './pages/travel-application/travel-application.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./travel-application.vue?vue&type=template&id=3c4893fb&scoped=true&\"\nvar renderjs\nimport script from \"./travel-application.vue?vue&type=script&lang=js&\"\nexport * from \"./travel-application.vue?vue&type=script&lang=js&\"\nimport style0 from \"./travel-application.vue?vue&type=style&index=0&id=3c4893fb&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3c4893fb\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"src/pages/travel-application/travel-application.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--14-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=template&id=3c4893fb&scoped=true&\"", "var components = {\n  uNavbar: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n    )\n  },\n  uInput: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-input/u-input\" */ \"uview-ui/components/u-input/u-input.vue\"\n    )\n  },\n  uRadioGroup: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"uview-ui/components/u-radio-group/u-radio-group.vue\"\n    )\n  },\n  uRadio: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-radio/u-radio\" */ \"uview-ui/components/u-radio/u-radio.vue\"\n    )\n  },\n  uIcon: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n    )\n  },\n  uTextarea: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-textarea/u-textarea\" */ \"uview-ui/components/u-textarea/u-textarea.vue\"\n    )\n  },\n  uDatetimePicker: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n    )\n  },\n  uPicker: function() {\n    return import(\n      /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n    )\n  }\n}\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var a0 = {\n    backgroundColor: \"#ffffff\"\n  }\n  var a1 = {\n    color: \"#333333\",\n    fontSize: \"18px\",\n    fontWeight: \"500\"\n  }\n  var a2 = {\n    marginRight: \"32px\"\n  }\n\n  if (!_vm._isMounted) {\n    _vm.e0 = function($event) {\n      _vm.showStartTimePicker = true\n    }\n\n    _vm.e1 = function($event) {\n      _vm.showEndTimePicker = true\n    }\n\n    _vm.e2 = function($event) {\n      _vm.showPurposePicker = true\n    }\n\n    _vm.e3 = function($event) {\n      _vm.showTravelTypePicker = true\n    }\n\n    _vm.e4 = function($event) {\n      _vm.showStartTimePicker = false\n    }\n\n    _vm.e5 = function($event) {\n      _vm.showEndTimePicker = false\n    }\n\n    _vm.e6 = function($event) {\n      _vm.showPurposePicker = false\n    }\n\n    _vm.e7 = function($event) {\n      _vm.showTravelTypePicker = false\n    }\n  }\n\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        a0: a0,\n        a1: a1,\n        a2: a2\n      }\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'TravelApplication',\n  data() {\n    return {\n      formData: {\n        startTime: '',\n        endTime: '',\n        purpose: '',\n        projectName: '',\n        budget: '',\n        travelType: '',\n        isFlight: 'no',\n        isLongTrip: 'no',\n        travelers: [],\n        description: '',\n        cities: ['北京', '上海'],\n        attachments: []\n      },\n      showStartTimePicker: false,\n      showEndTimePicker: false,\n      showPurposePicker: false,\n      showTravelTypePicker: false,\n      startTimeValue: Number(new Date()),\n      endTimeValue: Number(new Date()),\n      purposeOptions: [\n        ['商务洽谈', '技术交流', '培训学习', '会议参加', '项目实施', '其他']\n      ],\n      travelTypeOptions: [\n        ['国内', '国际']\n      ]\n    }\n  },\n  methods: {\n    handleSave() {\n      console.log('保存出差申请信息:', this.formData)\n      uni.showToast({\n        title: '保存成功',\n        icon: 'success'\n      })\n    },\n    confirmStartTime(e) {\n      this.formData.startTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')\n      this.showStartTimePicker = false\n    },\n    confirmEndTime(e) {\n      this.formData.endTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')\n      this.showEndTimePicker = false\n    },\n    confirmPurpose(e) {\n      this.formData.purpose = e.value[0]\n      this.showPurposePicker = false\n    },\n    confirmTravelType(e) {\n      this.formData.travelType = e.value[0]\n      this.showTravelTypePicker = false\n    },\n    addTraveler() {\n      console.log('添加出行人')\n      // 这里可以跳转到选择人员页面或弹出选择框\n    },\n    removeCity(index) {\n      this.formData.cities.splice(index, 1)\n    },\n    selectAttachment() {\n      console.log('选择附件')\n      // 这里可以调用文件选择API\n    }\n  }\n}\n", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=style&index=0&id=3c4893fb&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=style&index=0&id=3c4893fb&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753514800795\n      var cssReload = require(\"/Users/<USER>/Desktop/workspace/云梦泽项目/mPaaS-uniapp-tutorial/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}