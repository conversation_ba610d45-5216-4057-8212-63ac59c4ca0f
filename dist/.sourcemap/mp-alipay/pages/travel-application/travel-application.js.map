{"version": 3, "sources": ["webpack:///./src/main.js", "webpack:///./src/pages/travel-application/travel-application.vue?cd36", "webpack:///./src/pages/travel-application/travel-application.vue?ccb1", "webpack:///./src/pages/travel-application/travel-application.vue?e774", "webpack:///./src/pages/travel-application/travel-application.vue?a103", "webpack:///./src/pages/travel-application/travel-application.vue", "webpack:///./src/pages/travel-application/travel-application.vue?2248", "webpack:///./src/pages/travel-application/travel-application.vue?5771"], "names": ["createPage", "Page", "name", "data", "formData", "startTime", "endTime", "purpose", "projectName", "budget", "travelType", "isFlight", "isLongTrip", "travelers", "description", "cities", "attachments", "showStartTimePicker", "showEndTimePicker", "showPurposePicker", "showTravelTypePicker", "startTimeValue", "Number", "Date", "endTimeValue", "purposeOptions", "travelTypeOptions", "purposeList", "travelTypeList", "methods", "handleSave", "console", "log", "uni", "showToast", "title", "icon", "confirmStartTime", "e", "detail", "value", "confirmEndTime", "confirmPurpose", "confirmTravelType", "addTraveler", "removeCity", "index", "splice", "selectAttachment", "onFlightChange", "onLongTripChange", "goBack", "navigateBack"], "mappings": ";;;;;;;;;;;;AAAA;;AACA;;AACA;;;;AACAA,UAAU,CAACC,0BAAD,CAAV,C;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AACsE;AACL;AACsC;;;AAGvG;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzCA;AAAA;AAAA;AAAA;AAA+c,CAAgB,2eAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACAne;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;eAEe;AACbC,MAAI,EAAE,mBADO;AAEbC,MAFa,kBAEN;AACL,WAAO;AACLC,cAAQ,EAAE;AACRC,iBAAS,EAAE,EADH;AAERC,eAAO,EAAE,EAFD;AAGRC,eAAO,EAAE,EAHD;AAIRC,mBAAW,EAAE,EAJL;AAKRC,cAAM,EAAE,EALA;AAMRC,kBAAU,EAAE,EANJ;AAORC,gBAAQ,EAAE,IAPF;AAQRC,kBAAU,EAAE,IARJ;AASRC,iBAAS,EAAE,EATH;AAURC,mBAAW,EAAE,EAVL;AAWRC,cAAM,EAAE,CAAC,IAAD,EAAO,IAAP,CAXA;AAYRC,mBAAW,EAAE;AAZL,OADL;AAeLC,yBAAmB,EAAE,KAfhB;AAgBLC,uBAAiB,EAAE,KAhBd;AAiBLC,uBAAiB,EAAE,KAjBd;AAkBLC,0BAAoB,EAAE,KAlBjB;AAmBLC,oBAAc,EAAEC,MAAM,CAAC,IAAIC,IAAJ,EAAD,CAnBjB;AAoBLC,kBAAY,EAAEF,MAAM,CAAC,IAAIC,IAAJ,EAAD,CApBf;AAqBLE,oBAAc,EAAE,CACd,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,EAAyB,MAAzB,EAAiC,MAAjC,EAAyC,IAAzC,CADc,CArBX;AAwBLC,uBAAiB,EAAE,CAAC,CAAC,IAAD,EAAO,IAAP,CAAD,CAxBd;AAyBL;AACAC,iBAAW,EAAE,CACX,MADW,EAEX,MAFW,EAGX,MAHW,EAIX,MAJW,EAKX,MALW,EAMX,IANW,CA1BR;AAkCLC,oBAAc,EAAE,CAAC,IAAD,EAAO,IAAP;AAlCX,KAAP;AAoCD,GAvCY;AAwCbC,SAAO,EAAE;AACPC,cADO,wBACM;AACXC,aAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAK5B,QAA9B;AACA6B,SAAG,CAACC,SAAJ,CAAc;AACZC,aAAK,EAAE,MADK;AAEZC,YAAI,EAAE;AAFM,OAAd;AAID,KAPM;AAQPC,oBARO,4BAQUC,CARV,EAQa;AAElB,WAAKlC,QAAL,CAAcC,SAAd,GAA0BiC,CAAC,CAACC,MAAF,CAASC,KAAnC;AACA,WAAKvB,mBAAL,GAA2B,KAA3B;AAMD,KAjBM;AAkBPwB,kBAlBO,0BAkBQH,CAlBR,EAkBW;AAEhB,WAAKlC,QAAL,CAAcE,OAAd,GAAwBgC,CAAC,CAACC,MAAF,CAASC,KAAjC;AACA,WAAKtB,iBAAL,GAAyB,KAAzB;AAMD,KA3BM;AA4BPwB,kBA5BO,0BA4BQJ,CA5BR,EA4BW;AAEhB,WAAKlC,QAAL,CAAcG,OAAd,GAAwB,KAAKoB,WAAL,CAAiBW,CAAC,CAACC,MAAF,CAASC,KAA1B,CAAxB;AACA,WAAKrB,iBAAL,GAAyB,KAAzB;AAMD,KArCM;AAsCPwB,qBAtCO,6BAsCWL,CAtCX,EAsCc;AAEnB,WAAKlC,QAAL,CAAcM,UAAd,GAA2B,KAAKkB,cAAL,CAAoBU,CAAC,CAACC,MAAF,CAASC,KAA7B,CAA3B;AACA,WAAKpB,oBAAL,GAA4B,KAA5B;AAMD,KA/CM;AAgDPwB,eAhDO,yBAgDO;AACZb,aAAO,CAACC,GAAR,CAAY,OAAZ,EADY,CAEZ;AACD,KAnDM;AAoDPa,cApDO,sBAoDIC,KApDJ,EAoDW;AAChB,WAAK1C,QAAL,CAAcW,MAAd,CAAqBgC,MAArB,CAA4BD,KAA5B,EAAmC,CAAnC;AACD,KAtDM;AAuDPE,oBAvDO,8BAuDY;AACjBjB,aAAO,CAACC,GAAR,CAAY,MAAZ,EADiB,CAEjB;AACD,KA1DM;AA2DPiB,kBA3DO,0BA2DQX,CA3DR,EA2DW;AAChB,WAAKlC,QAAL,CAAcO,QAAd,GAAyB2B,CAAC,CAACC,MAAF,CAASC,KAAlC;AACD,KA7DM;AA8DPU,oBA9DO,4BA8DUZ,CA9DV,EA8Da;AAClB,WAAKlC,QAAL,CAAcQ,UAAd,GAA2B0B,CAAC,CAACC,MAAF,CAASC,KAApC;AACD,KAhEM;AAiEPW,UAjEO,oBAiEE;AACPlB,SAAG,CAACmB,YAAJ;AACD;AAnEM;AAxCI,C;;;;;;;;;;;;;;AC7Xf;AAAA;AAAA;AAAA;AAA42B,CAAgB,y1BAAG,EAAC,C;;;;;;;;;;;ACAh4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/travel-application/travel-application.js", "sourcesContent": ["import 'uni-pages';\nimport Vue from 'vue'\nimport Page from './pages/travel-application/travel-application.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./travel-application.vue?vue&type=template&id=3c4893fb&scoped=true&\"\nvar renderjs\nimport script from \"./travel-application.vue?vue&type=script&lang=js&\"\nexport * from \"./travel-application.vue?vue&type=script&lang=js&\"\nimport style0 from \"./travel-application.vue?vue&type=style&index=0&id=3c4893fb&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3c4893fb\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"src/pages/travel-application/travel-application.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--14-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=template&id=3c4893fb&scoped=true&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function($event) {\n      _vm.showStartTimePicker = true\n    }\n\n    _vm.e1 = function($event) {\n      _vm.showEndTimePicker = true\n    }\n\n    _vm.e2 = function($event) {\n      _vm.showPurposePicker = true\n    }\n\n    _vm.e3 = function($event) {\n      _vm.showTravelTypePicker = true\n    }\n\n    _vm.e4 = function($event) {\n      _vm.showStartTimePicker = false\n    }\n\n    _vm.e5 = function($event) {\n      _vm.showEndTimePicker = false\n    }\n\n    _vm.e6 = function($event) {\n      _vm.showPurposePicker = false\n    }\n\n    _vm.e7 = function($event) {\n      _vm.showTravelTypePicker = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: \"TravelApplication\",\n  data() {\n    return {\n      formData: {\n        startTime: \"\",\n        endTime: \"\",\n        purpose: \"\",\n        projectName: \"\",\n        budget: \"\",\n        travelType: \"\",\n        isFlight: \"no\",\n        isLongTrip: \"no\",\n        travelers: [],\n        description: \"\",\n        cities: [\"北京\", \"上海\"],\n        attachments: [],\n      },\n      showStartTimePicker: false,\n      showEndTimePicker: false,\n      showPurposePicker: false,\n      showTravelTypePicker: false,\n      startTimeValue: Number(new Date()),\n      endTimeValue: Number(new Date()),\n      purposeOptions: [\n        [\"商务洽谈\", \"技术交流\", \"培训学习\", \"会议参加\", \"项目实施\", \"其他\"],\n      ],\n      travelTypeOptions: [[\"国内\", \"国际\"]],\n      // 支付宝小程序原生picker数据\n      purposeList: [\n        \"商务洽谈\",\n        \"技术交流\",\n        \"培训学习\",\n        \"会议参加\",\n        \"项目实施\",\n        \"其他\",\n      ],\n      travelTypeList: [\"国内\", \"国际\"],\n    };\n  },\n  methods: {\n    handleSave() {\n      console.log(\"保存出差申请信息:\", this.formData);\n      uni.showToast({\n        title: \"保存成功\",\n        icon: \"success\",\n      });\n    },\n    confirmStartTime(e) {\n\n      this.formData.startTime = e.detail.value;\n      this.showStartTimePicker = false;\n\n\n\n\n\n    },\n    confirmEndTime(e) {\n\n      this.formData.endTime = e.detail.value;\n      this.showEndTimePicker = false;\n\n\n\n\n\n    },\n    confirmPurpose(e) {\n\n      this.formData.purpose = this.purposeList[e.detail.value];\n      this.showPurposePicker = false;\n\n\n\n\n\n    },\n    confirmTravelType(e) {\n\n      this.formData.travelType = this.travelTypeList[e.detail.value];\n      this.showTravelTypePicker = false;\n\n\n\n\n\n    },\n    addTraveler() {\n      console.log(\"添加出行人\");\n      // 这里可以跳转到选择人员页面或弹出选择框\n    },\n    removeCity(index) {\n      this.formData.cities.splice(index, 1);\n    },\n    selectAttachment() {\n      console.log(\"选择附件\");\n      // 这里可以调用文件选择API\n    },\n    onFlightChange(e) {\n      this.formData.isFlight = e.detail.value;\n    },\n    onLongTripChange(e) {\n      this.formData.isLongTrip = e.detail.value;\n    },\n    goBack() {\n      uni.navigateBack();\n    },\n  },\n};\n", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=style&index=0&id=3c4893fb&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel-application.vue?vue&type=style&index=0&id=3c4893fb&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753516912045\n      var cssReload = require(\"/Users/<USER>/Desktop/workspace/云梦泽项目/mPaaS-uniapp-tutorial/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}