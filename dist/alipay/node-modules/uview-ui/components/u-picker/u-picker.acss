
view.data-v-d45639b2, scroll-view.data-v-d45639b2, swiper-item.data-v-d45639b2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  -webkit-flex-shrink: 0;
          flex-shrink: 0;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
          flex-grow: 0;
  -webkit-flex-basis: auto;
          flex-basis: auto;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
          align-items: stretch;
  -webkit-align-content: flex-start;
          align-content: flex-start;
}
.u-picker.data-v-d45639b2 {
  position: relative;
}
.u-picker__view__column.data-v-d45639b2 {

  display: -webkit-box;
  display: -webkit-flex;
  display: flex;

  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
          flex: 1;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.u-picker__view__column__item.data-v-d45639b2 {

  display: -webkit-box;
  display: -webkit-flex;
  display: flex;

  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  font-size: 16px;
  text-align: center;
  display: block;
  color: #303133;
}
.u-picker__view__column__item--disabled.data-v-d45639b2 {
  cursor: not-allowed;
  opacity: 0.35;
}
.u-picker--loading.data-v-d45639b2 {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;

  display: -webkit-box;
  display: -webkit-flex;
  display: flex;

  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  background-color: rgba(255, 255, 255, 0.87);
  z-index: 1000;
}
