<u-popup vue-id="18db9a3a-1" show="{{show}}" data-event-opts="{{[['^close',[['closeHandler']]]]}}" onClose="__e" class="data-v-d45639b2" onVueInit="__l"><view class="u-picker data-v-d45639b2"><block a:if="{{showToolbar}}"><u-toolbar vue-id="{{('18db9a3a-2')+','+('18db9a3a-1')}}" cancelColor="{{cancelColor}}" confirmColor="{{confirmColor}}" cancelText="{{cancelText}}" confirmText="{{confirmText}}" title="{{title}}" data-event-opts="{{[['^cancel',[['cancel']]],['^confirm',[['confirm']]]]}}" onCancel="__e" onConfirm="__e" class="data-v-d45639b2" onVueInit="__l"></u-toolbar></block><picker-view class="u-picker__view data-v-d45639b2" style="{{'height:'+(''+$root.g0)+';'}}" indicatorStyle="{{'height: '+$root.g1}}" value="{{innerIndex}}" immediateChange="{{immediateChange}}" data-event-opts="{{[['change',[['changeHandler',['$event']]]]]}}" onChange="__e"><block a:for="{{$root.l1}}" a:for-item="item" a:for-index="index" a:key="index"><picker-view-column class="u-picker__view__column data-v-d45639b2"><block a:for="{{item.l0}}" a:for-item="item1" a:for-index="index1" a:key="index1"><block a:if="{{item.g2}}"><text class="u-picker__view__column__item u-line-1 data-v-d45639b2" style="{{'height:'+(item.g3)+';'+('line-height:'+(item.g4)+';')+('font-weight:'+(index1===innerIndex[index]?'bold':'normal')+';')+('display:'+('block')+';')}}">{{item1.m0}}</text></block></block></picker-view-column></block></picker-view><block a:if="{{loading}}"><view class="u-picker--loading data-v-d45639b2"><u-loading-icon vue-id="{{('18db9a3a-3')+','+('18db9a3a-1')}}" mode="circle" class="data-v-d45639b2" onVueInit="__l"></u-loading-icon></view></block></view></u-popup>