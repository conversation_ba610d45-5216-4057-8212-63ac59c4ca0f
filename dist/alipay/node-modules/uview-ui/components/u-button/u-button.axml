<button class="{{(('u-button u-reset-button data-v-3bf2dba7')+' '+bemClass)}}" style="{{$root.s0}}" hover-start-time="{{$root.m0}}" hover-stay-time="{{$root.m1}}" form-type="{{formType}}" open-type="{{openType}}" app-parameter="{{appParameter}}" hover-stop-propagation="{{hoverStopPropagation}}" send-message-title="{{sendMessageTitle}}" send-message-path="{{sendMessagePath}}" lang="{{lang}}" data-name="{{dataName}}" session-from="{{sessionFrom}}" send-message-img="{{sendMessageImg}}" show-message-card="{{showMessageCard}}" hover-class="{{!disabled&&!loading?'u-button--active':''}}" data-event-opts="{{[['getphonenumber',[['getphonenumber',['$event']]]],['getuserinfo',[['getuserinfo',['$event']]]],['error',[['error',['$event']]]],['opensetting',[['opensetting',['$event']]]],['launchapp',[['launchapp',['$event']]]],['agreeprivacyauthorization',[['agreeprivacyauthorization',['$event']]]],['tap',[['clickHandler',['$event']]]]]}}" onGetphonenumber="__e" onGetuserinfo="__e" onError="__e" onOpensetting="__e" onLaunchapp="__e" onAgreeprivacyauthorization="__e" onTap="__e"><block a:if="{{loading}}"><u-loading-icon vue-id="11334a63-1" mode="{{loadingMode}}" size="{{loadingSize*1.15}}" color="{{loadingColor}}" class="data-v-3bf2dba7" onVueInit="__l"></u-loading-icon><text class="u-button__loading-text data-v-3bf2dba7" style="{{'font-size:'+(textSize+'px')+';'}}">{{loadingText||text}}</text></block><block a:else><block a:if="{{icon}}"><u-icon vue-id="11334a63-2" name="{{icon}}" color="{{iconColorCom}}" size="{{textSize*1.35}}" customStyle="{{$root.a0}}" class="data-v-3bf2dba7" onVueInit="__l"></u-icon></block><block a:if="{{$slots.default}}"><slot></slot></block><block a:else><text class="u-button__text data-v-3bf2dba7" style="{{'font-size:'+(textSize+'px')+';'}}">{{text}}</text></block></block></button>