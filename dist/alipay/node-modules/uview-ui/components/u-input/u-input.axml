<view class="{{(('u-input data-v-fdbb9fe6')+' '+inputClass)}}" style="{{$root.s0}}"><view class="u-input__content data-v-fdbb9fe6"><block a:if="{{prefixIcon||$slots.prefix}}"><view class="u-input__content__prefix-icon data-v-fdbb9fe6"><block a:if="{{$slots.prefix}}"><slot name="prefix"></slot></block><block a:else><u-icon vue-id="8b8b8386-1" name="{{prefixIcon}}" size="18" customStyle="{{prefixIconStyle}}" class="data-v-fdbb9fe6" onVueInit="__l"></u-icon></block></view></block><view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="u-input__content__field-wrapper data-v-fdbb9fe6" onTap="__e"><input class="u-input__content__field-wrapper__field data-v-fdbb9fe6" style="{{$root.s1}}" type="{{type}}" focus="{{focus}}" cursor="{{cursor}}" auto-blur="{{autoBlur}}" disabled="{{disabled||readonly}}" maxlength="{{maxlength}}" placeholder="{{placeholder}}" placeholder-style="{{placeholderStyle}}" placeholder-class="{{placeholderClass}}" confirm-type="{{confirmType}}" confirm-hold="{{confirmHold}}" hold-keyboard="{{holdKeyboard}}" cursor-spacing="{{cursorSpacing}}" adjust-position="{{adjustPosition}}" selection-end="{{selectionEnd}}" selection-start="{{selectionStart}}" password="{{password||type==='password'||undefined}}" data-event-opts="{{[['input',[['onInput',['$event']]]],['blur',[['onBlur',['$event']]]],['focus',[['onFocus',['$event']]]],['confirm',[['onConfirm',['$event']]]],['keyboardheightchange',[['onkeyboardheightchange',['$event']]]]]}}" value="{{innerValue}}" onInput="__e" onBlur="__e" onFocus="__e" onConfirm="__e" onKeyboardheightchange="__e"/></view><block a:if="{{isShowClear}}"><view data-event-opts="{{[['tap',[['onClear',['$event']]]]]}}" class="u-input__content__clear data-v-fdbb9fe6" onTap="__e"><u-icon vue-id="8b8b8386-2" name="close" size="11" color="#ffffff" customStyle="line-height: 12px" class="data-v-fdbb9fe6" onVueInit="__l"></u-icon></view></block><block a:if="{{suffixIcon||$slots.suffix}}"><view class="u-input__content__subfix-icon data-v-fdbb9fe6"><block a:if="{{$slots.suffix}}"><slot name="suffix"></slot></block><block a:else><u-icon vue-id="8b8b8386-3" name="{{suffixIcon}}" size="18" customStyle="{{suffixIconStyle}}" class="data-v-fdbb9fe6" onVueInit="__l"></u-icon></block></view></block></view></view>