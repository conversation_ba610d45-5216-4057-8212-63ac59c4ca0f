<view class="u-popup data-v-52d4ddd1"><block a:if="{{overlay}}"><u-overlay vue-id="1edd48f9-1" show="{{show}}" duration="{{overlayDuration}}" customStyle="{{overlayStyle}}" opacity="{{overlayOpacity}}" data-event-opts="{{[['^click',[['overlayClick']]]]}}" onClick="__e" class="data-v-52d4ddd1" onVueInit="__l"></u-overlay></block><u-transition vue-id="1edd48f9-2" show="{{show}}" customStyle="{{transitionStyle}}" mode="{{position}}" duration="{{duration}}" data-event-opts="{{[['^afterEnter',[['afterEnter']]],['^click',[['clickHandler']]]]}}" onAfterEnter="__e" onClick="__e" class="data-v-52d4ddd1" onVueInit="__l"><view data-event-opts="{{[['tap',[['noop',['$event']]]]]}}" class="u-popup__content data-v-52d4ddd1" style="{{$root.s0}}" catchTap="__e"><block a:if="{{safeAreaInsetTop}}"><u-status-bar vue-id="{{('1edd48f9-3')+','+('1edd48f9-2')}}" class="data-v-52d4ddd1" onVueInit="__l"></u-status-bar></block><slot></slot><block a:if="{{closeable}}"><view class="{{(('u-popup__content__close data-v-52d4ddd1')+' '+('u-popup__content__close--'+closeIconPos))}}" hover-class="u-popup__content__close--hover" hover-stay-time="150" data-event-opts="{{[['tap',[['close',['$event']]]]]}}" catchTap="__e"><u-icon vue-id="{{('1edd48f9-4')+','+('1edd48f9-2')}}" name="close" color="#909399" size="18" bold="{{true}}" class="data-v-52d4ddd1" onVueInit="__l"></u-icon></view></block><block a:if="{{safeAreaInsetBottom}}"><u-safe-bottom vue-id="{{('1edd48f9-5')+','+('1edd48f9-2')}}" class="data-v-52d4ddd1" onVueInit="__l"></u-safe-bottom></block></view></u-transition></view>