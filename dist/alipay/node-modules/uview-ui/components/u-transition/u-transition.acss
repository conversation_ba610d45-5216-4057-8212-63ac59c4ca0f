@charset "UTF-8";
view.data-v-39e33bf2, scroll-view.data-v-39e33bf2, swiper-item.data-v-39e33bf2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  -webkit-flex-shrink: 0;
          flex-shrink: 0;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
          flex-grow: 0;
  -webkit-flex-basis: auto;
          flex-basis: auto;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
          align-items: stretch;
  -webkit-align-content: flex-start;
          align-content: flex-start;
}

/**
 * vue版本动画内置的动画模式有如下：
 * fade：淡入
 * zoom：缩放
 * fade-zoom：缩放淡入
 * fade-up：上滑淡入
 * fade-down：下滑淡入
 * fade-left：左滑淡入
 * fade-right：右滑淡入
 * slide-up：上滑进入
 * slide-down：下滑进入
 * slide-left：左滑进入
 * slide-right：右滑进入
 */
.u-fade-enter-active.data-v-39e33bf2,
.u-fade-leave-active.data-v-39e33bf2 {
  -webkit-transition-property: opacity;
  transition-property: opacity;
}
.u-fade-enter.data-v-39e33bf2,
.u-fade-leave-to.data-v-39e33bf2 {
  opacity: 0;
}
.u-fade-zoom-enter.data-v-39e33bf2,
.u-fade-zoom-leave-to.data-v-39e33bf2 {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  opacity: 0;
}
.u-fade-zoom-enter-active.data-v-39e33bf2,
.u-fade-zoom-leave-active.data-v-39e33bf2 {
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
}
.u-fade-down-enter-active.data-v-39e33bf2,
.u-fade-down-leave-active.data-v-39e33bf2,
.u-fade-left-enter-active.data-v-39e33bf2,
.u-fade-left-leave-active.data-v-39e33bf2,
.u-fade-right-enter-active.data-v-39e33bf2,
.u-fade-right-leave-active.data-v-39e33bf2,
.u-fade-up-enter-active.data-v-39e33bf2,
.u-fade-up-leave-active.data-v-39e33bf2 {
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: opacity, transform;
  transition-property: opacity, transform, -webkit-transform;
}
.u-fade-up-enter.data-v-39e33bf2,
.u-fade-up-leave-to.data-v-39e33bf2 {
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
  opacity: 0;
}
.u-fade-down-enter.data-v-39e33bf2,
.u-fade-down-leave-to.data-v-39e33bf2 {
  -webkit-transform: translate3d(0, -100%, 0);
          transform: translate3d(0, -100%, 0);
  opacity: 0;
}
.u-fade-left-enter.data-v-39e33bf2,
.u-fade-left-leave-to.data-v-39e33bf2 {
  -webkit-transform: translate3d(-100%, 0, 0);
          transform: translate3d(-100%, 0, 0);
  opacity: 0;
}
.u-fade-right-enter.data-v-39e33bf2,
.u-fade-right-leave-to.data-v-39e33bf2 {
  -webkit-transform: translate3d(100%, 0, 0);
          transform: translate3d(100%, 0, 0);
  opacity: 0;
}
.u-slide-down-enter-active.data-v-39e33bf2,
.u-slide-down-leave-active.data-v-39e33bf2,
.u-slide-left-enter-active.data-v-39e33bf2,
.u-slide-left-leave-active.data-v-39e33bf2,
.u-slide-right-enter-active.data-v-39e33bf2,
.u-slide-right-leave-active.data-v-39e33bf2,
.u-slide-up-enter-active.data-v-39e33bf2,
.u-slide-up-leave-active.data-v-39e33bf2 {
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
}
.u-slide-up-enter.data-v-39e33bf2,
.u-slide-up-leave-to.data-v-39e33bf2 {
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
}
.u-slide-down-enter.data-v-39e33bf2,
.u-slide-down-leave-to.data-v-39e33bf2 {
  -webkit-transform: translate3d(0, -100%, 0);
          transform: translate3d(0, -100%, 0);
}
.u-slide-left-enter.data-v-39e33bf2,
.u-slide-left-leave-to.data-v-39e33bf2 {
  -webkit-transform: translate3d(-100%, 0, 0);
          transform: translate3d(-100%, 0, 0);
}
.u-slide-right-enter.data-v-39e33bf2,
.u-slide-right-leave-to.data-v-39e33bf2 {
  -webkit-transform: translate3d(100%, 0, 0);
          transform: translate3d(100%, 0, 0);
}
.u-zoom-enter-active.data-v-39e33bf2,
.u-zoom-leave-active.data-v-39e33bf2 {
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
}
.u-zoom-enter.data-v-39e33bf2,
.u-zoom-leave-to.data-v-39e33bf2 {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
