<u-picker vue-id="01e0ed51-1" show="{{show}}" closeOnClickOverlay="{{closeOnClickOverlay}}" columns="{{columns}}" title="{{title}}" itemHeight="{{itemHeight}}" showToolbar="{{showToolbar}}" visibleItemCount="{{visibleItemCount}}" defaultIndex="{{innerDefaultIndex}}" cancelText="{{cancelText}}" confirmText="{{confirmText}}" cancelColor="{{cancelColor}}" confirmColor="{{confirmColor}}" immediateChange="{{immediateChange}}" ref="__r" data-ref="picker" data-event-opts="{{[['^close',[['close']]],['^cancel',[['cancel']]],['^confirm',[['confirm']]],['^change',[['change']]]]}}" onClose="__e" onCancel="__e" onConfirm="__e" onChange="__e" class="data-v-7d06fb79" onVueInit="__l"></u-picker>