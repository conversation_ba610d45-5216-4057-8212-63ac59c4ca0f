
view.data-v-643b3322, scroll-view.data-v-643b3322, swiper-item.data-v-643b3322 {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  -webkit-flex-shrink: 0;
          flex-shrink: 0;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
          flex-grow: 0;
  -webkit-flex-basis: auto;
          flex-basis: auto;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
          align-items: stretch;
  -webkit-align-content: flex-start;
          align-content: flex-start;
}
.u-radio.data-v-643b3322 {

  display: -webkit-box;
  display: -webkit-flex;
  display: flex;

  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
  overflow: hidden;
  flex-direction: row;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.u-radio-label--left.data-v-643b3322 {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.u-radio-label--right.data-v-643b3322 {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -webkit-flex-direction: row-reverse;
          flex-direction: row-reverse;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.u-radio__icon-wrap.data-v-643b3322 {
  box-sizing: border-box;
  -webkit-transition-property: border-color, background-color, color;
  transition-property: border-color, background-color, color;
  -webkit-transition-duration: 0.2s;
          transition-duration: 0.2s;
  color: #606266;

  display: -webkit-box;
  display: -webkit-flex;
  display: flex;

  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  color: transparent;
  text-align: center;
  margin-right: 6px;
  font-size: 20px;
  border-width: 1px;
  border-color: #c8c9cc;
  border-style: solid;
}
.u-radio__icon-wrap--circle.data-v-643b3322 {
  border-radius: 100%;
}
.u-radio__icon-wrap--square.data-v-643b3322 {
  border-radius: 3px;
}
.u-radio__icon-wrap--checked.data-v-643b3322 {
  color: #fff;
  background-color: red;
  border-color: #2979ff;
}
.u-radio__icon-wrap--disabled.data-v-643b3322 {
  background-color: #ebedf0 !important;
}
.u-radio__icon-wrap--disabled--checked.data-v-643b3322 {
  color: #c8c9cc !important;
}
.u-radio__label.data-v-643b3322 {
  word-wrap: break-word;
  margin-left: 5px;
  margin-right: 12px;
  color: #606266;
  font-size: 15px;
}
.u-radio__label--disabled.data-v-643b3322 {
  color: #c8c9cc;
}
