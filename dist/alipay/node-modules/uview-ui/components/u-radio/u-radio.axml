<view data-event-opts="{{[['tap',[['wrapperClickHandler',['$event']]]]]}}" class="{{((('u-radio data-v-643b3322')+' '+('u-radio-label--'+parentData.iconPlacement))+' '+(parentData.borderBottom&&parentData.placement==='column'&&'u-border-bottom'))}}" style="{{$root.s0}}" catchTap="__e"><view data-event-opts="{{[['tap',[['iconClickHandler',['$event']]]]]}}" class="{{(('u-radio__icon-wrap data-v-643b3322')+' '+iconClasses)}}" style="{{$root.s1}}" catchTap="__e"><block a:if="{{$slots.icon}}"><slot name="icon"></slot></block><block a:else><u-icon class="u-radio__icon-wrap__icon data-v-643b3322" vue-id="0580d41b-1" name="checkbox-mark" size="{{elIconSize}}" color="{{elIconColor}}" onVueInit="__l"></u-icon></block></view><text data-event-opts="{{[['tap',[['labelClickHandler',['$event']]]]]}}" class="u-radio__text data-v-643b3322" style="{{'color:'+(elDisabled?elInactiveColor:elLabelColor)+';'+('font-size:'+(elLabelSize)+';')+('line-height:'+(elLabelSize)+';')}}" catchTap="__e">{{label}}</text></view>