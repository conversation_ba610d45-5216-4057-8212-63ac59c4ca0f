<view class="{{(('u-textarea data-v-81cd9d32')+' '+textareaClass)}}" style="{{$root.s0}}"><textarea class="u-textarea__field data-v-81cd9d32" style="{{'height:'+($root.g0)+';'}}" placeholder="{{placeholder}}" placeholder-style="{{$root.g1}}" placeholder-class="{{placeholderClass}}" disabled="{{disabled}}" focus="{{focus}}" autoHeight="{{autoHeight}}" fixed="{{fixed}}" cursorSpacing="{{cursorSpacing}}" cursor="{{cursor}}" showConfirmBar="{{showConfirmBar}}" selectionStart="{{selectionStart}}" selectionEnd="{{selectionEnd}}" adjustPosition="{{adjustPosition}}" disableDefaultPadding="{{disableDefaultPadding}}" holdKeyboard="{{holdKeyboard}}" maxlength="{{maxlength}}" confirmType="{{confirmType}}" ignoreCompositionEvent="{{ignoreCompositionEvent}}" data-event-opts="{{[['focus',[['onFocus',['$event']]]],['blur',[['onBlur',['$event']]]],['linechange',[['onLinechange',['$event']]]],['input',[['onInput',['$event']]]],['confirm',[['onConfirm',['$event']]]],['keyboardheightchange',[['onKeyboardheightchange',['$event']]]]]}}" value="{{innerValue}}" onFocus="__e" onBlur="__e" onLinechange="__e" onInput="__e" onConfirm="__e" onKeyboardheightchange="__e"></textarea><block a:if="{{count}}"><text class="u-textarea__count data-v-81cd9d32" style="{{'background-color:'+(disabled?'transparent':'#fff')+';'}}">{{innerValue.length+"/"+maxlength}}</text></block></view>