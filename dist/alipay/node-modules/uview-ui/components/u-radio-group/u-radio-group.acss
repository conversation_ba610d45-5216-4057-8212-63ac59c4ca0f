
view.data-v-4a02ae53, scroll-view.data-v-4a02ae53, swiper-item.data-v-4a02ae53 {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  -webkit-flex-shrink: 0;
          flex-shrink: 0;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
          flex-grow: 0;
  -webkit-flex-basis: auto;
          flex-basis: auto;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
          align-items: stretch;
  -webkit-align-content: flex-start;
          align-content: flex-start;
}
.u-radio-group.data-v-4a02ae53 {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
          flex: 1;
}
.u-radio-group--row.data-v-4a02ae53 {

  display: -webkit-box;
  display: -webkit-flex;
  display: flex;

  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.u-radio-group--column.data-v-4a02ae53 {

  display: -webkit-box;
  display: -webkit-flex;
  display: flex;

  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
