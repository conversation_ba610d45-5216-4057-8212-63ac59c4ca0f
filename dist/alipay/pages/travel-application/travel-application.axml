<view class="travel-application data-v-3c4893fb"><view class="custom-navbar data-v-3c4893fb"><view class="navbar-content data-v-3c4893fb"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left data-v-3c4893fb" onTap="__e"><text class="back-icon data-v-3c4893fb">‹</text></view><view class="navbar-title data-v-3c4893fb"><text class="title-text data-v-3c4893fb">出差申请信息</text></view><view data-event-opts="{{[['tap',[['handleSave',['$event']]]]]}}" class="navbar-right data-v-3c4893fb" onTap="__e"><text class="save-text data-v-3c4893fb">保存</text></view></view></view><view class="form-container data-v-3c4893fb"><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">起始时间</text><input class="custom-input data-v-3c4893fb" placeholder="请选择起始时间" readonly="{{true}}" data-event-opts="{{[['tap',[['e0',['$event']]]],['input',[['__set_model',['$0','startTime','$event',[]],['formData']]]]]}}" value="{{formData.startTime}}" onTap="__e" onInput="__e"/></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">结束时间</text><input class="custom-input data-v-3c4893fb" placeholder="请选择结束时间" readonly="{{true}}" data-event-opts="{{[['tap',[['e1',['$event']]]],['input',[['__set_model',['$0','endTime','$event',[]],['formData']]]]]}}" value="{{formData.endTime}}" onTap="__e" onInput="__e"/></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">出差目的</text><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="custom-input-wrapper data-v-3c4893fb" onTap="__e"><input class="custom-input data-v-3c4893fb" placeholder="请选择出差目的" readonly="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','purpose','$event',[]],['formData']]]]]}}" value="{{formData.purpose}}" onInput="__e"/><text class="arrow-icon data-v-3c4893fb">▼</text></view></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">出差项目 项目名称</text><input class="custom-input data-v-3c4893fb" placeholder="请输入项目名称" data-event-opts="{{[['input',[['__set_model',['$0','projectName','$event',[]],['formData']]]]]}}" value="{{formData.projectName}}" onInput="__e"/></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">项目预算</text><input class="custom-input data-v-3c4893fb" placeholder="请输入项目预算" type="number" data-event-opts="{{[['input',[['__set_model',['$0','budget','$event',[]],['formData']]]]]}}" value="{{formData.budget}}" onInput="__e"/></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">国内国际</text><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="custom-input-wrapper data-v-3c4893fb" onTap="__e"><input class="custom-input data-v-3c4893fb" placeholder="请选择国内国际" readonly="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','travelType','$event',[]],['formData']]]]]}}" value="{{formData.travelType}}" onInput="__e"/><text class="arrow-icon data-v-3c4893fb">▼</text></view></view><view class="form-item radio-item data-v-3c4893fb"><text class="label data-v-3c4893fb">是否乘坐飞机</text><view class="radio-group data-v-3c4893fb"><radio-group data-event-opts="{{[['change',[['onFlightChange',['$event']]]]]}}" onChange="__e" class="data-v-3c4893fb"><label class="radio-label data-v-3c4893fb"><radio value="yes" checked="{{formData.isFlight==='yes'}}" class="data-v-3c4893fb"></radio><text class="radio-text data-v-3c4893fb">是</text></label><label class="radio-label data-v-3c4893fb"><radio value="no" checked="{{formData.isFlight==='no'}}" class="data-v-3c4893fb"></radio><text class="radio-text data-v-3c4893fb">否</text></label></radio-group></view></view><view class="form-item radio-item data-v-3c4893fb"><text class="label data-v-3c4893fb">是否长差(长差31天及以上)</text><view class="radio-group data-v-3c4893fb"><radio-group data-event-opts="{{[['change',[['onLongTripChange',['$event']]]]]}}" onChange="__e" class="data-v-3c4893fb"><label class="radio-label data-v-3c4893fb"><radio value="no" checked="{{formData.isLongTrip==='no'}}" class="data-v-3c4893fb"></radio><text class="radio-text data-v-3c4893fb">否</text></label></radio-group></view></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">出行人</text><view class="traveler-section data-v-3c4893fb"><view data-event-opts="{{[['tap',[['addTraveler',['$event']]]]]}}" class="add-traveler data-v-3c4893fb" onTap="__e"><text class="plus-icon data-v-3c4893fb">+</text></view></view></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">详细说明</text><textarea class="custom-textarea data-v-3c4893fb" placeholder="请输入详细说明" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['formData']]]]]}}" value="{{formData.description}}" onInput="__e"></textarea></view><view class="form-item data-v-3c4893fb"><view class="add-city-header data-v-3c4893fb"><text class="plus-icon data-v-3c4893fb">+</text><text class="add-city-text data-v-3c4893fb">添加出差城市</text></view><view class="city-tags data-v-3c4893fb"><block a:for="{{formData.cities}}" a:for-item="city" a:for-index="index" a:key="index"><view class="city-tag data-v-3c4893fb"><text class="city-name data-v-3c4893fb">{{city}}</text><text data-event-opts="{{[['tap',[['removeCity',[index]]]]]}}" class="close-icon data-v-3c4893fb" onTap="__e">×</text></view></block></view></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">附件</text><view class="attachment-section data-v-3c4893fb"><view data-event-opts="{{[['tap',[['selectAttachment',['$event']]]]]}}" class="attachment-placeholder data-v-3c4893fb" onTap="__e"><text class="photo-icon data-v-3c4893fb">📷</text></view></view></view></view><block a:if="{{showStartTimePicker}}"><picker mode="date" data-event-opts="{{[['change',[['confirmStartTime',['$event']]]],['cancel',[['e4',['$event']]]]]}}" onChange="__e" onCancel="__e" class="data-v-3c4893fb"><view class="data-v-3c4893fb"></view></picker></block><block a:if="{{showEndTimePicker}}"><picker mode="date" data-event-opts="{{[['change',[['confirmEndTime',['$event']]]],['cancel',[['e5',['$event']]]]]}}" onChange="__e" onCancel="__e" class="data-v-3c4893fb"><view class="data-v-3c4893fb"></view></picker></block><block a:if="{{showPurposePicker}}"><picker range="{{purposeList}}" data-event-opts="{{[['change',[['confirmPurpose',['$event']]]],['cancel',[['e6',['$event']]]]]}}" onChange="__e" onCancel="__e" class="data-v-3c4893fb"><view class="data-v-3c4893fb"></view></picker></block><block a:if="{{showTravelTypePicker}}"><picker range="{{travelTypeList}}" data-event-opts="{{[['change',[['confirmTravelType',['$event']]]],['cancel',[['e7',['$event']]]]]}}" onChange="__e" onCancel="__e" class="data-v-3c4893fb"><view class="data-v-3c4893fb"></view></picker></block></view>