<view class="travel-application data-v-3c4893fb"><u-navbar vue-id="34b23a76-1" is-back="{{true}}" title="出差申请信息" background="{{$root.a0}}" title-style="{{$root.a1}}" class="data-v-3c4893fb" onVueInit="__l"><view class="save-btn data-v-3c4893fb" slot="right" data-event-opts="{{[['tap',[['handleSave',['$event']]]]]}}" onTap="__e"><text class="save-text data-v-3c4893fb">保存</text></view></u-navbar><view class="form-container data-v-3c4893fb"><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">起始时间</text><u-input vue-id="34b23a76-2" placeholder="请选择起始时间" border="{{false}}" readonly="{{true}}" value="{{formData.startTime}}" data-event-opts="{{[['^click',[['e0']]],['^input',[['__set_model',['$0','startTime','$event',[]],['formData']]]]]}}" onClick="__e" onInput="__e" class="data-v-3c4893fb" onVueInit="__l"></u-input></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">结束时间</text><u-input vue-id="34b23a76-3" placeholder="请选择结束时间" border="{{false}}" readonly="{{true}}" value="{{formData.endTime}}" data-event-opts="{{[['^click',[['e1']]],['^input',[['__set_model',['$0','endTime','$event',[]],['formData']]]]]}}" onClick="__e" onInput="__e" class="data-v-3c4893fb" onVueInit="__l"></u-input></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">出差目的</text><u-input vue-id="34b23a76-4" placeholder="请选择出差目的" border="{{false}}" suffix-icon="arrow-down" readonly="{{true}}" value="{{formData.purpose}}" data-event-opts="{{[['^click',[['e2']]],['^input',[['__set_model',['$0','purpose','$event',[]],['formData']]]]]}}" onClick="__e" onInput="__e" class="data-v-3c4893fb" onVueInit="__l"></u-input></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">出差项目 项目名称</text><u-input onInput="__e" vue-id="34b23a76-5" placeholder="请输入项目名称" border="{{false}}" value="{{formData.projectName}}" data-event-opts="{{[['^input',[['__set_model',['$0','projectName','$event',[]],['formData']]]]]}}" class="data-v-3c4893fb" onVueInit="__l"></u-input></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">项目预算</text><u-input onInput="__e" vue-id="34b23a76-6" placeholder="请输入项目预算" border="{{false}}" type="number" value="{{formData.budget}}" data-event-opts="{{[['^input',[['__set_model',['$0','budget','$event',[]],['formData']]]]]}}" class="data-v-3c4893fb" onVueInit="__l"></u-input></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">国内国际</text><u-input vue-id="34b23a76-7" placeholder="请选择国内国际" border="{{false}}" suffix-icon="arrow-down" readonly="{{true}}" value="{{formData.travelType}}" data-event-opts="{{[['^click',[['e3']]],['^input',[['__set_model',['$0','travelType','$event',[]],['formData']]]]]}}" onClick="__e" onInput="__e" class="data-v-3c4893fb" onVueInit="__l"></u-input></view><view class="form-item radio-item data-v-3c4893fb"><text class="label data-v-3c4893fb">是否乘坐飞机</text><view class="radio-group data-v-3c4893fb"><u-radio-group onInput="__e" vue-id="34b23a76-8" placement="row" value="{{formData.isFlight}}" data-event-opts="{{[['^input',[['__set_model',['$0','isFlight','$event',[]],['formData']]]]]}}" class="data-v-3c4893fb" onVueInit="__l"><u-radio vue-id="{{('34b23a76-9')+','+('34b23a76-8')}}" customStyle="{{$root.a2}}" label="是" name="yes" class="data-v-3c4893fb" onVueInit="__l"></u-radio><u-radio vue-id="{{('34b23a76-10')+','+('34b23a76-8')}}" label="否" name="no" class="data-v-3c4893fb" onVueInit="__l"></u-radio></u-radio-group></view></view><view class="form-item radio-item data-v-3c4893fb"><text class="label data-v-3c4893fb">是否长差(长差31天及以上)</text><view class="radio-group data-v-3c4893fb"><u-radio-group onInput="__e" vue-id="34b23a76-11" placement="row" value="{{formData.isLongTrip}}" data-event-opts="{{[['^input',[['__set_model',['$0','isLongTrip','$event',[]],['formData']]]]]}}" class="data-v-3c4893fb" onVueInit="__l"><u-radio vue-id="{{('34b23a76-12')+','+('34b23a76-11')}}" label="否" name="no" class="data-v-3c4893fb" onVueInit="__l"></u-radio></u-radio-group></view></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">出行人</text><view class="traveler-section data-v-3c4893fb"><view data-event-opts="{{[['tap',[['addTraveler',['$event']]]]]}}" class="add-traveler data-v-3c4893fb" onTap="__e"><u-icon vue-id="34b23a76-13" name="plus-circle" size="20" color="#007AFF" class="data-v-3c4893fb" onVueInit="__l"></u-icon></view></view></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">详细说明</text><u-textarea onInput="__e" vue-id="34b23a76-14" placeholder="请输入详细说明" border="{{false}}" height="80" value="{{formData.description}}" data-event-opts="{{[['^input',[['__set_model',['$0','description','$event',[]],['formData']]]]]}}" class="data-v-3c4893fb" onVueInit="__l"></u-textarea></view><view class="form-item data-v-3c4893fb"><view class="add-city-header data-v-3c4893fb"><u-icon vue-id="34b23a76-15" name="plus-circle" size="20" color="#007AFF" class="data-v-3c4893fb" onVueInit="__l"></u-icon><text class="add-city-text data-v-3c4893fb">添加出差城市</text></view><view class="city-tags data-v-3c4893fb"><block a:for="{{formData.cities}}" a:for-item="city" a:for-index="index" a:key="index"><view class="city-tag data-v-3c4893fb"><text class="city-name data-v-3c4893fb">{{city}}</text><u-icon vue-id="{{'34b23a76-16-'+index}}" name="close-circle-fill" size="16" color="#999999" data-event-opts="{{[['^click',[['removeCity',[index]]]]]}}" onClick="__e" class="data-v-3c4893fb" onVueInit="__l"></u-icon></view></block></view></view><view class="form-item data-v-3c4893fb"><text class="label data-v-3c4893fb">附件</text><view class="attachment-section data-v-3c4893fb"><view data-event-opts="{{[['tap',[['selectAttachment',['$event']]]]]}}" class="attachment-placeholder data-v-3c4893fb" onTap="__e"><u-icon vue-id="34b23a76-17" name="photo" size="40" color="#CCCCCC" class="data-v-3c4893fb" onVueInit="__l"></u-icon></view></view></view></view><u-datetime-picker vue-id="34b23a76-18" show="{{showStartTimePicker}}" mode="datetime" value="{{startTimeValue}}" data-event-opts="{{[['^confirm',[['confirmStartTime']]],['^cancel',[['e4']]],['^input',[['__set_model',['','startTimeValue','$event',[]]]]]]}}" onConfirm="__e" onCancel="__e" onInput="__e" class="data-v-3c4893fb" onVueInit="__l"></u-datetime-picker><u-datetime-picker vue-id="34b23a76-19" show="{{showEndTimePicker}}" mode="datetime" value="{{endTimeValue}}" data-event-opts="{{[['^confirm',[['confirmEndTime']]],['^cancel',[['e5']]],['^input',[['__set_model',['','endTimeValue','$event',[]]]]]]}}" onConfirm="__e" onCancel="__e" onInput="__e" class="data-v-3c4893fb" onVueInit="__l"></u-datetime-picker><u-picker vue-id="34b23a76-20" show="{{showPurposePicker}}" columns="{{purposeOptions}}" data-event-opts="{{[['^confirm',[['confirmPurpose']]],['^cancel',[['e6']]]]}}" onConfirm="__e" onCancel="__e" class="data-v-3c4893fb" onVueInit="__l"></u-picker><u-picker vue-id="34b23a76-21" show="{{showTravelTypePicker}}" columns="{{travelTypeOptions}}" data-event-opts="{{[['^confirm',[['confirmTravelType']]],['^cancel',[['e7']]]]}}" onConfirm="__e" onCancel="__e" class="data-v-3c4893fb" onVueInit="__l"></u-picker></view>