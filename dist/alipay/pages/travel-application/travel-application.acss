.travel-application.data-v-3c4893fb {
  background-color: #f8f8f8;
  min-height: 100vh;
}
.save-btn.data-v-3c4893fb {
  padding: 0 16px;
}
.save-text.data-v-3c4893fb {
  color: #007AFF;
  font-size: 16px;
}
.form-container.data-v-3c4893fb {
  padding: 12px 16px;
}
.form-item.data-v-3c4893fb {
  background-color: #ffffff;
  margin-bottom: 12px;
  padding: 16px;
  border-radius: 8px;
}
.form-item .label.data-v-3c4893fb {
  display: block;
  color: #333333;
  font-size: 16px;
  margin-bottom: 12px;
  font-weight: 500;
}
.form-item.radio-item.data-v-3c4893fb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.form-item.radio-item .label.data-v-3c4893fb {
  margin-bottom: 0;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
          flex: 1;
}
.form-item.radio-item .radio-group.data-v-3c4893fb {
  -webkit-flex-shrink: 0;
          flex-shrink: 0;
}
.traveler-section .add-traveler.data-v-3c4893fb {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px dashed #CCCCCC;
  border-radius: 20px;
}
.add-city-header.data-v-3c4893fb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  margin-bottom: 12px;
}
.add-city-header .add-city-text.data-v-3c4893fb {
  margin-left: 8px;
  color: #007AFF;
  font-size: 16px;
}
.city-tags.data-v-3c4893fb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
          flex-wrap: wrap;
  gap: 8px;
}
.city-tag.data-v-3c4893fb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  background-color: #f0f0f0;
  padding: 6px 12px;
  border-radius: 16px;
}
.city-tag .city-name.data-v-3c4893fb {
  margin-right: 6px;
  color: #333333;
  font-size: 14px;
}
.attachment-section .attachment-placeholder.data-v-3c4893fb {
  width: 80px;
  height: 80px;
  border: 1px dashed #CCCCCC;
  border-radius: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  background-color: #fafafa;
}
