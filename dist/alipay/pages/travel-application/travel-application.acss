.travel-application.data-v-3c4893fb {
  background-color: #f8f8f8;
  min-height: 100vh;
}
.save-btn.data-v-3c4893fb {
  padding: 0 16px;
}
.save-text.data-v-3c4893fb {
  color: #007aff;
  font-size: 16px;
}
.form-container.data-v-3c4893fb {
  padding: 12px 16px;
}
.form-item.data-v-3c4893fb {
  background-color: #ffffff;
  margin-bottom: 12px;
  padding: 16px;
  border-radius: 8px;
}
.form-item .label.data-v-3c4893fb {
  display: block;
  color: #333333;
  font-size: 16px;
  margin-bottom: 12px;
  font-weight: 500;
}
.form-item.radio-item.data-v-3c4893fb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.form-item.radio-item .label.data-v-3c4893fb {
  margin-bottom: 0;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
          flex: 1;
}
.form-item.radio-item .radio-group.data-v-3c4893fb {
  -webkit-flex-shrink: 0;
          flex-shrink: 0;
}
.traveler-section .add-traveler.data-v-3c4893fb {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px dashed #cccccc;
  border-radius: 20px;
}
.add-city-header.data-v-3c4893fb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  margin-bottom: 12px;
}
.add-city-header .add-city-text.data-v-3c4893fb {
  margin-left: 8px;
  color: #007aff;
  font-size: 16px;
}
.city-tags.data-v-3c4893fb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
          flex-wrap: wrap;
  gap: 8px;
}
.city-tag.data-v-3c4893fb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  background-color: #f0f0f0;
  padding: 6px 12px;
  border-radius: 16px;
}
.city-tag .city-name.data-v-3c4893fb {
  margin-right: 6px;
  color: #333333;
  font-size: 14px;
}
.attachment-section .attachment-placeholder.data-v-3c4893fb {
  width: 80px;
  height: 80px;
  border: 1px dashed #cccccc;
  border-radius: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  background-color: #fafafa;
}
.radio-label.data-v-3c4893fb {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  margin-right: 32px;
}
.radio-label .radio-text.data-v-3c4893fb {
  margin-left: 8px;
  color: #333333;
  font-size: 16px;
}
.custom-navbar.data-v-3c4893fb {
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}
.custom-navbar .navbar-content.data-v-3c4893fb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}
.custom-navbar .navbar-content .navbar-left.data-v-3c4893fb {
  width: 60px;
}
.custom-navbar .navbar-content .navbar-left .back-icon.data-v-3c4893fb {
  font-size: 24px;
  color: #333333;
  font-weight: bold;
}
.custom-navbar .navbar-content .navbar-title.data-v-3c4893fb {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
          flex: 1;
  text-align: center;
}
.custom-navbar .navbar-content .navbar-title .title-text.data-v-3c4893fb {
  color: #333333;
  font-size: 18px;
  font-weight: 500;
}
.custom-navbar .navbar-content .navbar-right.data-v-3c4893fb {
  width: 60px;
  text-align: right;
}
.custom-input.data-v-3c4893fb {
  width: 100%;
  height: 40px;
  padding: 0 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 16px;
  color: #333333;
  background-color: #ffffff;
}
.custom-input-wrapper.data-v-3c4893fb {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.custom-input-wrapper .custom-input.data-v-3c4893fb {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
          flex: 1;
  padding-right: 40px;
}
.custom-input-wrapper .arrow-icon.data-v-3c4893fb {
  position: absolute;
  right: 12px;
  color: #999999;
  font-size: 12px;
}
.custom-textarea.data-v-3c4893fb {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 16px;
  color: #333333;
  background-color: #ffffff;
  resize: none;
}
.plus-icon.data-v-3c4893fb {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: #007aff;
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
}
.close-icon.data-v-3c4893fb {
  margin-left: 6px;
  color: #999999;
  font-size: 16px;
  font-weight: bold;
}
.photo-icon.data-v-3c4893fb {
  font-size: 32px;
  color: #cccccc;
}
