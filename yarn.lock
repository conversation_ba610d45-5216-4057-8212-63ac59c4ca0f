# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.8.3":
  "integrity" "sha1-M+JZA9dIEYFTThLsCiXxa2/PQZ4="
  "resolved" "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/highlight" "^7.8.3"

"@babel/compat-data@^7.9.6":
  "integrity" "sha1-P2BMQOQgExr/5vLIBS6aJ1riBJs="
  "resolved" "https://registry.npmmirror.com/@babel/compat-data/download/@babel/compat-data-7.9.6.tgz?cache=0&sync_timestamp=1588185911086&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fcompat-data%2Fdownload%2F%40babel%2Fcompat-data-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "browserslist" "^4.11.1"
    "invariant" "^2.2.4"
    "semver" "^5.5.0"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.1.0", "@babel/core@^7.3.3", "@babel/core@^7.3.4", "@babel/core@^7.7.5", "@babel/core@^7.9.0":
  "integrity" "sha1-2aofWAq/OyKG70C2kE05CQTGM3Y="
  "resolved" "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@babel/generator" "^7.9.6"
    "@babel/helper-module-transforms" "^7.9.0"
    "@babel/helpers" "^7.9.6"
    "@babel/parser" "^7.9.6"
    "@babel/template" "^7.8.6"
    "@babel/traverse" "^7.9.6"
    "@babel/types" "^7.9.6"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.1"
    "json5" "^2.1.2"
    "lodash" "^4.17.13"
    "resolve" "^1.3.2"
    "semver" "^5.4.1"
    "source-map" "^0.5.0"

"@babel/generator@^7.9.6":
  "integrity" "sha1-VAjIKsXemM2g132BJOmfofIXCkM="
  "resolved" "https://registry.npmmirror.com/@babel/generator/download/@babel/generator-7.9.6.tgz?cache=0&sync_timestamp=1588187312464&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fgenerator%2Fdownload%2F%40babel%2Fgenerator-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/types" "^7.9.6"
    "jsesc" "^2.5.1"
    "lodash" "^4.17.13"
    "source-map" "^0.5.0"

"@babel/helper-annotate-as-pure@^7.8.3":
  "integrity" "sha1-YLwLxlf2Ogkk/5pLSgskoTz03u4="
  "resolved" "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.8.3":
  "integrity" "sha1-yECXpCegYaxWocMOv1S3si0kFQM="
  "resolved" "https://registry.npmmirror.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-builder-binary-assignment-operator-visitor%2Fdownload%2F%40babel%2Fhelper-builder-binary-assignment-operator-visitor-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.8.3"
    "@babel/types" "^7.8.3"

"@babel/helper-compilation-targets@^7.8.7", "@babel/helper-compilation-targets@^7.9.6":
  "integrity" "sha1-HgW3zMnTjS+LQLRYs4CgTc+t04o="
  "resolved" "https://registry.npmmirror.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.9.6.tgz?cache=0&sync_timestamp=1588185905418&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-compilation-targets%2Fdownload%2F%40babel%2Fhelper-compilation-targets-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/compat-data" "^7.9.6"
    "browserslist" "^4.11.1"
    "invariant" "^2.2.4"
    "levenary" "^1.1.1"
    "semver" "^5.5.0"

"@babel/helper-create-class-features-plugin@^7.8.3":
  "integrity" "sha1-llyLCp8FGAH9nTs3LKDM8gCpCJc="
  "resolved" "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/helper-function-name" "^7.9.5"
    "@babel/helper-member-expression-to-functions" "^7.8.3"
    "@babel/helper-optimise-call-expression" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/helper-replace-supers" "^7.9.6"
    "@babel/helper-split-export-declaration" "^7.8.3"

"@babel/helper-create-regexp-features-plugin@^7.8.3", "@babel/helper-create-regexp-features-plugin@^7.8.8":
  "integrity" "sha1-XYQYC1iPVgt4ZO+u6okkPlgxIIc="
  "resolved" "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.8.8.tgz"
  "version" "7.8.8"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.8.3"
    "@babel/helper-regex" "^7.8.3"
    "regexpu-core" "^4.7.0"

"@babel/helper-define-map@^7.8.3":
  "integrity" "sha1-oGVcrVRRw3YLcm66h18c2PqgLBU="
  "resolved" "https://registry.npmmirror.com/@babel/helper-define-map/download/@babel/helper-define-map-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-function-name" "^7.8.3"
    "@babel/types" "^7.8.3"
    "lodash" "^4.17.13"

"@babel/helper-explode-assignable-expression@^7.8.3":
  "integrity" "sha1-pyjcW06J4w/C38fQT6KKkwZT+YI="
  "resolved" "https://registry.npmmirror.com/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/traverse" "^7.8.3"
    "@babel/types" "^7.8.3"

"@babel/helper-function-name@^7.8.3", "@babel/helper-function-name@^7.9.5":
  "integrity" "sha1-K1OCDTUnUSDhh0qC5aq+E3aSClw="
  "resolved" "https://registry.npmmirror.com/@babel/helper-function-name/download/@babel/helper-function-name-7.9.5.tgz"
  "version" "7.9.5"
  dependencies:
    "@babel/helper-get-function-arity" "^7.8.3"
    "@babel/template" "^7.8.3"
    "@babel/types" "^7.9.5"

"@babel/helper-get-function-arity@^7.8.3":
  "integrity" "sha1-uJS5R70AQ4HOY+odufCFR+kgq9U="
  "resolved" "https://registry.npmmirror.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-hoist-variables@^7.8.3":
  "integrity" "sha1-Hb6ba1XXjJtBg/yM3G4wzrg7cTQ="
  "resolved" "https://registry.npmmirror.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-member-expression-to-functions@^7.8.3":
  "integrity" "sha1-ZZtxBJjqbB2ZB+DHPyBu7n2twkw="
  "resolved" "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.8.3":
  "integrity" "sha1-f+OVibOcAWMxtrjD9EHo8LFBlJg="
  "resolved" "https://registry.npmmirror.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-module-transforms@^7.9.0":
  "integrity" "sha1-Q7NN/hWWGRhwfSRzJ0MTiOn+luU="
  "resolved" "https://registry.npmmirror.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.9.0.tgz"
  "version" "7.9.0"
  dependencies:
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/helper-replace-supers" "^7.8.6"
    "@babel/helper-simple-access" "^7.8.3"
    "@babel/helper-split-export-declaration" "^7.8.3"
    "@babel/template" "^7.8.6"
    "@babel/types" "^7.9.0"
    "lodash" "^4.17.13"

"@babel/helper-optimise-call-expression@^7.8.3":
  "integrity" "sha1-ftBxgT0Jx1KY708giVYAa2ER7Lk="
  "resolved" "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha1-nqKTvhm6vA9S/4yoizTDYRsghnA="
  "resolved" "https://registry.npmmirror.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.8.3.tgz"
  "version" "7.8.3"

"@babel/helper-regex@^7.8.3":
  "integrity" "sha1-E5dyYH1RuT8j7/5yEFsxnSpMaWU="
  "resolved" "https://registry.npmmirror.com/@babel/helper-regex/download/@babel/helper-regex-7.8.3.tgz?cache=0&sync_timestamp=1578951938163&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-regex%2Fdownload%2F%40babel%2Fhelper-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "lodash" "^4.17.13"

"@babel/helper-remap-async-to-generator@^7.8.3":
  "integrity" "sha1-JzxgDYub9QBhQsHjWIfVVcEu3YY="
  "resolved" "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.8.3"
    "@babel/helper-wrap-function" "^7.8.3"
    "@babel/template" "^7.8.3"
    "@babel/traverse" "^7.8.3"
    "@babel/types" "^7.8.3"

"@babel/helper-replace-supers@^7.8.3", "@babel/helper-replace-supers@^7.8.6", "@babel/helper-replace-supers@^7.9.6":
  "integrity" "sha1-AxSdfmpVhqtnZJls0x1pgaF+FEQ="
  "resolved" "https://registry.npmmirror.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.8.3"
    "@babel/helper-optimise-call-expression" "^7.8.3"
    "@babel/traverse" "^7.9.6"
    "@babel/types" "^7.9.6"

"@babel/helper-simple-access@^7.8.3":
  "integrity" "sha1-f4EJkotNq0ZUB2mGr1dSMd62Oa4="
  "resolved" "https://registry.npmmirror.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/template" "^7.8.3"
    "@babel/types" "^7.8.3"

"@babel/helper-split-export-declaration@^7.8.3":
  "integrity" "sha1-ManzAHD5E2inGCzwX4MXgQZfx6k="
  "resolved" "https://registry.npmmirror.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-validator-identifier@^7.9.0", "@babel/helper-validator-identifier@^7.9.5":
  "integrity" "sha1-kJd6jm+/a0MafcMXUu7iM78FLYA="
  "resolved" "https://registry.npmmirror.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.9.5.tgz"
  "version" "7.9.5"

"@babel/helper-wrap-function@^7.8.3":
  "integrity" "sha1-nb2yu1XvFKqgH+jJm2Kb1TUthhA="
  "resolved" "https://registry.npmmirror.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-function-name" "^7.8.3"
    "@babel/template" "^7.8.3"
    "@babel/traverse" "^7.8.3"
    "@babel/types" "^7.8.3"

"@babel/helpers@^7.9.6":
  "integrity" "sha1-CSx3R0NHHQu2x9461GWrPTSG1YA="
  "resolved" "https://registry.npmmirror.com/@babel/helpers/download/@babel/helpers-7.9.6.tgz?cache=0&sync_timestamp=1588185908061&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelpers%2Fdownload%2F%40babel%2Fhelpers-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/template" "^7.8.3"
    "@babel/traverse" "^7.9.6"
    "@babel/types" "^7.9.6"

"@babel/highlight@^7.8.3":
  "integrity" "sha1-TptFzLgreWBycbKXmtgse2gWMHk="
  "resolved" "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.9.0.tgz"
  "version" "7.9.0"
  dependencies:
    "@babel/helper-validator-identifier" "^7.9.0"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.3.3", "@babel/parser@^7.8.6", "@babel/parser@^7.9.6":
  "integrity" "sha1-Oxu7MNq+YAzXLbWHIJmDdv9lO8c="
  "resolved" "https://registry.npmmirror.com/@babel/parser/download/@babel/parser-7.9.6.tgz"
  "version" "7.9.6"

"@babel/plugin-proposal-async-generator-functions@^7.8.3":
  "integrity" "sha1-utMpxnCzgliXIbJ1QMfSiGAcbm8="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-async-generator-functions%2Fdownload%2F%40babel%2Fplugin-proposal-async-generator-functions-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/helper-remap-async-to-generator" "^7.8.3"
    "@babel/plugin-syntax-async-generators" "^7.8.0"

"@babel/plugin-proposal-class-properties@^7.8.3":
  "integrity" "sha1-XgZlSvXNBLYIkVqtqbKmeIAERk4="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.8.3.tgz?cache=0&sync_timestamp=1578951896490&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-class-properties%2Fdownload%2F%40babel%2Fplugin-proposal-class-properties-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-proposal-decorators@^7.8.3":
  "integrity" "sha1-IVaGCrZcWr8GjD9nBCGEBBBmVD4="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-decorators%2Fdownload%2F%40babel%2Fplugin-proposal-decorators-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/plugin-syntax-decorators" "^7.8.3"

"@babel/plugin-proposal-dynamic-import@^7.8.3":
  "integrity" "sha1-OMT+VVdEgm6X4q6TCw+0zAfmYFQ="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/plugin-syntax-dynamic-import" "^7.8.0"

"@babel/plugin-proposal-json-strings@^7.8.3":
  "integrity" "sha1-2lIWsjipi1ih4F1oUhBLEPmnDWs="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-json-strings%2Fdownload%2F%40babel%2Fplugin-proposal-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.0"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha1-5FciU/3u1lzd7s/as/kor+sv1dI="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.8.3.tgz?cache=0&sync_timestamp=1578952594995&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-nullish-coalescing-operator%2Fdownload%2F%40babel%2Fplugin-proposal-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.0"

"@babel/plugin-proposal-numeric-separator@^7.8.3":
  "integrity" "sha1-XWdpQJaZ7Js7aGhM2BFs7f+Tutg="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"

"@babel/plugin-proposal-object-rest-spread@^7.9.6":
  "integrity" "sha1-egk1hvyxiwgmbrGnF32mcaxXW2M="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.9.6.tgz?cache=0&sync_timestamp=1588185906386&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-object-rest-spread%2Fdownload%2F%40babel%2Fplugin-proposal-object-rest-spread-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.0"
    "@babel/plugin-transform-parameters" "^7.9.5"

"@babel/plugin-proposal-optional-catch-binding@^7.8.3":
  "integrity" "sha1-ne6WqxZQ7tiGRq6XNMoWesSpxck="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-optional-catch-binding%2Fdownload%2F%40babel%2Fplugin-proposal-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.0"

"@babel/plugin-proposal-optional-chaining@^7.9.0":
  "integrity" "sha1-MdsWsVTDnWuKZFKSRyuYOUwpKlg="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.9.0.tgz"
  "version" "7.9.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.0"

"@babel/plugin-proposal-unicode-property-regex@^7.4.4", "@babel/plugin-proposal-unicode-property-regex@^7.8.3":
  "integrity" "sha1-7jqV6QzcBP6M2S7DJ5+gF9aKDR0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.8.8.tgz?cache=0&sync_timestamp=1584039006999&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-unicode-property-regex%2Fdownload%2F%40babel%2Fplugin-proposal-unicode-property-regex-7.8.8.tgz"
  "version" "7.8.8"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.8.8"
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-async-generators@^7.8.0", "@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  "integrity" "sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.8.3":
  "integrity" "sha1-bLkzqIcsjTWb/eabvqrlFi/R6Pc="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-decorators@^7.8.3":
  "integrity" "sha1-jSwVqfGvYksAJflhaCqdU9MAG9o="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-decorators%2Fdownload%2F%40babel%2Fplugin-syntax-decorators-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-dynamic-import@^7.8.0", "@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-json-strings@^7.8.0", "@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.8.3":
  "integrity" "sha1-UhsGyDxASA8eWLT9M7kuzrHW6pQ="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  "integrity" "sha1-OZXX1///Qy9t3HQrR+cwwFRZmJc="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.0", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.0", "@babel/plugin-syntax-numeric-separator@^7.8.3":
  "integrity" "sha1-Dj+2Pgm+obEelkZyccgwgAfnxB8="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-object-rest-spread@^7.8.0", "@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz?cache=0&sync_timestamp=1578950070697&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-object-rest-spread%2Fdownload%2F%40babel%2Fplugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.0", "@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.0", "@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz?cache=0&sync_timestamp=1578952519472&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-optional-chaining%2Fdownload%2F%40babel%2Fplugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  "integrity" "sha1-Os3s5pXmsTqvV/wpHRqACVDHE5E="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.8.3.tgz?cache=0&sync_timestamp=1578952595485&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-top-level-await%2Fdownload%2F%40babel%2Fplugin-syntax-top-level-await-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-arrow-functions@^7.8.3":
  "integrity" "sha1-gndsLtDNnhpJlW2uuJYCTJRzuLY="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-async-to-generator@^7.8.3":
  "integrity" "sha1-Qwj60NlAnXHq+5sabuNfnWS2QIY="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/helper-remap-async-to-generator" "^7.8.3"

"@babel/plugin-transform-block-scoped-functions@^7.8.3":
  "integrity" "sha1-Q37sW3mbWFIHIISzrl72boNJ6KM="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.8.3.tgz?cache=0&sync_timestamp=1578951934748&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-block-scoped-functions%2Fdownload%2F%40babel%2Fplugin-transform-block-scoped-functions-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-block-scoping@^7.8.3":
  "integrity" "sha1-l9Ndq2aFekN8FmNYuR0JBQyGjzo="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "lodash" "^4.17.13"

"@babel/plugin-transform-classes@^7.9.5":
  "integrity" "sha1-gAWX3biu/CwpPtJ0WcH8yTWibCw="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.9.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-classes%2Fdownload%2F%40babel%2Fplugin-transform-classes-7.9.5.tgz"
  "version" "7.9.5"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.8.3"
    "@babel/helper-define-map" "^7.8.3"
    "@babel/helper-function-name" "^7.9.5"
    "@babel/helper-optimise-call-expression" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/helper-replace-supers" "^7.8.6"
    "@babel/helper-split-export-declaration" "^7.8.3"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.8.3":
  "integrity" "sha1-ltDSi3985OtbEguy4OlDNDyG+Bs="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-destructuring@^7.9.5":
  "integrity" "sha1-csl89fOGBK6jq/O5NbDhex23alA="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.9.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-destructuring%2Fdownload%2F%40babel%2Fplugin-transform-destructuring-7.9.5.tgz"
  "version" "7.9.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-dotall-regex@^7.4.4", "@babel/plugin-transform-dotall-regex@^7.8.3":
  "integrity" "sha1-w8bsXuYSXGmTxcvKINyGIanqem4="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-duplicate-keys@^7.8.3":
  "integrity" "sha1-jRLfMJqlN/JyiZxWXqF2jihuIfE="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-exponentiation-operator@^7.8.3":
  "integrity" "sha1-WBptf1aXDga/UVYM1k9elHtw17c="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-for-of@^7.9.0":
  "integrity" "sha1-DyYOJ9PinNG7MSjaXnbHYapsEI4="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.9.0.tgz"
  "version" "7.9.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-function-name@^7.8.3":
  "integrity" "sha1-J5NzyycyKqrWfCaD53bfxHGW7Ys="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-function-name" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-literals@^7.8.3":
  "integrity" "sha1-rvI5gj2RmU7Hto5VGTUl1229XcE="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-member-expression-literals@^7.8.3":
  "integrity" "sha1-lj/tS2IKx8v2Apx1VCQCn6OkBBA="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-modules-amd@^7.9.6":
  "integrity" "sha1-hTnsQsFT0S6jg24OOsMNWq57JY4="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.9.6.tgz?cache=0&sync_timestamp=1588185902641&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-modules-amd%2Fdownload%2F%40babel%2Fplugin-transform-modules-amd-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/helper-module-transforms" "^7.9.0"
    "@babel/helper-plugin-utils" "^7.8.3"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.9.6":
  "integrity" "sha1-ZLdHSkJ57liMrNGQZpXKchaHwnc="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.9.6.tgz?cache=0&sync_timestamp=1588185907042&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-modules-commonjs%2Fdownload%2F%40babel%2Fplugin-transform-modules-commonjs-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/helper-module-transforms" "^7.9.0"
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/helper-simple-access" "^7.8.3"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.9.6":
  "integrity" "sha1-IH8UYceKIx1TN6khQOUkIlENgaQ="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.9.6.tgz?cache=0&sync_timestamp=1588185909511&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-modules-systemjs%2Fdownload%2F%40babel%2Fplugin-transform-modules-systemjs-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/helper-hoist-variables" "^7.8.3"
    "@babel/helper-module-transforms" "^7.9.0"
    "@babel/helper-plugin-utils" "^7.8.3"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.9.0":
  "integrity" "sha1-6Qmsridv7CgPm4IaXzjh8ItIBpc="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.9.0.tgz?cache=0&sync_timestamp=1584746128785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-modules-umd%2Fdownload%2F%40babel%2Fplugin-transform-modules-umd-7.9.0.tgz"
  "version" "7.9.0"
  dependencies:
    "@babel/helper-module-transforms" "^7.9.0"
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-named-capturing-groups-regex@^7.8.3":
  "integrity" "sha1-oqcr/6ICrA4tBQav0JOcXsvEjGw="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.8.3"

"@babel/plugin-transform-new-target@^7.8.3":
  "integrity" "sha1-YMwq5m2FyVq1QOs0urtkNNTHDEM="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-object-super@^7.8.3":
  "integrity" "sha1-67ah56hv+paFi9asAQLWWUQmFyU="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/helper-replace-supers" "^7.8.3"

"@babel/plugin-transform-parameters@^7.9.5":
  "integrity" "sha1-FzsmV0b14Vsq/lJ+7aZbc2I6B5U="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.9.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-parameters%2Fdownload%2F%40babel%2Fplugin-transform-parameters-7.9.5.tgz"
  "version" "7.9.5"
  dependencies:
    "@babel/helper-get-function-arity" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-property-literals@^7.8.3":
  "integrity" "sha1-MxlDANhTnB7SjGKtUIe6OAe5gmM="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-regenerator@^7.8.7":
  "integrity" "sha1-Xkag3KK+4a2ChesFJ+arycN2cvg="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "regenerator-transform" "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.8.3":
  "integrity" "sha1-mgY1rE5mXSmxYoN908xQdF398fU="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-runtime@^7.9.0":
  "integrity" "sha1-O6gEQ4rQ2IChe8peqgzfHt7tsv0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"
    "resolve" "^1.8.1"
    "semver" "^5.5.1"

"@babel/plugin-transform-shorthand-properties@^7.8.3":
  "integrity" "sha1-KFRSFuAjqDLU06EYXtSSvP6sCMg="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-spread@^7.8.3":
  "integrity" "sha1-nI/+gXD9+4ixFOy5ILgvtulf5eg="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-sticky-regex@^7.8.3":
  "integrity" "sha1-vnoSkPgdrnZ0dUUhmeH3bWF1sQA="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-sticky-regex%2Fdownload%2F%40babel%2Fplugin-transform-sticky-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/helper-regex" "^7.8.3"

"@babel/plugin-transform-template-literals@^7.2.0", "@babel/plugin-transform-template-literals@^7.8.3":
  "integrity" "sha1-e/pHMrRV6mpDEwrcC6dn7A5AKoA="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-typeof-symbol@^7.8.4":
  "integrity" "sha1-7eQGIxXOCq+KZXqSCFjxovNfxBI="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-transform-unicode-regex@^7.8.3":
  "integrity" "sha1-DO8247pz5cVyc+/7GC9GuRoeyq0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/preset-env@^7.3.1", "@babel/preset-env@^7.9.0":
  "integrity" "sha1-3wY7J2xkVexvz8blOqzDjamwrqY="
  "resolved" "https://registry.npmmirror.com/@babel/preset-env/download/@babel/preset-env-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/compat-data" "^7.9.6"
    "@babel/helper-compilation-targets" "^7.9.6"
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/plugin-proposal-async-generator-functions" "^7.8.3"
    "@babel/plugin-proposal-dynamic-import" "^7.8.3"
    "@babel/plugin-proposal-json-strings" "^7.8.3"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-proposal-numeric-separator" "^7.8.3"
    "@babel/plugin-proposal-object-rest-spread" "^7.9.6"
    "@babel/plugin-proposal-optional-catch-binding" "^7.8.3"
    "@babel/plugin-proposal-optional-chaining" "^7.9.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.8.3"
    "@babel/plugin-syntax-async-generators" "^7.8.0"
    "@babel/plugin-syntax-dynamic-import" "^7.8.0"
    "@babel/plugin-syntax-json-strings" "^7.8.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.0"
    "@babel/plugin-syntax-numeric-separator" "^7.8.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.0"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"
    "@babel/plugin-transform-arrow-functions" "^7.8.3"
    "@babel/plugin-transform-async-to-generator" "^7.8.3"
    "@babel/plugin-transform-block-scoped-functions" "^7.8.3"
    "@babel/plugin-transform-block-scoping" "^7.8.3"
    "@babel/plugin-transform-classes" "^7.9.5"
    "@babel/plugin-transform-computed-properties" "^7.8.3"
    "@babel/plugin-transform-destructuring" "^7.9.5"
    "@babel/plugin-transform-dotall-regex" "^7.8.3"
    "@babel/plugin-transform-duplicate-keys" "^7.8.3"
    "@babel/plugin-transform-exponentiation-operator" "^7.8.3"
    "@babel/plugin-transform-for-of" "^7.9.0"
    "@babel/plugin-transform-function-name" "^7.8.3"
    "@babel/plugin-transform-literals" "^7.8.3"
    "@babel/plugin-transform-member-expression-literals" "^7.8.3"
    "@babel/plugin-transform-modules-amd" "^7.9.6"
    "@babel/plugin-transform-modules-commonjs" "^7.9.6"
    "@babel/plugin-transform-modules-systemjs" "^7.9.6"
    "@babel/plugin-transform-modules-umd" "^7.9.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.8.3"
    "@babel/plugin-transform-new-target" "^7.8.3"
    "@babel/plugin-transform-object-super" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.9.5"
    "@babel/plugin-transform-property-literals" "^7.8.3"
    "@babel/plugin-transform-regenerator" "^7.8.7"
    "@babel/plugin-transform-reserved-words" "^7.8.3"
    "@babel/plugin-transform-shorthand-properties" "^7.8.3"
    "@babel/plugin-transform-spread" "^7.8.3"
    "@babel/plugin-transform-sticky-regex" "^7.8.3"
    "@babel/plugin-transform-template-literals" "^7.8.3"
    "@babel/plugin-transform-typeof-symbol" "^7.8.4"
    "@babel/plugin-transform-unicode-regex" "^7.8.3"
    "@babel/preset-modules" "^0.1.3"
    "@babel/types" "^7.9.6"
    "browserslist" "^4.11.1"
    "core-js-compat" "^3.6.2"
    "invariant" "^2.2.2"
    "levenary" "^1.1.1"
    "semver" "^5.5.0"

"@babel/preset-modules@^0.1.3":
  "integrity" "sha1-EyQrU7XvjIg8PPfd3VWzbOgPvHI="
  "resolved" "https://registry.npmmirror.com/@babel/preset-modules/download/@babel/preset-modules-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/register@^7.0.0":
  "integrity" "sha1-AkZO3ldUi927Xp9wXSY7fD9D1Is="
  "resolved" "https://registry.npmmirror.com/@babel/register/download/@babel/register-7.9.0.tgz"
  "version" "7.9.0"
  dependencies:
    "find-cache-dir" "^2.0.0"
    "lodash" "^4.17.13"
    "make-dir" "^2.1.0"
    "pirates" "^4.0.0"
    "source-map-support" "^0.5.16"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.3.1", "@babel/runtime@^7.7.2", "@babel/runtime@^7.8.4", "@babel/runtime@^7.9.2":
  "integrity" "sha1-qRAutcre3z8x0IqezylK94J+op8="
  "resolved" "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.9.6.tgz?cache=0&sync_timestamp=1588185905751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/template@^7.3.3", "@babel/template@^7.8.3", "@babel/template@^7.8.6":
  "integrity" "sha1-hrIq8V+CjfsIZHT5ZNzD45xDzis="
  "resolved" "https://registry.npmmirror.com/@babel/template/download/@babel/template-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@babel/parser" "^7.8.6"
    "@babel/types" "^7.8.6"

"@babel/traverse@^7.1.0", "@babel/traverse@^7.3.3", "@babel/traverse@^7.8.3", "@babel/traverse@^7.9.6":
  "integrity" "sha1-VUDXV3aXv2GcxXuSqg8cIxqU9EI="
  "resolved" "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.9.6.tgz?cache=0&sync_timestamp=1588185904779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@babel/generator" "^7.9.6"
    "@babel/helper-function-name" "^7.9.5"
    "@babel/helper-split-export-declaration" "^7.8.3"
    "@babel/parser" "^7.9.6"
    "@babel/types" "^7.9.6"
    "debug" "^4.1.0"
    "globals" "^11.1.0"
    "lodash" "^4.17.13"

"@babel/types@^7.0.0", "@babel/types@^7.3.0", "@babel/types@^7.3.3", "@babel/types@^7.4.4", "@babel/types@^7.8.3", "@babel/types@^7.8.6", "@babel/types@^7.9.0", "@babel/types@^7.9.5", "@babel/types@^7.9.6":
  "integrity" "sha1-LFUCtCclHp3hvS3/la3WRtlcyfc="
  "resolved" "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.9.6.tgz"
  "version" "7.9.6"
  dependencies:
    "@babel/helper-validator-identifier" "^7.9.5"
    "lodash" "^4.17.13"
    "to-fast-properties" "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  "integrity" "sha1-daLotRy3WKdVPWgEpZMteqznXDk="
  "resolved" "https://registry.npmmirror.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz"
  "version" "0.2.3"

"@cnakazawa/watch@^1.0.3":
  "integrity" "sha1-+GSuhQBND8q29QvpFBxNo2jRZWo="
  "resolved" "https://registry.npmmirror.com/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "exec-sh" "^0.3.2"
    "minimist" "^1.2.0"

"@dcloudio/types@*":
  "integrity" "sha1-7DSb3mhr3MbImz9bl92A4/4OwyQ="
  "resolved" "https://registry.npmmirror.com/@dcloudio/types/download/@dcloudio/types-1.0.0.tgz"
  "version" "1.0.0"

"@dcloudio/uni-app-plus@^2.0.0-27520200518001":
  "integrity" "sha1-QAdFO0VjDlAJ4HWfT356OqCCEpA="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-app-plus/download/@dcloudio/uni-app-plus-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/uni-automator@^2.0.0-27520200518001":
  "integrity" "sha1-htA5klXw5865+jadVHfIsH41Dhw="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-automator/download/@dcloudio/uni-automator-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"
  dependencies:
    "address" "^1.1.2"
    "debug" "^4.1.1"
    "default-gateway" "^6.0.0"
    "licia" "^1.21.0"
    "postcss-selector-parser" "^6.0.2"
    "qrcode-reader" "^1.0.4"
    "qrcode-terminal" "^0.12.0"
    "ws" "^7.2.3"

"@dcloudio/uni-cli-shared@^2.0.0-27520200518001":
  "integrity" "sha1-Cohyg0NzJ5QdP520eqgWnTKFgpU="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-cli-shared/download/@dcloudio/uni-cli-shared-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"
  dependencies:
    "hash-sum" "^1.0.2"
    "postcss-urlrewrite" "^0.2.2"
    "strip-json-comments" "^2.0.1"

"@dcloudio/uni-h5@^2.0.0-27520200518001":
  "integrity" "sha1-eLshOeQEHmMcg2uICG4v9ihQRXM="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-h5/download/@dcloudio/uni-h5-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"
  dependencies:
    "base64-arraybuffer" "^0.2.0"
    "intersection-observer" "^0.7.0"
    "safe-area-insets" "^1.4.1"

"@dcloudio/uni-helper-json@*":
  "integrity" "sha1-R5nHOOungn5zdDJRfiwZZ2A4V+I="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-helper-json/download/@dcloudio/uni-helper-json-1.0.5.tgz"
  "version" "1.0.5"

"@dcloudio/uni-migration@^2.0.0-27520200518001":
  "integrity" "sha1-FPMERXwnoPKzvjXohAEhqmDytlg="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-migration/download/@dcloudio/uni-migration-2.0.0-27520200518001.tgz?cache=0&sync_timestamp=1590046603392&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40dcloudio%2Funi-migration%2Fdownload%2F%40dcloudio%2Funi-migration-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"
  dependencies:
    "commander" "^4.0.1"
    "fs-extra" "^8.1.0"
    "mustache" "^3.1.0"
    "stricter-htmlparser2" "^3.9.6"

"@dcloudio/uni-mp-alipay@^2.0.0-27520200518001":
  "integrity" "sha1-Px3i1OgkP5fJvD1YzfAx/yrb9QE="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-mp-alipay/download/@dcloudio/uni-mp-alipay-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/uni-mp-baidu@^2.0.0-27520200518001":
  "integrity" "sha1-d+8EykvGVyhtDaURDpxL8u9u7DM="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-mp-baidu/download/@dcloudio/uni-mp-baidu-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/uni-mp-qq@^2.0.0-27520200518001":
  "integrity" "sha1-dVNwkR+p2oPdhyGzduBkyhXbjz8="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-mp-qq/download/@dcloudio/uni-mp-qq-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/uni-mp-toutiao@^2.0.0-27520200518001":
  "integrity" "sha1-oWOZvlTFmw5kyH7qffo2nOwyE5U="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-mp-toutiao/download/@dcloudio/uni-mp-toutiao-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/uni-mp-weixin@^2.0.0-27520200518001":
  "integrity" "sha1-z9HBIHa7Emc+rRuhlRqHOsizXQ0="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-mp-weixin/download/@dcloudio/uni-mp-weixin-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/uni-quickapp-native@^2.0.0-27520200518001":
  "integrity" "sha1-IiUs189Icoj8ul8P8BNk/llhF58="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-quickapp-native/download/@dcloudio/uni-quickapp-native-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"
  dependencies:
    "@hap-toolkit/dsl-vue" "0.6.13"
    "@hap-toolkit/packager" "0.6.13"
    "@hap-toolkit/server" "0.6.13"
    "module-alias" "^2.1.0"

"@dcloudio/uni-quickapp-webview@^2.0.0-27520200518001":
  "integrity" "sha1-aJ2E/v1GLZOWVLDQRjPhV00yREI="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-quickapp-webview/download/@dcloudio/uni-quickapp-webview-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/uni-stat@^2.0.0-27520200518001":
  "integrity" "sha1-WhsAoqlPd7RPGXKL/Qyp5uq91ok="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-stat/download/@dcloudio/uni-stat-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/uni-template-compiler@^2.0.0-27520200518001":
  "integrity" "sha1-E7KaDRZX+DBSB0MW/13jYNw21Qo="
  "resolved" "https://registry.npmmirror.com/@dcloudio/uni-template-compiler/download/@dcloudio/uni-template-compiler-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"
  dependencies:
    "@babel/parser" "^7.3.3"
    "@babel/traverse" "^7.3.3"
    "@babel/types" "^7.3.3"
    "vue-template-compiler" "^2.6.10"

"@dcloudio/vue-cli-plugin-hbuilderx@^2.0.0-27520200518001":
  "integrity" "sha1-oLpT5imDEfE6MHZymUw9FdEvs98="
  "resolved" "https://registry.npmmirror.com/@dcloudio/vue-cli-plugin-hbuilderx/download/@dcloudio/vue-cli-plugin-hbuilderx-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"
  dependencies:
    "acorn" "^5.2.1"
    "css" "~2.2.1"
    "escodegen" "^1.8.1"

"@dcloudio/vue-cli-plugin-uni-optimize@^2.0.0-27520200518001":
  "integrity" "sha1-FekMx5GAvAPk5sd3sTYzozmKE8A="
  "resolved" "https://registry.npmmirror.com/@dcloudio/vue-cli-plugin-uni-optimize/download/@dcloudio/vue-cli-plugin-uni-optimize-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/vue-cli-plugin-uni@^2.0.0-27520200518001":
  "integrity" "sha1-16UFuBWBGYqqeoqG3nYtdLXHYoI="
  "resolved" "https://registry.npmmirror.com/@dcloudio/vue-cli-plugin-uni/download/@dcloudio/vue-cli-plugin-uni-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"
  dependencies:
    "@dcloudio/uni-stat" "^2.0.0-27520200518001"
    "buffer-json" "^2.0.0"
    "copy-webpack-plugin" "^5.1.1"
    "cross-env" "^5.2.0"
    "envinfo" "^6.0.1"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "lru-cache" "^4.1.2"
    "mkdirp" "^0.5.1"
    "module-alias" "^2.1.0"
    "postcss" "^7.0.7"
    "postcss-import" "^12.0.1"
    "postcss-selector-parser" "^5.0.0"
    "postcss-value-parser" "^3.3.1"
    "strip-json-comments" "^2.0.1"
    "update-check" "^1.5.3"
    "webpack-merge" "^4.1.4"
    "wrap-loader" "^0.2.0"
    "xregexp" "4.0.0"

"@dcloudio/webpack-uni-mp-loader@^2.0.0-27520200518001":
  "integrity" "sha1-YKoditu5cbCfAlBFcwotcHEyWlc="
  "resolved" "https://registry.npmmirror.com/@dcloudio/webpack-uni-mp-loader/download/@dcloudio/webpack-uni-mp-loader-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"

"@dcloudio/webpack-uni-pages-loader@^2.0.0-27520200518001":
  "integrity" "sha1-IxvAPUGUrtmluL7pWfAqNyg3q8E="
  "resolved" "https://registry.npmmirror.com/@dcloudio/webpack-uni-pages-loader/download/@dcloudio/webpack-uni-pages-loader-2.0.0-27520200518001.tgz?cache=0&sync_timestamp=1590046654411&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40dcloudio%2Fwebpack-uni-pages-loader%2Fdownload%2F%40dcloudio%2Fwebpack-uni-pages-loader-2.0.0-27520200518001.tgz"
  "version" "2.0.0-27520200518001"
  dependencies:
    "merge" "^1.2.1"
    "strip-json-comments" "^2.0.1"

"@hap-toolkit/aaptjs@^1.0.0":
  "integrity" "sha1-Rg1FqEbAt4akIcgs4l/Nn/8JKOY="
  "resolved" "https://registry.npmmirror.com/@hap-toolkit/aaptjs/download/@hap-toolkit/aaptjs-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "shelljs" "^0.8.1"

"@hap-toolkit/compiler@^0.6.13", "@hap-toolkit/compiler@0.6.15":
  "integrity" "sha1-2OKhby7Kj5Gy7K/+y3gSrG0brIQ="
  "resolved" "https://registry.npmmirror.com/@hap-toolkit/compiler/download/@hap-toolkit/compiler-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fcompiler%2Fdownload%2F%40hap-toolkit%2Fcompiler-0.6.15.tgz"
  "version" "0.6.15"
  dependencies:
    "@babel/core" "^7.3.4"
    "@babel/plugin-transform-template-literals" "^7.2.0"
    "@hap-toolkit/shared-utils" "0.6.15"
    "css" "^2.2.4"
    "css-what" "^2.1.3"
    "escodegen" "^1.11.1"
    "esprima" "^4.0.1"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.2.3"
    "parse5" "^3.0.3"
    "source-map" "^0.7.3"
    "webpack" "^4.29.5"

"@hap-toolkit/debugger@^0.6.13":
  "integrity" "sha1-UeGiB40KK8AxQSrOKMmsVQXUAOM="
  "resolved" "https://registry.npmmirror.com/@hap-toolkit/debugger/download/@hap-toolkit/debugger-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fdebugger%2Fdownload%2F%40hap-toolkit%2Fdebugger-0.6.15.tgz"
  "version" "0.6.15"
  dependencies:
    "@hap-toolkit/shared-utils" "0.6.15"
    "adb-commander" "^0.1.8"
    "adb-devices-emitter" "^0.1.8"
    "chrome-simple-launcher" "0.1.3"
    "koa" "^2.7.0"
    "koa-body" "^4.0.8"
    "koa-router" "^7.4.0"
    "koa-static" "^5.0.0"
    "qr-image" "^3.2.0"
    "socket.io" "^2.2.0"

"@hap-toolkit/dsl-vue@0.6.13":
  "integrity" "sha1-mJ2tXDc4PZ4PCrn7opMle7t38Fk="
  "resolved" "https://registry.npmmirror.com/@hap-toolkit/dsl-vue/download/@hap-toolkit/dsl-vue-0.6.13.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fdsl-vue%2Fdownload%2F%40hap-toolkit%2Fdsl-vue-0.6.13.tgz"
  "version" "0.6.13"
  dependencies:
    "@hap-toolkit/compiler" "^0.6.13"
    "@hap-toolkit/packager" "^0.6.13"
    "@hap-toolkit/shared-utils" "^0.6.13"
    "css-loader" "^2.1.1"
    "md5" "^2.2.1"
    "mini-css-extract-plugin" "^0.5.0"
    "url-loader" "^2.1.0"
    "vue-loader" "^15.6.4"
    "vue-template-compiler" "^2.6.7"
    "webpack-sources" "^1.3.0"

"@hap-toolkit/packager@^0.6.13":
  "integrity" "sha1-+kPpUEsm2mYS/cUsY4Yp/zsim3w="
  "resolved" "https://registry.npmmirror.com/@hap-toolkit/packager/download/@hap-toolkit/packager-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fpackager%2Fdownload%2F%40hap-toolkit%2Fpackager-0.6.15.tgz"
  "version" "0.6.15"
  dependencies:
    "@babel/core" "^7.3.3"
    "@babel/preset-env" "^7.3.1"
    "@babel/register" "^7.0.0"
    "@babel/runtime" "^7.3.1"
    "@hap-toolkit/aaptjs" "^1.0.0"
    "@hap-toolkit/compiler" "0.6.15"
    "@hap-toolkit/shared-utils" "0.6.15"
    "babel-loader" "^8.0.5"
    "fs-extra" "^7.0.1"
    "hash-sum" "^1.0.2"
    "jsrsasign" "^7.2.2"
    "jszip" "^3.1.5"
    "koa-bodyparser" "^4.2.1"
    "koa-router" "^7.4.0"
    "loader-utils" "^1.2.3"
    "moment" "^2.24.0"
    "qr-image" "^3.2.0"
    "webpack" "^4.29.5"

"@hap-toolkit/packager@0.6.13":
  "integrity" "sha1-1JMCf99j9aMKLw+mLX1DgW2lbEo="
  "resolved" "https://registry.npmmirror.com/@hap-toolkit/packager/download/@hap-toolkit/packager-0.6.13.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fpackager%2Fdownload%2F%40hap-toolkit%2Fpackager-0.6.13.tgz"
  "version" "0.6.13"
  dependencies:
    "@babel/core" "^7.3.3"
    "@babel/preset-env" "^7.3.1"
    "@babel/register" "^7.0.0"
    "@babel/runtime" "^7.3.1"
    "@hap-toolkit/compiler" "^0.6.13"
    "@hap-toolkit/shared-utils" "^0.6.13"
    "aaptjs" "^1.3.1"
    "babel-loader" "^8.0.5"
    "fs-extra" "^7.0.1"
    "hash-sum" "^1.0.2"
    "jsrsasign" "^7.2.2"
    "jszip" "^3.1.5"
    "koa-bodyparser" "^4.2.1"
    "koa-router" "^7.4.0"
    "loader-utils" "^1.2.3"
    "moment" "^2.24.0"
    "qr-image" "^3.2.0"
    "webpack" "^4.29.5"

"@hap-toolkit/server@0.6.13":
  "integrity" "sha1-zIOktE30RWvb1BpLkl8GKi1OVDQ="
  "resolved" "https://registry.npmmirror.com/@hap-toolkit/server/download/@hap-toolkit/server-0.6.13.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fserver%2Fdownload%2F%40hap-toolkit%2Fserver-0.6.13.tgz"
  "version" "0.6.13"
  dependencies:
    "@babel/runtime" "^7.3.1"
    "@hap-toolkit/debugger" "^0.6.13"
    "@hap-toolkit/packager" "^0.6.13"
    "@hap-toolkit/shared-utils" "^0.6.13"
    "jszip" "^3.2.0"
    "koa" "^2.7.0"
    "koa-body" "^4.0.8"
    "koa-mount" "^4.0.0"
    "koa-router" "^7.4.0"
    "koa-send" "^5.0.0"
    "koa-static" "^5.0.0"
    "opn" "^5.4.0"
    "portfinder" "^1.0.20"
    "qr-image" "^3.2.0"

"@hap-toolkit/shared-utils@^0.6.13", "@hap-toolkit/shared-utils@0.6.15":
  "integrity" "sha1-QIVwJnpzBHxGytay9xYgNhrfteA="
  "resolved" "https://registry.npmmirror.com/@hap-toolkit/shared-utils/download/@hap-toolkit/shared-utils-0.6.15.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hap-toolkit%2Fshared-utils%2Fdownload%2F%40hap-toolkit%2Fshared-utils-0.6.15.tgz"
  "version" "0.6.15"
  dependencies:
    "chalk" "^2.4.2"
    "qrcode-terminal" "^0.12.0"

"@hapi/address@2.x.x":
  "integrity" "sha1-XWftQ/P9QaadS5/3tW58DR0KgeU="
  "resolved" "https://registry.npmmirror.com/@hapi/address/download/@hapi/address-2.1.4.tgz?cache=0&sync_timestamp=1584171913757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hapi%2Faddress%2Fdownload%2F%40hapi%2Faddress-2.1.4.tgz"
  "version" "2.1.4"

"@hapi/bourne@1.x.x":
  "integrity" "sha1-CnCVreoGckPOMoPhtWuKj0U7JCo="
  "resolved" "https://registry.npmmirror.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz"
  "version" "1.3.2"

"@hapi/hoek@^8.3.0", "@hapi/hoek@8.x.x":
  "integrity" "sha1-/elgZMpEbeyMVajC8TCVewcMbgY="
  "resolved" "https://registry.npmmirror.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz"
  "version" "8.5.1"

"@hapi/joi@^15.0.1":
  "integrity" "sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc="
  "resolved" "https://registry.npmmirror.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz"
  "version" "15.1.1"
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  "integrity" "sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck="
  "resolved" "https://registry.npmmirror.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  "integrity" "sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg="
  "resolved" "https://registry.npmmirror.com/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "cssnano" "^4.0.0"
    "cssnano-preset-default" "^4.0.0"
    "postcss" "^7.0.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  "integrity" "sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0="
  "resolved" "https://registry.npmmirror.com/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "camelcase" "^5.3.1"
    "find-up" "^4.1.0"
    "get-package-type" "^0.1.0"
    "js-yaml" "^3.13.1"
    "resolve-from" "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  "integrity" "sha1-JlIL8Jq+SlZEzVQU43ElqJVCQd0="
  "resolved" "https://registry.npmmirror.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.2.tgz"
  "version" "0.1.2"

"@jest/console@^25.5.0":
  "integrity" "sha1-dwgAeZ1RDzcynFCKnt0Le0R9mrs="
  "resolved" "https://registry.npmmirror.com/@jest/console/download/@jest/console-25.5.0.tgz?cache=0&sync_timestamp=1588675319681&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Fconsole%2Fdownload%2F%40jest%2Fconsole-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "chalk" "^3.0.0"
    "jest-message-util" "^25.5.0"
    "jest-util" "^25.5.0"
    "slash" "^3.0.0"

"@jest/core@^25.5.4":
  "integrity" "sha1-PvdBL3M5IQ8APN82ZGu8p4bv57Q="
  "resolved" "https://registry.npmmirror.com/@jest/core/download/@jest/core-25.5.4.tgz?cache=0&sync_timestamp=1588675318326&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Fcore%2Fdownload%2F%40jest%2Fcore-25.5.4.tgz"
  "version" "25.5.4"
  dependencies:
    "@jest/console" "^25.5.0"
    "@jest/reporters" "^25.5.1"
    "@jest/test-result" "^25.5.0"
    "@jest/transform" "^25.5.1"
    "@jest/types" "^25.5.0"
    "ansi-escapes" "^4.2.1"
    "chalk" "^3.0.0"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-changed-files" "^25.5.0"
    "jest-config" "^25.5.4"
    "jest-haste-map" "^25.5.1"
    "jest-message-util" "^25.5.0"
    "jest-regex-util" "^25.2.6"
    "jest-resolve" "^25.5.1"
    "jest-resolve-dependencies" "^25.5.4"
    "jest-runner" "^25.5.4"
    "jest-runtime" "^25.5.4"
    "jest-snapshot" "^25.5.1"
    "jest-util" "^25.5.0"
    "jest-validate" "^25.5.0"
    "jest-watcher" "^25.5.0"
    "micromatch" "^4.0.2"
    "p-each-series" "^2.1.0"
    "realpath-native" "^2.0.0"
    "rimraf" "^3.0.0"
    "slash" "^3.0.0"
    "strip-ansi" "^6.0.0"

"@jest/environment@^25.5.0":
  "integrity" "sha1-qjOwwhpxbGVoZjjn74FsDjoMezc="
  "resolved" "https://registry.npmmirror.com/@jest/environment/download/@jest/environment-25.5.0.tgz?cache=0&sync_timestamp=1588675332085&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Fenvironment%2Fdownload%2F%40jest%2Fenvironment-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/fake-timers" "^25.5.0"
    "@jest/types" "^25.5.0"
    "jest-mock" "^25.5.0"

"@jest/fake-timers@^25.5.0":
  "integrity" "sha1-RjUuAFM8AkyQwrwq2fKVn38RQYU="
  "resolved" "https://registry.npmmirror.com/@jest/fake-timers/download/@jest/fake-timers-25.5.0.tgz?cache=0&sync_timestamp=1588675324067&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Ffake-timers%2Fdownload%2F%40jest%2Ffake-timers-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "jest-message-util" "^25.5.0"
    "jest-mock" "^25.5.0"
    "jest-util" "^25.5.0"
    "lolex" "^5.0.0"

"@jest/globals@^25.5.2":
  "integrity" "sha1-XkXp3o0ihxavMlfus5kcwuFiyog="
  "resolved" "https://registry.npmmirror.com/@jest/globals/download/@jest/globals-25.5.2.tgz?cache=0&sync_timestamp=1588675303209&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Fglobals%2Fdownload%2F%40jest%2Fglobals-25.5.2.tgz"
  "version" "25.5.2"
  dependencies:
    "@jest/environment" "^25.5.0"
    "@jest/types" "^25.5.0"
    "expect" "^25.5.0"

"@jest/reporters@^25.5.1":
  "integrity" "sha1-y2hrzGgPZkwtuvfthz6TqmgRU4s="
  "resolved" "https://registry.npmmirror.com/@jest/reporters/download/@jest/reporters-25.5.1.tgz?cache=0&sync_timestamp=1588675337559&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Freporters%2Fdownload%2F%40jest%2Freporters-25.5.1.tgz"
  "version" "25.5.1"
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^25.5.0"
    "@jest/test-result" "^25.5.0"
    "@jest/transform" "^25.5.1"
    "@jest/types" "^25.5.0"
    "chalk" "^3.0.0"
    "collect-v8-coverage" "^1.0.0"
    "exit" "^0.1.2"
    "glob" "^7.1.2"
    "graceful-fs" "^4.2.4"
    "istanbul-lib-coverage" "^3.0.0"
    "istanbul-lib-instrument" "^4.0.0"
    "istanbul-lib-report" "^3.0.0"
    "istanbul-lib-source-maps" "^4.0.0"
    "istanbul-reports" "^3.0.2"
    "jest-haste-map" "^25.5.1"
    "jest-resolve" "^25.5.1"
    "jest-util" "^25.5.0"
    "jest-worker" "^25.5.0"
    "slash" "^3.0.0"
    "source-map" "^0.6.0"
    "string-length" "^3.1.0"
    "terminal-link" "^2.0.0"
    "v8-to-istanbul" "^4.1.3"
  optionalDependencies:
    "node-notifier" "^6.0.0"

"@jest/source-map@^25.5.0":
  "integrity" "sha1-31wg1gUKopLCxtPw0sdgavMVvRs="
  "resolved" "https://registry.npmmirror.com/@jest/source-map/download/@jest/source-map-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "callsites" "^3.0.0"
    "graceful-fs" "^4.2.4"
    "source-map" "^0.6.0"

"@jest/test-result@^25.5.0":
  "integrity" "sha1-E5oEMjDN7/6botg0Gyfy78d86Hw="
  "resolved" "https://registry.npmmirror.com/@jest/test-result/download/@jest/test-result-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/console" "^25.5.0"
    "@jest/types" "^25.5.0"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "collect-v8-coverage" "^1.0.0"

"@jest/test-sequencer@^25.5.4":
  "integrity" "sha1-m05oWzaVTDjQ8FLlltKBYb3Itzc="
  "resolved" "https://registry.npmmirror.com/@jest/test-sequencer/download/@jest/test-sequencer-25.5.4.tgz?cache=0&sync_timestamp=1588675313073&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Ftest-sequencer%2Fdownload%2F%40jest%2Ftest-sequencer-25.5.4.tgz"
  "version" "25.5.4"
  dependencies:
    "@jest/test-result" "^25.5.0"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^25.5.1"
    "jest-runner" "^25.5.4"
    "jest-runtime" "^25.5.4"

"@jest/transform@^25.5.1":
  "integrity" "sha1-BGndwXaZ3Sv5hdtV+g+5MJ9cLbM="
  "resolved" "https://registry.npmmirror.com/@jest/transform/download/@jest/transform-25.5.1.tgz"
  "version" "25.5.1"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^25.5.0"
    "babel-plugin-istanbul" "^6.0.0"
    "chalk" "^3.0.0"
    "convert-source-map" "^1.4.0"
    "fast-json-stable-stringify" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^25.5.1"
    "jest-regex-util" "^25.2.6"
    "jest-util" "^25.5.0"
    "micromatch" "^4.0.2"
    "pirates" "^4.0.1"
    "realpath-native" "^2.0.0"
    "slash" "^3.0.0"
    "source-map" "^0.6.1"
    "write-file-atomic" "^3.0.0"

"@jest/types@^25.5.0":
  "integrity" "sha1-TWpHk/e5WZ/DaAh3uFapfbzPKp0="
  "resolved" "https://registry.npmmirror.com/@jest/types/download/@jest/types-25.5.0.tgz?cache=0&sync_timestamp=1588675411534&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40jest%2Ftypes%2Fdownload%2F%40jest%2Ftypes-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^15.0.0"
    "chalk" "^3.0.0"

"@jimp/bmp@^0.10.3":
  "integrity" "sha512-keMOc5woiDmONXsB/6aXLR4Z5Q+v8lFq3EY2rcj2FmstbDMhRuGbmcBxlEgOqfRjwvtf/wOtJ3Of37oAWtVfLg=="
  "resolved" "https://registry.npmmirror.com/@jimp/bmp/-/bmp-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "bmp-js" "^0.1.0"
    "core-js" "^3.4.1"

"@jimp/core@^0.10.3":
  "integrity" "sha512-Gd5IpL3U2bFIO57Fh/OA3HCpWm4uW/pU01E75rI03BXfTdz3T+J7TwvyG1XaqsQ7/DSlS99GXtLQPlfFIe28UA=="
  "resolved" "https://registry.npmmirror.com/@jimp/core/-/core-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "any-base" "^1.1.0"
    "buffer" "^5.2.0"
    "core-js" "^3.4.1"
    "exif-parser" "^0.1.12"
    "file-type" "^9.0.0"
    "load-bmfont" "^1.3.1"
    "mkdirp" "^0.5.1"
    "phin" "^2.9.1"
    "pixelmatch" "^4.0.2"
    "tinycolor2" "^1.4.1"

"@jimp/custom@^0.10.3", "@jimp/custom@>=0.3.5":
  "integrity" "sha512-nZmSI+jwTi5IRyNLbKSXQovoeqsw+D0Jn0SxW08wYQvdkiWA8bTlDQFgQ7HVwCAKBm8oKkDB/ZEo9qvHJ+1gAQ=="
  "resolved" "https://registry.npmmirror.com/@jimp/custom/-/custom-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/core" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/gif@^0.10.3":
  "integrity" "sha512-vjlRodSfz1CrUvvrnUuD/DsLK1GHB/yDZXHthVdZu23zYJIW7/WrIiD1IgQ5wOMV7NocfrvPn2iqUfBP81/WWA=="
  "resolved" "https://registry.npmmirror.com/@jimp/gif/-/gif-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"
    "omggif" "^1.0.9"

"@jimp/jpeg@^0.10.3":
  "integrity" "sha512-AAANwgUZOt6f6P7LZxY9lyJ9xclqutYJlsxt3JbriXUGJgrrFAIkcKcqv1nObgmQASSAQKYaMV9KdHjMlWFKlQ=="
  "resolved" "https://registry.npmmirror.com/@jimp/jpeg/-/jpeg-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"
    "jpeg-js" "^0.3.4"

"@jimp/plugin-blit@^0.10.3", "@jimp/plugin-blit@>=0.3.5":
  "integrity" "sha512-5zlKlCfx4JWw9qUVC7GI4DzXyxDWyFvgZLaoGFoT00mlXlN75SarlDwc9iZ/2e2kp4bJWxz3cGgG4G/WXrbg3Q=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-blit/-/plugin-blit-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-blur@^0.10.3", "@jimp/plugin-blur@>=0.3.5":
  "integrity" "sha512-cTOK3rjh1Yjh23jSfA6EHCHjsPJDEGLC8K2y9gM7dnTUK1y9NNmkFS23uHpyjgsWFIoH9oRh2SpEs3INjCpZhQ=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-blur/-/plugin-blur-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-circle@^0.10.3":
  "integrity" "sha512-51GAPIVelqAcfuUpaM5JWJ0iWl4vEjNXB7p4P7SX5udugK5bxXUjO6KA2qgWmdpHuCKtoNgkzWU9fNSuYp7tCA=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-circle/-/plugin-circle-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-color@^0.10.3", "@jimp/plugin-color@>=0.8.0":
  "integrity" "sha512-RgeHUElmlTH7vpI4WyQrz6u59spiKfVQbsG/XUzfWGamFSixa24ZDwX/yV/Ts+eNaz7pZeIuv533qmKPvw2ujg=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-color/-/plugin-color-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"
    "tinycolor2" "^1.4.1"

"@jimp/plugin-contain@^0.10.3":
  "integrity" "sha512-bYJKW9dqzcB0Ihc6u7jSyKa3juStzbLs2LFr6fu8TzA2WkMS/R8h+ddkiO36+F9ILTWHP0CIA3HFe5OdOGcigw=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-contain/-/plugin-contain-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-cover@^0.10.3":
  "integrity" "sha512-pOxu0cM0BRPzdV468n4dMocJXoMbTnARDY/EpC3ZW15SpMuc/dr1KhWQHgoQX5kVW1Wt8zgqREAJJCQ5KuPKDA=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-cover/-/plugin-cover-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-crop@^0.10.3", "@jimp/plugin-crop@>=0.3.5":
  "integrity" "sha512-nB7HgOjjl9PgdHr076xZ3Sr6qHYzeBYBs9qvs3tfEEUeYMNnvzgCCGtUl6eMakazZFCMk3mhKmcB9zQuHFOvkg=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-crop/-/plugin-crop-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-displace@^0.10.3":
  "integrity" "sha512-8t3fVKCH5IVqI4lewe4lFFjpxxr69SQCz5/tlpDLQZsrNScNJivHdQ09zljTrVTCSgeCqQJIKgH2Q7Sk/pAZ0w=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-displace/-/plugin-displace-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-dither@^0.10.3":
  "integrity" "sha512-JCX/oNSnEg1kGQ8ffZ66bEgQOLCY3Rn+lrd6v1jjLy/mn9YVZTMsxLtGCXpiCDC2wG/KTmi4862ysmP9do9dAQ=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-dither/-/plugin-dither-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-fisheye@^0.10.3":
  "integrity" "sha512-RRZb1wqe+xdocGcFtj2xHU7sF7xmEZmIa6BmrfSchjyA2b32TGPWKnP3qyj7p6LWEsXn+19hRYbjfyzyebPElQ=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-fisheye/-/plugin-fisheye-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-flip@^0.10.3":
  "integrity" "sha512-0epbi8XEzp0wmSjoW9IB0iMu0yNF17aZOxLdURCN3Zr+8nWPs5VNIMqSVa1Y62GSyiMDpVpKF/ITiXre+EqrPg=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-flip/-/plugin-flip-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-gaussian@^0.10.3":
  "integrity" "sha512-25eHlFbHUDnMMGpgRBBeQ2AMI4wsqCg46sue0KklI+c2BaZ+dGXmJA5uT8RTOrt64/K9Wz5E+2n7eBnny4dfpQ=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-gaussian/-/plugin-gaussian-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-invert@^0.10.3":
  "integrity" "sha512-effYSApWY/FbtlzqsKXlTLkgloKUiHBKjkQnqh5RL4oQxh/33j6aX+HFdDyQKtsXb8CMd4xd7wyiD2YYabTa0g=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-invert/-/plugin-invert-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-mask@^0.10.3":
  "integrity" "sha512-twrg8q8TIhM9Z6Jcu9/5f+OCAPaECb0eKrrbbIajJqJ3bCUlj5zbfgIhiQIzjPJ6KjpnFPSqHQfHkU1Vvk/nVw=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-mask/-/plugin-mask-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-normalize@^0.10.3":
  "integrity" "sha512-xkb5eZI/mMlbwKkDN79+1/t/+DBo8bBXZUMsT4gkFgMRKNRZ6NQPxlv1d3QpRzlocsl6UMxrHnhgnXdLAcgrXw=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-normalize/-/plugin-normalize-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-print@^0.10.3":
  "integrity" "sha512-wjRiI6yjXsAgMe6kVjizP+RgleUCLkH256dskjoNvJzmzbEfO7xQw9g6M02VET+emnbY0CO83IkrGm2q43VRyg=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-print/-/plugin-print-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"
    "load-bmfont" "^1.4.0"

"@jimp/plugin-resize@^0.10.3", "@jimp/plugin-resize@>=0.3.5", "@jimp/plugin-resize@>=0.8.0":
  "integrity" "sha512-rf8YmEB1d7Sg+g4LpqF0Mp+dfXfb6JFJkwlAIWPUOR7lGsPWALavEwTW91c0etEdnp0+JB9AFpy6zqq7Lwkq6w=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-resize/-/plugin-resize-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-rotate@^0.10.3", "@jimp/plugin-rotate@>=0.3.5":
  "integrity" "sha512-YXLlRjm18fkW9MOHUaVAxWjvgZM851ofOipytz5FyKp4KZWDLk+dZK1JNmVmK7MyVmAzZ5jsgSLhIgj+GgN0Eg=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-rotate/-/plugin-rotate-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-scale@^0.10.3", "@jimp/plugin-scale@>=0.3.5":
  "integrity" "sha512-5DXD7x7WVcX1gUgnlFXQa8F+Q3ThRYwJm+aesgrYvDOY+xzRoRSdQvhmdd4JEEue3lyX44DvBSgCIHPtGcEPaw=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-scale/-/plugin-scale-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-shadow@^0.10.3":
  "integrity" "sha512-/nkFXpt2zVcdP4ETdkAUL0fSzyrC5ZFxdcphbYBodqD7fXNqChS/Un1eD4xCXWEpW8cnG9dixZgQgStjywH0Mg=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-shadow/-/plugin-shadow-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugin-threshold@^0.10.3":
  "integrity" "sha512-Dzh0Yq2wXP2SOnxcbbiyA4LJ2luwrdf1MghNIt9H+NX7B+IWw/N8qA2GuSm9n4BPGSLluuhdAWJqHcTiREriVA=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugin-threshold/-/plugin-threshold-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"

"@jimp/plugins@^0.10.3":
  "integrity" "sha512-jTT3/7hOScf0EIKiAXmxwayHhryhc1wWuIe3FrchjDjr9wgIGNN2a7XwCgPl3fML17DXK1x8EzDneCdh261bkw=="
  "resolved" "https://registry.npmmirror.com/@jimp/plugins/-/plugins-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/plugin-blit" "^0.10.3"
    "@jimp/plugin-blur" "^0.10.3"
    "@jimp/plugin-circle" "^0.10.3"
    "@jimp/plugin-color" "^0.10.3"
    "@jimp/plugin-contain" "^0.10.3"
    "@jimp/plugin-cover" "^0.10.3"
    "@jimp/plugin-crop" "^0.10.3"
    "@jimp/plugin-displace" "^0.10.3"
    "@jimp/plugin-dither" "^0.10.3"
    "@jimp/plugin-fisheye" "^0.10.3"
    "@jimp/plugin-flip" "^0.10.3"
    "@jimp/plugin-gaussian" "^0.10.3"
    "@jimp/plugin-invert" "^0.10.3"
    "@jimp/plugin-mask" "^0.10.3"
    "@jimp/plugin-normalize" "^0.10.3"
    "@jimp/plugin-print" "^0.10.3"
    "@jimp/plugin-resize" "^0.10.3"
    "@jimp/plugin-rotate" "^0.10.3"
    "@jimp/plugin-scale" "^0.10.3"
    "@jimp/plugin-shadow" "^0.10.3"
    "@jimp/plugin-threshold" "^0.10.3"
    "core-js" "^3.4.1"
    "timm" "^1.6.1"

"@jimp/png@^0.10.3":
  "integrity" "sha512-YKqk/dkl+nGZxSYIDQrqhmaP8tC3IK8H7dFPnnzFVvbhDnyYunqBZZO3SaZUKTichClRw8k/CjBhbc+hifSGWg=="
  "resolved" "https://registry.npmmirror.com/@jimp/png/-/png-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.10.3"
    "core-js" "^3.4.1"
    "pngjs" "^3.3.3"

"@jimp/tiff@^0.10.3":
  "integrity" "sha512-7EsJzZ5Y/EtinkBGuwX3Bi4S+zgbKouxjt9c82VJTRJOQgLWsE/RHqcyRCOQBhHAZ9QexYmDz34medfLKdoX0g=="
  "resolved" "https://registry.npmmirror.com/@jimp/tiff/-/tiff-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "core-js" "^3.4.1"
    "utif" "^2.0.1"

"@jimp/types@^0.10.3":
  "integrity" "sha512-XGmBakiHZqseSWr/puGN+CHzx0IKBSpsKlmEmsNV96HKDiP6eu8NSnwdGCEq2mmIHe0JNcg1hqg59hpwtQ7Tiw=="
  "resolved" "https://registry.npmmirror.com/@jimp/types/-/types-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/bmp" "^0.10.3"
    "@jimp/gif" "^0.10.3"
    "@jimp/jpeg" "^0.10.3"
    "@jimp/png" "^0.10.3"
    "@jimp/tiff" "^0.10.3"
    "core-js" "^3.4.1"
    "timm" "^1.6.1"

"@jimp/utils@^0.10.3":
  "integrity" "sha512-VcSlQhkil4ReYmg1KkN+WqHyYfZ2XfZxDsKAHSfST1GEz/RQHxKZbX+KhFKtKflnL0F4e6DlNQj3vznMNXCR2w=="
  "resolved" "https://registry.npmmirror.com/@jimp/utils/-/utils-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "core-js" "^3.4.1"
    "regenerator-runtime" "^0.13.3"

"@mrmlnc/readdir-enhanced@^2.2.1":
  "integrity" "sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4="
  "resolved" "https://registry.npmmirror.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "call-me-maybe" "^1.0.1"
    "glob-to-regexp" "^0.3.0"

"@nodelib/fs.stat@^1.1.2":
  "integrity" "sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz"
  "version" "1.1.3"

"@sinonjs/commons@^1.7.0":
  "integrity" "sha1-yNaIIahUxVW7oXLzsGlZoAObI20="
  "resolved" "https://registry.npmmirror.com/@sinonjs/commons/download/@sinonjs/commons-1.8.0.tgz?cache=0&sync_timestamp=1589985579769&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40sinonjs%2Fcommons%2Fdownload%2F%40sinonjs%2Fcommons-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "type-detect" "4.0.8"

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  "integrity" "sha1-cG9kvLSouWQrSK46zkRMcDNNYV0="
  "resolved" "https://registry.npmmirror.com/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.7.1.tgz"
  "version" "1.7.1"
  dependencies:
    "chalk" "^1.1.3"
    "error-stack-parser" "^2.0.0"
    "string-width" "^2.0.0"

"@soda/get-current-script@^1.0.0":
  "integrity" "sha1-YjqkBiNVDjuUdnz/6wlqb7WX7Qk="
  "resolved" "https://registry.npmmirror.com/@soda/get-current-script/download/@soda/get-current-script-1.0.0.tgz"
  "version" "1.0.0"

"@types/babel__core@^7.1.7":
  "integrity" "sha1-HaytiEA2SlfJjQ3UhVxt03Usa4k="
  "resolved" "https://registry.npmmirror.com/@types/babel__core/download/@types/babel__core-7.1.7.tgz"
  "version" "7.1.7"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha1-SQF2ezl+hxGuuZ3405bXunt/DgQ="
  "resolved" "https://registry.npmmirror.com/@types/babel__generator/download/@types/babel__generator-7.6.1.tgz?cache=0&sync_timestamp=1588199664093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fbabel__generator%2Fdownload%2F%40types%2Fbabel__generator-7.6.1.tgz"
  "version" "7.6.1"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  "integrity" "sha1-T/Y9a1Lt2sHee5daUiPtMuzqkwc="
  "resolved" "https://registry.npmmirror.com/@types/babel__template/download/@types/babel__template-7.0.2.tgz?cache=0&sync_timestamp=1588199666194&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fbabel__template%2Fdownload%2F%40types%2Fbabel__template-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  "integrity" "sha1-GuMBDov4hR0ySHi0Ks7HGYZIbRg="
  "resolved" "https://registry.npmmirror.com/@types/babel__traverse/download/@types/babel__traverse-7.0.11.tgz?cache=0&sync_timestamp=1588199665939&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fbabel__traverse%2Fdownload%2F%40types%2Fbabel__traverse-7.0.11.tgz"
  "version" "7.0.11"
  dependencies:
    "@babel/types" "^7.3.0"

"@types/bluebird@^3.5.37":
  "integrity" "sha512-Jhy+MWRlro6UjVi578V/4ZGNfeCOcNCp0YaFNIUGFKlImowqwb1O/22wDVk3FDGMLqxdpOV3qQHD5fPEH4hK6A=="
  "resolved" "https://registry.npmmirror.com/@types/bluebird/-/bluebird-3.5.42.tgz"
  "version" "3.5.42"

"@types/color-name@^1.1.1":
  "integrity" "sha1-HBJhu+qhCoBVu8XYq4S3sq/IRqA="
  "resolved" "https://registry.npmmirror.com/@types/color-name/download/@types/color-name-1.1.1.tgz"
  "version" "1.1.1"

"@types/events@*":
  "integrity" "sha1-KGLz9Yqaf3w+eNefEw3U1xwlwqc="
  "resolved" "https://registry.npmmirror.com/@types/events/download/@types/events-3.0.0.tgz?cache=0&sync_timestamp=1588200013267&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fevents%2Fdownload%2F%40types%2Fevents-3.0.0.tgz"
  "version" "3.0.0"

"@types/formidable@^1.0.31":
  "integrity" "sha1-J0+dwtChqc4f7vSMJMoIWefslHs="
  "resolved" "https://registry.npmmirror.com/@types/formidable/download/@types/formidable-1.0.31.tgz"
  "version" "1.0.31"
  dependencies:
    "@types/events" "*"
    "@types/node" "*"

"@types/glob@^7.1.1":
  "integrity" "sha1-qlmhxuP7xCHgfM0xqUTDDrpSFXU="
  "resolved" "https://registry.npmmirror.com/@types/glob/download/@types/glob-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "@types/events" "*"
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/graceful-fs@^4.1.2":
  "integrity" "sha1-A5rzX+Jr7DUAPo2G0u6cWGNUNI8="
  "resolved" "https://registry.npmmirror.com/@types/graceful-fs/download/@types/graceful-fs-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  "integrity" "sha1-edeni61CGfTAPWVXocctnKa6YtU="
  "resolved" "https://registry.npmmirror.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.2.tgz"
  "version" "2.0.2"

"@types/istanbul-lib-report@*":
  "integrity" "sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY="
  "resolved" "https://registry.npmmirror.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz?cache=0&sync_timestamp=1588227930185&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fistanbul-lib-report%2Fdownload%2F%40types%2Fistanbul-lib-report-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^1.1.1":
  "integrity" "sha1-6HXMaJ5HvOVJ7IHz315vbxHPrrI="
  "resolved" "https://registry.npmmirror.com/@types/istanbul-reports/download/@types/istanbul-reports-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@types/istanbul-lib-coverage" "*"
    "@types/istanbul-lib-report" "*"

"@types/json-schema@^7.0.8":
  "integrity" "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA=="
  "resolved" "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz"
  "version" "7.0.15"

"@types/minimatch@*":
  "integrity" "sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0="
  "resolved" "https://registry.npmmirror.com/@types/minimatch/download/@types/minimatch-3.0.3.tgz"
  "version" "3.0.3"

"@types/node@*":
  "integrity" "sha1-Q6Y/xe3OImvtEGsxuHUWUlYnEQc="
  "resolved" "https://registry.npmmirror.com/@types/node/download/@types/node-14.0.4.tgz?cache=0&sync_timestamp=1589930215015&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-14.0.4.tgz"
  "version" "14.0.4"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4="
  "resolved" "https://registry.npmmirror.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz"
  "version" "2.4.0"

"@types/prettier@^1.19.0":
  "integrity" "sha1-M1CYSfjmeeSt0ViVn9sIZEDpVT8="
  "resolved" "https://registry.npmmirror.com/@types/prettier/download/@types/prettier-1.19.1.tgz?cache=0&sync_timestamp=1588202414050&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fprettier%2Fdownload%2F%40types%2Fprettier-1.19.1.tgz"
  "version" "1.19.1"

"@types/q@^1.5.1":
  "integrity" "sha1-FZJUFOCtLNdlv+9YhC9+JqesyyQ="
  "resolved" "https://registry.npmmirror.com/@types/q/download/@types/q-1.5.4.tgz"
  "version" "1.5.4"

"@types/stack-utils@^1.0.1":
  "integrity" "sha1-CoUdO9lkmPolwzq3J47TvWXwbD4="
  "resolved" "https://registry.npmmirror.com/@types/stack-utils/download/@types/stack-utils-1.0.1.tgz"
  "version" "1.0.1"

"@types/yargs-parser@*":
  "integrity" "sha1-yz+fdBhp4gzOMw/765JxWQSDiC0="
  "resolved" "https://registry.npmmirror.com/@types/yargs-parser/download/@types/yargs-parser-15.0.0.tgz?cache=0&sync_timestamp=1588203262235&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fyargs-parser%2Fdownload%2F%40types%2Fyargs-parser-15.0.0.tgz"
  "version" "15.0.0"

"@types/yargs@^15.0.0":
  "integrity" "sha1-lH6aZWFIO97prf/Jg+kaaQKvi3k="
  "resolved" "https://registry.npmmirror.com/@types/yargs/download/@types/yargs-15.0.5.tgz?cache=0&sync_timestamp=1589406630222&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fyargs%2Fdownload%2F%40types%2Fyargs-15.0.5.tgz"
  "version" "15.0.5"
  dependencies:
    "@types/yargs-parser" "*"

"@types/yauzl@^2.9.1":
  "integrity" "sha512-oJoftv0LSuaDZE3Le4DbKX+KS9G36NzOeSap90UIK0yMA/NhKJhqlSGtNDORNRaIbQfzjXDrQa0ytJ6mNRGz/Q=="
  "resolved" "https://registry.npmmirror.com/@types/yauzl/-/yauzl-2.10.3.tgz"
  "version" "2.10.3"
  dependencies:
    "@types/node" "*"

"@vue/babel-helper-vue-jsx-merge-props@^1.0.0":
  "integrity" "sha1-BI/leZWNpAj7eosqPsBQtQpmEEA="
  "resolved" "https://registry.npmmirror.com/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.0.0.tgz"
  "version" "1.0.0"

"@vue/babel-plugin-transform-vue-jsx@^1.1.2":
  "integrity" "sha1-wKPm78Ai515CR7RIqPxrhvA+kcA="
  "resolved" "https://registry.npmmirror.com/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "html-tags" "^2.0.0"
    "lodash.kebabcase" "^4.1.1"
    "svg-tags" "^1.0.0"

"@vue/babel-preset-app@^4.3.1":
  "integrity" "sha1-u2eq5WKYMGfFskLCf7feF/QM8Qk="
  "resolved" "https://registry.npmmirror.com/@vue/babel-preset-app/download/@vue/babel-preset-app-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "@babel/core" "^7.9.0"
    "@babel/helper-compilation-targets" "^7.8.7"
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/plugin-proposal-class-properties" "^7.8.3"
    "@babel/plugin-proposal-decorators" "^7.8.3"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.8.3"
    "@babel/plugin-transform-runtime" "^7.9.0"
    "@babel/preset-env" "^7.9.0"
    "@babel/runtime" "^7.9.2"
    "@vue/babel-preset-jsx" "^1.1.2"
    "babel-plugin-dynamic-import-node" "^2.3.0"
    "core-js" "^3.6.4"
    "core-js-compat" "^3.6.4"

"@vue/babel-preset-jsx@^1.1.2":
  "integrity" "sha1-LhaetMIE6jfKZsLqhaiAv8mdTyA="
  "resolved" "https://registry.npmmirror.com/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    "@vue/babel-sugar-functional-vue" "^1.1.2"
    "@vue/babel-sugar-inject-h" "^1.1.2"
    "@vue/babel-sugar-v-model" "^1.1.2"
    "@vue/babel-sugar-v-on" "^1.1.2"

"@vue/babel-sugar-functional-vue@^1.1.2":
  "integrity" "sha1-9+JPugnm8e5wEEVgqICAV1VfGpo="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.1.2":
  "integrity" "sha1-ilJ2ttji7Rb/yAeKrZQjYnTm7fA="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.1.2":
  "integrity" "sha1-H/b9G4ACI/ycsehNzrXlLXN6gZI="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    "camelcase" "^5.0.0"
    "html-tags" "^2.0.0"
    "svg-tags" "^1.0.0"

"@vue/babel-sugar-v-on@^1.1.2":
  "integrity" "sha1-su+ZuPL6sJ++rSWq1w70Lhz1sTs="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    "camelcase" "^5.0.0"

"@vue/cli-overlay@^4.3.1":
  "integrity" "sha1-Q0UpwYi2KKVHc2cCAWZ6C0o2Hgc="
  "resolved" "https://registry.npmmirror.com/@vue/cli-overlay/download/@vue/cli-overlay-4.3.1.tgz"
  "version" "4.3.1"

"@vue/cli-plugin-babel@~4.3.0":
  "integrity" "sha1-bjpqoYWVuYrVxSiYooUNRSQEcSs="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "@babel/core" "^7.9.0"
    "@vue/babel-preset-app" "^4.3.1"
    "@vue/cli-shared-utils" "^4.3.1"
    "babel-loader" "^8.1.0"
    "cache-loader" "^4.1.0"
    "thread-loader" "^2.1.3"
    "webpack" "^4.0.0"

"@vue/cli-plugin-router@^4.3.1":
  "integrity" "sha1-C6WJ9Omh8+ZKj/bM2S984oRVhr8="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-router/download/@vue/cli-plugin-router-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "@vue/cli-shared-utils" "^4.3.1"

"@vue/cli-plugin-vuex@^4.3.1":
  "integrity" "sha1-K3Ov9W+eG+MQGIc9XtLVnxVedHY="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-vuex/download/@vue/cli-plugin-vuex-4.3.1.tgz"
  "version" "4.3.1"

"@vue/cli-service@^3.0.0 || ^4.0.0-0", "@vue/cli-service@~4.3.0":
  "integrity" "sha1-lLISHQjjQ6Vffs7yYK9SV6n/5+U="
  "resolved" "https://registry.npmmirror.com/@vue/cli-service/download/@vue/cli-service-4.3.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-service%2Fdownload%2F%40vue%2Fcli-service-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@soda/get-current-script" "^1.0.0"
    "@vue/cli-overlay" "^4.3.1"
    "@vue/cli-plugin-router" "^4.3.1"
    "@vue/cli-plugin-vuex" "^4.3.1"
    "@vue/cli-shared-utils" "^4.3.1"
    "@vue/component-compiler-utils" "^3.0.2"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    "acorn" "^7.1.0"
    "acorn-walk" "^7.1.1"
    "address" "^1.1.2"
    "autoprefixer" "^9.7.5"
    "browserslist" "^4.11.1"
    "cache-loader" "^4.1.0"
    "case-sensitive-paths-webpack-plugin" "^2.3.0"
    "cli-highlight" "^2.1.4"
    "clipboardy" "^2.3.0"
    "cliui" "^6.0.0"
    "copy-webpack-plugin" "^5.1.1"
    "css-loader" "^3.4.2"
    "cssnano" "^4.1.10"
    "debug" "^4.1.1"
    "default-gateway" "^5.0.5"
    "dotenv" "^8.2.0"
    "dotenv-expand" "^5.1.0"
    "file-loader" "^4.2.0"
    "fs-extra" "^7.0.1"
    "globby" "^9.2.0"
    "hash-sum" "^2.0.0"
    "html-webpack-plugin" "^3.2.0"
    "launch-editor-middleware" "^2.2.1"
    "lodash.defaultsdeep" "^4.6.1"
    "lodash.mapvalues" "^4.6.0"
    "lodash.transform" "^4.6.0"
    "mini-css-extract-plugin" "^0.9.0"
    "minimist" "^1.2.5"
    "pnp-webpack-plugin" "^1.6.4"
    "portfinder" "^1.0.25"
    "postcss-loader" "^3.0.0"
    "ssri" "^7.1.0"
    "terser-webpack-plugin" "^2.3.5"
    "thread-loader" "^2.1.3"
    "url-loader" "^2.2.0"
    "vue-loader" "^15.9.1"
    "vue-style-loader" "^4.1.2"
    "webpack" "^4.0.0"
    "webpack-bundle-analyzer" "^3.6.1"
    "webpack-chain" "^6.4.0"
    "webpack-dev-server" "^3.10.3"
    "webpack-merge" "^4.2.2"

"@vue/cli-shared-utils@^4.3.1":
  "integrity" "sha1-p0v01Tgl1KSwWoSwPgI5dIcbw4o="
  "resolved" "https://registry.npmmirror.com/@vue/cli-shared-utils/download/@vue/cli-shared-utils-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "@hapi/joi" "^15.0.1"
    "chalk" "^2.4.2"
    "execa" "^1.0.0"
    "launch-editor" "^2.2.1"
    "lru-cache" "^5.1.1"
    "node-ipc" "^9.1.1"
    "open" "^6.3.0"
    "ora" "^3.4.0"
    "read-pkg" "^5.1.1"
    "request" "^2.88.2"
    "request-promise-native" "^1.0.8"
    "semver" "^6.1.0"
    "strip-ansi" "^6.0.0"

"@vue/component-compiler-utils@^3.0.2", "@vue/component-compiler-utils@^3.1.0":
  "integrity" "sha1-ghOl/zIC+fITf+VTcPnouWVggcM="
  "resolved" "https://registry.npmmirror.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "consolidate" "^0.15.1"
    "hash-sum" "^1.0.2"
    "lru-cache" "^4.1.2"
    "merge-source-map" "^1.1.0"
    "postcss" "^7.0.14"
    "postcss-selector-parser" "^6.0.2"
    "source-map" "~0.6.1"
    "vue-template-es2015-compiler" "^1.9.0"
  optionalDependencies:
    "prettier" "^1.18.2"

"@vue/preload-webpack-plugin@^1.1.0":
  "integrity" "sha1-GHI1MNME9EMCHaIpLW7JUCgmEEo="
  "resolved" "https://registry.npmmirror.com/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.1.tgz"
  "version" "1.1.1"

"@vue/web-component-wrapper@^1.2.0":
  "integrity" "sha1-uw5G8VhafiibTuYGfcxaauYvHdE="
  "resolved" "https://registry.npmmirror.com/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.2.0.tgz"
  "version" "1.2.0"

"@webassemblyjs/ast@1.9.0":
  "integrity" "sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fast%2Fdownload%2F%40webassemblyjs%2Fast-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  "integrity" "sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Ffloating-point-hex-parser%2Fdownload%2F%40webassemblyjs%2Ffloating-point-hex-parser-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-api-error@1.9.0":
  "integrity" "sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-buffer@1.9.0":
  "integrity" "sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz?cache=0&sync_timestamp=1580600188490&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-buffer%2Fdownload%2F%40webassemblyjs%2Fhelper-buffer-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-code-frame@1.9.0":
  "integrity" "sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-code-frame%2Fdownload%2F%40webassemblyjs%2Fhelper-code-frame-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  "integrity" "sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-fsm%2Fdownload%2F%40webassemblyjs%2Fhelper-fsm-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-module-context@1.9.0":
  "integrity" "sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-module-context%2Fdownload%2F%40webassemblyjs%2Fhelper-module-context-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  "integrity" "sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-wasm-bytecode%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-bytecode-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-wasm-section@1.9.0":
  "integrity" "sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-wasm-section%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-section-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  "integrity" "sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fieee754%2Fdownload%2F%40webassemblyjs%2Fieee754-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  "integrity" "sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fleb128%2Fdownload%2F%40webassemblyjs%2Fleb128-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  "integrity" "sha1-BNM7Y2945qaBMifoJAL3Y3tiKas="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/wasm-edit@1.9.0":
  "integrity" "sha1-P+bXnT8PkiGDqoYALELdJWz+6c8="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-edit%2Fdownload%2F%40webassemblyjs%2Fwasm-edit-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  "integrity" "sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-gen%2Fdownload%2F%40webassemblyjs%2Fwasm-gen-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  "integrity" "sha1-IhEYHlsxMmRDzIES658LkChyGmE="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-opt%2Fdownload%2F%40webassemblyjs%2Fwasm-opt-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  "integrity" "sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-parser%2Fdownload%2F%40webassemblyjs%2Fwasm-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  "integrity" "sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwast-parser%2Fdownload%2F%40webassemblyjs%2Fwast-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  "integrity" "sha1-STXVTIX+9jewDOn1I3dFHQDUeJk="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz?cache=0&sync_timestamp=1580599638157&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwast-printer%2Fdownload%2F%40webassemblyjs%2Fwast-printer-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A="
  "resolved" "https://registry.npmmirror.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0="
  "resolved" "https://registry.npmmirror.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz"
  "version" "4.2.2"

"aaptjs@^1.3.1":
  "integrity" "sha1-xA48CkPunQZ8xixKg7Z6YZUCArU="
  "resolved" "https://registry.npmmirror.com/aaptjs/download/aaptjs-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "shelljs" "^0.8.1"

"abab@^2.0.0":
  "integrity" "sha1-Yj4gdeAustPyR15J+ZyRhGRnkHo="
  "resolved" "https://registry.npmmirror.com/abab/download/abab-2.0.3.tgz"
  "version" "2.0.3"

"accepts@^1.3.5", "accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.7":
  "integrity" "sha1-UxvHJlF6OytB+FACHGzBXqq1B80="
  "resolved" "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "mime-types" "~2.1.24"
    "negotiator" "0.6.2"

"acorn-globals@^4.3.2":
  "integrity" "sha1-n6GSat3BHJcwjE5m163Q1Awycuc="
  "resolved" "https://registry.npmmirror.com/acorn-globals/download/acorn-globals-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "acorn" "^6.0.1"
    "acorn-walk" "^6.0.1"

"acorn-walk@^6.0.1":
  "integrity" "sha1-Ejy487hMIXHx9/slJhWxx4prGow="
  "resolved" "https://registry.npmmirror.com/acorn-walk/download/acorn-walk-6.2.0.tgz?cache=0&sync_timestamp=1581612804260&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn-walk%2Fdownload%2Facorn-walk-6.2.0.tgz"
  "version" "6.2.0"

"acorn-walk@^7.1.1":
  "integrity" "sha1-NF8N/61cc15zc9L+yaECPmpEuD4="
  "resolved" "https://registry.npmmirror.com/acorn-walk/download/acorn-walk-7.1.1.tgz?cache=0&sync_timestamp=1581612804260&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn-walk%2Fdownload%2Facorn-walk-7.1.1.tgz"
  "version" "7.1.1"

"acorn@^5.2.1":
  "integrity" "sha1-Po2KmUfQWZoXltECJddDL0pKz14="
  "resolved" "https://registry.npmmirror.com/acorn/download/acorn-5.7.4.tgz?cache=0&sync_timestamp=1589007904036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-5.7.4.tgz"
  "version" "5.7.4"

"acorn@^6.0.1":
  "integrity" "sha1-Ux5Yuj9RudrLmmZGyk3r9bFMpHQ="
  "resolved" "https://registry.npmmirror.com/acorn/download/acorn-6.4.1.tgz?cache=0&sync_timestamp=1589007904036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-6.4.1.tgz"
  "version" "6.4.1"

"acorn@^6.4.1":
  "integrity" "sha1-Ux5Yuj9RudrLmmZGyk3r9bFMpHQ="
  "resolved" "https://registry.npmmirror.com/acorn/download/acorn-6.4.1.tgz?cache=0&sync_timestamp=1589007904036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-6.4.1.tgz"
  "version" "6.4.1"

"acorn@^7.1.0", "acorn@^7.1.1":
  "integrity" "sha1-F+p+QNfIZA/1SmlMiJwm8xcE7/4="
  "resolved" "https://registry.npmmirror.com/acorn/download/acorn-7.2.0.tgz?cache=0&sync_timestamp=1589007904036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-7.2.0.tgz"
  "version" "7.2.0"

"adb-commander@^0.1.8":
  "integrity" "sha1-tvnZ0leX4ylJcTZZO2jr6+u8568="
  "resolved" "https://registry.npmmirror.com/adb-commander/download/adb-commander-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "adb-driver" "^0.1.8"

"adb-devices-emitter@^0.1.8":
  "integrity" "sha1-EXJ9LtHEpRQ5/eK2hK0WpMmPAuk="
  "resolved" "https://registry.npmmirror.com/adb-devices-emitter/download/adb-devices-emitter-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "adb-commander" "^0.1.8"

"adb-driver@^0.1.8":
  "integrity" "sha1-9KZWy6BryFcE5h7koV54ll7vqvQ="
  "resolved" "https://registry.npmmirror.com/adb-driver/download/adb-driver-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "which" "^1.3.1"

"adbkit-logcat@^1.1.0":
  "integrity" "sha512-57iYRLdjmhI1fnc890KyflzWpnIb/aq5ET3fbn3axdyyeyKeP4Ji/GhnfBNguG1Tw7SQRL2eBnA+hvbwIsTtNg=="
  "resolved" "https://registry.npmmirror.com/adbkit-logcat/-/adbkit-logcat-1.1.0.tgz"
  "version" "1.1.0"

"adbkit-monkey@~1.0.1":
  "integrity" "sha512-uU8p+p4sv7gLsjO/At4iPufoPD3R16kVbzDVecdIerR9RzhEK6PcyAJghdOXwrYKbhm7SmPQWsxVRloDBMINDA=="
  "resolved" "https://registry.npmmirror.com/adbkit-monkey/-/adbkit-monkey-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "async" "~0.2.9"

"adbkit@^2.11.1":
  "integrity" "sha512-hDTiRg9NX3HQt7WoDAPCplUpvzr4ZzQa2lq7BdTTJ/iOZ6O7YNAs6UYD8sFAiBEcYHDRIyq3cm9sZP6uZnhvXw=="
  "resolved" "https://registry.npmmirror.com/adbkit/-/adbkit-2.11.1.tgz"
  "version" "2.11.1"
  dependencies:
    "adbkit-logcat" "^1.1.0"
    "adbkit-monkey" "~1.0.1"
    "bluebird" "~2.9.24"
    "commander" "^2.3.0"
    "debug" "~2.6.3"
    "node-forge" "^0.7.1"
    "split" "~0.3.3"

"address@^1.1.2":
  "integrity" "sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY="
  "resolved" "https://registry.npmmirror.com/address/download/address-1.1.2.tgz"
  "version" "1.1.2"

"after@0.8.2":
  "integrity" "sha1-/ts5T58OAqqXaOcCvaI7UF+ufh8="
  "resolved" "https://registry.npmmirror.com/after/download/after-0.8.2.tgz"
  "version" "0.8.2"

"agent-base@5":
  "integrity" "sha512-TMeqbNl2fMW0nMjTEPOwe3J/PRFP4vqeoNuQMG0HlMrtm5QxKqdvAkZ1pRBQ/ulIyDD5Yq0nJ7YbdD8ey0TO3g=="
  "resolved" "https://registry.npmmirror.com/agent-base/-/agent-base-5.1.1.tgz"
  "version" "5.1.1"

"aggregate-error@^3.0.0":
  "integrity" "sha1-2y/nJG5Tb0DZtUQqOeEX191qJOA="
  "resolved" "https://registry.npmmirror.com/aggregate-error/download/aggregate-error-3.0.1.tgz?cache=0&sync_timestamp=1570167911603&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faggregate-error%2Fdownload%2Faggregate-error-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv-errors@^1.0.0":
  "integrity" "sha1-81mGrOuRr63sQQL72FAUlQzvpk0="
  "resolved" "https://registry.npmmirror.com/ajv-errors/download/ajv-errors-1.0.1.tgz"
  "version" "1.0.1"

"ajv-keywords@^3.1.0", "ajv-keywords@^3.4.1":
  "integrity" "sha1-75FuJxxkrBIXH9g4TqrmsjRYVNo="
  "resolved" "https://registry.npmmirror.com/ajv-keywords/download/ajv-keywords-3.4.1.tgz"
  "version" "3.4.1"

"ajv-keywords@^3.5.2":
  "integrity" "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="
  "resolved" "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv@^6.1.0", "ajv@^6.10.2", "ajv@^6.12.0", "ajv@^6.5.5", "ajv@^6.9.1", "ajv@>=5.0.0":
  "integrity" "sha1-xinF7O0XuvMUQ3kY0tqIyZ1ZWM0="
  "resolved" "https://registry.npmmirror.com/ajv/download/ajv-6.12.2.tgz?cache=0&sync_timestamp=1587338610933&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv%2Fdownload%2Fajv-6.12.2.tgz"
  "version" "6.12.2"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^6.12.5":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"alphanum-sort@^1.0.0":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://registry.npmmirror.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"ansi-colors@^3.0.0":
  "integrity" "sha1-46PaS/uubIapwoViXeEkojQCb78="
  "resolved" "https://registry.npmmirror.com/ansi-colors/download/ansi-colors-3.2.4.tgz"
  "version" "3.2.4"

"ansi-escapes@^4.2.1":
  "integrity" "sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE="
  "resolved" "https://registry.npmmirror.com/ansi-escapes/download/ansi-escapes-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "type-fest" "^0.11.0"

"ansi-html@0.0.7":
  "integrity" "sha1-gTWEAhliqenm/QOflA0S9WynhZ4="
  "resolved" "https://registry.npmmirror.com/ansi-html/download/ansi-html-0.0.7.tgz"
  "version" "0.0.7"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="
  "resolved" "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-3.0.0.tgz"
  "version" "3.0.0"

"ansi-regex@^4.1.0":
  "integrity" "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="
  "resolved" "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-4.1.0.tgz"
  "version" "4.1.0"

"ansi-regex@^5.0.0":
  "integrity" "sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U="
  "resolved" "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.0.tgz"
  "version" "5.0.0"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.0":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^3.2.1":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha1-kK51xCTQCNJiTFvynq0xd+v881k="
  "resolved" "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "@types/color-name" "^1.1.1"
    "color-convert" "^2.0.1"

"any-base@^1.1.0":
  "integrity" "sha512-uMgjozySS8adZZYePpaWs8cxB9/kdzmpX6SgJZ+wbz1K5eYk5QMYDVJaZKhxyIHUdnnJkfR7SVgStgH7LkGUyg=="
  "resolved" "https://registry.npmmirror.com/any-base/-/any-base-1.1.0.tgz"
  "version" "1.1.0"

"any-promise@^1.0.0", "any-promise@^1.1.0":
  "integrity" "sha1-q8av7tzqUugJzcA3au0845Y10X8="
  "resolved" "https://registry.npmmirror.com/any-promise/download/any-promise-1.3.0.tgz"
  "version" "1.3.0"

"anymatch@^2.0.0":
  "integrity" "sha1-vLJLTzeTTZqnrBe0ra+J58du8us="
  "resolved" "https://registry.npmmirror.com/anymatch/download/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@^3.0.3":
  "integrity" "sha1-xV7PAhheJGklk5kxDBc84xIzsUI="
  "resolved" "https://registry.npmmirror.com/anymatch/download/anymatch-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"anymatch@~3.1.1":
  "integrity" "sha1-xV7PAhheJGklk5kxDBc84xIzsUI="
  "resolved" "https://registry.npmmirror.com/anymatch/download/anymatch-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"aproba@^1.0.3 || ^2.0.0", "aproba@^1.1.1":
  "integrity" "sha1-aALmJk79GMeQobDVF/DyYnvyyUo="
  "resolved" "https://registry.npmmirror.com/aproba/download/aproba-1.2.0.tgz"
  "version" "1.2.0"

"arch@^2.1.1":
  "integrity" "sha1-DFK75zRLtPomDEQ9LLrZwA/y8L8="
  "resolved" "https://registry.npmmirror.com/arch/download/arch-2.1.2.tgz?cache=0&sync_timestamp=1589130903544&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farch%2Fdownload%2Farch-2.1.2.tgz"
  "version" "2.1.2"

"are-we-there-yet@^2.0.0":
  "integrity" "sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw=="
  "resolved" "https://registry.npmmirror.com/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "delegates" "^1.0.0"
    "readable-stream" "^3.6.0"

"argparse@^1.0.7":
  "integrity" "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE="
  "resolved" "https://registry.npmmirror.com/argparse/download/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://registry.npmmirror.com/arr-diff/download/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="
  "resolved" "https://registry.npmmirror.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://registry.npmmirror.com/arr-union/download/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-equal@^1.0.0":
  "integrity" "sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM="
  "resolved" "https://registry.npmmirror.com/array-equal/download/array-equal-1.0.0.tgz"
  "version" "1.0.0"

"array-flatten@^2.1.0":
  "integrity" "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk="
  "resolved" "https://registry.npmmirror.com/array-flatten/download/array-flatten-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-flatten%2Fdownload%2Farray-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://registry.npmmirror.com/array-flatten/download/array-flatten-1.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-flatten%2Fdownload%2Farray-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-union@^1.0.1", "array-union@^1.0.2":
  "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
  "resolved" "https://registry.npmmirror.com/array-union/download/array-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-uniq@^1.0.1":
  "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
  "resolved" "https://registry.npmmirror.com/array-uniq/download/array-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://registry.npmmirror.com/array-unique/download/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"arraybuffer.slice@~0.0.7":
  "integrity" "sha1-O7xCdd1YTMGxCAm4nU6LY6aednU="
  "resolved" "https://registry.npmmirror.com/arraybuffer.slice/download/arraybuffer.slice-0.0.7.tgz"
  "version" "0.0.7"

"asn1.js@^4.0.0":
  "integrity" "sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA="
  "resolved" "https://registry.npmmirror.com/asn1.js/download/asn1.js-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"asn1@~0.2.3":
  "integrity" "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY="
  "resolved" "https://registry.npmmirror.com/asn1/download/asn1-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="
  "resolved" "https://registry.npmmirror.com/assert-plus/download/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assert@^1.1.1":
  "integrity" "sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs="
  "resolved" "https://registry.npmmirror.com/assert/download/assert-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "object-assign" "^4.1.1"
    "util" "0.10.3"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://registry.npmmirror.com/assign-symbols/download/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^1.0.0":
  "integrity" "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k="
  "resolved" "https://registry.npmmirror.com/astral-regex/download/astral-regex-1.0.0.tgz"
  "version" "1.0.0"

"async-each@^1.0.1":
  "integrity" "sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8="
  "resolved" "https://registry.npmmirror.com/async-each/download/async-each-1.0.3.tgz"
  "version" "1.0.3"

"async-limiter@~1.0.0":
  "integrity" "sha1-3TeelPDbgxCwgpH51kwyCXZmF/0="
  "resolved" "https://registry.npmmirror.com/async-limiter/download/async-limiter-1.0.1.tgz?cache=0&sync_timestamp=1574272018408&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync-limiter%2Fdownload%2Fasync-limiter-1.0.1.tgz"
  "version" "1.0.1"

"async@^2.6.2":
  "integrity" "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8="
  "resolved" "https://registry.npmmirror.com/async/download/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"async@~0.2.9":
  "integrity" "sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ=="
  "resolved" "https://registry.npmmirror.com/async/-/async-0.2.10.tgz"
  "version" "0.2.10"

"asyncbox@^2.3.1":
  "integrity" "sha512-TCuA73K6Gvn+5tFGsWf4jc+PsR9RmYXw/AF0mv+CRB3VhHLjqHh/w9gPvYILnV0RcRFfjADHtzZexpxWlsP3Tg=="
  "resolved" "https://registry.npmmirror.com/asyncbox/-/asyncbox-2.9.4.tgz"
  "version" "2.9.4"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "@types/bluebird" "^3.5.37"
    "bluebird" "^3.5.1"
    "lodash" "^4.17.4"
    "source-map-support" "^0.5.5"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://registry.npmmirror.com/asynckit/download/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k="
  "resolved" "https://registry.npmmirror.com/atob/download/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^9.7.5":
  "integrity" "sha1-aOLSvve6TDplQ29mLQpWp0HlZRE="
  "resolved" "https://registry.npmmirror.com/autoprefixer/download/autoprefixer-9.8.0.tgz"
  "version" "9.8.0"
  dependencies:
    "browserslist" "^4.12.0"
    "caniuse-lite" "^1.0.30001061"
    "chalk" "^2.4.2"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "postcss" "^7.0.30"
    "postcss-value-parser" "^4.1.0"

"aws-sign2@~0.7.0":
  "integrity" "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="
  "resolved" "https://registry.npmmirror.com/aws-sign2/download/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.8.0":
  "integrity" "sha1-fjPY99RJs/ZzzXLeuavcVS2+Uo4="
  "resolved" "https://registry.npmmirror.com/aws4/download/aws4-1.9.1.tgz"
  "version" "1.9.1"

"babel-jest@^25.5.1":
  "integrity" "sha1-vC5hAfhJ1vauwJcg/8e8UzLmKFM="
  "resolved" "https://registry.npmmirror.com/babel-jest/download/babel-jest-25.5.1.tgz"
  "version" "25.5.1"
  dependencies:
    "@jest/transform" "^25.5.1"
    "@jest/types" "^25.5.0"
    "@types/babel__core" "^7.1.7"
    "babel-plugin-istanbul" "^6.0.0"
    "babel-preset-jest" "^25.5.0"
    "chalk" "^3.0.0"
    "graceful-fs" "^4.2.4"
    "slash" "^3.0.0"

"babel-loader@^8.0.5", "babel-loader@^8.1.0":
  "integrity" "sha1-xhHVESvVIJq+i5+oTD5NolJ18cM="
  "resolved" "https://registry.npmmirror.com/babel-loader/download/babel-loader-8.1.0.tgz?cache=0&sync_timestamp=1584715910722&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-loader%2Fdownload%2Fbabel-loader-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "find-cache-dir" "^2.1.0"
    "loader-utils" "^1.4.0"
    "mkdirp" "^0.5.3"
    "pify" "^4.0.1"
    "schema-utils" "^2.6.5"

"babel-plugin-dynamic-import-node@^2.3.0", "babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M="
  "resolved" "https://registry.npmmirror.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-import@^1.11.0":
  "integrity" "sha1-xTL9Uz3521O0fU1Ns2dgkPxcB6U="
  "resolved" "https://registry.npmmirror.com/babel-plugin-import/download/babel-plugin-import-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

"babel-plugin-istanbul@^6.0.0":
  "integrity" "sha1-4VnM3Jr5XgtXDHW0Vzt8NNZx12U="
  "resolved" "https://registry.npmmirror.com/babel-plugin-istanbul/download/babel-plugin-istanbul-6.0.0.tgz?cache=0&sync_timestamp=1577063702695&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-plugin-istanbul%2Fdownload%2Fbabel-plugin-istanbul-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-instrument" "^4.0.0"
    "test-exclude" "^6.0.0"

"babel-plugin-jest-hoist@^25.5.0":
  "integrity" "sha1-EpyAulx/x1uvOkW5Pi43LVfKJnc="
  "resolved" "https://registry.npmmirror.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__traverse" "^7.0.6"

"babel-preset-current-node-syntax@^0.1.2":
  "integrity" "sha1-+0pMUf44ymD+3h3HSrNeuEPLQdY="
  "resolved" "https://registry.npmmirror.com/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"babel-preset-jest@^25.5.0":
  "integrity" "sha1-wdfxkYKUh6kHdkxlMH+qDmZZC0k="
  "resolved" "https://registry.npmmirror.com/babel-preset-jest/download/babel-preset-jest-25.5.0.tgz?cache=0&sync_timestamp=1588614908454&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-preset-jest%2Fdownload%2Fbabel-preset-jest-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "babel-plugin-jest-hoist" "^25.5.0"
    "babel-preset-current-node-syntax" "^0.1.2"

"backo2@1.0.2":
  "integrity" "sha1-MasayLEpNjRj41s+u2n038+6eUc="
  "resolved" "https://registry.npmmirror.com/backo2/download/backo2-1.0.2.tgz"
  "version" "1.0.2"

"balanced-match@^1.0.0":
  "integrity" "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="
  "resolved" "https://registry.npmmirror.com/balanced-match/download/balanced-match-1.0.0.tgz"
  "version" "1.0.0"

"base@^0.11.1":
  "integrity" "sha1-e95c7RRbbVUakNuH+DxVi060io8="
  "resolved" "https://registry.npmmirror.com/base/download/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-arraybuffer@^0.2.0":
  "integrity" "sha1-S5RPrAGRqlkHr+LYyZnMxXzoD0U="
  "resolved" "https://registry.npmmirror.com/base64-arraybuffer/download/base64-arraybuffer-0.2.0.tgz"
  "version" "0.2.0"

"base64-arraybuffer@0.1.5":
  "integrity" "sha1-c5JncZI7Whl0etZmqlzUv5xunOg="
  "resolved" "https://registry.npmmirror.com/base64-arraybuffer/download/base64-arraybuffer-0.1.5.tgz"
  "version" "0.1.5"

"base64-js@^1.0.2", "base64-js@^1.3.1":
  "integrity" "sha1-WOzoy3XdB+ce0IxzarxfrE2/jfE="
  "resolved" "https://registry.npmmirror.com/base64-js/download/base64-js-1.3.1.tgz"
  "version" "1.3.1"

"base64id@2.0.0":
  "integrity" "sha1-J3Csa8R9MSr5eov5pjQ0LgzSXLY="
  "resolved" "https://registry.npmmirror.com/base64id/download/base64id-2.0.0.tgz"
  "version" "2.0.0"

"batch@0.6.1":
  "integrity" "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="
  "resolved" "https://registry.npmmirror.com/batch/download/batch-0.6.1.tgz"
  "version" "0.6.1"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4="
  "resolved" "https://registry.npmmirror.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"better-assert@~1.0.0":
  "integrity" "sha1-QIZrnhueC1W0gYlDEeaPr/rrxSI="
  "resolved" "https://registry.npmmirror.com/better-assert/download/better-assert-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "callsite" "1.0.0"

"bfj@^6.1.1":
  "integrity" "sha1-MlyGGoIryzWKQceKM7jm4ght3n8="
  "resolved" "https://registry.npmmirror.com/bfj/download/bfj-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "bluebird" "^3.5.5"
    "check-types" "^8.0.3"
    "hoopy" "^0.1.4"
    "tryer" "^1.0.1"

"big.js@^3.1.3":
  "integrity" "sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4="
  "resolved" "https://registry.npmmirror.com/big.js/download/big.js-3.2.0.tgz"
  "version" "3.2.0"

"big.js@^5.2.2":
  "integrity" "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg="
  "resolved" "https://registry.npmmirror.com/big.js/download/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^1.0.0":
  "integrity" "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U="
  "resolved" "https://registry.npmmirror.com/binary-extensions/download/binary-extensions-1.13.1.tgz"
  "version" "1.13.1"

"binary-extensions@^2.0.0":
  "integrity" "sha1-I8DfFPaogHf1+YbA0WfsA8PVU3w="
  "resolved" "https://registry.npmmirror.com/binary-extensions/download/binary-extensions-2.0.0.tgz"
  "version" "2.0.0"

"bindings@^1.5.0":
  "integrity" "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8="
  "resolved" "https://registry.npmmirror.com/bindings/download/bindings-1.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbindings%2Fdownload%2Fbindings-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "file-uri-to-path" "1.0.0"

"bl@^4.0.3":
  "integrity" "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w=="
  "resolved" "https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer" "^5.5.0"
    "inherits" "^2.0.4"
    "readable-stream" "^3.4.0"

"blob@0.0.5":
  "integrity" "sha1-1oDu7yX4zZGtUz9bAe7UjmTK9oM="
  "resolved" "https://registry.npmmirror.com/blob/download/blob-0.0.5.tgz?cache=0&sync_timestamp=1580722883513&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fblob%2Fdownload%2Fblob-0.0.5.tgz"
  "version" "0.0.5"

"bluebird@^3.1.1", "bluebird@^3.5.1", "bluebird@^3.5.5":
  "integrity" "sha1-nyKcFb4nJFT/qXOs4NvueaGww28="
  "resolved" "https://registry.npmmirror.com/bluebird/download/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"bluebird@~2.9.24":
  "integrity" "sha512-ZDzCb87X7/IP1uzQ5eJZB+WoQRGTnKL5DHWvPw6kkMbQseouiQIrEi3P1UGE0D1k0N5/+aP/5GMCyHZ1xYJyHQ=="
  "resolved" "https://registry.npmmirror.com/bluebird/-/bluebird-2.9.34.tgz"
  "version" "2.9.34"

"bmp-js@^0.1.0":
  "integrity" "sha512-vHdS19CnY3hwiNdkaqk93DvjVLfbEcI8mys4UjuWrlX1haDmroo8o4xCzh4wD6DGV6HxRCyauwhHRqMTfERtjw=="
  "resolved" "https://registry.npmmirror.com/bmp-js/-/bmp-js-0.1.0.tgz"
  "version" "0.1.0"

"bn.js@^4.0.0", "bn.js@^4.1.0", "bn.js@^4.4.0":
  "integrity" "sha1-LN4J617jQfSEdGuwMJsyU7GxRC8="
  "resolved" "https://registry.npmmirror.com/bn.js/download/bn.js-4.11.8.tgz"
  "version" "4.11.8"

"bn.js@^5.1.1":
  "integrity" "sha1-SO/EAxqcQEG5yZxpQdkDRjq2LrU="
  "resolved" "https://registry.npmmirror.com/bn.js/download/bn.js-5.1.1.tgz"
  "version" "5.1.1"

"body-parser@1.19.0":
  "integrity" "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io="
  "resolved" "https://registry.npmmirror.com/body-parser/download/body-parser-1.19.0.tgz"
  "version" "1.19.0"
  dependencies:
    "bytes" "3.1.0"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "on-finished" "~2.3.0"
    "qs" "6.7.0"
    "raw-body" "2.4.0"
    "type-is" "~1.6.17"

"bonjour@^3.5.0":
  "integrity" "sha1-jokKGD2O6aI5OzhExpGkK897yfU="
  "resolved" "https://registry.npmmirror.com/bonjour/download/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "^2.1.0"
    "deep-equal" "^1.0.1"
    "dns-equal" "^1.0.0"
    "dns-txt" "^2.0.2"
    "multicast-dns" "^6.0.1"
    "multicast-dns-service-types" "^1.1.0"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://registry.npmmirror.com/boolbase/download/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
  "resolved" "https://registry.npmmirror.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^2.3.1":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "https://registry.npmmirror.com/braces/download/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^2.3.2":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "https://registry.npmmirror.com/braces/download/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^3.0.1", "braces@~3.0.2":
  "integrity" "sha1-NFThpGLujVmeI23zNs2epPiv4Qc="
  "resolved" "https://registry.npmmirror.com/braces/download/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"brorand@^1.0.1":
  "integrity" "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="
  "resolved" "https://registry.npmmirror.com/brorand/download/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browser-process-hrtime@^1.0.0":
  "integrity" "sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY="
  "resolved" "https://registry.npmmirror.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz"
  "version" "1.0.0"

"browser-resolve@^1.11.3":
  "integrity" "sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY="
  "resolved" "https://registry.npmmirror.com/browser-resolve/download/browser-resolve-1.11.3.tgz"
  "version" "1.11.3"
  dependencies:
    "resolve" "1.1.7"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g="
  "resolved" "https://registry.npmmirror.com/browserify-aes/download/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA="
  "resolved" "https://registry.npmmirror.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw="
  "resolved" "https://registry.npmmirror.com/browserify-des/download/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0", "browserify-rsa@^4.0.1":
  "integrity" "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ="
  "resolved" "https://registry.npmmirror.com/browserify-rsa/download/browserify-rsa-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.1.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha1-VF0LGwfmssmSEQgr8bEsznoLDhE="
  "resolved" "https://registry.npmmirror.com/browserify-sign/download/browserify-sign-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "bn.js" "^5.1.1"
    "browserify-rsa" "^4.0.1"
    "create-hash" "^1.2.0"
    "create-hmac" "^1.1.7"
    "elliptic" "^6.5.2"
    "inherits" "^2.0.4"
    "parse-asn1" "^5.1.5"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8="
  "resolved" "https://registry.npmmirror.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^4.0.0", "browserslist@^4.11.1", "browserslist@^4.12.0", "browserslist@^4.8.5":
  "integrity" "sha1-BsbVcVoe3mxR/Dn/Z/1kf3QLZW0="
  "resolved" "https://registry.npmmirror.com/browserslist/download/browserslist-4.12.0.tgz"
  "version" "4.12.0"
  dependencies:
    "caniuse-lite" "^1.0.30001043"
    "electron-to-chromium" "^1.3.413"
    "node-releases" "^1.1.53"
    "pkg-up" "^2.0.0"

"bser@2.1.1":
  "integrity" "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU="
  "resolved" "https://registry.npmmirror.com/bser/download/bser-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "node-int64" "^0.4.0"

"buffer-crc32@~0.2.3":
  "integrity" "sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ=="
  "resolved" "https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
  "version" "0.2.13"

"buffer-equal@0.0.1":
  "integrity" "sha512-RgSV6InVQ9ODPdLWJ5UAqBqJBOg370Nz6ZQtRzpt6nUjc8v0St97uJ4PYC6NztqIScrAXafKM3mZPMygSe1ggA=="
  "resolved" "https://registry.npmmirror.com/buffer-equal/-/buffer-equal-0.0.1.tgz"
  "version" "0.0.1"

"buffer-from@^1.0.0":
  "integrity" "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8="
  "resolved" "https://registry.npmmirror.com/buffer-from/download/buffer-from-1.1.1.tgz"
  "version" "1.1.1"

"buffer-indexof@^1.0.0":
  "integrity" "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow="
  "resolved" "https://registry.npmmirror.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"buffer-json@^2.0.0":
  "integrity" "sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM="
  "resolved" "https://registry.npmmirror.com/buffer-json/download/buffer-json-2.0.0.tgz"
  "version" "2.0.0"

"buffer-xor@^1.0.3":
  "integrity" "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="
  "resolved" "https://registry.npmmirror.com/buffer-xor/download/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg="
  "resolved" "https://registry.npmmirror.com/buffer/download/buffer-4.9.2.tgz?cache=0&sync_timestamp=1588706716358&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbuffer%2Fdownload%2Fbuffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"buffer@^5.2.0":
  "integrity" "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ=="
  "resolved" "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"buffer@^5.2.1":
  "integrity" "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ=="
  "resolved" "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"buffer@^5.5.0":
  "integrity" "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ=="
  "resolved" "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"builtin-status-codes@^3.0.0":
  "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
  "resolved" "https://registry.npmmirror.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://registry.npmmirror.com/bytes/download/bytes-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbytes%2Fdownload%2Fbytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.0":
  "integrity" "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="
  "resolved" "https://registry.npmmirror.com/bytes/download/bytes-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbytes%2Fdownload%2Fbytes-3.1.0.tgz"
  "version" "3.1.0"

"cacache@^12.0.2", "cacache@^12.0.3":
  "integrity" "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw="
  "resolved" "https://registry.npmmirror.com/cacache/download/cacache-12.0.4.tgz"
  "version" "12.0.4"
  dependencies:
    "bluebird" "^3.5.5"
    "chownr" "^1.1.1"
    "figgy-pudding" "^3.5.1"
    "glob" "^7.1.4"
    "graceful-fs" "^4.1.15"
    "infer-owner" "^1.0.3"
    "lru-cache" "^5.1.1"
    "mississippi" "^3.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.3"
    "ssri" "^6.0.1"
    "unique-filename" "^1.1.1"
    "y18n" "^4.0.0"

"cacache@^13.0.1":
  "integrity" "sha1-qAAMIWlwiQgvhSh6GuxuOCAkpxw="
  "resolved" "https://registry.npmmirror.com/cacache/download/cacache-13.0.1.tgz"
  "version" "13.0.1"
  dependencies:
    "chownr" "^1.1.2"
    "figgy-pudding" "^3.5.1"
    "fs-minipass" "^2.0.0"
    "glob" "^7.1.4"
    "graceful-fs" "^4.2.2"
    "infer-owner" "^1.0.4"
    "lru-cache" "^5.1.1"
    "minipass" "^3.0.0"
    "minipass-collect" "^1.0.2"
    "minipass-flush" "^1.0.5"
    "minipass-pipeline" "^1.2.2"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "p-map" "^3.0.0"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.7.1"
    "ssri" "^7.0.0"
    "unique-filename" "^1.1.1"

"cache-base@^1.0.1":
  "integrity" "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="
  "resolved" "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"cache-content-type@^1.0.0":
  "integrity" "sha1-A1zeKwjuISn0qDFeqPAKANuhRTw="
  "resolved" "https://registry.npmmirror.com/cache-content-type/download/cache-content-type-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "mime-types" "^2.1.18"
    "ylru" "^1.2.0"

"cache-loader@^4.1.0":
  "integrity" "sha1-mUjK41OuwKH8ser9ojAIFuyFOH4="
  "resolved" "https://registry.npmmirror.com/cache-loader/download/cache-loader-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer-json" "^2.0.0"
    "find-cache-dir" "^3.0.0"
    "loader-utils" "^1.2.3"
    "mkdirp" "^0.5.1"
    "neo-async" "^2.6.1"
    "schema-utils" "^2.0.0"

"call-me-maybe@^1.0.1":
  "integrity" "sha1-JtII6onje1y95gJQoV8DHBak1ms="
  "resolved" "https://registry.npmmirror.com/call-me-maybe/download/call-me-maybe-1.0.1.tgz"
  "version" "1.0.1"

"caller-callsite@^2.0.0":
  "integrity" "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ="
  "resolved" "https://registry.npmmirror.com/caller-callsite/download/caller-callsite-2.0.0.tgz?cache=0&sync_timestamp=1562668933683&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaller-callsite%2Fdownload%2Fcaller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^2.0.0":
  "integrity" "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ="
  "resolved" "https://registry.npmmirror.com/caller-path/download/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsite@1.0.0":
  "integrity" "sha1-KAOY5dZkvXQDi28JBRU+borxvCA="
  "resolved" "https://registry.npmmirror.com/callsite/download/callsite-1.0.0.tgz"
  "version" "1.0.0"

"callsites@^2.0.0":
  "integrity" "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="
  "resolved" "https://registry.npmmirror.com/callsites/download/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@^3.0.0":
  "integrity" "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="
  "resolved" "https://registry.npmmirror.com/callsites/download/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@3.0.x":
  "integrity" "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="
  "resolved" "https://registry.npmmirror.com/camel-case/download/camel-case-3.0.0.tgz?cache=0&sync_timestamp=1576748709736&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamel-case%2Fdownload%2Fcamel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "^2.2.0"
    "upper-case" "^1.1.1"

"camelcase@^5.0.0", "camelcase@^5.2.0", "camelcase@^5.3.1":
  "integrity" "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="
  "resolved" "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"caniuse-api@^3.0.0":
  "integrity" "sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA="
  "resolved" "https://registry.npmmirror.com/caniuse-api/download/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30001043", "caniuse-lite@^1.0.30001061":
  "integrity" "sha1-2BS2SDOFBLMVIirObxpTPZpV45A="
  "resolved" "https://registry.npmmirror.com/caniuse-lite/download/caniuse-lite-1.0.30001062.tgz?cache=0&sync_timestamp=1589869994538&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcaniuse-lite%2Fdownload%2Fcaniuse-lite-1.0.30001062.tgz"
  "version" "1.0.30001062"

"capture-exit@^2.0.0":
  "integrity" "sha1-+5U7+uvreB9iiYI52rtCbQilCaQ="
  "resolved" "https://registry.npmmirror.com/capture-exit/download/capture-exit-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "rsvp" "^4.8.4"

"case-sensitive-paths-webpack-plugin@^2.3.0":
  "integrity" "sha1-I6xhPMmoVuT4j/i7c7u16YmCXPc="
  "resolved" "https://registry.npmmirror.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.3.0.tgz"
  "version" "2.3.0"

"caseless@~0.12.0":
  "integrity" "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="
  "resolved" "https://registry.npmmirror.com/caseless/download/caseless-0.12.0.tgz"
  "version" "0.12.0"

"centra@^2.7.0":
  "integrity" "sha512-PbFMgMSrmgx6uxCdm57RUos9Tc3fclMvhLSATYN39XsDV29B89zZ3KA89jmY0vwSGazyU+uerqwa6t+KaodPcg=="
  "resolved" "https://registry.npmmirror.com/centra/-/centra-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "follow-redirects" "^1.15.6"

"chalk@^1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.0.0":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.0.1":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.3.0":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.4.1":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.4.2":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^3.0.0":
  "integrity" "sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"charenc@~0.0.1":
  "integrity" "sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc="
  "resolved" "https://registry.npmmirror.com/charenc/download/charenc-0.0.2.tgz"
  "version" "0.0.2"

"check-types@^8.0.3":
  "integrity" "sha1-M1bMoZyIlUTy16le1JzlCKDs9VI="
  "resolved" "https://registry.npmmirror.com/check-types/download/check-types-8.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcheck-types%2Fdownload%2Fcheck-types-8.0.3.tgz"
  "version" "8.0.3"

"chokidar@^2.1.8":
  "integrity" "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc="
  "resolved" "https://registry.npmmirror.com/chokidar/download/chokidar-2.1.8.tgz?cache=0&sync_timestamp=1587911196018&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.1"
    "braces" "^2.3.2"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.3"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "normalize-path" "^3.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.2.1"
    "upath" "^1.1.1"
  optionalDependencies:
    "fsevents" "^1.2.7"

"chokidar@^3.4.0":
  "integrity" "sha1-swYRQjzjdjV8dlubj5BLn7o8C+g="
  "resolved" "https://registry.npmmirror.com/chokidar/download/chokidar-3.4.0.tgz?cache=0&sync_timestamp=1587911196018&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "anymatch" "~3.1.1"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.0"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.4.0"
  optionalDependencies:
    "fsevents" "~2.1.2"

"chokidar@>=3.0.0 <4.0.0":
  "integrity" "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw=="
  "resolved" "https://registry.npmmirror.com/chokidar/-/chokidar-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chownr@^1.1.1", "chownr@^1.1.2":
  "integrity" "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs="
  "resolved" "https://registry.npmmirror.com/chownr/download/chownr-1.1.4.tgz"
  "version" "1.1.4"

"chrome-simple-launcher@0.1.3":
  "integrity" "sha1-pLK6PBZdtWTGQIfompu2SmO883A="
  "resolved" "https://registry.npmmirror.com/chrome-simple-launcher/download/chrome-simple-launcher-0.1.3.tgz?cache=0&sync_timestamp=1563437692271&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchrome-simple-launcher%2Fdownload%2Fchrome-simple-launcher-0.1.3.tgz"
  "version" "0.1.3"

"chrome-trace-event@^1.0.2":
  "integrity" "sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ="
  "resolved" "https://registry.npmmirror.com/chrome-trace-event/download/chrome-trace-event-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tslib" "^1.9.0"

"ci-info@^2.0.0":
  "integrity" "sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y="
  "resolved" "https://registry.npmmirror.com/ci-info/download/ci-info-2.0.0.tgz"
  "version" "2.0.0"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94="
  "resolved" "https://registry.npmmirror.com/cipher-base/download/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"class-utils@^0.3.5":
  "integrity" "sha1-+TNprouafOAv1B+q0MqDAzGQxGM="
  "resolved" "https://registry.npmmirror.com/class-utils/download/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-css@4.2.x":
  "integrity" "sha1-UHtd59l7SO5T2ErbAWD/YhY4D3g="
  "resolved" "https://registry.npmmirror.com/clean-css/download/clean-css-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "source-map" "~0.6.0"

"clean-stack@^2.0.0":
  "integrity" "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs="
  "resolved" "https://registry.npmmirror.com/clean-stack/download/clean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cli-cursor@^2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "https://registry.npmmirror.com/cli-cursor/download/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-highlight@^2.1.4":
  "integrity" "sha1-CYy2Qs8X9CrcHBFF4H+WDsTXUis="
  "resolved" "https://registry.npmmirror.com/cli-highlight/download/cli-highlight-2.1.4.tgz?cache=0&sync_timestamp=1573948719956&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-highlight%2Fdownload%2Fcli-highlight-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "chalk" "^3.0.0"
    "highlight.js" "^9.6.0"
    "mz" "^2.4.0"
    "parse5" "^5.1.1"
    "parse5-htmlparser2-tree-adapter" "^5.1.1"
    "yargs" "^15.0.0"

"cli-spinners@^2.0.0":
  "integrity" "sha1-BjIjmktapMlYYQFCw0u3plH8jfU="
  "resolved" "https://registry.npmmirror.com/cli-spinners/download/cli-spinners-2.3.0.tgz?cache=0&sync_timestamp=1586157510340&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-spinners%2Fdownload%2Fcli-spinners-2.3.0.tgz"
  "version" "2.3.0"

"clipboardy@^2.3.0":
  "integrity" "sha1-PCkDZQxo5GqRs4iYW8J3QofbopA="
  "resolved" "https://registry.npmmirror.com/clipboardy/download/clipboardy-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "arch" "^2.1.1"
    "execa" "^1.0.0"
    "is-wsl" "^2.1.1"

"cliui@^5.0.0":
  "integrity" "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U="
  "resolved" "https://registry.npmmirror.com/cliui/download/cliui-5.0.0.tgz?cache=0&sync_timestamp=1573942320052&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcliui%2Fdownload%2Fcliui-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "string-width" "^3.1.0"
    "strip-ansi" "^5.2.0"
    "wrap-ansi" "^5.1.0"

"cliui@^6.0.0":
  "integrity" "sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE="
  "resolved" "https://registry.npmmirror.com/cliui/download/cliui-6.0.0.tgz?cache=0&sync_timestamp=1573942320052&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcliui%2Fdownload%2Fcliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "https://registry.npmmirror.com/clone/download/clone-1.0.4.tgz"
  "version" "1.0.4"

"co-body@^5.1.1":
  "integrity" "sha1-WgpljEYCkTHg46MG9nZHMC9xwSQ="
  "resolved" "https://registry.npmmirror.com/co-body/download/co-body-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "inflation" "^2.0.0"
    "qs" "^6.4.0"
    "raw-body" "^2.2.0"
    "type-is" "^1.6.14"

"co-body@^6.0.0":
  "integrity" "sha1-lluTN9f1ZVSAeHRx9CN2ZIIIJ+M="
  "resolved" "https://registry.npmmirror.com/co-body/download/co-body-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "inflation" "^2.0.0"
    "qs" "^6.5.2"
    "raw-body" "^2.3.3"
    "type-is" "^1.6.16"

"co@^4.6.0":
  "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
  "resolved" "https://registry.npmmirror.com/co/download/co-4.6.0.tgz"
  "version" "4.6.0"

"coa@^2.0.2":
  "integrity" "sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM="
  "resolved" "https://registry.npmmirror.com/coa/download/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    "chalk" "^2.4.1"
    "q" "^1.1.2"

"collect-v8-coverage@^1.0.0":
  "integrity" "sha1-zCyOlPwYu9/+ZNZTRXDIpnOyf1k="
  "resolved" "https://registry.npmmirror.com/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz"
  "version" "1.0.1"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://registry.npmmirror.com/collection-visit/download/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0", "color-convert@^1.9.1":
  "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
  "resolved" "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM="
  "resolved" "https://registry.npmmirror.com/color-convert/download/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.0.0", "color-name@~1.1.4":
  "integrity" "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="
  "resolved" "https://registry.npmmirror.com/color-name/download/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-string@^1.5.2":
  "integrity" "sha1-ybvF8BtYtUkvPWhXRZy2WQziBMw="
  "resolved" "https://registry.npmmirror.com/color-string/download/color-string-1.5.3.tgz"
  "version" "1.5.3"
  dependencies:
    "color-name" "^1.0.0"
    "simple-swizzle" "^0.2.2"

"color-support@^1.1.2":
  "integrity" "sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg=="
  "resolved" "https://registry.npmmirror.com/color-support/-/color-support-1.1.3.tgz"
  "version" "1.1.3"

"color@^3.0.0":
  "integrity" "sha1-aBSOf4XUGtdknF+oyBBvCY0inhA="
  "resolved" "https://registry.npmmirror.com/color/download/color-3.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcolor%2Fdownload%2Fcolor-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "color-convert" "^1.9.1"
    "color-string" "^1.5.2"

"combined-stream@^1.0.6", "combined-stream@~1.0.6":
  "integrity" "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8="
  "resolved" "https://registry.npmmirror.com/combined-stream/download/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.18.0", "commander@^2.20.0", "commander@^2.3.0":
  "integrity" "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="
  "resolved" "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1587781596778&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^4.0.1":
  "integrity" "sha1-n9YCvZNilOnp70aj9NaWQESxgGg="
  "resolved" "https://registry.npmmirror.com/commander/download/commander-4.1.1.tgz?cache=0&sync_timestamp=1587781596778&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-4.1.1.tgz"
  "version" "4.1.1"

"commander@~2.19.0":
  "integrity" "sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So="
  "resolved" "https://registry.npmmirror.com/commander/download/commander-2.19.0.tgz?cache=0&sync_timestamp=1587781596778&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.19.0.tgz"
  "version" "2.19.0"

"commander@2.17.x":
  "integrity" "sha1-vXerfebelCBc6sxy8XFtKfIKd78="
  "resolved" "https://registry.npmmirror.com/commander/download/commander-2.17.1.tgz?cache=0&sync_timestamp=1587781596778&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.17.1.tgz"
  "version" "2.17.1"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://registry.npmmirror.com/commondir/download/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-bind@1.0.0":
  "integrity" "sha1-AMYIq33Nk4l8AAllGx06jh5zu9E="
  "resolved" "https://registry.npmmirror.com/component-bind/download/component-bind-1.0.0.tgz"
  "version" "1.0.0"

"component-emitter@^1.2.1":
  "integrity" "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="
  "resolved" "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"component-emitter@~1.3.0":
  "integrity" "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="
  "resolved" "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"component-emitter@1.2.1":
  "integrity" "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY="
  "resolved" "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.2.1.tgz"
  "version" "1.2.1"

"component-inherit@0.0.3":
  "integrity" "sha1-ZF/ErfWLcrZJ1crmUTVhnbJv8UM="
  "resolved" "https://registry.npmmirror.com/component-inherit/download/component-inherit-0.0.3.tgz"
  "version" "0.0.3"

"compressible@~2.0.16":
  "integrity" "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o="
  "resolved" "https://registry.npmmirror.com/compressible/download/compressible-2.0.18.tgz?cache=0&sync_timestamp=1578286264482&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcompressible%2Fdownload%2Fcompressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.4":
  "integrity" "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48="
  "resolved" "https://registry.npmmirror.com/compression/download/compression-1.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcompression%2Fdownload%2Fcompression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.5.0":
  "integrity" "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ="
  "resolved" "https://registry.npmmirror.com/concat-stream/download/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"connect-history-api-fallback@^1.6.0":
  "integrity" "sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w="
  "resolved" "https://registry.npmmirror.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"console-browserify@^1.1.0":
  "integrity" "sha1-ZwY871fOts9Jk6KrOlWECujEkzY="
  "resolved" "https://registry.npmmirror.com/console-browserify/download/console-browserify-1.2.0.tgz"
  "version" "1.2.0"

"console-control-strings@^1.0.0", "console-control-strings@^1.1.0":
  "integrity" "sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ=="
  "resolved" "https://registry.npmmirror.com/console-control-strings/-/console-control-strings-1.1.0.tgz"
  "version" "1.1.0"

"consolidate@^0.15.1":
  "integrity" "sha1-IasEMjXHGgfUXZqtmFk7DbpWurc="
  "resolved" "https://registry.npmmirror.com/consolidate/download/consolidate-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "bluebird" "^3.1.1"

"constants-browserify@^1.0.0":
  "integrity" "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="
  "resolved" "https://registry.npmmirror.com/constants-browserify/download/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"content-disposition@~0.5.2", "content-disposition@0.5.3":
  "integrity" "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70="
  "resolved" "https://registry.npmmirror.com/content-disposition/download/content-disposition-0.5.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcontent-disposition%2Fdownload%2Fcontent-disposition-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "safe-buffer" "5.1.2"

"content-type@^1.0.4", "content-type@~1.0.4":
  "integrity" "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="
  "resolved" "https://registry.npmmirror.com/content-type/download/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.4.0", "convert-source-map@^1.6.0", "convert-source-map@^1.7.0":
  "integrity" "sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI="
  "resolved" "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://registry.npmmirror.com/cookie-signature/download/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.3.1":
  "integrity" "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s="
  "resolved" "https://registry.npmmirror.com/cookie/download/cookie-0.3.1.tgz"
  "version" "0.3.1"

"cookie@0.4.0":
  "integrity" "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo="
  "resolved" "https://registry.npmmirror.com/cookie/download/cookie-0.4.0.tgz"
  "version" "0.4.0"

"cookies@~0.8.0":
  "integrity" "sha1-EpPOSzkXQKhAbjyYcOgoxLVPP5A="
  "resolved" "https://registry.npmmirror.com/cookies/download/cookies-0.8.0.tgz?cache=0&sync_timestamp=1570851324736&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcookies%2Fdownload%2Fcookies-0.8.0.tgz"
  "version" "0.8.0"
  dependencies:
    "depd" "~2.0.0"
    "keygrip" "~1.1.0"

"copy-concurrently@^1.0.0":
  "integrity" "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA="
  "resolved" "https://registry.npmmirror.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "^1.1.1"
    "fs-write-stream-atomic" "^1.0.8"
    "iferr" "^0.1.5"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://registry.npmmirror.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-to@^2.0.1":
  "integrity" "sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU="
  "resolved" "https://registry.npmmirror.com/copy-to/download/copy-to-2.0.1.tgz"
  "version" "2.0.1"

"copy-webpack-plugin@^5.1.1":
  "integrity" "sha1-VIGgPeoRI9iKmIxv+LeCRyFPC4g="
  "resolved" "https://registry.npmmirror.com/copy-webpack-plugin/download/copy-webpack-plugin-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cacache" "^12.0.3"
    "find-cache-dir" "^2.1.0"
    "glob-parent" "^3.1.0"
    "globby" "^7.1.1"
    "is-glob" "^4.0.1"
    "loader-utils" "^1.2.3"
    "minimatch" "^3.0.4"
    "normalize-path" "^3.0.0"
    "p-limit" "^2.2.1"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^2.1.2"
    "webpack-log" "^2.0.0"

"core-js-compat@^3.6.2", "core-js-compat@^3.6.4":
  "integrity" "sha1-KlHZpOJd/W5pAlGqgfmePAVIHxw="
  "resolved" "https://registry.npmmirror.com/core-js-compat/download/core-js-compat-3.6.5.tgz?cache=0&sync_timestamp=1586535809290&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js-compat%2Fdownload%2Fcore-js-compat-3.6.5.tgz"
  "version" "3.6.5"
  dependencies:
    "browserslist" "^4.8.5"
    "semver" "7.0.0"

"core-js@^3.4.1", "core-js@^3.6.4":
  "integrity" "sha1-c5XcJzrzf7LlDpvT2f6EEoUjHRo="
  "resolved" "https://registry.npmmirror.com/core-js/download/core-js-3.6.5.tgz?cache=0&sync_timestamp=1586450269267&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js%2Fdownload%2Fcore-js-3.6.5.tgz"
  "version" "3.6.5"

"core-util-is@~1.0.0", "core-util-is@1.0.2":
  "integrity" "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="
  "resolved" "https://registry.npmmirror.com/core-util-is/download/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cosmiconfig@^5.0.0":
  "integrity" "sha1-BA9yaAnFked6F8CjYmykW08Wixo="
  "resolved" "https://registry.npmmirror.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz?cache=0&sync_timestamp=1572710769619&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcosmiconfig%2Fdownload%2Fcosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"create-ecdh@^4.0.0":
  "integrity" "sha1-yREbbzMEXEaX8UR4f5JUzcd8Rf8="
  "resolved" "https://registry.npmmirror.com/create-ecdh/download/create-ecdh-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.0.0"

"create-hash@^1.1.0", "create-hash@^1.1.2", "create-hash@^1.2.0":
  "integrity" "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY="
  "resolved" "https://registry.npmmirror.com/create-hash/download/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.4", "create-hmac@^1.1.7":
  "integrity" "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8="
  "resolved" "https://registry.npmmirror.com/create-hmac/download/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"cross-env@^5.2.0":
  "integrity" "sha1-ssdsHKet1m3IdNEXmEZglPVRs00="
  "resolved" "https://registry.npmmirror.com/cross-env/download/cross-env-5.2.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-env%2Fdownload%2Fcross-env-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "cross-spawn" "^6.0.5"

"cross-env@^7.0.2":
  "integrity" "sha1-vV7TEzmpOjQYrE88qco0Awgq5fk="
  "resolved" "https://registry.npmmirror.com/cross-env/download/cross-env-7.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-env%2Fdownload%2Fcross-env-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "cross-spawn" "^7.0.1"

"cross-spawn@^6.0.0":
  "integrity" "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q="
  "resolved" "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.5":
  "integrity" "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q="
  "resolved" "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.0", "cross-spawn@^7.0.1":
  "integrity" "sha1-0Nfc+nTokRXHYZ9PchqU4f23FtY="
  "resolved" "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypt@~0.0.1":
  "integrity" "sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs="
  "resolved" "https://registry.npmmirror.com/crypt/download/crypt-0.0.2.tgz"
  "version" "0.0.2"

"crypto-browserify@^3.11.0":
  "integrity" "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw="
  "resolved" "https://registry.npmmirror.com/crypto-browserify/download/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"css-color-names@^0.0.4", "css-color-names@0.0.4":
  "integrity" "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="
  "resolved" "https://registry.npmmirror.com/css-color-names/download/css-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-declaration-sorter@^4.0.1":
  "integrity" "sha1-wZiUD2OnbX42wecQGLABchBUyyI="
  "resolved" "https://registry.npmmirror.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.1"
    "timsort" "^0.3.0"

"css-loader@*", "css-loader@^3.4.2":
  "integrity" "sha1-lawWRo4a3NlchEcp4LsWdjnrC88="
  "resolved" "https://registry.npmmirror.com/css-loader/download/css-loader-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "camelcase" "^5.3.1"
    "cssesc" "^3.0.0"
    "icss-utils" "^4.1.1"
    "loader-utils" "^1.2.3"
    "normalize-path" "^3.0.0"
    "postcss" "^7.0.27"
    "postcss-modules-extract-imports" "^2.0.0"
    "postcss-modules-local-by-default" "^3.0.2"
    "postcss-modules-scope" "^2.2.0"
    "postcss-modules-values" "^3.0.0"
    "postcss-value-parser" "^4.0.3"
    "schema-utils" "^2.6.6"
    "semver" "^6.3.0"

"css-loader@^2.1.1":
  "integrity" "sha1-2CVPcuQSuyI4u0TdZ0/770lzM+o="
  "resolved" "https://registry.npmmirror.com/css-loader/download/css-loader-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "camelcase" "^5.2.0"
    "icss-utils" "^4.1.0"
    "loader-utils" "^1.2.3"
    "normalize-path" "^3.0.0"
    "postcss" "^7.0.14"
    "postcss-modules-extract-imports" "^2.0.0"
    "postcss-modules-local-by-default" "^2.0.6"
    "postcss-modules-scope" "^2.1.0"
    "postcss-modules-values" "^2.0.0"
    "postcss-value-parser" "^3.3.0"
    "schema-utils" "^1.0.0"

"css-select-base-adapter@^0.1.1":
  "integrity" "sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc="
  "resolved" "https://registry.npmmirror.com/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@^1.1.0":
  "integrity" "sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg="
  "resolved" "https://registry.npmmirror.com/css-select/download/css-select-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "boolbase" "~1.0.0"
    "css-what" "2.1"
    "domutils" "1.5.1"
    "nth-check" "~1.0.1"

"css-select@^2.0.0":
  "integrity" "sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8="
  "resolved" "https://registry.npmmirror.com/css-select/download/css-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha1-mL69YsTB2flg7DQM+fdSLjBwmiI="
  "resolved" "https://registry.npmmirror.com/css-tree/download/css-tree-1.0.0-alpha.37.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-tree%2Fdownload%2Fcss-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "^0.6.1"

"css-tree@1.0.0-alpha.39":
  "integrity" "sha1-K/8//huz93bPfu/ZHuXLp3oUnus="
  "resolved" "https://registry.npmmirror.com/css-tree/download/css-tree-1.0.0-alpha.39.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-tree%2Fdownload%2Fcss-tree-1.0.0-alpha.39.tgz"
  "version" "1.0.0-alpha.39"
  dependencies:
    "mdn-data" "2.0.6"
    "source-map" "^0.6.1"

"css-what@^2.1.3", "css-what@2.1":
  "integrity" "sha1-ptdgRXM2X+dGhsPzEcVlE9iChfI="
  "resolved" "https://registry.npmmirror.com/css-what/download/css-what-2.1.3.tgz"
  "version" "2.1.3"

"css-what@^3.2.1":
  "integrity" "sha1-9KjxJCEGRiG0VnVeNKA6LCLfXaE="
  "resolved" "https://registry.npmmirror.com/css-what/download/css-what-3.2.1.tgz"
  "version" "3.2.1"

"css@^2.2.4", "css@~2.2.1":
  "integrity" "sha1-xkZ1XHOXHyu6amAeLPL9cbEpiSk="
  "resolved" "https://registry.npmmirror.com/css/download/css-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "inherits" "^2.0.3"
    "source-map" "^0.6.1"
    "source-map-resolve" "^0.5.2"
    "urix" "^0.1.0"

"cssesc@^2.0.0":
  "integrity" "sha1-OxO9G7HLNuG8taTc0n9UxdyzVwM="
  "resolved" "https://registry.npmmirror.com/cssesc/download/cssesc-2.0.0.tgz"
  "version" "2.0.0"

"cssesc@^3.0.0":
  "integrity" "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4="
  "resolved" "https://registry.npmmirror.com/cssesc/download/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssnano-preset-default@^4.0.0", "cssnano-preset-default@^4.0.7":
  "integrity" "sha1-UexmLM/KD4izltzZZ5zbkxvhf3Y="
  "resolved" "https://registry.npmmirror.com/cssnano-preset-default/download/cssnano-preset-default-4.0.7.tgz"
  "version" "4.0.7"
  dependencies:
    "css-declaration-sorter" "^4.0.1"
    "cssnano-util-raw-cache" "^4.0.1"
    "postcss" "^7.0.0"
    "postcss-calc" "^7.0.1"
    "postcss-colormin" "^4.0.3"
    "postcss-convert-values" "^4.0.1"
    "postcss-discard-comments" "^4.0.2"
    "postcss-discard-duplicates" "^4.0.2"
    "postcss-discard-empty" "^4.0.1"
    "postcss-discard-overridden" "^4.0.1"
    "postcss-merge-longhand" "^4.0.11"
    "postcss-merge-rules" "^4.0.3"
    "postcss-minify-font-values" "^4.0.2"
    "postcss-minify-gradients" "^4.0.2"
    "postcss-minify-params" "^4.0.2"
    "postcss-minify-selectors" "^4.0.2"
    "postcss-normalize-charset" "^4.0.1"
    "postcss-normalize-display-values" "^4.0.2"
    "postcss-normalize-positions" "^4.0.2"
    "postcss-normalize-repeat-style" "^4.0.2"
    "postcss-normalize-string" "^4.0.2"
    "postcss-normalize-timing-functions" "^4.0.2"
    "postcss-normalize-unicode" "^4.0.1"
    "postcss-normalize-url" "^4.0.1"
    "postcss-normalize-whitespace" "^4.0.2"
    "postcss-ordered-values" "^4.1.2"
    "postcss-reduce-initial" "^4.0.3"
    "postcss-reduce-transforms" "^4.0.2"
    "postcss-svgo" "^4.0.2"
    "postcss-unique-selectors" "^4.0.1"

"cssnano-util-get-arguments@^4.0.0":
  "integrity" "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8="
  "resolved" "https://registry.npmmirror.com/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-get-match@^4.0.0":
  "integrity" "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0="
  "resolved" "https://registry.npmmirror.com/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-raw-cache@^4.0.1":
  "integrity" "sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI="
  "resolved" "https://registry.npmmirror.com/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"cssnano-util-same-parent@^4.0.0":
  "integrity" "sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M="
  "resolved" "https://registry.npmmirror.com/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz"
  "version" "4.0.1"

"cssnano@^4.0.0", "cssnano@^4.1.10":
  "integrity" "sha1-CsQfCxPRPUZUh+ERt3jULaYxuLI="
  "resolved" "https://registry.npmmirror.com/cssnano/download/cssnano-4.1.10.tgz"
  "version" "4.1.10"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "cssnano-preset-default" "^4.0.7"
    "is-resolvable" "^1.0.0"
    "postcss" "^7.0.0"

"csso@^4.0.2":
  "integrity" "sha1-DZmF3IUsfMKyys+74QeQFNGo6QM="
  "resolved" "https://registry.npmmirror.com/csso/download/csso-4.0.3.tgz?cache=0&sync_timestamp=1585052130344&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcsso%2Fdownload%2Fcsso-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "css-tree" "1.0.0-alpha.39"

"cssom@^0.4.1":
  "integrity" "sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA="
  "resolved" "https://registry.npmmirror.com/cssom/download/cssom-0.4.4.tgz?cache=0&sync_timestamp=1573719337707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssom%2Fdownload%2Fcssom-0.4.4.tgz"
  "version" "0.4.4"

"cssom@~0.3.6":
  "integrity" "sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o="
  "resolved" "https://registry.npmmirror.com/cssom/download/cssom-0.3.8.tgz?cache=0&sync_timestamp=1573719337707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssom%2Fdownload%2Fcssom-0.3.8.tgz"
  "version" "0.3.8"

"cssstyle@^2.0.0":
  "integrity" "sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI="
  "resolved" "https://registry.npmmirror.com/cssstyle/download/cssstyle-2.3.0.tgz?cache=0&sync_timestamp=1588171504463&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssstyle%2Fdownload%2Fcssstyle-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "cssom" "~0.3.6"

"cyclist@^1.0.1":
  "integrity" "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk="
  "resolved" "https://registry.npmmirror.com/cyclist/download/cyclist-1.0.1.tgz"
  "version" "1.0.1"

"dashdash@^1.12.0":
  "integrity" "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA="
  "resolved" "https://registry.npmmirror.com/dashdash/download/dashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"data-urls@^1.1.0":
  "integrity" "sha1-Fe4Fgrql4iu1nHcUDaj5x2lju/4="
  "resolved" "https://registry.npmmirror.com/data-urls/download/data-urls-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "abab" "^2.0.0"
    "whatwg-mimetype" "^2.2.0"
    "whatwg-url" "^7.0.0"

"de-indent@^1.0.2":
  "integrity" "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0="
  "resolved" "https://registry.npmmirror.com/de-indent/download/de-indent-1.0.2.tgz"
  "version" "1.0.2"

"debug@^2.2.0":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.0":
  "integrity" "sha1-6D0X3hbYp++3cX7b5fsQE17uYps="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.1.1":
  "integrity" "sha1-6D0X3hbYp++3cX7b5fsQE17uYps="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.5":
  "integrity" "sha1-6D0X3hbYp++3cX7b5fsQE17uYps="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.0.1", "debug@^4.1.0", "debug@^4.1.1", "debug@~4.1.0", "debug@4":
  "integrity" "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "ms" "^2.1.1"

"debug@~2.6.3":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@~3.1.0":
  "integrity" "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "ms" "2.0.0"

"debug@2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1580010393599&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://registry.npmmirror.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-equal@^1.0.1":
  "integrity" "sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o="
  "resolved" "https://registry.npmmirror.com/deep-equal/download/deep-equal-1.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-equal@~1.0.1":
  "integrity" "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU="
  "resolved" "https://registry.npmmirror.com/deep-equal/download/deep-equal-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.0.1.tgz"
  "version" "1.0.1"

"deep-extend@^0.6.0":
  "integrity" "sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw="
  "resolved" "https://registry.npmmirror.com/deep-extend/download/deep-extend-0.6.0.tgz"
  "version" "0.6.0"

"deep-is@~0.1.3":
  "integrity" "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="
  "resolved" "https://registry.npmmirror.com/deep-is/download/deep-is-0.1.3.tgz"
  "version" "0.1.3"

"deepmerge@^1.5.2":
  "integrity" "sha1-EEmdhohEza1P7ghC34x/bwyVp1M="
  "resolved" "https://registry.npmmirror.com/deepmerge/download/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"deepmerge@^4.2.2":
  "integrity" "sha1-RNLqNnm49NT/ujPwPYZfwee/SVU="
  "resolved" "https://registry.npmmirror.com/deepmerge/download/deepmerge-4.2.2.tgz"
  "version" "4.2.2"

"default-gateway@^4.2.0":
  "integrity" "sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs="
  "resolved" "https://registry.npmmirror.com/default-gateway/download/default-gateway-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "execa" "^1.0.0"
    "ip-regex" "^2.1.0"

"default-gateway@^5.0.5":
  "integrity" "sha1-T9a9XShV05s0zFpZUFSG6ar8mxA="
  "resolved" "https://registry.npmmirror.com/default-gateway/download/default-gateway-5.0.5.tgz"
  "version" "5.0.5"
  dependencies:
    "execa" "^3.3.0"

"default-gateway@^6.0.0":
  "integrity" "sha1-0q6IKP+OSVSctS+JsRTpqU7QBH0="
  "resolved" "https://registry.npmmirror.com/default-gateway/download/default-gateway-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "execa" "^4.0.0"

"defaults@^1.0.3":
  "integrity" "sha1-xlYFHpgX2f8I7YgUd/P+QBnz730="
  "resolved" "https://registry.npmmirror.com/defaults/download/defaults-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "clone" "^1.0.2"

"define-properties@^1.1.2", "define-properties@^1.1.3":
  "integrity" "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE="
  "resolved" "https://registry.npmmirror.com/define-properties/download/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://registry.npmmirror.com/define-property/download/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://registry.npmmirror.com/define-property/download/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha1-1Flono1lS6d+AqgX+HENcCyxbp0="
  "resolved" "https://registry.npmmirror.com/define-property/download/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"del@^4.1.1":
  "integrity" "sha1-no8RciLqRKMf86FWwEm5kFKp8LQ="
  "resolved" "https://registry.npmmirror.com/del/download/del-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "@types/glob" "^7.1.1"
    "globby" "^6.1.0"
    "is-path-cwd" "^2.0.0"
    "is-path-in-cwd" "^2.0.0"
    "p-map" "^2.0.0"
    "pify" "^4.0.1"
    "rimraf" "^2.6.3"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://registry.npmmirror.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"delegates@^1.0.0":
  "integrity" "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o="
  "resolved" "https://registry.npmmirror.com/delegates/download/delegates-1.0.0.tgz"
  "version" "1.0.0"

"depd@^1.1.2", "depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz"
  "version" "1.1.2"

"depd@~2.0.0":
  "integrity" "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="
  "resolved" "https://registry.npmmirror.com/depd/download/depd-2.0.0.tgz"
  "version" "2.0.0"

"des.js@^1.0.0":
  "integrity" "sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM="
  "resolved" "https://registry.npmmirror.com/des.js/download/des.js-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"destroy@^1.0.4", "destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://registry.npmmirror.com/destroy/download/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-newline@^3.0.0":
  "integrity" "sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE="
  "resolved" "https://registry.npmmirror.com/detect-newline/download/detect-newline-3.1.0.tgz"
  "version" "3.1.0"

"detect-node@^2.0.4":
  "integrity" "sha1-AU7o+PZpxcWAI9pkuBecCDooxGw="
  "resolved" "https://registry.npmmirror.com/detect-node/download/detect-node-2.0.4.tgz"
  "version" "2.0.4"

"diff-sequences@^25.2.6":
  "integrity" "sha1-X0Z8AO3TU1K3vKRteSfWDmh6dt0="
  "resolved" "https://registry.npmmirror.com/diff-sequences/download/diff-sequences-25.2.6.tgz"
  "version" "25.2.6"

"diffie-hellman@^5.0.0":
  "integrity" "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU="
  "resolved" "https://registry.npmmirror.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dir-glob@^2.0.0", "dir-glob@^2.2.2":
  "integrity" "sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ="
  "resolved" "https://registry.npmmirror.com/dir-glob/download/dir-glob-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "path-type" "^3.0.0"

"dns-equal@^1.0.0":
  "integrity" "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="
  "resolved" "https://registry.npmmirror.com/dns-equal/download/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^1.3.1":
  "integrity" "sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo="
  "resolved" "https://registry.npmmirror.com/dns-packet/download/dns-packet-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "ip" "^1.1.0"
    "safe-buffer" "^5.0.1"

"dns-txt@^2.0.2":
  "integrity" "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY="
  "resolved" "https://registry.npmmirror.com/dns-txt/download/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "^1.0.0"

"dom-converter@^0.2":
  "integrity" "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g="
  "resolved" "https://registry.npmmirror.com/dom-converter/download/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-serializer@0":
  "integrity" "sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E="
  "resolved" "https://registry.npmmirror.com/dom-serializer/download/dom-serializer-0.2.2.tgz?cache=0&sync_timestamp=1589067578490&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdom-serializer%2Fdownload%2Fdom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"dom-walk@^0.1.0":
  "integrity" "sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w=="
  "resolved" "https://registry.npmmirror.com/dom-walk/-/dom-walk-0.1.2.tgz"
  "version" "0.1.2"

"domain-browser@^1.1.1":
  "integrity" "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto="
  "resolved" "https://registry.npmmirror.com/domain-browser/download/domain-browser-1.2.0.tgz?cache=0&sync_timestamp=1590035413031&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomain-browser%2Fdownload%2Fdomain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@^1.3.0", "domelementtype@^1.3.1", "domelementtype@1":
  "integrity" "sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8="
  "resolved" "https://registry.npmmirror.com/domelementtype/download/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domelementtype@^2.0.1":
  "integrity" "sha1-H4vf6R9aeAYydOgDtL3O326U+U0="
  "resolved" "https://registry.npmmirror.com/domelementtype/download/domelementtype-2.0.1.tgz"
  "version" "2.0.1"

"domexception@^1.0.1":
  "integrity" "sha1-k3RCZEymoxJh7zbj7Gd/6AVYLJA="
  "resolved" "https://registry.npmmirror.com/domexception/download/domexception-1.0.1.tgz?cache=0&sync_timestamp=1576355459111&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomexception%2Fdownload%2Fdomexception-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "webidl-conversions" "^4.0.2"

"domhandler@^2.3.0":
  "integrity" "sha1-iAUJfpM9ZehVRvcm1g9euItE+AM="
  "resolved" "https://registry.npmmirror.com/domhandler/download/domhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1"

"domutils@^1.5.1", "domutils@^1.7.0":
  "integrity" "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo="
  "resolved" "https://registry.npmmirror.com/domutils/download/domutils-1.7.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomutils%2Fdownload%2Fdomutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@1.5.1":
  "integrity" "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8="
  "resolved" "https://registry.npmmirror.com/domutils/download/domutils-1.5.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomutils%2Fdownload%2Fdomutils-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"dot-prop@^5.2.0":
  "integrity" "sha1-w07MKVVtxF8fTCJpe29JBODMT8s="
  "resolved" "https://registry.npmmirror.com/dot-prop/download/dot-prop-5.2.0.tgz?cache=0&sync_timestamp=1572621117377&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdot-prop%2Fdownload%2Fdot-prop-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "is-obj" "^2.0.0"

"dotenv-expand@^5.1.0":
  "integrity" "sha1-P7rwIL/XlIhAcuomsel5HUWmKfA="
  "resolved" "https://registry.npmmirror.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz"
  "version" "5.1.0"

"dotenv@^8.2.0":
  "integrity" "sha1-l+YZJZradQ7qPk6j4mvO6lQksWo="
  "resolved" "https://registry.npmmirror.com/dotenv/download/dotenv-8.2.0.tgz"
  "version" "8.2.0"

"duplexer@^0.1.1":
  "integrity" "sha1-rOb/gIwc5mtX0ev5eXessCM0z8E="
  "resolved" "https://registry.npmmirror.com/duplexer/download/duplexer-0.1.1.tgz"
  "version" "0.1.1"

"duplexify@^3.4.2", "duplexify@^3.6.0":
  "integrity" "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk="
  "resolved" "https://registry.npmmirror.com/duplexify/download/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"easy-stack@^1.0.0":
  "integrity" "sha1-EskbMIWjfwuqM26UhurEv5Tj54g="
  "resolved" "https://registry.npmmirror.com/easy-stack/download/easy-stack-1.0.0.tgz"
  "version" "1.0.0"

"ecc-jsbn@~0.1.1":
  "integrity" "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk="
  "resolved" "https://registry.npmmirror.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://registry.npmmirror.com/ee-first/download/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^2.6.1":
  "integrity" "sha1-SGYSh1c9zFPjZsehrlLDoSDuybo="
  "resolved" "https://registry.npmmirror.com/ejs/download/ejs-2.7.4.tgz"
  "version" "2.7.4"

"electron-to-chromium@^1.3.413":
  "integrity" "sha1-aCgx7PPOUFIxl498eVooE3QMrnw="
  "resolved" "https://registry.npmmirror.com/electron-to-chromium/download/electron-to-chromium-1.3.448.tgz"
  "version" "1.3.448"

"elliptic@^6.0.0", "elliptic@^6.5.2":
  "integrity" "sha1-BcVnjXFzwEnYykM1UiJKSV0ON2I="
  "resolved" "https://registry.npmmirror.com/elliptic/download/elliptic-6.5.2.tgz"
  "version" "6.5.2"
  dependencies:
    "bn.js" "^4.4.0"
    "brorand" "^1.0.1"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.0"

"emoji-regex@^7.0.1":
  "integrity" "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY="
  "resolved" "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-7.0.3.tgz"
  "version" "7.0.3"

"emoji-regex@^8.0.0":
  "integrity" "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="
  "resolved" "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^2.0.0":
  "integrity" "sha1-TapNnbAPmBmIDHn6RXrlsJof04k="
  "resolved" "https://registry.npmmirror.com/emojis-list/download/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"emojis-list@^3.0.0":
  "integrity" "sha1-VXBmIEatKeLpFucariYKvf9Pang="
  "resolved" "https://registry.npmmirror.com/emojis-list/download/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@^1.0.2", "encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://registry.npmmirror.com/encodeurl/download/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0", "end-of-stream@^1.4.1":
  "integrity" "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA="
  "resolved" "https://registry.npmmirror.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"engine.io-client@~3.4.0":
  "integrity" "sha1-T7LvKx/h06ocYhxqjYfx/FVCa1A="
  "resolved" "https://registry.npmmirror.com/engine.io-client/download/engine.io-client-3.4.2.tgz?cache=0&sync_timestamp=1589350950920&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fengine.io-client%2Fdownload%2Fengine.io-client-3.4.2.tgz"
  "version" "3.4.2"
  dependencies:
    "component-emitter" "~1.3.0"
    "component-inherit" "0.0.3"
    "debug" "~4.1.0"
    "engine.io-parser" "~2.2.0"
    "has-cors" "1.1.0"
    "indexof" "0.0.1"
    "parseqs" "0.0.5"
    "parseuri" "0.0.5"
    "ws" "~6.1.0"
    "xmlhttprequest-ssl" "~1.5.4"
    "yeast" "0.1.2"

"engine.io-parser@~2.2.0":
  "integrity" "sha1-MSxIlPV9UqArQgho2ntcHISvgO0="
  "resolved" "https://registry.npmmirror.com/engine.io-parser/download/engine.io-parser-2.2.0.tgz?cache=0&sync_timestamp=1589895483603&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fengine.io-parser%2Fdownload%2Fengine.io-parser-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "after" "0.8.2"
    "arraybuffer.slice" "~0.0.7"
    "base64-arraybuffer" "0.1.5"
    "blob" "0.0.5"
    "has-binary2" "~1.0.2"

"engine.io@~3.4.0":
  "integrity" "sha1-phy8E/oMsn2UU/0HminumAVksGk="
  "resolved" "https://registry.npmmirror.com/engine.io/download/engine.io-3.4.1.tgz?cache=0&sync_timestamp=1587113655084&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fengine.io%2Fdownload%2Fengine.io-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "accepts" "~1.3.4"
    "base64id" "2.0.0"
    "cookie" "0.3.1"
    "debug" "~4.1.0"
    "engine.io-parser" "~2.2.0"
    "ws" "^7.1.2"

"enhanced-resolve@^4.1.0":
  "integrity" "sha1-KTfiuAZs0P584JkKmPDXGjUYn2Y="
  "resolved" "https://registry.npmmirror.com/enhanced-resolve/download/enhanced-resolve-4.1.1.tgz?cache=0&sync_timestamp=1572991764265&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fenhanced-resolve%2Fdownload%2Fenhanced-resolve-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.5.0"
    "tapable" "^1.0.0"

"entities@^1.1.1":
  "integrity" "sha1-vfpzUplmTfr9NFKe1PhSKidf6lY="
  "resolved" "https://registry.npmmirror.com/entities/download/entities-1.1.2.tgz"
  "version" "1.1.2"

"entities@^2.0.0":
  "integrity" "sha1-rHTbC7qNM4CLvzaAnDpcNoNTFDY="
  "resolved" "https://registry.npmmirror.com/entities/download/entities-2.0.2.tgz"
  "version" "2.0.2"

"envinfo@^6.0.1":
  "integrity" "sha1-3sUfLdOPtKH7W/VoSIwGrR5+CKc="
  "resolved" "https://registry.npmmirror.com/envinfo/download/envinfo-6.0.1.tgz"
  "version" "6.0.1"

"errno@^0.1.3", "errno@~0.1.7":
  "integrity" "sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg="
  "resolved" "https://registry.npmmirror.com/errno/download/errno-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.3.1":
  "integrity" "sha1-tKxAZIEH/c3PriQvQovqihTU8b8="
  "resolved" "https://registry.npmmirror.com/error-ex/download/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.0":
  "integrity" "sha1-WpmnB716TFinl5AtSNgoA+3mqtg="
  "resolved" "https://registry.npmmirror.com/error-stack-parser/download/error-stack-parser-2.0.6.tgz?cache=0&sync_timestamp=1578288503034&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ferror-stack-parser%2Fdownload%2Ferror-stack-parser-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "stackframe" "^1.1.1"

"es-abstract@^1.17.0-next.1", "es-abstract@^1.17.2", "es-abstract@^1.17.5":
  "integrity" "sha1-2MnR1myJgfuSAOIlHXme7pJ3Suk="
  "resolved" "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.17.5.tgz"
  "version" "1.17.5"
  dependencies:
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.1"
    "is-callable" "^1.1.5"
    "is-regex" "^1.0.5"
    "object-inspect" "^1.7.0"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.0"
    "string.prototype.trimleft" "^2.1.1"
    "string.prototype.trimright" "^2.1.1"

"es-to-primitive@^1.2.1":
  "integrity" "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo="
  "resolved" "https://registry.npmmirror.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"escape-html@^1.0.3", "escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://registry.npmmirror.com/escape-html/download/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz?cache=0&sync_timestamp=1587627212242&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fescape-string-regexp%2Fdownload%2Fescape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escodegen@^1.11.1", "escodegen@^1.8.1":
  "integrity" "sha1-ugHQyCeLXpWppFNQFCAmZZAnpFc="
  "resolved" "https://registry.npmmirror.com/escodegen/download/escodegen-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "esprima" "^4.0.1"
    "estraverse" "^4.2.0"
    "esutils" "^2.0.2"
    "optionator" "^0.8.1"
  optionalDependencies:
    "source-map" "~0.6.1"

"eslint-scope@^4.0.3":
  "integrity" "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg="
  "resolved" "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"esprima@^4.0.0", "esprima@^4.0.1":
  "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
  "resolved" "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fesprima%2Fdownload%2Fesprima-4.0.1.tgz"
  "version" "4.0.1"

"esrecurse@^4.1.0":
  "integrity" "sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8="
  "resolved" "https://registry.npmmirror.com/esrecurse/download/esrecurse-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "estraverse" "^4.1.0"

"estraverse@^4.1.0", "estraverse@^4.1.1", "estraverse@^4.2.0":
  "integrity" "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0="
  "resolved" "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1586996117385&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz"
  "version" "4.3.0"

"esutils@^2.0.2":
  "integrity" "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="
  "resolved" "https://registry.npmmirror.com/esutils/download/esutils-2.0.3.tgz?cache=0&sync_timestamp=1564535492241&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fesutils%2Fdownload%2Fesutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://registry.npmmirror.com/etag/download/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-pubsub@4.3.0":
  "integrity" "sha1-9o2Ba8KfHsAsU53FjI3UDOcss24="
  "resolved" "https://registry.npmmirror.com/event-pubsub/download/event-pubsub-4.3.0.tgz"
  "version" "4.3.0"

"eventemitter3@^4.0.0":
  "integrity" "sha1-tUY6zmNaCD0Bi9x8kXtMXxCoU4Q="
  "resolved" "https://registry.npmmirror.com/eventemitter3/download/eventemitter3-4.0.4.tgz?cache=0&sync_timestamp=1589283150629&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feventemitter3%2Fdownload%2Feventemitter3-4.0.4.tgz"
  "version" "4.0.4"

"events@^3.0.0":
  "integrity" "sha1-hCea8bNMt1qoi/X/KR9tC9mzGlk="
  "resolved" "https://registry.npmmirror.com/events/download/events-3.1.0.tgz?cache=0&sync_timestamp=1578498298945&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fevents%2Fdownload%2Fevents-3.1.0.tgz"
  "version" "3.1.0"

"eventsource@^1.0.7":
  "integrity" "sha1-j7xyyT/NNAiAkLwKTmT0tc7m2NA="
  "resolved" "https://registry.npmmirror.com/eventsource/download/eventsource-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "original" "^1.0.0"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI="
  "resolved" "https://registry.npmmirror.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"exec-sh@^0.3.2":
  "integrity" "sha1-OgGM61JsxvbfK7UEsr/o46STTsU="
  "resolved" "https://registry.npmmirror.com/exec-sh/download/exec-sh-0.3.4.tgz"
  "version" "0.3.4"

"execa@^1.0.0":
  "integrity" "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg="
  "resolved" "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz?cache=0&sync_timestamp=1588947631735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^3.2.0":
  "integrity" "sha1-wI7UVQ72XYWPrCaf/IVyRG8364k="
  "resolved" "https://registry.npmmirror.com/execa/download/execa-3.4.0.tgz?cache=0&sync_timestamp=1588947631735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "p-finally" "^2.0.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"execa@^3.3.0":
  "integrity" "sha1-wI7UVQ72XYWPrCaf/IVyRG8364k="
  "resolved" "https://registry.npmmirror.com/execa/download/execa-3.4.0.tgz?cache=0&sync_timestamp=1588947631735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "p-finally" "^2.0.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"execa@^4.0.0":
  "integrity" "sha1-mISIeB8fAjjNFW96rt4Rw+hTtME="
  "resolved" "https://registry.npmmirror.com/execa/download/execa-4.0.1.tgz?cache=0&sync_timestamp=1588947631735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"exif-parser@^0.1.12":
  "integrity" "sha512-c2bQfLNbMzLPmzQuOr8fy0csy84WmwnER81W88DzTp9CYNPJ6yzOj2EZAh9pywYpqHnshVLHQJ8WzldAyfY+Iw=="
  "resolved" "https://registry.npmmirror.com/exif-parser/-/exif-parser-0.1.12.tgz"
  "version" "0.1.12"

"exit@^0.1.2":
  "integrity" "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw="
  "resolved" "https://registry.npmmirror.com/exit/download/exit-0.1.2.tgz"
  "version" "0.1.2"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://registry.npmmirror.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"expect@^25.5.0":
  "integrity" "sha1-8H+EhxKigTu1kWfaP7goyiH1i7o="
  "resolved" "https://registry.npmmirror.com/expect/download/expect-25.5.0.tgz?cache=0&sync_timestamp=1588675340802&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexpect%2Fdownload%2Fexpect-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "ansi-styles" "^4.0.0"
    "jest-get-type" "^25.2.6"
    "jest-matcher-utils" "^25.5.0"
    "jest-message-util" "^25.5.0"
    "jest-regex-util" "^25.2.6"

"express@^4.16.3", "express@^4.17.1":
  "integrity" "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ="
  "resolved" "https://registry.npmmirror.com/express/download/express-4.17.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexpress%2Fdownload%2Fexpress-4.17.1.tgz"
  "version" "4.17.1"
  dependencies:
    "accepts" "~1.3.7"
    "array-flatten" "1.1.1"
    "body-parser" "1.19.0"
    "content-disposition" "0.5.3"
    "content-type" "~1.0.4"
    "cookie" "0.4.0"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "~1.1.2"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.5"
    "qs" "6.7.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.1.2"
    "send" "0.17.1"
    "serve-static" "1.14.1"
    "setprototypeof" "1.1.1"
    "statuses" "~1.5.0"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@~3.0.2":
  "integrity" "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="
  "resolved" "https://registry.npmmirror.com/extend/download/extend-3.0.2.tgz"
  "version" "3.0.2"

"extglob@^2.0.4":
  "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
  "resolved" "https://registry.npmmirror.com/extglob/download/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extract-zip@^2.0.0":
  "integrity" "sha512-GDhU9ntwuKyGXdZBUgTIe+vXnWj0fppUEtMDL0+idd5Sta8TGpHssn/eusA9mrPr9qNDym6SxAYZjNvCn/9RBg=="
  "resolved" "https://registry.npmmirror.com/extract-zip/-/extract-zip-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "debug" "^4.1.1"
    "get-stream" "^5.1.0"
    "yauzl" "^2.10.0"
  optionalDependencies:
    "@types/yauzl" "^2.9.1"

"extsprintf@^1.2.0":
  "integrity" "sha1-4mifjzVvrWLMplo6kcXfX5VRaS8="
  "resolved" "https://registry.npmmirror.com/extsprintf/download/extsprintf-1.4.0.tgz"
  "version" "1.4.0"

"extsprintf@1.3.0":
  "integrity" "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="
  "resolved" "https://registry.npmmirror.com/extsprintf/download/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@^3.1.1":
  "integrity" "sha1-VFFFB3xQFJHjOxXsQIwpQ3bpSuQ="
  "resolved" "https://registry.npmmirror.com/fast-deep-equal/download/fast-deep-equal-3.1.1.tgz"
  "version" "3.1.1"

"fast-glob@^2.2.6":
  "integrity" "sha1-aVOFfDr6R1//ku5gFdUtpwpM050="
  "resolved" "https://registry.npmmirror.com/fast-glob/download/fast-glob-2.2.7.tgz?cache=0&sync_timestamp=1582318661510&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-glob%2Fdownload%2Ffast-glob-2.2.7.tgz"
  "version" "2.2.7"
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    "glob-parent" "^3.1.0"
    "is-glob" "^4.0.0"
    "merge2" "^1.2.3"
    "micromatch" "^3.1.10"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="
  "resolved" "https://registry.npmmirror.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz?cache=0&sync_timestamp=1576367703577&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-json-stable-stringify%2Fdownload%2Ffast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@~2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://registry.npmmirror.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"faye-websocket@^0.10.0":
  "integrity" "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ="
  "resolved" "https://registry.npmmirror.com/faye-websocket/download/faye-websocket-0.10.0.tgz"
  "version" "0.10.0"
  dependencies:
    "websocket-driver" ">=0.5.1"

"faye-websocket@~0.11.1":
  "integrity" "sha1-XA6aiWjokSwoZjn96XeosgnyUI4="
  "resolved" "https://registry.npmmirror.com/faye-websocket/download/faye-websocket-0.11.3.tgz"
  "version" "0.11.3"
  dependencies:
    "websocket-driver" ">=0.5.1"

"fb-watchman@^2.0.0":
  "integrity" "sha1-/IT7OdJwnPP/bXQ3BhV7tXCKioU="
  "resolved" "https://registry.npmmirror.com/fb-watchman/download/fb-watchman-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "bser" "2.1.1"

"fd-slicer@~1.1.0":
  "integrity" "sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g=="
  "resolved" "https://registry.npmmirror.com/fd-slicer/-/fd-slicer-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "pend" "~1.2.0"

"figgy-pudding@^3.5.1":
  "integrity" "sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4="
  "resolved" "https://registry.npmmirror.com/figgy-pudding/download/figgy-pudding-3.5.2.tgz"
  "version" "3.5.2"

"file-loader@*", "file-loader@^4.2.0":
  "integrity" "sha1-eA8ED3KbPRgBnyBgX3I+hEuKWK8="
  "resolved" "https://registry.npmmirror.com/file-loader/download/file-loader-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "loader-utils" "^1.2.3"
    "schema-utils" "^2.5.0"

"file-type@^9.0.0":
  "integrity" "sha512-Qe/5NJrgIOlwijpq3B7BEpzPFcgzggOTagZmkXQY4LA6bsXKTUstK7Wp12lEJ/mLKTpvIZxmIuRcLYWT6ov9lw=="
  "resolved" "https://registry.npmmirror.com/file-type/-/file-type-9.0.0.tgz"
  "version" "9.0.0"

"file-uri-to-path@1.0.0":
  "integrity" "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90="
  "resolved" "https://registry.npmmirror.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"
  "version" "1.0.0"

"filesize@^3.6.1":
  "integrity" "sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc="
  "resolved" "https://registry.npmmirror.com/filesize/download/filesize-3.6.1.tgz"
  "version" "3.6.1"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://registry.npmmirror.com/fill-range/download/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha1-GRmmp8df44ssfHflGYU12prN2kA="
  "resolved" "https://registry.npmmirror.com/fill-range/download/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@~1.1.2":
  "integrity" "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0="
  "resolved" "https://registry.npmmirror.com/finalhandler/download/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"find-cache-dir@^2.0.0", "find-cache-dir@^2.1.0":
  "integrity" "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc="
  "resolved" "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz?cache=0&sync_timestamp=1583734591888&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-cache-dir%2Fdownload%2Ffind-cache-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^2.0.0"
    "pkg-dir" "^3.0.0"

"find-cache-dir@^3.0.0":
  "integrity" "sha1-ibM/rUpGcNqpT4Vff74x1thP6IA="
  "resolved" "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-3.3.1.tgz?cache=0&sync_timestamp=1583734591888&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-cache-dir%2Fdownload%2Ffind-cache-dir-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-cache-dir@^3.3.1":
  "integrity" "sha1-ibM/rUpGcNqpT4Vff74x1thP6IA="
  "resolved" "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-3.3.1.tgz?cache=0&sync_timestamp=1583734591888&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-cache-dir%2Fdownload%2Ffind-cache-dir-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-up@^2.1.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npmmirror.com/find-up/download/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"find-up@^3.0.0":
  "integrity" "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M="
  "resolved" "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"find-up@^4.0.0", "find-up@^4.1.0":
  "integrity" "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk="
  "resolved" "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flush-write-stream@^1.0.0":
  "integrity" "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug="
  "resolved" "https://registry.npmmirror.com/flush-write-stream/download/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "readable-stream" "^2.3.6"

"flyio@^0.6.2":
  "integrity" "sha1-xdg+t6m0/ByRWkY9LqbfznVcLW8="
  "resolved" "https://registry.npmmirror.com/flyio/download/flyio-0.6.14.tgz"
  "version" "0.6.14"
  dependencies:
    "request" "^2.85.0"

"follow-redirects@^1.0.0", "follow-redirects@^1.15.6":
  "integrity" "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ=="
  "resolved" "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.9.tgz"
  "version" "1.15.9"

"for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://registry.npmmirror.com/for-in/download/for-in-1.0.2.tgz"
  "version" "1.0.2"

"forever-agent@~0.6.1":
  "integrity" "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="
  "resolved" "https://registry.npmmirror.com/forever-agent/download/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"form-data@~2.3.2":
  "integrity" "sha1-3M5SwF9kTymManq5Nr1yTO/786Y="
  "resolved" "https://registry.npmmirror.com/form-data/download/form-data-2.3.3.tgz?cache=0&sync_timestamp=1573027118125&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fform-data%2Fdownload%2Fform-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"formidable@^1.1.1":
  "integrity" "sha1-v2muopcpgmdfAIZTQrmCmG9rjdk="
  "resolved" "https://registry.npmmirror.com/formidable/download/formidable-1.2.2.tgz"
  "version" "1.2.2"

"forwarded@~0.1.2":
  "integrity" "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="
  "resolved" "https://registry.npmmirror.com/forwarded/download/forwarded-0.1.2.tgz"
  "version" "0.1.2"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://registry.npmmirror.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@~0.5.2", "fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://registry.npmmirror.com/fresh/download/fresh-0.5.2.tgz"
  "version" "0.5.2"

"from2@^2.1.0":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "https://registry.npmmirror.com/from2/download/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-constants@^1.0.0":
  "integrity" "sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow=="
  "resolved" "https://registry.npmmirror.com/fs-constants/-/fs-constants-1.0.0.tgz"
  "version" "1.0.0"

"fs-extra@^7.0.1":
  "integrity" "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk="
  "resolved" "https://registry.npmmirror.com/fs-extra/download/fs-extra-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-extra@^8.1.0":
  "integrity" "sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA="
  "resolved" "https://registry.npmmirror.com/fs-extra/download/fs-extra-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-minipass@^2.0.0":
  "integrity" "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs="
  "resolved" "https://registry.npmmirror.com/fs-minipass/download/fs-minipass-2.1.0.tgz?cache=0&sync_timestamp=1579628584498&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffs-minipass%2Fdownload%2Ffs-minipass-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "minipass" "^3.0.0"

"fs-write-stream-atomic@^1.0.8":
  "integrity" "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="
  "resolved" "https://registry.npmmirror.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "^4.1.2"
    "iferr" "^0.1.5"
    "imurmurhash" "^0.1.4"
    "readable-stream" "1 || 2"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.npmmirror.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@^1.2.7":
  "integrity" "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg="
  "resolved" "https://registry.npmmirror.com/fsevents/download/fsevents-1.2.13.tgz?cache=0&sync_timestamp=1588787369955&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffsevents%2Fdownload%2Ffsevents-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "bindings" "^1.5.0"
    "nan" "^2.12.1"

"fsevents@^2.1.2", "fsevents@~2.1.2":
  "integrity" "sha1-+3OHA66NL5/pAMM4Nt3r7ouX8j4="
  "resolved" "https://registry.npmmirror.com/fsevents/download/fsevents-2.1.3.tgz?cache=0&sync_timestamp=1588787369955&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffsevents%2Fdownload%2Ffsevents-2.1.3.tgz"
  "version" "2.1.3"

"fsevents@~2.3.2":
  "integrity" "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw=="
  "resolved" "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz"
  "version" "2.3.3"

"function-bind@^1.1.1":
  "integrity" "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="
  "resolved" "https://registry.npmmirror.com/function-bind/download/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"gauge@^3.0.0":
  "integrity" "sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q=="
  "resolved" "https://registry.npmmirror.com/gauge/-/gauge-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "aproba" "^1.0.3 || ^2.0.0"
    "color-support" "^1.1.2"
    "console-control-strings" "^1.0.0"
    "has-unicode" "^2.0.1"
    "object-assign" "^4.1.1"
    "signal-exit" "^3.0.0"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"
    "wide-align" "^1.1.2"

"gensync@^1.0.0-beta.1":
  "integrity" "sha1-WPQ2H/mH5f9uHnohCCeqNx6qwmk="
  "resolved" "https://registry.npmmirror.com/gensync/download/gensync-1.0.0-beta.1.tgz"
  "version" "1.0.0-beta.1"

"get-caller-file@^2.0.1":
  "integrity" "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="
  "resolved" "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-package-type@^0.1.0":
  "integrity" "sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro="
  "resolved" "https://registry.npmmirror.com/get-package-type/download/get-package-type-0.1.0.tgz"
  "version" "0.1.0"

"get-stream@^4.0.0":
  "integrity" "sha1-wbJVV189wh1Zv8ec09K0axw6VLU="
  "resolved" "https://registry.npmmirror.com/get-stream/download/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^5.0.0", "get-stream@^5.1.0":
  "integrity" "sha1-ASA83JJZf5uQkGfD5lbMH008Tck="
  "resolved" "https://registry.npmmirror.com/get-stream/download/get-stream-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://registry.npmmirror.com/get-value/download/get-value-2.0.6.tgz"
  "version" "2.0.6"

"getpass@^0.1.1":
  "integrity" "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo="
  "resolved" "https://registry.npmmirror.com/getpass/download/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-parent@^3.1.0":
  "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
  "resolved" "https://registry.npmmirror.com/glob-parent/download/glob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob-parent@~5.1.0":
  "integrity" "sha1-tsHvQXxOVmPqSY8cRa+saRa7wik="
  "resolved" "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-to-regexp@^0.3.0":
  "integrity" "sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs="
  "resolved" "https://registry.npmmirror.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz"
  "version" "0.3.0"

"glob@^7.0.0", "glob@^7.0.3", "glob@^7.1.1", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4":
  "integrity" "sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY="
  "resolved" "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1573078079496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglob%2Fdownload%2Fglob-7.1.6.tgz"
  "version" "7.1.6"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"global@~4.4.0":
  "integrity" "sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w=="
  "resolved" "https://registry.npmmirror.com/global/-/global-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "min-document" "^2.19.0"
    "process" "^0.11.10"

"globals@^11.1.0":
  "integrity" "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="
  "resolved" "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz"
  "version" "11.12.0"

"globby@^6.1.0":
  "integrity" "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw="
  "resolved" "https://registry.npmmirror.com/globby/download/globby-6.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "array-union" "^1.0.1"
    "glob" "^7.0.3"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"globby@^7.1.1":
  "integrity" "sha1-+yzP+UAfhgCUXfral0QMypcrhoA="
  "resolved" "https://registry.npmmirror.com/globby/download/globby-7.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "array-union" "^1.0.1"
    "dir-glob" "^2.0.0"
    "glob" "^7.1.2"
    "ignore" "^3.3.5"
    "pify" "^3.0.0"
    "slash" "^1.0.0"

"globby@^9.2.0":
  "integrity" "sha1-/QKacGxwPSm90XD0tts6P3p8tj0="
  "resolved" "https://registry.npmmirror.com/globby/download/globby-9.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-9.2.0.tgz"
  "version" "9.2.0"
  dependencies:
    "@types/glob" "^7.1.1"
    "array-union" "^1.0.2"
    "dir-glob" "^2.2.2"
    "fast-glob" "^2.2.6"
    "glob" "^7.1.3"
    "ignore" "^4.0.3"
    "pify" "^4.0.1"
    "slash" "^2.0.0"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.15", "graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.2.0", "graceful-fs@^4.2.2", "graceful-fs@^4.2.4":
  "integrity" "sha1-Ila94U02MpWMRl68ltxGfKB6Kfs="
  "resolved" "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.4.tgz?cache=0&sync_timestamp=1588086876757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.4.tgz"
  "version" "4.2.4"

"growly@^1.3.0":
  "integrity" "sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE="
  "resolved" "https://registry.npmmirror.com/growly/download/growly-1.3.0.tgz"
  "version" "1.3.0"

"gzip-size@^5.0.0":
  "integrity" "sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ="
  "resolved" "https://registry.npmmirror.com/gzip-size/download/gzip-size-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "duplexer" "^0.1.1"
    "pify" "^4.0.1"

"handle-thing@^2.0.0":
  "integrity" "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04="
  "resolved" "https://registry.npmmirror.com/handle-thing/download/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"har-schema@^2.0.0":
  "integrity" "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="
  "resolved" "https://registry.npmmirror.com/har-schema/download/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~5.1.3":
  "integrity" "sha1-HvievT5JllV2de7ZiTEQ3DUPoIA="
  "resolved" "https://registry.npmmirror.com/har-validator/download/har-validator-5.1.3.tgz"
  "version" "5.1.3"
  dependencies:
    "ajv" "^6.5.5"
    "har-schema" "^2.0.0"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "https://registry.npmmirror.com/has-ansi/download/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-binary2@~1.0.2":
  "integrity" "sha1-d3asYn8+p3JQz8My2rfd9eT10R0="
  "resolved" "https://registry.npmmirror.com/has-binary2/download/has-binary2-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "isarray" "2.0.1"

"has-cors@1.1.0":
  "integrity" "sha1-XkdHk/fqmEPRu5nCPu9J/xJv/zk="
  "resolved" "https://registry.npmmirror.com/has-cors/download/has-cors-1.1.0.tgz"
  "version" "1.1.0"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://registry.npmmirror.com/has-flag/download/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="
  "resolved" "https://registry.npmmirror.com/has-flag/download/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.0", "has-symbols@^1.0.1":
  "integrity" "sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg="
  "resolved" "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.1.tgz"
  "version" "1.0.1"

"has-unicode@^2.0.1":
  "integrity" "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ=="
  "resolved" "https://registry.npmmirror.com/has-unicode/-/has-unicode-2.0.1.tgz"
  "version" "2.0.1"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://registry.npmmirror.com/has-value/download/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://registry.npmmirror.com/has-value/download/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://registry.npmmirror.com/has-values/download/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://registry.npmmirror.com/has-values/download/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.0", "has@^1.0.3":
  "integrity" "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y="
  "resolved" "https://registry.npmmirror.com/has/download/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM="
  "resolved" "https://registry.npmmirror.com/hash-base/download/hash-base-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "inherits" "^2.0.4"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"hash-sum@^1.0.2":
  "integrity" "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ="
  "resolved" "https://registry.npmmirror.com/hash-sum/download/hash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash-sum@^2.0.0":
  "integrity" "sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo="
  "resolved" "https://registry.npmmirror.com/hash-sum/download/hash-sum-2.0.0.tgz"
  "version" "2.0.0"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I="
  "resolved" "https://registry.npmmirror.com/hash.js/download/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"he@^1.1.0", "he@1.2.x":
  "integrity" "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="
  "resolved" "https://registry.npmmirror.com/he/download/he-1.2.0.tgz"
  "version" "1.2.0"

"hex-color-regex@^1.1.0":
  "integrity" "sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4="
  "resolved" "https://registry.npmmirror.com/hex-color-regex/download/hex-color-regex-1.1.0.tgz"
  "version" "1.1.0"

"highlight.js@^9.6.0":
  "integrity" "sha1-7SGqAB/mJSuxCj121HVzxlOf4Tw="
  "resolved" "https://registry.npmmirror.com/highlight.js/download/highlight.js-9.18.1.tgz"
  "version" "9.18.1"

"hmac-drbg@^1.0.0":
  "integrity" "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="
  "resolved" "https://registry.npmmirror.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"hoopy@^0.1.4":
  "integrity" "sha1-YJIH1mEQADOpqUAq096mdzgcGx0="
  "resolved" "https://registry.npmmirror.com/hoopy/download/hoopy-0.1.4.tgz"
  "version" "0.1.4"

"hosted-git-info@^2.1.4":
  "integrity" "sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg="
  "resolved" "https://registry.npmmirror.com/hosted-git-info/download/hosted-git-info-2.8.8.tgz?cache=0&sync_timestamp=1583017392137&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhosted-git-info%2Fdownload%2Fhosted-git-info-2.8.8.tgz"
  "version" "2.8.8"

"hpack.js@^2.1.6":
  "integrity" "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI="
  "resolved" "https://registry.npmmirror.com/hpack.js/download/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"hsl-regex@^1.0.0":
  "integrity" "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4="
  "resolved" "https://registry.npmmirror.com/hsl-regex/download/hsl-regex-1.0.0.tgz"
  "version" "1.0.0"

"hsla-regex@^1.0.0":
  "integrity" "sha1-wc56MWjIxmFAM6S194d/OyJfnDg="
  "resolved" "https://registry.npmmirror.com/hsla-regex/download/hsla-regex-1.0.0.tgz"
  "version" "1.0.0"

"html-comment-regex@^1.1.0":
  "integrity" "sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c="
  "resolved" "https://registry.npmmirror.com/html-comment-regex/download/html-comment-regex-1.1.2.tgz"
  "version" "1.1.2"

"html-encoding-sniffer@^1.0.2":
  "integrity" "sha1-5w2EuU2lOqN14R/jo1G+ZkLKRvg="
  "resolved" "https://registry.npmmirror.com/html-encoding-sniffer/download/html-encoding-sniffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "whatwg-encoding" "^1.0.1"

"html-entities@^1.3.1":
  "integrity" "sha1-+5oaS1sUxdq6gtPjTGrk/nAaDkQ="
  "resolved" "https://registry.npmmirror.com/html-entities/download/html-entities-1.3.1.tgz"
  "version" "1.3.1"

"html-escaper@^2.0.0":
  "integrity" "sha1-39YAJ9o2o238viNiYsAKWCJoFFM="
  "resolved" "https://registry.npmmirror.com/html-escaper/download/html-escaper-2.0.2.tgz"
  "version" "2.0.2"

"html-minifier@^3.2.3":
  "integrity" "sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw="
  "resolved" "https://registry.npmmirror.com/html-minifier/download/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.x"
    "clean-css" "4.2.x"
    "commander" "2.17.x"
    "he" "1.2.x"
    "param-case" "2.1.x"
    "relateurl" "0.2.x"
    "uglify-js" "3.4.x"

"html-tags@^2.0.0":
  "integrity" "sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos="
  "resolved" "https://registry.npmmirror.com/html-tags/download/html-tags-2.0.0.tgz"
  "version" "2.0.0"

"html-webpack-plugin@^3.2.0", "html-webpack-plugin@>=2.26.0":
  "integrity" "sha1-sBq71yOsqqeze2r0SS69oD2d03s="
  "resolved" "https://registry.npmmirror.com/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz?cache=0&sync_timestamp=1588268411154&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtml-webpack-plugin%2Fdownload%2Fhtml-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "html-minifier" "^3.2.3"
    "loader-utils" "^0.2.16"
    "lodash" "^4.17.3"
    "pretty-error" "^2.0.2"
    "tapable" "^1.0.0"
    "toposort" "^1.0.0"
    "util.promisify" "1.0.0"

"htmlparser2@^3.3.0":
  "integrity" "sha1-vWedw/WYl7ajS7EHSchVu1OpOS8="
  "resolved" "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "domelementtype" "^1.3.1"
    "domhandler" "^2.3.0"
    "domutils" "^1.5.1"
    "entities" "^1.1.1"
    "inherits" "^2.0.1"
    "readable-stream" "^3.1.1"

"http-assert@^1.3.0":
  "integrity" "sha1-xfcl1neqfoc+9zYZm4lobM6zeHg="
  "resolved" "https://registry.npmmirror.com/http-assert/download/http-assert-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "deep-equal" "~1.0.1"
    "http-errors" "~1.7.2"

"http-deceiver@^1.2.7":
  "integrity" "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="
  "resolved" "https://registry.npmmirror.com/http-deceiver/download/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@^1.3.1", "http-errors@^1.6.3", "http-errors@~1.7.2", "http-errors@1.7.3":
  "integrity" "sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY="
  "resolved" "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz?cache=0&sync_timestamp=1561418493658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.4"
    "setprototypeof" "1.1.1"
    "statuses" ">= 1.5.0 < 2"
    "toidentifier" "1.0.0"

"http-errors@~1.6.2":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "https://registry.npmmirror.com/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1561418493658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@1.7.2":
  "integrity" "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8="
  "resolved" "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.2.tgz?cache=0&sync_timestamp=1561418493658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.2.tgz"
  "version" "1.7.2"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.1"
    "statuses" ">= 1.5.0 < 2"
    "toidentifier" "1.0.0"

"http-parser-js@>=0.4.0 <0.4.11":
  "integrity" "sha1-ksnBN0w1CF912zWexWzCV8u5P6Q="
  "resolved" "https://registry.npmmirror.com/http-parser-js/download/http-parser-js-0.4.10.tgz?cache=0&sync_timestamp=1572714627611&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-parser-js%2Fdownload%2Fhttp-parser-js-0.4.10.tgz"
  "version" "0.4.10"

"http-proxy-middleware@0.19.1":
  "integrity" "sha1-GDx9xKoUeRUDBkmMIQza+WCApDo="
  "resolved" "https://registry.npmmirror.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz?cache=0&sync_timestamp=1589915518285&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-proxy-middleware%2Fdownload%2Fhttp-proxy-middleware-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "http-proxy" "^1.17.0"
    "is-glob" "^4.0.0"
    "lodash" "^4.17.11"
    "micromatch" "^3.1.10"

"http-proxy@^1.17.0":
  "integrity" "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk="
  "resolved" "https://registry.npmmirror.com/http-proxy/download/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"http-signature@~1.2.0":
  "integrity" "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE="
  "resolved" "https://registry.npmmirror.com/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1585807808622&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"https-browserify@^1.0.0":
  "integrity" "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="
  "resolved" "https://registry.npmmirror.com/https-browserify/download/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"https-proxy-agent@^4.0.0":
  "integrity" "sha512-zoDhWrkR3of1l9QAL8/scJZyLu8j/gBkcwcaQOZh7Gyh/+uJQzGVETdgT30akuwkpL8HTRfssqI3BZuV18teDg=="
  "resolved" "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "agent-base" "5"
    "debug" "4"

"human-signals@^1.1.1":
  "integrity" "sha1-xbHNFPUK6uCatsWf5jujOV/k36M="
  "resolved" "https://registry.npmmirror.com/human-signals/download/human-signals-1.1.1.tgz?cache=0&sync_timestamp=1584198662293&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhuman-signals%2Fdownload%2Fhuman-signals-1.1.1.tgz"
  "version" "1.1.1"

"iconv-lite@0.4.24":
  "integrity" "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="
  "resolved" "https://registry.npmmirror.com/iconv-lite/download/iconv-lite-0.4.24.tgz?cache=0&sync_timestamp=1579333981154&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-replace-symbols@^1.1.0":
  "integrity" "sha1-Bupvg2ead0njhs/h/oEq5dsiPe0="
  "resolved" "https://registry.npmmirror.com/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz"
  "version" "1.1.0"

"icss-utils@^4.0.0", "icss-utils@^4.1.0", "icss-utils@^4.1.1":
  "integrity" "sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc="
  "resolved" "https://registry.npmmirror.com/icss-utils/download/icss-utils-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "postcss" "^7.0.14"

"ieee754@^1.1.13", "ieee754@^1.1.4":
  "integrity" "sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q="
  "resolved" "https://registry.npmmirror.com/ieee754/download/ieee754-1.1.13.tgz"
  "version" "1.1.13"

"iferr@^0.1.5":
  "integrity" "sha1-xg7taebY/bazEEofy8ocGS3FtQE="
  "resolved" "https://registry.npmmirror.com/iferr/download/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore@^3.3.5":
  "integrity" "sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM="
  "resolved" "https://registry.npmmirror.com/ignore/download/ignore-3.3.10.tgz?cache=0&sync_timestamp=1565775199290&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fignore%2Fdownload%2Fignore-3.3.10.tgz"
  "version" "3.3.10"

"ignore@^4.0.3":
  "integrity" "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw="
  "resolved" "https://registry.npmmirror.com/ignore/download/ignore-4.0.6.tgz?cache=0&sync_timestamp=1565775199290&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fignore%2Fdownload%2Fignore-4.0.6.tgz"
  "version" "4.0.6"

"immediate@~3.0.5":
  "integrity" "sha1-nbHb0Pr43m++D13V5Wu2BigN5ps="
  "resolved" "https://registry.npmmirror.com/immediate/download/immediate-3.0.6.tgz"
  "version" "3.0.6"

"import-cwd@^2.0.0":
  "integrity" "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk="
  "resolved" "https://registry.npmmirror.com/import-cwd/download/import-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "import-from" "^2.1.0"

"import-fresh@^2.0.0":
  "integrity" "sha1-2BNVwVYS04bGH53dOSLUMEgipUY="
  "resolved" "https://registry.npmmirror.com/import-fresh/download/import-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-from@^2.1.0":
  "integrity" "sha1-M1238qev/VOqpHHUuAId7ja387E="
  "resolved" "https://registry.npmmirror.com/import-from/download/import-from-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "resolve-from" "^3.0.0"

"import-local@^2.0.0":
  "integrity" "sha1-VQcL44pZk88Y72236WH1vuXFoJ0="
  "resolved" "https://registry.npmmirror.com/import-local/download/import-local-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pkg-dir" "^3.0.0"
    "resolve-cwd" "^2.0.0"

"import-local@^3.0.2":
  "integrity" "sha1-qM/QQx0d5KIZlwPQA+PmI2T6bbY="
  "resolved" "https://registry.npmmirror.com/import-local/download/import-local-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "pkg-dir" "^4.2.0"
    "resolve-cwd" "^3.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.npmmirror.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE="
  "resolved" "https://registry.npmmirror.com/indent-string/download/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"indexes-of@^1.0.1":
  "integrity" "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="
  "resolved" "https://registry.npmmirror.com/indexes-of/download/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"indexof@0.0.1":
  "integrity" "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10="
  "resolved" "https://registry.npmmirror.com/indexof/download/indexof-0.0.1.tgz"
  "version" "0.0.1"

"infer-owner@^1.0.3", "infer-owner@^1.0.4":
  "integrity" "sha1-xM78qo5RBRwqQLos6KPScpWvlGc="
  "resolved" "https://registry.npmmirror.com/infer-owner/download/infer-owner-1.0.4.tgz"
  "version" "1.0.4"

"inflation@^2.0.0":
  "integrity" "sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8="
  "resolved" "https://registry.npmmirror.com/inflation/download/inflation-2.0.0.tgz"
  "version" "2.0.0"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npmmirror.com/inflight/download/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="
  "resolved" "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.1":
  "integrity" "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="
  "resolved" "https://registry.npmmirror.com/inherits/download/inherits-2.0.1.tgz"
  "version" "2.0.1"

"inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://registry.npmmirror.com/inherits/download/inherits-2.0.3.tgz"
  "version" "2.0.3"

"ini@~1.3.0":
  "integrity" "sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc="
  "resolved" "https://registry.npmmirror.com/ini/download/ini-1.3.5.tgz"
  "version" "1.3.5"

"internal-ip@^4.3.0":
  "integrity" "sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc="
  "resolved" "https://registry.npmmirror.com/internal-ip/download/internal-ip-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "default-gateway" "^4.2.0"
    "ipaddr.js" "^1.9.0"

"interpret@^1.0.0":
  "integrity" "sha1-1QYaYiS+WOgIOYX1AU2EQ1lXYpY="
  "resolved" "https://registry.npmmirror.com/interpret/download/interpret-1.2.0.tgz"
  "version" "1.2.0"

"intersection-observer@^0.7.0":
  "integrity" "sha1-7ha+6XjbU1FurS8KgVSwm0ALvck="
  "resolved" "https://registry.npmmirror.com/intersection-observer/download/intersection-observer-0.7.0.tgz"
  "version" "0.7.0"

"invariant@^2.2.2", "invariant@^2.2.4":
  "integrity" "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY="
  "resolved" "https://registry.npmmirror.com/invariant/download/invariant-2.2.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finvariant%2Fdownload%2Finvariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"ip-regex@^2.1.0":
  "integrity" "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk="
  "resolved" "https://registry.npmmirror.com/ip-regex/download/ip-regex-2.1.0.tgz"
  "version" "2.1.0"

"ip@^1.1.0", "ip@^1.1.5":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://registry.npmmirror.com/ip/download/ip-1.1.5.tgz"
  "version" "1.1.5"

"ipaddr.js@^1.9.0", "ipaddr.js@1.9.1":
  "integrity" "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="
  "resolved" "https://registry.npmmirror.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-absolute-url@^2.0.0":
  "integrity" "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="
  "resolved" "https://registry.npmmirror.com/is-absolute-url/download/is-absolute-url-2.1.0.tgz?cache=0&sync_timestamp=1569736493122&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-absolute-url%2Fdownload%2Fis-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-absolute-url@^3.0.3":
  "integrity" "sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg="
  "resolved" "https://registry.npmmirror.com/is-absolute-url/download/is-absolute-url-3.0.3.tgz?cache=0&sync_timestamp=1569736493122&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-absolute-url%2Fdownload%2Fis-absolute-url-3.0.3.tgz"
  "version" "3.0.3"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY="
  "resolved" "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arguments@^1.0.4":
  "integrity" "sha1-P6+WbHy6D/Q3+zH2JQCC/PBEjPM="
  "resolved" "https://registry.npmmirror.com/is-arguments/download/is-arguments-1.0.4.tgz"
  "version" "1.0.4"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@^0.3.1":
  "integrity" "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM="
  "resolved" "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-binary-path@^1.0.0":
  "integrity" "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="
  "resolved" "https://registry.npmmirror.com/is-binary-path/download/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-binary-path@~2.1.0":
  "integrity" "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk="
  "resolved" "https://registry.npmmirror.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-buffer@^1.1.5", "is-buffer@~1.1.1":
  "integrity" "sha1-76ouqdqg16suoTqXsritUf776L4="
  "resolved" "https://registry.npmmirror.com/is-buffer/download/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.4", "is-callable@^1.1.5":
  "integrity" "sha1-9+RrWWiQRW23Tn9ul2yzJz0G+qs="
  "resolved" "https://registry.npmmirror.com/is-callable/download/is-callable-1.1.5.tgz"
  "version" "1.1.5"

"is-ci@^2.0.0":
  "integrity" "sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw="
  "resolved" "https://registry.npmmirror.com/is-ci/download/is-ci-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ci-info" "^2.0.0"

"is-color-stop@^1.0.0":
  "integrity" "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U="
  "resolved" "https://registry.npmmirror.com/is-color-stop/download/is-color-stop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-color-names" "^0.0.4"
    "hex-color-regex" "^1.1.0"
    "hsl-regex" "^1.0.0"
    "hsla-regex" "^1.0.0"
    "rgb-regex" "^1.0.1"
    "rgba-regex" "^1.0.0"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="
  "resolved" "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha1-vac28s2P0G0yhE53Q7+nSUw7/X4="
  "resolved" "https://registry.npmmirror.com/is-date-object/download/is-date-object-1.0.2.tgz?cache=0&sync_timestamp=1576729182289&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-date-object%2Fdownload%2Fis-date-object-1.0.2.tgz"
  "version" "1.0.2"

"is-descriptor@^0.1.0":
  "integrity" "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco="
  "resolved" "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0", "is-descriptor@^1.0.2":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "https://registry.npmmirror.com/is-directory/download/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-docker@^2.0.0":
  "integrity" "sha1-LLDfDnXi0GT+GGTDfN6st7Lc8ls="
  "resolved" "https://registry.npmmirror.com/is-docker/download/is-docker-2.0.0.tgz"
  "version" "2.0.0"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://registry.npmmirror.com/is-extendable/download/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ="
  "resolved" "https://registry.npmmirror.com/is-extendable/download/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://registry.npmmirror.com/is-extglob/download/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="
  "resolved" "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-function@^1.0.1":
  "integrity" "sha512-lw7DUp0aWXYg+CBCN+JKkcE0Q2RayZnSvnZBlwgxHBQhqt5pZNVy4Ri7H9GmmXkdu7LUthszM+Tor1u/2iBcpQ=="
  "resolved" "https://registry.npmmirror.com/is-function/-/is-function-1.0.2.tgz"
  "version" "1.0.2"

"is-generator-fn@^2.0.0":
  "integrity" "sha1-fRQK3DiarzARqPKipM+m+q3/sRg="
  "resolved" "https://registry.npmmirror.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz"
  "version" "2.1.0"

"is-generator-function@^1.0.7":
  "integrity" "sha1-0hMuUpuwAAp/gHlNS99c1eWBNSI="
  "resolved" "https://registry.npmmirror.com/is-generator-function/download/is-generator-function-1.0.7.tgz"
  "version" "1.0.7"

"is-glob@^3.1.0":
  "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
  "resolved" "https://registry.npmmirror.com/is-glob/download/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@~4.0.1":
  "integrity" "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw="
  "resolved" "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-extglob" "^2.1.1"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://registry.npmmirror.com/is-number/download/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss="
  "resolved" "https://registry.npmmirror.com/is-number/download/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^2.0.0":
  "integrity" "sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI="
  "resolved" "https://registry.npmmirror.com/is-obj/download/is-obj-2.0.0.tgz"
  "version" "2.0.0"

"is-path-cwd@^2.0.0":
  "integrity" "sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s="
  "resolved" "https://registry.npmmirror.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz?cache=0&sync_timestamp=1562347283002&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-path-cwd%2Fdownload%2Fis-path-cwd-2.2.0.tgz"
  "version" "2.2.0"

"is-path-in-cwd@^2.0.0":
  "integrity" "sha1-v+Lcomxp85cmWkAJljYCk1oFOss="
  "resolved" "https://registry.npmmirror.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "is-path-inside" "^2.1.0"

"is-path-inside@^2.1.0":
  "integrity" "sha1-fJgQWH1lmkDSe8201WFuqwWUlLI="
  "resolved" "https://registry.npmmirror.com/is-path-inside/download/is-path-inside-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "path-is-inside" "^1.0.2"

"is-plain-obj@^1.0.0":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "https://registry.npmmirror.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="
  "resolved" "https://registry.npmmirror.com/is-plain-object/download/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-regex@^1.0.4", "is-regex@^1.0.5":
  "integrity" "sha1-OdWJo1i/GJZ/cmlnEguPwa7XTq4="
  "resolved" "https://registry.npmmirror.com/is-regex/download/is-regex-1.0.5.tgz?cache=0&sync_timestamp=1576454688491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-regex%2Fdownload%2Fis-regex-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has" "^1.0.3"

"is-resolvable@^1.0.0":
  "integrity" "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg="
  "resolved" "https://registry.npmmirror.com/is-resolvable/download/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://registry.npmmirror.com/is-stream/download/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha1-venDJoDW+uBBKdasnZIc54FfeOM="
  "resolved" "https://registry.npmmirror.com/is-stream/download/is-stream-2.0.0.tgz"
  "version" "2.0.0"

"is-svg@^3.0.0":
  "integrity" "sha1-kyHb0pwhLlypnE+peUxxS8r6L3U="
  "resolved" "https://registry.npmmirror.com/is-svg/download/is-svg-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "html-comment-regex" "^1.1.0"

"is-symbol@^1.0.2":
  "integrity" "sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc="
  "resolved" "https://registry.npmmirror.com/is-symbol/download/is-symbol-1.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-symbol%2Fdownload%2Fis-symbol-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "has-symbols" "^1.0.1"

"is-typedarray@^1.0.0", "is-typedarray@~1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://registry.npmmirror.com/is-typedarray/download/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-windows@^1.0.2":
  "integrity" "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="
  "resolved" "https://registry.npmmirror.com/is-windows/download/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "https://registry.npmmirror.com/is-wsl/download/is-wsl-1.1.0.tgz?cache=0&sync_timestamp=1588494180082&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-wsl%2Fdownload%2Fis-wsl-1.1.0.tgz"
  "version" "1.1.0"

"is-wsl@^2.1.1":
  "integrity" "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE="
  "resolved" "https://registry.npmmirror.com/is-wsl/download/is-wsl-2.2.0.tgz?cache=0&sync_timestamp=1588494180082&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-wsl%2Fdownload%2Fis-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@^1.0.0", "isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isarray@0.0.1":
  "integrity" "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="
  "resolved" "https://registry.npmmirror.com/isarray/download/isarray-0.0.1.tgz"
  "version" "0.0.1"

"isarray@2.0.1":
  "integrity" "sha1-o32U7ZzaLVmGXJ92/llu4fM4dB4="
  "resolved" "https://registry.npmmirror.com/isarray/download/isarray-2.0.1.tgz"
  "version" "2.0.1"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://registry.npmmirror.com/isexe/download/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://registry.npmmirror.com/isobject/download/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npmmirror.com/isobject/download/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isstream@~0.1.2":
  "integrity" "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="
  "resolved" "https://registry.npmmirror.com/isstream/download/isstream-0.1.2.tgz"
  "version" "0.1.2"

"istanbul-lib-coverage@^3.0.0":
  "integrity" "sha1-9ZRKN8cLVQsCp4pcOyBVsoDOyOw="
  "resolved" "https://registry.npmmirror.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.0.0.tgz"
  "version" "3.0.0"

"istanbul-lib-instrument@^4.0.0":
  "integrity" "sha1-hzxv/4l0UBGCIndGlqPyiQLXfB0="
  "resolved" "https://registry.npmmirror.com/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "@babel/core" "^7.7.5"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-coverage" "^3.0.0"
    "semver" "^6.3.0"

"istanbul-lib-report@^3.0.0":
  "integrity" "sha1-dRj+UupE3jcvRgp2tezan/tz2KY="
  "resolved" "https://registry.npmmirror.com/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "istanbul-lib-coverage" "^3.0.0"
    "make-dir" "^3.0.0"
    "supports-color" "^7.1.0"

"istanbul-lib-source-maps@^4.0.0":
  "integrity" "sha1-dXQ85tlruG3H7kNSz2Nmoj8LGtk="
  "resolved" "https://registry.npmmirror.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.0.tgz?cache=0&sync_timestamp=1577062405633&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fistanbul-lib-source-maps%2Fdownload%2Fistanbul-lib-source-maps-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "debug" "^4.1.1"
    "istanbul-lib-coverage" "^3.0.0"
    "source-map" "^0.6.1"

"istanbul-reports@^3.0.2":
  "integrity" "sha1-1ZMhDlAAaDdQywn8BkTktuJ/1Ts="
  "resolved" "https://registry.npmmirror.com/istanbul-reports/download/istanbul-reports-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "html-escaper" "^2.0.0"
    "istanbul-lib-report" "^3.0.0"

"javascript-stringify@^2.0.1":
  "integrity" "sha1-bvNYA1MQ411mfGde1j0+t8GqGeU="
  "resolved" "https://registry.npmmirror.com/javascript-stringify/download/javascript-stringify-2.0.1.tgz"
  "version" "2.0.1"

"jest-changed-files@^25.5.0":
  "integrity" "sha1-FBzCNWfOs/U0Um+GFLo5QhODY0w="
  "resolved" "https://registry.npmmirror.com/jest-changed-files/download/jest-changed-files-25.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-changed-files%2Fdownload%2Fjest-changed-files-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "execa" "^3.2.0"
    "throat" "^5.0.0"

"jest-cli@^25.5.4":
  "integrity" "sha1-ufGoTRMBqSxcIXaEy3mECDHbnw0="
  "resolved" "https://registry.npmmirror.com/jest-cli/download/jest-cli-25.5.4.tgz?cache=0&sync_timestamp=1588675322996&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-cli%2Fdownload%2Fjest-cli-25.5.4.tgz"
  "version" "25.5.4"
  dependencies:
    "@jest/core" "^25.5.4"
    "@jest/test-result" "^25.5.0"
    "@jest/types" "^25.5.0"
    "chalk" "^3.0.0"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "import-local" "^3.0.2"
    "is-ci" "^2.0.0"
    "jest-config" "^25.5.4"
    "jest-util" "^25.5.0"
    "jest-validate" "^25.5.0"
    "prompts" "^2.0.1"
    "realpath-native" "^2.0.0"
    "yargs" "^15.3.1"

"jest-config@^25.5.4":
  "integrity" "sha1-OOIFez+Xbvcwmyssjc0qcIpn8Cw="
  "resolved" "https://registry.npmmirror.com/jest-config/download/jest-config-25.5.4.tgz?cache=0&sync_timestamp=1588675314467&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-config%2Fdownload%2Fjest-config-25.5.4.tgz"
  "version" "25.5.4"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^25.5.4"
    "@jest/types" "^25.5.0"
    "babel-jest" "^25.5.1"
    "chalk" "^3.0.0"
    "deepmerge" "^4.2.2"
    "glob" "^7.1.1"
    "graceful-fs" "^4.2.4"
    "jest-environment-jsdom" "^25.5.0"
    "jest-environment-node" "^25.5.0"
    "jest-get-type" "^25.2.6"
    "jest-jasmine2" "^25.5.4"
    "jest-regex-util" "^25.2.6"
    "jest-resolve" "^25.5.1"
    "jest-util" "^25.5.0"
    "jest-validate" "^25.5.0"
    "micromatch" "^4.0.2"
    "pretty-format" "^25.5.0"
    "realpath-native" "^2.0.0"

"jest-diff@^25.5.0":
  "integrity" "sha1-HdJu1k+WZnwGjO8Ca2d9+gGvz6k="
  "resolved" "https://registry.npmmirror.com/jest-diff/download/jest-diff-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "chalk" "^3.0.0"
    "diff-sequences" "^25.2.6"
    "jest-get-type" "^25.2.6"
    "pretty-format" "^25.5.0"

"jest-docblock@^25.3.0":
  "integrity" "sha1-i3d6J+NHfNd6FowFKQxHGldWI+8="
  "resolved" "https://registry.npmmirror.com/jest-docblock/download/jest-docblock-25.3.0.tgz"
  "version" "25.3.0"
  dependencies:
    "detect-newline" "^3.0.0"

"jest-each@^25.5.0":
  "integrity" "sha1-DDwnl+giXLe+x+TSSdzZa5NL5RY="
  "resolved" "https://registry.npmmirror.com/jest-each/download/jest-each-25.5.0.tgz?cache=0&sync_timestamp=1588675329383&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-each%2Fdownload%2Fjest-each-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "chalk" "^3.0.0"
    "jest-get-type" "^25.2.6"
    "jest-util" "^25.5.0"
    "pretty-format" "^25.5.0"

"jest-environment-jsdom@^25.5.0":
  "integrity" "sha1-3L5NouqZdweZcEDs9uJWCuxOmDQ="
  "resolved" "https://registry.npmmirror.com/jest-environment-jsdom/download/jest-environment-jsdom-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/environment" "^25.5.0"
    "@jest/fake-timers" "^25.5.0"
    "@jest/types" "^25.5.0"
    "jest-mock" "^25.5.0"
    "jest-util" "^25.5.0"
    "jsdom" "^15.2.1"

"jest-environment-node@^25.5.0":
  "integrity" "sha1-D1UnDZSASQKYjmStyjfGzg99B6E="
  "resolved" "https://registry.npmmirror.com/jest-environment-node/download/jest-environment-node-25.5.0.tgz?cache=0&sync_timestamp=1588675336982&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-environment-node%2Fdownload%2Fjest-environment-node-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/environment" "^25.5.0"
    "@jest/fake-timers" "^25.5.0"
    "@jest/types" "^25.5.0"
    "jest-mock" "^25.5.0"
    "jest-util" "^25.5.0"
    "semver" "^6.3.0"

"jest-get-type@^25.2.6":
  "integrity" "sha1-Cwoy+riQi0TVCL6BaBSH26u42Hc="
  "resolved" "https://registry.npmmirror.com/jest-get-type/download/jest-get-type-25.2.6.tgz"
  "version" "25.2.6"

"jest-haste-map@^25.5.1":
  "integrity" "sha1-HfEPcWwdlOYKHr93mMn7PaJiCUM="
  "resolved" "https://registry.npmmirror.com/jest-haste-map/download/jest-haste-map-25.5.1.tgz"
  "version" "25.5.1"
  dependencies:
    "@jest/types" "^25.5.0"
    "@types/graceful-fs" "^4.1.2"
    "anymatch" "^3.0.3"
    "fb-watchman" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-serializer" "^25.5.0"
    "jest-util" "^25.5.0"
    "jest-worker" "^25.5.0"
    "micromatch" "^4.0.2"
    "sane" "^4.0.3"
    "walker" "^1.0.7"
    "which" "^2.0.2"
  optionalDependencies:
    "fsevents" "^2.1.2"

"jest-jasmine2@^25.5.4":
  "integrity" "sha1-ZsqLMo+xo8U2SBb4lY9pcKhSaWg="
  "resolved" "https://registry.npmmirror.com/jest-jasmine2/download/jest-jasmine2-25.5.4.tgz?cache=0&sync_timestamp=1588675312987&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-jasmine2%2Fdownload%2Fjest-jasmine2-25.5.4.tgz"
  "version" "25.5.4"
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^25.5.0"
    "@jest/source-map" "^25.5.0"
    "@jest/test-result" "^25.5.0"
    "@jest/types" "^25.5.0"
    "chalk" "^3.0.0"
    "co" "^4.6.0"
    "expect" "^25.5.0"
    "is-generator-fn" "^2.0.0"
    "jest-each" "^25.5.0"
    "jest-matcher-utils" "^25.5.0"
    "jest-message-util" "^25.5.0"
    "jest-runtime" "^25.5.4"
    "jest-snapshot" "^25.5.1"
    "jest-util" "^25.5.0"
    "pretty-format" "^25.5.0"
    "throat" "^5.0.0"

"jest-leak-detector@^25.5.0":
  "integrity" "sha1-IpHGKUsM5AQkG7Vv5g4tDD408Ls="
  "resolved" "https://registry.npmmirror.com/jest-leak-detector/download/jest-leak-detector-25.5.0.tgz?cache=0&sync_timestamp=1588675417838&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-leak-detector%2Fdownload%2Fjest-leak-detector-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "jest-get-type" "^25.2.6"
    "pretty-format" "^25.5.0"

"jest-matcher-utils@^25.5.0":
  "integrity" "sha1-+8mKEtcw5dJFPX8e1KTZSONLeGc="
  "resolved" "https://registry.npmmirror.com/jest-matcher-utils/download/jest-matcher-utils-25.5.0.tgz?cache=0&sync_timestamp=1588675334251&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-matcher-utils%2Fdownload%2Fjest-matcher-utils-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "chalk" "^3.0.0"
    "jest-diff" "^25.5.0"
    "jest-get-type" "^25.2.6"
    "pretty-format" "^25.5.0"

"jest-message-util@^25.5.0":
  "integrity" "sha1-6hHZMgTMeul0VuHYcWJRGFuIgOo="
  "resolved" "https://registry.npmmirror.com/jest-message-util/download/jest-message-util-25.5.0.tgz?cache=0&sync_timestamp=1588675416652&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-message-util%2Fdownload%2Fjest-message-util-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/types" "^25.5.0"
    "@types/stack-utils" "^1.0.1"
    "chalk" "^3.0.0"
    "graceful-fs" "^4.2.4"
    "micromatch" "^4.0.2"
    "slash" "^3.0.0"
    "stack-utils" "^1.0.1"

"jest-mock@^25.5.0":
  "integrity" "sha1-qRpU2r0U437NYWZda24GNgpVOHo="
  "resolved" "https://registry.npmmirror.com/jest-mock/download/jest-mock-25.5.0.tgz?cache=0&sync_timestamp=1588675318142&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-mock%2Fdownload%2Fjest-mock-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"

"jest-pnp-resolver@^1.2.1":
  "integrity" "sha1-7NrmBMB3p/vHDe+21RfDwciYkjo="
  "resolved" "https://registry.npmmirror.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.1.tgz"
  "version" "1.2.1"

"jest-regex-util@^25.2.6":
  "integrity" "sha1-2EfTi6FdIRjTsGOQBWAo0PL9OWQ="
  "resolved" "https://registry.npmmirror.com/jest-regex-util/download/jest-regex-util-25.2.6.tgz"
  "version" "25.2.6"

"jest-resolve-dependencies@^25.5.4":
  "integrity" "sha1-hVAfU5V8jjvkRuhjp0d3taFzl6c="
  "resolved" "https://registry.npmmirror.com/jest-resolve-dependencies/download/jest-resolve-dependencies-25.5.4.tgz?cache=0&sync_timestamp=1588675308084&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-resolve-dependencies%2Fdownload%2Fjest-resolve-dependencies-25.5.4.tgz"
  "version" "25.5.4"
  dependencies:
    "@jest/types" "^25.5.0"
    "jest-regex-util" "^25.2.6"
    "jest-snapshot" "^25.5.1"

"jest-resolve@*", "jest-resolve@^25.5.1":
  "integrity" "sha1-Dm+8+nwm0qX+j0VgiNwzKnkmaCk="
  "resolved" "https://registry.npmmirror.com/jest-resolve/download/jest-resolve-25.5.1.tgz?cache=0&sync_timestamp=1588675323403&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-resolve%2Fdownload%2Fjest-resolve-25.5.1.tgz"
  "version" "25.5.1"
  dependencies:
    "@jest/types" "^25.5.0"
    "browser-resolve" "^1.11.3"
    "chalk" "^3.0.0"
    "graceful-fs" "^4.2.4"
    "jest-pnp-resolver" "^1.2.1"
    "read-pkg-up" "^7.0.1"
    "realpath-native" "^2.0.0"
    "resolve" "^1.17.0"
    "slash" "^3.0.0"

"jest-runner@^25.5.4":
  "integrity" "sha1-/+xd84ddpfXIeK5tChe45OzXxx0="
  "resolved" "https://registry.npmmirror.com/jest-runner/download/jest-runner-25.5.4.tgz?cache=0&sync_timestamp=1588675313164&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-runner%2Fdownload%2Fjest-runner-25.5.4.tgz"
  "version" "25.5.4"
  dependencies:
    "@jest/console" "^25.5.0"
    "@jest/environment" "^25.5.0"
    "@jest/test-result" "^25.5.0"
    "@jest/types" "^25.5.0"
    "chalk" "^3.0.0"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-config" "^25.5.4"
    "jest-docblock" "^25.3.0"
    "jest-haste-map" "^25.5.1"
    "jest-jasmine2" "^25.5.4"
    "jest-leak-detector" "^25.5.0"
    "jest-message-util" "^25.5.0"
    "jest-resolve" "^25.5.1"
    "jest-runtime" "^25.5.4"
    "jest-util" "^25.5.0"
    "jest-worker" "^25.5.0"
    "source-map-support" "^0.5.6"
    "throat" "^5.0.0"

"jest-runtime@^25.5.4":
  "integrity" "sha1-3Jgf4sshN6vNMZ50zK5/fu/7+qs="
  "resolved" "https://registry.npmmirror.com/jest-runtime/download/jest-runtime-25.5.4.tgz?cache=0&sync_timestamp=1588675313048&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-runtime%2Fdownload%2Fjest-runtime-25.5.4.tgz"
  "version" "25.5.4"
  dependencies:
    "@jest/console" "^25.5.0"
    "@jest/environment" "^25.5.0"
    "@jest/globals" "^25.5.2"
    "@jest/source-map" "^25.5.0"
    "@jest/test-result" "^25.5.0"
    "@jest/transform" "^25.5.1"
    "@jest/types" "^25.5.0"
    "@types/yargs" "^15.0.0"
    "chalk" "^3.0.0"
    "collect-v8-coverage" "^1.0.0"
    "exit" "^0.1.2"
    "glob" "^7.1.3"
    "graceful-fs" "^4.2.4"
    "jest-config" "^25.5.4"
    "jest-haste-map" "^25.5.1"
    "jest-message-util" "^25.5.0"
    "jest-mock" "^25.5.0"
    "jest-regex-util" "^25.2.6"
    "jest-resolve" "^25.5.1"
    "jest-snapshot" "^25.5.1"
    "jest-util" "^25.5.0"
    "jest-validate" "^25.5.0"
    "realpath-native" "^2.0.0"
    "slash" "^3.0.0"
    "strip-bom" "^4.0.0"
    "yargs" "^15.3.1"

"jest-serializer@^25.5.0":
  "integrity" "sha1-qZP0hOdptO1U5w4O/bdAB/UDBys="
  "resolved" "https://registry.npmmirror.com/jest-serializer/download/jest-serializer-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "graceful-fs" "^4.2.4"

"jest-snapshot@^25.5.1":
  "integrity" "sha1-GipXZJH5lh640AwuX9R5vCjl/38="
  "resolved" "https://registry.npmmirror.com/jest-snapshot/download/jest-snapshot-25.5.1.tgz?cache=0&sync_timestamp=1588675302868&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-snapshot%2Fdownload%2Fjest-snapshot-25.5.1.tgz"
  "version" "25.5.1"
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^25.5.0"
    "@types/prettier" "^1.19.0"
    "chalk" "^3.0.0"
    "expect" "^25.5.0"
    "graceful-fs" "^4.2.4"
    "jest-diff" "^25.5.0"
    "jest-get-type" "^25.2.6"
    "jest-matcher-utils" "^25.5.0"
    "jest-message-util" "^25.5.0"
    "jest-resolve" "^25.5.1"
    "make-dir" "^3.0.0"
    "natural-compare" "^1.4.0"
    "pretty-format" "^25.5.0"
    "semver" "^6.3.0"

"jest-util@^25.5.0":
  "integrity" "sha1-McY7XW6QEnTSZKT+yEkjCqP6NbA="
  "resolved" "https://registry.npmmirror.com/jest-util/download/jest-util-25.5.0.tgz?cache=0&sync_timestamp=1588675360560&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-util%2Fdownload%2Fjest-util-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "chalk" "^3.0.0"
    "graceful-fs" "^4.2.4"
    "is-ci" "^2.0.0"
    "make-dir" "^3.0.0"

"jest-validate@^25.5.0":
  "integrity" "sha1-+0yT8zLC5M9wFRpijlijXkWaQTo="
  "resolved" "https://registry.npmmirror.com/jest-validate/download/jest-validate-25.5.0.tgz?cache=0&sync_timestamp=1588675419522&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-validate%2Fdownload%2Fjest-validate-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "camelcase" "^5.3.1"
    "chalk" "^3.0.0"
    "jest-get-type" "^25.2.6"
    "leven" "^3.1.0"
    "pretty-format" "^25.5.0"

"jest-watcher@^25.5.0":
  "integrity" "sha1-1hENEB35i63r5DUAOVb9SkZehFY="
  "resolved" "https://registry.npmmirror.com/jest-watcher/download/jest-watcher-25.5.0.tgz?cache=0&sync_timestamp=1588675365689&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjest-watcher%2Fdownload%2Fjest-watcher-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/test-result" "^25.5.0"
    "@jest/types" "^25.5.0"
    "ansi-escapes" "^4.2.1"
    "chalk" "^3.0.0"
    "jest-util" "^25.5.0"
    "string-length" "^3.1.0"

"jest-worker@^25.4.0", "jest-worker@^25.5.0":
  "integrity" "sha1-JhHQcbec6g9D7lej0RhZOsFUfbE="
  "resolved" "https://registry.npmmirror.com/jest-worker/download/jest-worker-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "merge-stream" "^2.0.0"
    "supports-color" "^7.0.0"

"jest@^25.4.0":
  "integrity" "sha1-8hEHtkic/jKwds4q3K3uNYesuds="
  "resolved" "https://registry.npmmirror.com/jest/download/jest-25.5.4.tgz"
  "version" "25.5.4"
  dependencies:
    "@jest/core" "^25.5.4"
    "import-local" "^3.0.2"
    "jest-cli" "^25.5.4"

"jimp@^0.10.1":
  "integrity" "sha512-meVWmDMtyUG5uYjFkmzu0zBgnCvvxwWNi27c4cg55vWNVC9ES4Lcwb+ogx+uBBQE3Q+dLKjXaLl0JVW+nUNwbQ=="
  "resolved" "https://registry.npmmirror.com/jimp/-/jimp-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/custom" "^0.10.3"
    "@jimp/plugins" "^0.10.3"
    "@jimp/types" "^0.10.3"
    "core-js" "^3.4.1"
    "regenerator-runtime" "^0.13.3"

"jpeg-js@^0.3.4":
  "integrity" "sha512-9IXdWudL61npZjvLuVe/ktHiA41iE8qFyLB+4VDTblEsWBzeg8WQTlktdUK4CdncUqtUgUg0bbOmTE2bKBKaBQ=="
  "resolved" "https://registry.npmmirror.com/jpeg-js/-/jpeg-js-0.3.7.tgz"
  "version" "0.3.7"

"js-message@1.0.5":
  "integrity" "sha1-IwDSSxrwjondCVvBpMnJz8uJLRU="
  "resolved" "https://registry.npmmirror.com/js-message/download/js-message-1.0.5.tgz"
  "version" "1.0.5"

"js-queue@2.0.0":
  "integrity" "sha1-NiITz4YPRo8BJfxslqvBdCUx+Ug="
  "resolved" "https://registry.npmmirror.com/js-queue/download/js-queue-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "easy-stack" "^1.0.0"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="
  "resolved" "https://registry.npmmirror.com/js-tokens/download/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc="
  "resolved" "https://registry.npmmirror.com/js-yaml/download/js-yaml-3.13.1.tgz"
  "version" "3.13.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsbn@~0.1.0":
  "integrity" "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="
  "resolved" "https://registry.npmmirror.com/jsbn/download/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsdom@^15.2.1":
  "integrity" "sha1-0v6xrvcYP4a+UhuMaDP/UpbQfsU="
  "resolved" "https://registry.npmmirror.com/jsdom/download/jsdom-15.2.1.tgz"
  "version" "15.2.1"
  dependencies:
    "abab" "^2.0.0"
    "acorn" "^7.1.0"
    "acorn-globals" "^4.3.2"
    "array-equal" "^1.0.0"
    "cssom" "^0.4.1"
    "cssstyle" "^2.0.0"
    "data-urls" "^1.1.0"
    "domexception" "^1.0.1"
    "escodegen" "^1.11.1"
    "html-encoding-sniffer" "^1.0.2"
    "nwsapi" "^2.2.0"
    "parse5" "5.1.0"
    "pn" "^1.1.0"
    "request" "^2.88.0"
    "request-promise-native" "^1.0.7"
    "saxes" "^3.1.9"
    "symbol-tree" "^3.2.2"
    "tough-cookie" "^3.0.1"
    "w3c-hr-time" "^1.0.1"
    "w3c-xmlserializer" "^1.1.2"
    "webidl-conversions" "^4.0.2"
    "whatwg-encoding" "^1.0.5"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^7.0.0"
    "ws" "^7.0.0"
    "xml-name-validator" "^3.0.0"

"jsesc@^2.5.1":
  "integrity" "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q="
  "resolved" "https://registry.npmmirror.com/jsesc/download/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://registry.npmmirror.com/jsesc/download/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-better-errors@^1.0.1", "json-parse-better-errors@^1.0.2":
  "integrity" "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="
  "resolved" "https://registry.npmmirror.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-schema-traverse@^0.4.1":
  "integrity" "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="
  "resolved" "https://registry.npmmirror.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema@0.2.3":
  "integrity" "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="
  "resolved" "https://registry.npmmirror.com/json-schema/download/json-schema-0.2.3.tgz"
  "version" "0.2.3"

"json-stringify-safe@~5.0.1":
  "integrity" "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="
  "resolved" "https://registry.npmmirror.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json3@^3.3.2":
  "integrity" "sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E="
  "resolved" "https://registry.npmmirror.com/json3/download/json3-3.3.3.tgz"
  "version" "3.3.3"

"json5@^0.5.0":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "https://registry.npmmirror.com/json5/download/json5-0.5.1.tgz?cache=0&sync_timestamp=1586046271069&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^1.0.1":
  "integrity" "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4="
  "resolved" "https://registry.npmmirror.com/json5/download/json5-1.0.1.tgz?cache=0&sync_timestamp=1586046271069&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.2":
  "integrity" "sha1-ybD3+pIzv+WAf+ZvzzpWF+1ZfUM="
  "resolved" "https://registry.npmmirror.com/json5/download/json5-2.1.3.tgz?cache=0&sync_timestamp=1586046271069&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "minimist" "^1.2.5"

"jsonfile@^4.0.0":
  "integrity" "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss="
  "resolved" "https://registry.npmmirror.com/jsonfile/download/jsonfile-4.0.0.tgz"
  "version" "4.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsprim@^1.2.2":
  "integrity" "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI="
  "resolved" "https://registry.npmmirror.com/jsprim/download/jsprim-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.2.3"
    "verror" "1.10.0"

"jsrsasign@^7.2.2":
  "integrity" "sha1-rlIwy1V0RRu5eanMaXQoxg9ZjSA="
  "resolved" "https://registry.npmmirror.com/jsrsasign/download/jsrsasign-7.2.2.tgz"
  "version" "7.2.2"

"jszip@^3.1.5", "jszip@^3.2.0":
  "integrity" "sha1-GmlCH6Xwu5vCIqRryogYL7oHU1A="
  "resolved" "https://registry.npmmirror.com/jszip/download/jszip-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "lie" "~3.3.0"
    "pako" "~1.0.2"
    "readable-stream" "~2.3.6"
    "set-immediate-shim" "~1.0.1"

"keygrip@~1.1.0":
  "integrity" "sha1-hxsWgdXhWcYqRFsMdLYV4JF+ciY="
  "resolved" "https://registry.npmmirror.com/keygrip/download/keygrip-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "tsscmp" "1.0.6"

"killable@^1.0.1":
  "integrity" "sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI="
  "resolved" "https://registry.npmmirror.com/killable/download/killable-1.0.1.tgz"
  "version" "1.0.1"

"kind-of@^3.0.2", "kind-of@^3.0.3", "kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0":
  "integrity" "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"kind-of@^6.0.2":
  "integrity" "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"kleur@^3.0.3":
  "integrity" "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4="
  "resolved" "https://registry.npmmirror.com/kleur/download/kleur-3.0.3.tgz"
  "version" "3.0.3"

"klona@^2.0.4":
  "integrity" "sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA=="
  "resolved" "https://registry.npmmirror.com/klona/-/klona-2.0.6.tgz"
  "version" "2.0.6"

"koa-body@^4.0.8":
  "integrity" "sha1-E4HJG9Le9wRLp5AEW/PPss27TFE="
  "resolved" "https://registry.npmmirror.com/koa-body/download/koa-body-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "@types/formidable" "^1.0.31"
    "co-body" "^5.1.1"
    "formidable" "^1.1.1"

"koa-bodyparser@^4.2.1":
  "integrity" "sha1-J0x3hVX/SPoiHufzap+9us4idZo="
  "resolved" "https://registry.npmmirror.com/koa-bodyparser/download/koa-bodyparser-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "co-body" "^6.0.0"
    "copy-to" "^2.0.1"

"koa-compose@^3.0.0":
  "integrity" "sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec="
  "resolved" "https://registry.npmmirror.com/koa-compose/download/koa-compose-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "any-promise" "^1.1.0"

"koa-compose@^4.1.0":
  "integrity" "sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc="
  "resolved" "https://registry.npmmirror.com/koa-compose/download/koa-compose-4.1.0.tgz"
  "version" "4.1.0"

"koa-convert@^1.2.0":
  "integrity" "sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA="
  "resolved" "https://registry.npmmirror.com/koa-convert/download/koa-convert-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "co" "^4.6.0"
    "koa-compose" "^3.0.0"

"koa-mount@^4.0.0":
  "integrity" "sha1-4CZeWBmOGhTviJUUxgclT/OGMpw="
  "resolved" "https://registry.npmmirror.com/koa-mount/download/koa-mount-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "debug" "^4.0.1"
    "koa-compose" "^4.1.0"

"koa-router@^7.4.0":
  "integrity" "sha1-ruH3rcAtXLMdfWdGXJ6syCXoxeA="
  "resolved" "https://registry.npmmirror.com/koa-router/download/koa-router-7.4.0.tgz"
  "version" "7.4.0"
  dependencies:
    "debug" "^3.1.0"
    "http-errors" "^1.3.1"
    "koa-compose" "^3.0.0"
    "methods" "^1.0.1"
    "path-to-regexp" "^1.1.1"
    "urijs" "^1.19.0"

"koa-send@^5.0.0":
  "integrity" "sha1-XoRB4H71VzdzTXztJbhC5QZG5+s="
  "resolved" "https://registry.npmmirror.com/koa-send/download/koa-send-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "debug" "^3.1.0"
    "http-errors" "^1.6.3"
    "mz" "^2.7.0"
    "resolve-path" "^1.4.0"

"koa-static@^5.0.0":
  "integrity" "sha1-XpL8lrU3rVIZ9CUxnJW2R3J3aUM="
  "resolved" "https://registry.npmmirror.com/koa-static/download/koa-static-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "debug" "^3.1.0"
    "koa-send" "^5.0.0"

"koa@^2.7.0":
  "integrity" "sha1-ySv7Qt79hvNlwxv2P+kY2xH8XHQ="
  "resolved" "https://registry.npmmirror.com/koa/download/koa-2.12.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fkoa%2Fdownload%2Fkoa-2.12.0.tgz"
  "version" "2.12.0"
  dependencies:
    "accepts" "^1.3.5"
    "cache-content-type" "^1.0.0"
    "content-disposition" "~0.5.2"
    "content-type" "^1.0.4"
    "cookies" "~0.8.0"
    "debug" "~3.1.0"
    "delegates" "^1.0.0"
    "depd" "^1.1.2"
    "destroy" "^1.0.4"
    "encodeurl" "^1.0.2"
    "escape-html" "^1.0.3"
    "fresh" "~0.5.2"
    "http-assert" "^1.3.0"
    "http-errors" "^1.6.3"
    "is-generator-function" "^1.0.7"
    "koa-compose" "^4.1.0"
    "koa-convert" "^1.2.0"
    "on-finished" "^2.3.0"
    "only" "~0.0.2"
    "parseurl" "^1.3.2"
    "statuses" "^1.5.0"
    "type-is" "^1.6.16"
    "vary" "^1.1.2"

"launch-editor-middleware@^2.2.1":
  "integrity" "sha1-4UsH5scVSwpLhqD9NFeE5FgEwVc="
  "resolved" "https://registry.npmmirror.com/launch-editor-middleware/download/launch-editor-middleware-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "launch-editor" "^2.2.1"

"launch-editor@^2.2.1":
  "integrity" "sha1-hxtaPuOdZoD8wm03kwtu7aidsMo="
  "resolved" "https://registry.npmmirror.com/launch-editor/download/launch-editor-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "chalk" "^2.3.0"
    "shell-quote" "^1.6.1"

"leven@^3.1.0":
  "integrity" "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I="
  "resolved" "https://registry.npmmirror.com/leven/download/leven-3.1.0.tgz"
  "version" "3.1.0"

"levenary@^1.1.1":
  "integrity" "sha1-hCqe6Y0gdap/ru2+MmeekgX0b3c="
  "resolved" "https://registry.npmmirror.com/levenary/download/levenary-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "leven" "^3.1.0"

"levn@~0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "https://registry.npmmirror.com/levn/download/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"licia@^1.21.0":
  "integrity" "sha1-wBf1WmXPz2TP+Y5kN9LoDBivTEM="
  "resolved" "https://registry.npmmirror.com/licia/download/licia-1.21.2.tgz"
  "version" "1.21.2"

"lie@~3.3.0":
  "integrity" "sha1-3Pgt7lRfRgdNryAMfBxaCOD0D2o="
  "resolved" "https://registry.npmmirror.com/lie/download/lie-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "immediate" "~3.0.5"

"lines-and-columns@^1.1.6":
  "integrity" "sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA="
  "resolved" "https://registry.npmmirror.com/lines-and-columns/download/lines-and-columns-1.1.6.tgz"
  "version" "1.1.6"

"load-bmfont@^1.3.1", "load-bmfont@^1.4.0":
  "integrity" "sha512-qElWkmjW9Oq1F9EI5Gt7aD9zcdHb9spJCW1L/dmPf7KzCCEJxq8nhHz5eCgI9aMf7vrG/wyaCqdsI+Iy9ZTlog=="
  "resolved" "https://registry.npmmirror.com/load-bmfont/-/load-bmfont-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "buffer-equal" "0.0.1"
    "mime" "^1.3.4"
    "parse-bmfont-ascii" "^1.0.3"
    "parse-bmfont-binary" "^1.0.5"
    "parse-bmfont-xml" "^1.1.4"
    "phin" "^3.7.1"
    "xhr" "^2.0.1"
    "xtend" "^4.0.0"

"loader-runner@^2.3.1", "loader-runner@^2.4.0":
  "integrity" "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c="
  "resolved" "https://registry.npmmirror.com/loader-runner/download/loader-runner-2.4.0.tgz"
  "version" "2.4.0"

"loader-utils@^0.2.16":
  "integrity" "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g="
  "resolved" "https://registry.npmmirror.com/loader-utils/download/loader-utils-0.2.17.tgz"
  "version" "0.2.17"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"
    "object-assign" "^4.0.1"

"loader-utils@^1.0.2", "loader-utils@^1.1.0", "loader-utils@^1.2.3", "loader-utils@^1.4.0":
  "integrity" "sha1-xXm140yzSxp07cbB+za/o3HVphM="
  "resolved" "https://registry.npmmirror.com/loader-utils/download/loader-utils-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^2.0.0":
  "integrity" "sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw=="
  "resolved" "https://registry.npmmirror.com/loader-utils/-/loader-utils-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"locate-path@^2.0.0":
  "integrity" "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4="
  "resolved" "https://registry.npmmirror.com/locate-path/download/locate-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-locate" "^2.0.0"
    "path-exists" "^3.0.0"

"locate-path@^3.0.0":
  "integrity" "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4="
  "resolved" "https://registry.npmmirror.com/locate-path/download/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"locate-path@^5.0.0":
  "integrity" "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA="
  "resolved" "https://registry.npmmirror.com/locate-path/download/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash.defaultsdeep@^4.6.1":
  "integrity" "sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY="
  "resolved" "https://registry.npmmirror.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz"
  "version" "4.6.1"

"lodash.kebabcase@^4.1.1":
  "integrity" "sha1-hImxyw0p/4gZXM7KRI/21swpXDY="
  "resolved" "https://registry.npmmirror.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.mapvalues@^4.6.0":
  "integrity" "sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw="
  "resolved" "https://registry.npmmirror.com/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz"
  "version" "4.6.0"

"lodash.memoize@^4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://registry.npmmirror.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.sortby@^4.7.0":
  "integrity" "sha1-7dFMgk4sycHgsKG0K7UhBRakJDg="
  "resolved" "https://registry.npmmirror.com/lodash.sortby/download/lodash.sortby-4.7.0.tgz"
  "version" "4.7.0"

"lodash.transform@^4.6.0":
  "integrity" "sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A="
  "resolved" "https://registry.npmmirror.com/lodash.transform/download/lodash.transform-4.6.0.tgz"
  "version" "4.6.0"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://registry.npmmirror.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.17.11", "lodash@^4.17.13", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.3", "lodash@^4.17.4", "lodash@^4.2.1":
  "integrity" "sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg="
  "resolved" "https://registry.npmmirror.com/lodash/download/lodash-4.17.15.tgz"
  "version" "4.17.15"

"log-symbols@^2.2.0":
  "integrity" "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo="
  "resolved" "https://registry.npmmirror.com/log-symbols/download/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"loglevel@^1.6.8":
  "integrity" "sha1-iiX7ddCSIw7NRFcnDYC1TigBEXE="
  "resolved" "https://registry.npmmirror.com/loglevel/download/loglevel-1.6.8.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Floglevel%2Fdownload%2Floglevel-1.6.8.tgz"
  "version" "1.6.8"

"lolex@^5.0.0":
  "integrity" "sha1-lTaU0JjOfAe8XtbQ5CvGwMbVo2c="
  "resolved" "https://registry.npmmirror.com/lolex/download/lolex-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"loose-envify@^1.0.0":
  "integrity" "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8="
  "resolved" "https://registry.npmmirror.com/loose-envify/download/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lower-case@^1.1.1":
  "integrity" "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="
  "resolved" "https://registry.npmmirror.com/lower-case/download/lower-case-1.1.4.tgz"
  "version" "1.1.4"

"lru-cache@^4.1.2":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npmmirror.com/lru-cache/download/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^5.1.1":
  "integrity" "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA="
  "resolved" "https://registry.npmmirror.com/lru-cache/download/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"make-dir@^2.0.0":
  "integrity" "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="
  "resolved" "https://registry.npmmirror.com/make-dir/download/make-dir-2.1.0.tgz?cache=0&sync_timestamp=1587567610342&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmake-dir%2Fdownload%2Fmake-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^2.1.0":
  "integrity" "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="
  "resolved" "https://registry.npmmirror.com/make-dir/download/make-dir-2.1.0.tgz?cache=0&sync_timestamp=1587567610342&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmake-dir%2Fdownload%2Fmake-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^3.0.0", "make-dir@^3.0.2":
  "integrity" "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8="
  "resolved" "https://registry.npmmirror.com/make-dir/download/make-dir-3.1.0.tgz?cache=0&sync_timestamp=1587567610342&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmake-dir%2Fdownload%2Fmake-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"makeerror@1.0.x":
  "integrity" "sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw="
  "resolved" "https://registry.npmmirror.com/makeerror/download/makeerror-1.0.11.tgz"
  "version" "1.0.11"
  dependencies:
    "tmpl" "1.0.x"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://registry.npmmirror.com/map-cache/download/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://registry.npmmirror.com/map-visit/download/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"md5.js@^1.3.4":
  "integrity" "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8="
  "resolved" "https://registry.npmmirror.com/md5.js/download/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"md5@^2.2.1":
  "integrity" "sha1-U6s41f48iJG6RlMp6iP6wFQBJvk="
  "resolved" "https://registry.npmmirror.com/md5/download/md5-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "charenc" "~0.0.1"
    "crypt" "~0.0.1"
    "is-buffer" "~1.1.1"

"mdn-data@2.0.4":
  "integrity" "sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs="
  "resolved" "https://registry.npmmirror.com/mdn-data/download/mdn-data-2.0.4.tgz?cache=0&sync_timestamp=1584029100530&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.4.tgz"
  "version" "2.0.4"

"mdn-data@2.0.6":
  "integrity" "sha1-hS3GD8ql2qLoz2yRicRA7T4EKXg="
  "resolved" "https://registry.npmmirror.com/mdn-data/download/mdn-data-2.0.6.tgz?cache=0&sync_timestamp=1584029100530&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.6.tgz"
  "version" "2.0.6"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://registry.npmmirror.com/media-typer/download/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memory-fs@^0.4.1":
  "integrity" "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="
  "resolved" "https://registry.npmmirror.com/memory-fs/download/memory-fs-0.4.1.tgz?cache=0&sync_timestamp=1570537491040&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmemory-fs%2Fdownload%2Fmemory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"memory-fs@^0.5.0":
  "integrity" "sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw="
  "resolved" "https://registry.npmmirror.com/memory-fs/download/memory-fs-0.5.0.tgz?cache=0&sync_timestamp=1570537491040&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmemory-fs%2Fdownload%2Fmemory-fs-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://registry.npmmirror.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"merge-source-map@^1.1.0":
  "integrity" "sha1-L93n5gIJOfcJBqaPLXrmheTIxkY="
  "resolved" "https://registry.npmmirror.com/merge-source-map/download/merge-source-map-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "source-map" "^0.6.1"

"merge-stream@^2.0.0":
  "integrity" "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A="
  "resolved" "https://registry.npmmirror.com/merge-stream/download/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge@^1.2.1":
  "integrity" "sha1-OL6/gMMiCopIe2/Ps5QbsRcgwUU="
  "resolved" "https://registry.npmmirror.com/merge/download/merge-1.2.1.tgz"
  "version" "1.2.1"

"merge2@^1.2.3":
  "integrity" "sha1-WzZu6DsvFYLEj4fkfPGpNSEDyoE="
  "resolved" "https://registry.npmmirror.com/merge2/download/merge2-1.3.0.tgz"
  "version" "1.3.0"

"methods@^1.0.1", "methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://registry.npmmirror.com/methods/download/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^3.1.10", "micromatch@^3.1.4":
  "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
  "resolved" "https://registry.npmmirror.com/micromatch/download/micromatch-3.1.10.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^4.0.2":
  "integrity" "sha1-T8sJmb+fvC/L3SEvbWKbmlbDklk="
  "resolved" "https://registry.npmmirror.com/micromatch/download/micromatch-4.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "braces" "^3.0.1"
    "picomatch" "^2.0.5"

"miller-rabin@^4.0.0":
  "integrity" "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0="
  "resolved" "https://registry.npmmirror.com/miller-rabin/download/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@>= 1.43.0 < 2", "mime-db@1.44.0":
  "integrity" "sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I="
  "resolved" "https://registry.npmmirror.com/mime-db/download/mime-db-1.44.0.tgz?cache=0&sync_timestamp=1587603398892&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-db%2Fdownload%2Fmime-db-1.44.0.tgz"
  "version" "1.44.0"

"mime-types@^2.1.12", "mime-types@^2.1.18", "mime-types@~2.1.17", "mime-types@~2.1.19", "mime-types@~2.1.24":
  "integrity" "sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8="
  "resolved" "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.27.tgz?cache=0&sync_timestamp=1587700357177&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.27.tgz"
  "version" "2.1.27"
  dependencies:
    "mime-db" "1.44.0"

"mime@^1.3.4":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mime@^2.0.3", "mime@^2.4.4":
  "integrity" "sha1-2N4uy5KYLe27ZUHJtoQdfyGOoAk="
  "resolved" "https://registry.npmmirror.com/mime/download/mime-2.4.5.tgz"
  "version" "2.4.5"

"mime@1.6.0":
  "integrity" "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="
  "resolved" "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^1.0.0":
  "integrity" "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI="
  "resolved" "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@^2.1.0":
  "integrity" "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="
  "resolved" "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"min-document@^2.19.0":
  "integrity" "sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ=="
  "resolved" "https://registry.npmmirror.com/min-document/-/min-document-2.19.0.tgz"
  "version" "2.19.0"
  dependencies:
    "dom-walk" "^0.1.0"

"mini-css-extract-plugin@^0.5.0":
  "integrity" "sha1-rABZsCuWklFaY3EVsMyf7To1x7A="
  "resolved" "https://registry.npmmirror.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.5.0.tgz?cache=0&sync_timestamp=1576856580721&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmini-css-extract-plugin%2Fdownload%2Fmini-css-extract-plugin-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "schema-utils" "^1.0.0"
    "webpack-sources" "^1.1.0"

"mini-css-extract-plugin@^0.9.0":
  "integrity" "sha1-R/LPB6oWWrNXM7H8l9TEbAVkM54="
  "resolved" "https://registry.npmmirror.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.9.0.tgz?cache=0&sync_timestamp=1576856580721&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmini-css-extract-plugin%2Fdownload%2Fmini-css-extract-plugin-0.9.0.tgz"
  "version" "0.9.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "normalize-url" "1.9.1"
    "schema-utils" "^1.0.0"
    "webpack-sources" "^1.1.0"

"mini-types@*":
  "integrity" "sha1-e31OHb/gGwzlwbIJelpTFUxi2xs="
  "resolved" "https://registry.npmmirror.com/mini-types/download/mini-types-0.1.2.tgz"
  "version" "0.1.2"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc="
  "resolved" "https://registry.npmmirror.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.0", "minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="
  "resolved" "https://registry.npmmirror.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4":
  "integrity" "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM="
  "resolved" "https://registry.npmmirror.com/minimatch/download/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.1.1", "minimist@^1.2.0", "minimist@^1.2.5":
  "integrity" "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="
  "resolved" "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz"
  "version" "1.2.5"

"minipass-collect@^1.0.2":
  "integrity" "sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc="
  "resolved" "https://registry.npmmirror.com/minipass-collect/download/minipass-collect-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minipass" "^3.0.0"

"minipass-flush@^1.0.5":
  "integrity" "sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M="
  "resolved" "https://registry.npmmirror.com/minipass-flush/download/minipass-flush-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "minipass" "^3.0.0"

"minipass-pipeline@^1.2.2":
  "integrity" "sha1-VfeDkwfXSFnW6K2pw+vnLOwhajQ="
  "resolved" "https://registry.npmmirror.com/minipass-pipeline/download/minipass-pipeline-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "minipass" "^3.0.0"

"minipass@^3.0.0", "minipass@^3.1.1":
  "integrity" "sha1-fUL/HzljVILhX5zbUxhN7r1YFf0="
  "resolved" "https://registry.npmmirror.com/minipass/download/minipass-3.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fminipass%2Fdownload%2Fminipass-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "yallist" "^4.0.0"

"miniprogram-api-typings@*":
  "integrity" "sha1-wKkcov6oDjRFCTmcWAy61QSo+QU="
  "resolved" "https://registry.npmmirror.com/miniprogram-api-typings/download/miniprogram-api-typings-2.11.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fminiprogram-api-typings%2Fdownload%2Fminiprogram-api-typings-2.11.0.tgz"
  "version" "2.11.0"

"mississippi@^3.0.0":
  "integrity" "sha1-6goykfl+C16HdrNj1fChLZTGcCI="
  "resolved" "https://registry.npmmirror.com/mississippi/download/mississippi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^3.0.0"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mixin-deep@^1.2.0":
  "integrity" "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY="
  "resolved" "https://registry.npmmirror.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp-classic@^0.5.2":
  "integrity" "sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A=="
  "resolved" "https://registry.npmmirror.com/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz"
  "version" "0.5.3"

"mkdirp@^0.5.1", "mkdirp@^0.5.3", "mkdirp@~0.5.1":
  "integrity" "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8="
  "resolved" "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1587535418745&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "minimist" "^1.2.5"

"module-alias@^2.1.0":
  "integrity" "sha1-FRzc7MJOJXOf8KpuUeHFcWl0wOA="
  "resolved" "https://registry.npmmirror.com/module-alias/download/module-alias-2.2.2.tgz"
  "version" "2.2.2"

"moment@^2.24.0":
  "integrity" "sha1-Xh+Cxrr8pug+gIswyHBe7Q3L05o="
  "resolved" "https://registry.npmmirror.com/moment/download/moment-2.26.0.tgz"
  "version" "2.26.0"

"move-concurrently@^1.0.1":
  "integrity" "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="
  "resolved" "https://registry.npmmirror.com/move-concurrently/download/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "^1.1.1"
    "copy-concurrently" "^1.0.0"
    "fs-write-stream-atomic" "^1.0.8"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.3"

"ms@^2.1.1":
  "integrity" "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="
  "resolved" "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.1":
  "integrity" "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="
  "resolved" "https://registry.npmmirror.com/ms/download/ms-2.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.1.tgz"
  "version" "2.1.1"

"multicast-dns-service-types@^1.1.0":
  "integrity" "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="
  "resolved" "https://registry.npmmirror.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@^6.0.1":
  "integrity" "sha1-oOx72QVcQoL3kMPIL04o2zsxsik="
  "resolved" "https://registry.npmmirror.com/multicast-dns/download/multicast-dns-6.2.3.tgz?cache=0&sync_timestamp=1585239065356&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmulticast-dns%2Fdownload%2Fmulticast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "^1.3.1"
    "thunky" "^1.0.2"

"mustache@^3.1.0":
  "integrity" "sha1-ieeKnSB9ePJ5mx6Vdkolv3GigyI="
  "resolved" "https://registry.npmmirror.com/mustache/download/mustache-3.2.1.tgz"
  "version" "3.2.1"

"mz@^2.4.0", "mz@^2.7.0":
  "integrity" "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI="
  "resolved" "https://registry.npmmirror.com/mz/download/mz-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "any-promise" "^1.0.0"
    "object-assign" "^4.0.1"
    "thenify-all" "^1.0.0"

"nan@^2.12.1":
  "integrity" "sha1-174036MQW5FJTDFHCJMV7/iHSwE="
  "resolved" "https://registry.npmmirror.com/nan/download/nan-2.14.1.tgz"
  "version" "2.14.1"

"nanomatch@^1.2.9":
  "integrity" "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk="
  "resolved" "https://registry.npmmirror.com/nanomatch/download/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://registry.npmmirror.com/natural-compare/download/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.2":
  "integrity" "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="
  "resolved" "https://registry.npmmirror.com/negotiator/download/negotiator-0.6.2.tgz"
  "version" "0.6.2"

"neo-async@^2.5.0", "neo-async@^2.6.0", "neo-async@^2.6.1":
  "integrity" "sha1-rCetpmFn+ohJpq3dg39rGJrSCBw="
  "resolved" "https://registry.npmmirror.com/neo-async/download/neo-async-2.6.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fneo-async%2Fdownload%2Fneo-async-2.6.1.tgz"
  "version" "2.6.1"

"neo-async@^2.6.2":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"nice-try@^1.0.4":
  "integrity" "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="
  "resolved" "https://registry.npmmirror.com/nice-try/download/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"no-case@^2.2.0":
  "integrity" "sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw="
  "resolved" "https://registry.npmmirror.com/no-case/download/no-case-2.3.2.tgz?cache=0&sync_timestamp=1576748705107&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fno-case%2Fdownload%2Fno-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "^1.1.1"

"node-forge@^0.7.1":
  "integrity" "sha512-sol30LUpz1jQFBjOKwbjxijiE3b6pjd74YwfD0fJOKPjF+fONKb2Yg8rYgS6+bK6VDl+/wfr4IYpC7jDzLUIfw=="
  "resolved" "https://registry.npmmirror.com/node-forge/-/node-forge-0.7.6.tgz"
  "version" "0.7.6"

"node-forge@0.9.0":
  "integrity" "sha1-1iQFDtu0SHStyhK7mlLsY8t4JXk="
  "resolved" "https://registry.npmmirror.com/node-forge/download/node-forge-0.9.0.tgz?cache=0&sync_timestamp=1569524669712&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-forge%2Fdownload%2Fnode-forge-0.9.0.tgz"
  "version" "0.9.0"

"node-int64@^0.4.0":
  "integrity" "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs="
  "resolved" "https://registry.npmmirror.com/node-int64/download/node-int64-0.4.0.tgz"
  "version" "0.4.0"

"node-ipc@^9.1.1":
  "integrity" "sha1-TiRe1pOOZRAOWV68XcNLFujdXWk="
  "resolved" "https://registry.npmmirror.com/node-ipc/download/node-ipc-9.1.1.tgz"
  "version" "9.1.1"
  dependencies:
    "event-pubsub" "4.3.0"
    "js-message" "1.0.5"
    "js-queue" "2.0.0"

"node-libs-browser@^2.2.1":
  "integrity" "sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU="
  "resolved" "https://registry.npmmirror.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^3.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.1"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.11.0"
    "vm-browserify" "^1.0.1"

"node-modules-regexp@^1.0.0":
  "integrity" "sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA="
  "resolved" "https://registry.npmmirror.com/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz"
  "version" "1.0.0"

"node-notifier@^6.0.0":
  "integrity" "sha1-zqMZ4GuqFt7sjOXNfxM8Ska2jhI="
  "resolved" "https://registry.npmmirror.com/node-notifier/download/node-notifier-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "growly" "^1.3.0"
    "is-wsl" "^2.1.1"
    "semver" "^6.3.0"
    "shellwords" "^0.1.1"
    "which" "^1.3.1"

"node-releases@^1.1.53":
  "integrity" "sha1-vAVKQX0xbjrayQ6vt+GTKALyhwU="
  "resolved" "https://registry.npmmirror.com/node-releases/download/node-releases-1.1.56.tgz?cache=0&sync_timestamp=1589962280441&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-releases%2Fdownload%2Fnode-releases-1.1.56.tgz"
  "version" "1.1.56"

"node-simctl@^6.1.0":
  "integrity" "sha512-157a3XqQFatcPT8BijH3IQml/GW8qByVjhe04reG86SawyJGfosM3s+qugd1kaar3nsKo+ad6KSS4GB7e9fxig=="
  "resolved" "https://registry.npmmirror.com/node-simctl/-/node-simctl-6.6.0.tgz"
  "version" "6.6.0"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "asyncbox" "^2.3.1"
    "bluebird" "^3.5.1"
    "lodash" "^4.2.1"
    "npmlog" "^5.0.0"
    "rimraf" "^3.0.0"
    "semver" "^7.0.0"
    "source-map-support" "^0.5.5"
    "teen_process" "^1.5.1"
    "uuid" "^8.0.0"
    "which" "^2.0.0"

"normalize-package-data@^2.5.0":
  "integrity" "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg="
  "resolved" "https://registry.npmmirror.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://registry.npmmirror.com/normalize-path/download/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="
  "resolved" "https://registry.npmmirror.com/normalize-path/download/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "https://registry.npmmirror.com/normalize-range/download/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^3.0.0":
  "integrity" "sha1-suHE3E98bVd0PfczpPWXjRhlBVk="
  "resolved" "https://registry.npmmirror.com/normalize-url/download/normalize-url-3.3.0.tgz"
  "version" "3.3.0"

"normalize-url@1.9.1":
  "integrity" "sha1-LMDWazHqIwNkWENuNiDYWVTGbDw="
  "resolved" "https://registry.npmmirror.com/normalize-url/download/normalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^4.0.0":
  "integrity" "sha1-t+zR5e1T2o43pV4cImnguX7XSOo="
  "resolved" "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"npmlog@^5.0.0":
  "integrity" "sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw=="
  "resolved" "https://registry.npmmirror.com/npmlog/-/npmlog-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "are-we-there-yet" "^2.0.0"
    "console-control-strings" "^1.1.0"
    "gauge" "^3.0.0"
    "set-blocking" "^2.0.0"

"nth-check@^1.0.2", "nth-check@~1.0.1":
  "integrity" "sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw="
  "resolved" "https://registry.npmmirror.com/nth-check/download/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"num2fraction@^1.2.2":
  "integrity" "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4="
  "resolved" "https://registry.npmmirror.com/num2fraction/download/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"nwsapi@^2.2.0":
  "integrity" "sha1-IEh5qePQaP8qVROcLHcngGgaOLc="
  "resolved" "https://registry.npmmirror.com/nwsapi/download/nwsapi-2.2.0.tgz"
  "version" "2.2.0"

"oauth-sign@~0.9.0":
  "integrity" "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU="
  "resolved" "https://registry.npmmirror.com/oauth-sign/download/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-component@0.0.3":
  "integrity" "sha1-8MaapQ78lbhmwYb0AKM3acsvEpE="
  "resolved" "https://registry.npmmirror.com/object-component/download/object-component-0.0.3.tgz"
  "version" "0.0.3"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://registry.npmmirror.com/object-copy/download/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-inspect@^1.7.0":
  "integrity" "sha1-9Pa9GBrXfwBrXs5gvQtvOY/3Smc="
  "resolved" "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.7.0.tgz"
  "version" "1.7.0"

"object-is@^1.0.1":
  "integrity" "sha1-xdLof/nhGfeLegiEQVGeLuwVc7Y="
  "resolved" "https://registry.npmmirror.com/object-is/download/object-is-1.1.2.tgz?cache=0&sync_timestamp=1586894009620&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-is%2Fdownload%2Fobject-is-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.5"

"object-keys@^1.0.11", "object-keys@^1.0.12", "object-keys@^1.1.1":
  "integrity" "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="
  "resolved" "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://registry.npmmirror.com/object-visit/download/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.1.0":
  "integrity" "sha1-lovxEA15Vrs8oIbwBvhGs7xACNo="
  "resolved" "https://registry.npmmirror.com/object.assign/download/object.assign-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "define-properties" "^1.1.2"
    "function-bind" "^1.1.1"
    "has-symbols" "^1.0.0"
    "object-keys" "^1.0.11"

"object.getownpropertydescriptors@^2.0.3", "object.getownpropertydescriptors@^2.1.0":
  "integrity" "sha1-Npvx+VktiridcS3O1cuBx8U1Jkk="
  "resolved" "https://registry.npmmirror.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.getownpropertydescriptors%2Fdownload%2Fobject.getownpropertydescriptors-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.0-next.1"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://registry.npmmirror.com/object.pick/download/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"object.values@^1.1.0":
  "integrity" "sha1-aKmezeNWt+kpWjxeDOMdyMlT3l4="
  "resolved" "https://registry.npmmirror.com/object.values/download/object.values-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.0-next.1"
    "function-bind" "^1.1.1"
    "has" "^1.0.3"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4="
  "resolved" "https://registry.npmmirror.com/obuf/download/obuf-1.1.2.tgz"
  "version" "1.1.2"

"omggif@^1.0.9":
  "integrity" "sha512-LMJTtvgc/nugXj0Vcrrs68Mn2D1r0zf630VNtqtpI1FEO7e+O9FP4gqs9AcnBaSEeoHIPm28u6qgPR0oyEpGSw=="
  "resolved" "https://registry.npmmirror.com/omggif/-/omggif-1.0.10.tgz"
  "version" "1.0.10"

"on-finished@^2.3.0", "on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://registry.npmmirror.com/on-finished/download/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8="
  "resolved" "https://registry.npmmirror.com/on-headers/download/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.npmmirror.com/once/download/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "https://registry.npmmirror.com/onetime/download/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"onetime@^5.1.0":
  "integrity" "sha1-//DzyRYX/mK7UBiWNumayKbfe+U="
  "resolved" "https://registry.npmmirror.com/onetime/download/onetime-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "mimic-fn" "^2.1.0"

"only@~0.0.2":
  "integrity" "sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q="
  "resolved" "https://registry.npmmirror.com/only/download/only-0.0.2.tgz"
  "version" "0.0.2"

"open@^6.3.0":
  "integrity" "sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk="
  "resolved" "https://registry.npmmirror.com/open/download/open-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "is-wsl" "^1.1.0"

"opener@^1.5.1":
  "integrity" "sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0="
  "resolved" "https://registry.npmmirror.com/opener/download/opener-1.5.1.tgz"
  "version" "1.5.1"

"opn@^5.4.0", "opn@^5.5.0":
  "integrity" "sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w="
  "resolved" "https://registry.npmmirror.com/opn/download/opn-5.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fopn%2Fdownload%2Fopn-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optionator@^0.8.1":
  "integrity" "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU="
  "resolved" "https://registry.npmmirror.com/optionator/download/optionator-0.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Foptionator%2Fdownload%2Foptionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.6"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "word-wrap" "~1.2.3"

"ora@^3.4.0":
  "integrity" "sha1-vwdSSRBZo+8+1MhQl1Md6f280xg="
  "resolved" "https://registry.npmmirror.com/ora/download/ora-3.4.0.tgz?cache=0&sync_timestamp=1587481470351&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fora%2Fdownload%2Fora-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "chalk" "^2.4.2"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^2.0.0"
    "log-symbols" "^2.2.0"
    "strip-ansi" "^5.2.0"
    "wcwidth" "^1.0.1"

"original@^1.0.0":
  "integrity" "sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8="
  "resolved" "https://registry.npmmirror.com/original/download/original-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "url-parse" "^1.4.3"

"os-browserify@^0.3.0":
  "integrity" "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="
  "resolved" "https://registry.npmmirror.com/os-browserify/download/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"p-each-series@^2.1.0":
  "integrity" "sha1-lhyN0/GV6pbHR+Y2smK4AKaxr0g="
  "resolved" "https://registry.npmmirror.com/p-each-series/download/p-each-series-2.1.0.tgz"
  "version" "2.1.0"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://registry.npmmirror.com/p-finally/download/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-finally@^2.0.0":
  "integrity" "sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE="
  "resolved" "https://registry.npmmirror.com/p-finally/download/p-finally-2.0.1.tgz"
  "version" "2.0.1"

"p-limit@^1.1.0":
  "integrity" "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg="
  "resolved" "https://registry.npmmirror.com/p-limit/download/p-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "^1.0.0"

"p-limit@^2.0.0", "p-limit@^2.2.0", "p-limit@^2.2.1", "p-limit@^2.3.0":
  "integrity" "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE="
  "resolved" "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^2.0.0":
  "integrity" "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM="
  "resolved" "https://registry.npmmirror.com/p-locate/download/p-locate-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-limit" "^1.1.0"

"p-locate@^3.0.0":
  "integrity" "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ="
  "resolved" "https://registry.npmmirror.com/p-locate/download/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha1-o0KLtwiLOmApL2aRkni3wpetTwc="
  "resolved" "https://registry.npmmirror.com/p-locate/download/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^2.0.0":
  "integrity" "sha1-MQko/u+cnsxltosXaTAYpmXOoXU="
  "resolved" "https://registry.npmmirror.com/p-map/download/p-map-2.1.0.tgz"
  "version" "2.1.0"

"p-map@^3.0.0":
  "integrity" "sha1-1wTZr4orpoTiYA2aIVmD1BQal50="
  "resolved" "https://registry.npmmirror.com/p-map/download/p-map-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-retry@^3.0.1":
  "integrity" "sha1-MWtMiJPiyNwc+okfQGxLQivr8yg="
  "resolved" "https://registry.npmmirror.com/p-retry/download/p-retry-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "retry" "^0.12.0"

"p-try@^1.0.0":
  "integrity" "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M="
  "resolved" "https://registry.npmmirror.com/p-try/download/p-try-1.0.0.tgz"
  "version" "1.0.0"

"p-try@^2.0.0":
  "integrity" "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="
  "resolved" "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@^1.0.5", "pako@~1.0.2", "pako@~1.0.5":
  "integrity" "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8="
  "resolved" "https://registry.npmmirror.com/pako/download/pako-1.0.11.tgz"
  "version" "1.0.11"

"parallel-transform@^1.1.0":
  "integrity" "sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw="
  "resolved" "https://registry.npmmirror.com/parallel-transform/download/parallel-transform-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cyclist" "^1.0.1"
    "inherits" "^2.0.3"
    "readable-stream" "^2.1.5"

"param-case@2.1.x":
  "integrity" "sha1-35T9jPZTHs915r75oIWPvHK+Ikc="
  "resolved" "https://registry.npmmirror.com/param-case/download/param-case-2.1.1.tgz?cache=0&sync_timestamp=1576721608924&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparam-case%2Fdownload%2Fparam-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "^2.2.0"

"parse-asn1@^5.0.0", "parse-asn1@^5.1.5":
  "integrity" "sha1-ADJxND2ljclMrOSU+u89IUfs6g4="
  "resolved" "https://registry.npmmirror.com/parse-asn1/download/parse-asn1-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "asn1.js" "^4.0.0"
    "browserify-aes" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"
    "safe-buffer" "^5.1.1"

"parse-bmfont-ascii@^1.0.3":
  "integrity" "sha512-U4RrVsUFCleIOBsIGYOMKjn9PavsGOXxbvYGtMOEfnId0SVNsgehXh1DxUdVPLoxd5mvcEtvmKs2Mmf0Mpa1ZA=="
  "resolved" "https://registry.npmmirror.com/parse-bmfont-ascii/-/parse-bmfont-ascii-1.0.6.tgz"
  "version" "1.0.6"

"parse-bmfont-binary@^1.0.5":
  "integrity" "sha512-GxmsRea0wdGdYthjuUeWTMWPqm2+FAd4GI8vCvhgJsFnoGhTrLhXDDupwTo7rXVAgaLIGoVHDZS9p/5XbSqeWA=="
  "resolved" "https://registry.npmmirror.com/parse-bmfont-binary/-/parse-bmfont-binary-1.0.6.tgz"
  "version" "1.0.6"

"parse-bmfont-xml@^1.1.4":
  "integrity" "sha512-0cEliVMZEhrFDwMh4SxIyVJpqYoOWDJ9P895tFuS+XuNzI5UBmBk5U5O4KuJdTnZpSBI4LFA2+ZiJaiwfSwlMA=="
  "resolved" "https://registry.npmmirror.com/parse-bmfont-xml/-/parse-bmfont-xml-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "xml-parse-from-string" "^1.0.0"
    "xml2js" "^0.5.0"

"parse-headers@^2.0.0":
  "integrity" "sha512-Tz11t3uKztEW5FEVZnj1ox8GKblWn+PvHY9TmJV5Mll2uHEwRdR/5Li1OlXoECjLYkApdhWy44ocONwXLiKO5A=="
  "resolved" "https://registry.npmmirror.com/parse-headers/-/parse-headers-2.0.6.tgz"
  "version" "2.0.6"

"parse-json@^4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "https://registry.npmmirror.com/parse-json/download/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse-json@^5.0.0":
  "integrity" "sha1-c+URTJhtFD76NxLU6iTbmkJm9g8="
  "resolved" "https://registry.npmmirror.com/parse-json/download/parse-json-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"
    "lines-and-columns" "^1.1.6"

"parse5-htmlparser2-tree-adapter@^5.1.1":
  "integrity" "sha1-6MdD1OkhlNUpPs3isIvjHmdGHLw="
  "resolved" "https://registry.npmmirror.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-5.1.1.tgz?cache=0&sync_timestamp=1586993715810&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse5-htmlparser2-tree-adapter%2Fdownload%2Fparse5-htmlparser2-tree-adapter-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "parse5" "^5.1.1"

"parse5@^3.0.3":
  "integrity" "sha1-BC95L/3TaFFVHPTp4Gazh0q0W1w="
  "resolved" "https://registry.npmmirror.com/parse5/download/parse5-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "@types/node" "*"

"parse5@^5.1.1":
  "integrity" "sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg="
  "resolved" "https://registry.npmmirror.com/parse5/download/parse5-5.1.1.tgz"
  "version" "5.1.1"

"parse5@5.1.0":
  "integrity" "sha1-xZNByXI/QUxFKXVWTHwApo1YrNI="
  "resolved" "https://registry.npmmirror.com/parse5/download/parse5-5.1.0.tgz"
  "version" "5.1.0"

"parseqs@0.0.5":
  "integrity" "sha1-1SCKNzjkZ2bikbouoXNoSSGouJ0="
  "resolved" "https://registry.npmmirror.com/parseqs/download/parseqs-0.0.5.tgz"
  "version" "0.0.5"
  dependencies:
    "better-assert" "~1.0.0"

"parseuri@0.0.5":
  "integrity" "sha1-gCBKUNTbt3m/3G6+J3jZDkvOMgo="
  "resolved" "https://registry.npmmirror.com/parseuri/download/parseuri-0.0.5.tgz?cache=0&sync_timestamp=1568821002283&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparseuri%2Fdownload%2Fparseuri-0.0.5.tgz"
  "version" "0.0.5"
  dependencies:
    "better-assert" "~1.0.0"

"parseurl@^1.3.2", "parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="
  "resolved" "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://registry.npmmirror.com/pascalcase/download/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.1":
  "integrity" "sha1-5sTd1+06onxoogzE5Q4aTug7vEo="
  "resolved" "https://registry.npmmirror.com/path-browserify/download/path-browserify-0.0.1.tgz"
  "version" "0.0.1"

"path-dirname@^1.0.0":
  "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
  "resolved" "https://registry.npmmirror.com/path-dirname/download/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://registry.npmmirror.com/path-exists/download/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM="
  "resolved" "https://registry.npmmirror.com/path-exists/download/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0", "path-is-absolute@1.0.1":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.2":
  "integrity" "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="
  "resolved" "https://registry.npmmirror.com/path-is-inside/download/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz?cache=0&sync_timestamp=1574441431664&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0":
  "integrity" "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="
  "resolved" "https://registry.npmmirror.com/path-key/download/path-key-3.1.1.tgz?cache=0&sync_timestamp=1574441431664&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-3.1.1.tgz"
  "version" "3.1.1"

"path-key@^3.1.0":
  "integrity" "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="
  "resolved" "https://registry.npmmirror.com/path-key/download/path-key-3.1.1.tgz?cache=0&sync_timestamp=1574441431664&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.6":
  "integrity" "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw="
  "resolved" "https://registry.npmmirror.com/path-parse/download/path-parse-1.0.6.tgz"
  "version" "1.0.6"

"path-to-regexp@^1.1.1":
  "integrity" "sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo="
  "resolved" "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "isarray" "0.0.1"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^3.0.0":
  "integrity" "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428="
  "resolved" "https://registry.npmmirror.com/path-type/download/path-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "^3.0.0"

"pbkdf2@^3.0.3":
  "integrity" "sha1-l2wgZTBhexTrsyEUI597CTNuk6Y="
  "resolved" "https://registry.npmmirror.com/pbkdf2/download/pbkdf2-3.0.17.tgz"
  "version" "3.0.17"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"pend@~1.2.0":
  "integrity" "sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg=="
  "resolved" "https://registry.npmmirror.com/pend/-/pend-1.2.0.tgz"
  "version" "1.2.0"

"performance-now@^2.1.0":
  "integrity" "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="
  "resolved" "https://registry.npmmirror.com/performance-now/download/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"phin@^2.9.1":
  "integrity" "sha512-CzFr90qM24ju5f88quFC/6qohjC144rehe5n6DH900lgXmUe86+xCKc10ev56gRKC4/BkHUoG4uSiQgBiIXwDA=="
  "resolved" "https://registry.npmmirror.com/phin/-/phin-2.9.3.tgz"
  "version" "2.9.3"

"phin@^3.7.1":
  "integrity" "sha512-GEazpTWwTZaEQ9RhL7Nyz0WwqilbqgLahDM3D0hxWwmVDI52nXEybHqiN6/elwpkJBhcuj+WbBu+QfT0uhPGfQ=="
  "resolved" "https://registry.npmmirror.com/phin/-/phin-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "centra" "^2.7.0"

"picomatch@^2.0.4", "picomatch@^2.0.5", "picomatch@^2.2.1":
  "integrity" "sha1-IfMz6ba46v8CRo9RRupAbTRfTa0="
  "resolved" "https://registry.npmmirror.com/picomatch/download/picomatch-2.2.2.tgz"
  "version" "2.2.2"

"pify@^2.0.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://registry.npmmirror.com/pify/download/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^2.3.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://registry.npmmirror.com/pify/download/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "https://registry.npmmirror.com/pify/download/pify-3.0.0.tgz"
  "version" "3.0.0"

"pify@^4.0.1":
  "integrity" "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE="
  "resolved" "https://registry.npmmirror.com/pify/download/pify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@^2.0.0":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "https://registry.npmmirror.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "https://registry.npmmirror.com/pinkie/download/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pirates@^4.0.0", "pirates@^4.0.1":
  "integrity" "sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c="
  "resolved" "https://registry.npmmirror.com/pirates/download/pirates-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "node-modules-regexp" "^1.0.0"

"pixelmatch@^4.0.2":
  "integrity" "sha512-J8B6xqiO37sU/gkcMglv6h5Jbd9xNER7aHzpfRdNmV4IbQBzBpe4l9XmbG+xPF/znacgu2jfEw+wHffaq/YkXA=="
  "resolved" "https://registry.npmmirror.com/pixelmatch/-/pixelmatch-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "pngjs" "^3.0.0"

"pkg-dir@^3.0.0":
  "integrity" "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM="
  "resolved" "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^3.0.0"

"pkg-dir@^4.1.0":
  "integrity" "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM="
  "resolved" "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"pkg-dir@^4.2.0":
  "integrity" "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM="
  "resolved" "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"pkg-up@^2.0.0":
  "integrity" "sha1-yBmscoBZpGHKscOImivjxJoATX8="
  "resolved" "https://registry.npmmirror.com/pkg-up/download/pkg-up-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"pn@^1.1.0":
  "integrity" "sha1-4vTO8OIZ9GPBeas3Rj5OHs3Muvs="
  "resolved" "https://registry.npmmirror.com/pn/download/pn-1.1.0.tgz"
  "version" "1.1.0"

"pngjs@^3.0.0", "pngjs@^3.3.3":
  "integrity" "sha512-NCrCHhWmnQklfH4MtJMRjZ2a8c80qXeMlQMv2uVp9ISJMTt562SbGd6n2oq0PaPgKm7Z6pL9E2UlLIhC+SHL3w=="
  "resolved" "https://registry.npmmirror.com/pngjs/-/pngjs-3.4.0.tgz"
  "version" "3.4.0"

"pnp-webpack-plugin@^1.6.4":
  "integrity" "sha1-yXEaxNxIpoXauvyG+Lbdn434QUk="
  "resolved" "https://registry.npmmirror.com/pnp-webpack-plugin/download/pnp-webpack-plugin-1.6.4.tgz"
  "version" "1.6.4"
  dependencies:
    "ts-pnp" "^1.1.6"

"portfinder@^1.0.20", "portfinder@^1.0.25", "portfinder@^1.0.26":
  "integrity" "sha1-R1ZY1WyjC+1yrH8TeO01C9G2TnA="
  "resolved" "https://registry.npmmirror.com/portfinder/download/portfinder-1.0.26.tgz"
  "version" "1.0.26"
  dependencies:
    "async" "^2.6.2"
    "debug" "^3.1.1"
    "mkdirp" "^0.5.1"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://registry.npmmirror.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^7.0.1":
  "integrity" "sha1-UE780AjKAnMSBWiweSsWzc3oqsE="
  "resolved" "https://registry.npmmirror.com/postcss-calc/download/postcss-calc-7.0.2.tgz?cache=0&sync_timestamp=1582014221563&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-calc%2Fdownload%2Fpostcss-calc-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "postcss" "^7.0.27"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.0.2"

"postcss-colormin@^4.0.3":
  "integrity" "sha1-rgYLzpPteUrHEmTwgTLVUJVr04E="
  "resolved" "https://registry.npmmirror.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "color" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-comment@^2.0.0":
  "integrity" "sha1-bIgI5kzuJcMxRlGKioKUSwXxHm8="
  "resolved" "https://registry.npmmirror.com/postcss-comment/download/postcss-comment-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "^6.0.0"

"postcss-convert-values@^4.0.1":
  "integrity" "sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8="
  "resolved" "https://registry.npmmirror.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-discard-comments@^4.0.2":
  "integrity" "sha1-H7q9LCRr/2qq15l7KwkY9NevQDM="
  "resolved" "https://registry.npmmirror.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-duplicates@^4.0.2":
  "integrity" "sha1-P+EzzTyCKC5VD8myORdqkge3hOs="
  "resolved" "https://registry.npmmirror.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-empty@^4.0.1":
  "integrity" "sha1-yMlR6fc+2UKAGUWERKAq2Qu592U="
  "resolved" "https://registry.npmmirror.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-overridden@^4.0.1":
  "integrity" "sha1-ZSrvipZybwKfXj4AFG7npOdV/1c="
  "resolved" "https://registry.npmmirror.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-helpers@^0.3.2":
  "integrity" "sha1-z4ch2NZgXSV3MC+Wav79of6pkpw="
  "resolved" "https://registry.npmmirror.com/postcss-helpers/download/postcss-helpers-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "urijs" "^1.18.12"

"postcss-import@^12.0.1":
  "integrity" "sha1-z4x6sLXMq1ZJAkU25WX4QZKLcVM="
  "resolved" "https://registry.npmmirror.com/postcss-import/download/postcss-import-12.0.1.tgz"
  "version" "12.0.1"
  dependencies:
    "postcss" "^7.0.1"
    "postcss-value-parser" "^3.2.3"
    "read-cache" "^1.0.0"
    "resolve" "^1.1.7"

"postcss-load-config@^2.0.0":
  "integrity" "sha1-yE1pK3u3tB3c7ZTuYuirMbQXsAM="
  "resolved" "https://registry.npmmirror.com/postcss-load-config/download/postcss-load-config-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "import-cwd" "^2.0.0"

"postcss-loader@^3.0.0":
  "integrity" "sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0="
  "resolved" "https://registry.npmmirror.com/postcss-loader/download/postcss-loader-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "postcss" "^7.0.0"
    "postcss-load-config" "^2.0.0"
    "schema-utils" "^1.0.0"

"postcss-merge-longhand@^4.0.11":
  "integrity" "sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ="
  "resolved" "https://registry.npmmirror.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz"
  "version" "4.0.11"
  dependencies:
    "css-color-names" "0.0.4"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "stylehacks" "^4.0.0"

"postcss-merge-rules@^4.0.3":
  "integrity" "sha1-NivqT/Wh+Y5AdacTxsslrv75plA="
  "resolved" "https://registry.npmmirror.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "cssnano-util-same-parent" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"
    "vendors" "^1.0.0"

"postcss-minify-font-values@^4.0.2":
  "integrity" "sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY="
  "resolved" "https://registry.npmmirror.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-gradients@^4.0.2":
  "integrity" "sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE="
  "resolved" "https://registry.npmmirror.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "is-color-stop" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-params@^4.0.2":
  "integrity" "sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ="
  "resolved" "https://registry.npmmirror.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "browserslist" "^4.0.0"
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "uniqs" "^2.0.0"

"postcss-minify-selectors@^4.0.2":
  "integrity" "sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g="
  "resolved" "https://registry.npmmirror.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"postcss-modules-extract-imports@^2.0.0":
  "integrity" "sha1-gYcZoa4doyX5gyRGsBE27rSTzX4="
  "resolved" "https://registry.npmmirror.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "^7.0.5"

"postcss-modules-local-by-default@^2.0.6":
  "integrity" "sha1-3ZlT9t1Ha1/R7y2IMMiSl2C1bmM="
  "resolved" "https://registry.npmmirror.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-2.0.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-local-by-default%2Fdownload%2Fpostcss-modules-local-by-default-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "postcss" "^7.0.6"
    "postcss-selector-parser" "^6.0.0"
    "postcss-value-parser" "^3.3.1"

"postcss-modules-local-by-default@^3.0.2":
  "integrity" "sha1-6KZWG+kUqvPAUodjd1JMqQ27eRU="
  "resolved" "https://registry.npmmirror.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-3.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-local-by-default%2Fdownload%2Fpostcss-modules-local-by-default-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "icss-utils" "^4.1.1"
    "postcss" "^7.0.16"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.0.0"

"postcss-modules-scope@^2.1.0", "postcss-modules-scope@^2.2.0":
  "integrity" "sha1-OFyuATzHdD9afXYC0Qc6iequYu4="
  "resolved" "https://registry.npmmirror.com/postcss-modules-scope/download/postcss-modules-scope-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "postcss" "^7.0.6"
    "postcss-selector-parser" "^6.0.0"

"postcss-modules-values@^2.0.0":
  "integrity" "sha1-R5tG3Axco9x/pScIUYNrnscVL2Q="
  "resolved" "https://registry.npmmirror.com/postcss-modules-values/download/postcss-modules-values-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-values%2Fdownload%2Fpostcss-modules-values-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "icss-replace-symbols" "^1.1.0"
    "postcss" "^7.0.6"

"postcss-modules-values@^3.0.0":
  "integrity" "sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA="
  "resolved" "https://registry.npmmirror.com/postcss-modules-values/download/postcss-modules-values-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-values%2Fdownload%2Fpostcss-modules-values-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "icss-utils" "^4.0.0"
    "postcss" "^7.0.6"

"postcss-normalize-charset@^4.0.1":
  "integrity" "sha1-izWt067oOhNrBHHg1ZvlilAoXdQ="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-normalize-display-values@^4.0.2":
  "integrity" "sha1-Db4EpM6QY9RmftK+R2u4MMglk1o="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-positions@^4.0.2":
  "integrity" "sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-repeat-style@^4.0.2":
  "integrity" "sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-string@^4.0.2":
  "integrity" "sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-timing-functions@^4.0.2":
  "integrity" "sha1-jgCcoqOUnNr4rSPmtquZy159KNk="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-unicode@^4.0.1":
  "integrity" "sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-url@^4.0.1":
  "integrity" "sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^3.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-whitespace@^4.0.2":
  "integrity" "sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-ordered-values@^4.1.2":
  "integrity" "sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4="
  "resolved" "https://registry.npmmirror.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-reduce-initial@^4.0.3":
  "integrity" "sha1-f9QuvqXpyBRgljniwuhK4nC6SN8="
  "resolved" "https://registry.npmmirror.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"

"postcss-reduce-transforms@^4.0.2":
  "integrity" "sha1-F++kBerMbge+NBSlyi0QdGgdTik="
  "resolved" "https://registry.npmmirror.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-selector-parser@^3.0.0":
  "integrity" "sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA="
  "resolved" "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "dot-prop" "^5.2.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^5.0.0":
  "integrity" "sha1-JJBENWaXsztk8aj3yAki3d7nGVw="
  "resolved" "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-5.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "cssesc" "^2.0.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^6.0.0", "postcss-selector-parser@^6.0.2":
  "integrity" "sha1-k0z3mdAWyDQRhZ4J3Oyt4BKG7Fw="
  "resolved" "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-6.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "cssesc" "^3.0.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-svgo@^4.0.2":
  "integrity" "sha1-F7mXvHEbMzurFDqu07jT1uPTglg="
  "resolved" "https://registry.npmmirror.com/postcss-svgo/download/postcss-svgo-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "is-svg" "^3.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "svgo" "^1.0.0"

"postcss-unique-selectors@^4.0.1":
  "integrity" "sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w="
  "resolved" "https://registry.npmmirror.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "postcss" "^7.0.0"
    "uniqs" "^2.0.0"

"postcss-urlrewrite@^0.2.2":
  "integrity" "sha1-utU/TeBLwIEvJ4czMUvTBi7Ta9Q="
  "resolved" "https://registry.npmmirror.com/postcss-urlrewrite/download/postcss-urlrewrite-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "postcss-helpers" "^0.3.2"

"postcss-value-parser@^3.0.0", "postcss-value-parser@^3.2.3", "postcss-value-parser@^3.3.0", "postcss-value-parser@^3.3.1":
  "integrity" "sha1-n/giVH4okyE88cMO+lGsX9G6goE="
  "resolved" "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-value-parser@^4.0.0":
  "integrity" "sha1-RD9qIM7WSBor2k+oUypuVdeJoss="
  "resolved" "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-4.1.0.tgz"
  "version" "4.1.0"

"postcss-value-parser@^4.0.2":
  "integrity" "sha1-RD9qIM7WSBor2k+oUypuVdeJoss="
  "resolved" "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-4.1.0.tgz"
  "version" "4.1.0"

"postcss-value-parser@^4.0.3":
  "integrity" "sha1-RD9qIM7WSBor2k+oUypuVdeJoss="
  "resolved" "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-4.1.0.tgz"
  "version" "4.1.0"

"postcss-value-parser@^4.1.0":
  "integrity" "sha1-RD9qIM7WSBor2k+oUypuVdeJoss="
  "resolved" "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz?cache=0&sync_timestamp=1588083210998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-4.1.0.tgz"
  "version" "4.1.0"

"postcss@^6.0.0":
  "integrity" "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ="
  "resolved" "https://registry.npmmirror.com/postcss/download/postcss-6.0.23.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "^2.4.1"
    "source-map" "^0.6.1"
    "supports-color" "^5.4.0"

"postcss@^7.0.0", "postcss@^7.0.1", "postcss@^7.0.14", "postcss@^7.0.16", "postcss@^7.0.27", "postcss@^7.0.30", "postcss@^7.0.5", "postcss@^7.0.6", "postcss@^7.0.7":
  "integrity" "sha1-zJN4vv/kagLLxFBqBHfQX86pqOI="
  "resolved" "https://registry.npmmirror.com/postcss/download/postcss-7.0.30.tgz?cache=0&sync_timestamp=1589233594806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.30.tgz"
  "version" "7.0.30"
  dependencies:
    "chalk" "^2.4.2"
    "source-map" "^0.6.1"
    "supports-color" "^6.1.0"

"prelude-ls@~1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "https://registry.npmmirror.com/prelude-ls/download/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prepend-http@^1.0.0":
  "integrity" "sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw="
  "resolved" "https://registry.npmmirror.com/prepend-http/download/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"prettier@^1.18.2":
  "integrity" "sha1-99f1/4qc2HKnvkyhQglZVqYHl8s="
  "resolved" "https://registry.npmmirror.com/prettier/download/prettier-1.19.1.tgz?cache=0&sync_timestamp=1587491448785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fprettier%2Fdownload%2Fprettier-1.19.1.tgz"
  "version" "1.19.1"

"pretty-error@^2.0.2":
  "integrity" "sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM="
  "resolved" "https://registry.npmmirror.com/pretty-error/download/pretty-error-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "renderkid" "^2.0.1"
    "utila" "~0.4"

"pretty-format@^25.5.0":
  "integrity" "sha1-eHPB13T2gsNLjUi2dDor8qxVeRo="
  "resolved" "https://registry.npmmirror.com/pretty-format/download/pretty-format-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "ansi-regex" "^5.0.0"
    "ansi-styles" "^4.0.0"
    "react-is" "^16.12.0"

"private@^0.1.8":
  "integrity" "sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8="
  "resolved" "https://registry.npmmirror.com/private/download/private-0.1.8.tgz"
  "version" "0.1.8"

"process-nextick-args@~2.0.0":
  "integrity" "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="
  "resolved" "https://registry.npmmirror.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@^0.11.10":
  "integrity" "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="
  "resolved" "https://registry.npmmirror.com/process/download/process-0.11.10.tgz"
  "version" "0.11.10"

"progress@^2.0.1":
  "integrity" "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA=="
  "resolved" "https://registry.npmmirror.com/progress/-/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise-inflight@^1.0.1":
  "integrity" "sha1-mEcocL8igTL8vdhoEputEsPAKeM="
  "resolved" "https://registry.npmmirror.com/promise-inflight/download/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"prompts@^2.0.1":
  "integrity" "sha1-SAVy2J7POVZtK9P+LJ/Mt8TAsGg="
  "resolved" "https://registry.npmmirror.com/prompts/download/prompts-2.3.2.tgz?cache=0&sync_timestamp=1584535638103&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fprompts%2Fdownload%2Fprompts-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "kleur" "^3.0.3"
    "sisteransi" "^1.0.4"

"proxy-addr@~2.0.5":
  "integrity" "sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8="
  "resolved" "https://registry.npmmirror.com/proxy-addr/download/proxy-addr-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "forwarded" "~0.1.2"
    "ipaddr.js" "1.9.1"

"proxy-from-env@^1.0.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"prr@~1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY="
  "resolved" "https://registry.npmmirror.com/prr/download/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "https://registry.npmmirror.com/pseudomap/download/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.28":
  "integrity" "sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ="
  "resolved" "https://registry.npmmirror.com/psl/download/psl-1.8.0.tgz?cache=0&sync_timestamp=1585142991033&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpsl%2Fdownload%2Fpsl-1.8.0.tgz"
  "version" "1.8.0"

"public-encrypt@^4.0.0":
  "integrity" "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA="
  "resolved" "https://registry.npmmirror.com/public-encrypt/download/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^2.0.0":
  "integrity" "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk="
  "resolved" "https://registry.npmmirror.com/pump/download/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pump@^3.0.0":
  "integrity" "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ="
  "resolved" "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.3":
  "integrity" "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4="
  "resolved" "https://registry.npmmirror.com/pumpify/download/pumpify-1.5.1.tgz?cache=0&sync_timestamp=1569938200736&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpumpify%2Fdownload%2Fpumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^1.2.4":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://registry.npmmirror.com/punycode/download/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="
  "resolved" "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz"
  "version" "2.1.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "https://registry.npmmirror.com/punycode/download/punycode-1.3.2.tgz"
  "version" "1.3.2"

"puppeteer@^3.0.1":
  "integrity" "sha512-23zNqRltZ1PPoK28uRefWJ/zKb5Jhnzbbwbpcna2o5+QMn17F0khq5s1bdH3vPlyj+J36pubccR8wiNA/VE0Vw=="
  "resolved" "https://registry.npmmirror.com/puppeteer/-/puppeteer-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "debug" "^4.1.0"
    "extract-zip" "^2.0.0"
    "https-proxy-agent" "^4.0.0"
    "mime" "^2.0.3"
    "progress" "^2.0.1"
    "proxy-from-env" "^1.0.0"
    "rimraf" "^3.0.2"
    "tar-fs" "^2.0.0"
    "unbzip2-stream" "^1.3.3"
    "ws" "^7.2.3"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "https://registry.npmmirror.com/q/download/q-1.5.1.tgz"
  "version" "1.5.1"

"qr-image@^3.2.0":
  "integrity" "sha1-n6gpW+rlDEoUnPn5CaHbRkqGcug="
  "resolved" "https://registry.npmmirror.com/qr-image/download/qr-image-3.2.0.tgz"
  "version" "3.2.0"

"qrcode-reader@^1.0.4":
  "integrity" "sha1-ldm7noEwgANhqWy1pDEkrR2eBrg="
  "resolved" "https://registry.npmmirror.com/qrcode-reader/download/qrcode-reader-1.0.4.tgz"
  "version" "1.0.4"

"qrcode-terminal@^0.12.0":
  "integrity" "sha1-u1tpnvf58FBQkqN0i+RGT+cbWBk="
  "resolved" "https://registry.npmmirror.com/qrcode-terminal/download/qrcode-terminal-0.12.0.tgz"
  "version" "0.12.0"

"qs@^6.4.0":
  "integrity" "sha1-kJCykNH5FyjTwi5UhDykSupatoc="
  "resolved" "https://registry.npmmirror.com/qs/download/qs-6.9.4.tgz"
  "version" "6.9.4"

"qs@^6.5.2":
  "integrity" "sha1-kJCykNH5FyjTwi5UhDykSupatoc="
  "resolved" "https://registry.npmmirror.com/qs/download/qs-6.9.4.tgz"
  "version" "6.9.4"

"qs@~6.5.2":
  "integrity" "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY="
  "resolved" "https://registry.npmmirror.com/qs/download/qs-6.5.2.tgz"
  "version" "6.5.2"

"qs@6.7.0":
  "integrity" "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="
  "resolved" "https://registry.npmmirror.com/qs/download/qs-6.7.0.tgz"
  "version" "6.7.0"

"query-string@^4.1.0":
  "integrity" "sha1-u7aTucqRXCMlFbIosaArYJBD2+s="
  "resolved" "https://registry.npmmirror.com/query-string/download/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="
  "resolved" "https://registry.npmmirror.com/querystring-es3/download/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "https://registry.npmmirror.com/querystring/download/querystring-0.2.0.tgz"
  "version" "0.2.0"

"querystringify@^2.1.1":
  "integrity" "sha1-YOWl/WSn+L+k0qsu1v30yFutFU4="
  "resolved" "https://registry.npmmirror.com/querystringify/download/querystringify-2.1.1.tgz"
  "version" "2.1.1"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5":
  "integrity" "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo="
  "resolved" "https://registry.npmmirror.com/randombytes/download/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg="
  "resolved" "https://registry.npmmirror.com/randomfill/download/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="
  "resolved" "https://registry.npmmirror.com/range-parser/download/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@^2.2.0", "raw-body@^2.3.3":
  "integrity" "sha1-MKyC+Yu1rowVLmcUnayNVRU7Fow="
  "resolved" "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "bytes" "3.1.0"
    "http-errors" "1.7.3"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"raw-body@2.4.0":
  "integrity" "sha1-oc5vucm8NWylLoklarWQWeE9AzI="
  "resolved" "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "bytes" "3.1.0"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"rc@^1.0.1", "rc@^1.1.6":
  "integrity" "sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0="
  "resolved" "https://registry.npmmirror.com/rc/download/rc-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "deep-extend" "^0.6.0"
    "ini" "~1.3.0"
    "minimist" "^1.2.0"
    "strip-json-comments" "~2.0.1"

"react-is@^16.12.0":
  "integrity" "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ="
  "resolved" "https://registry.npmmirror.com/react-is/download/react-is-16.13.1.tgz"
  "version" "16.13.1"

"read-cache@^1.0.0":
  "integrity" "sha1-5mTvMRYRZsl1HNvo28+GtftY93Q="
  "resolved" "https://registry.npmmirror.com/read-cache/download/read-cache-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "pify" "^2.3.0"

"read-pkg-up@^7.0.1":
  "integrity" "sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc="
  "resolved" "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg-up%2Fdownload%2Fread-pkg-up-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "find-up" "^4.1.0"
    "read-pkg" "^5.2.0"
    "type-fest" "^0.8.1"

"read-pkg@^5.1.1", "read-pkg@^5.2.0":
  "integrity" "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w="
  "resolved" "https://registry.npmmirror.com/read-pkg/download/read-pkg-5.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fread-pkg%2Fdownload%2Fread-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"readable-stream@^2.0.0", "readable-stream@^2.0.1", "readable-stream@^2.0.2", "readable-stream@^2.1.5", "readable-stream@^2.2.2", "readable-stream@^2.3.3", "readable-stream@^2.3.6", "readable-stream@~2.3.6", "readable-stream@1 || 2":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6":
  "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
  "resolved" "https://registry.npmmirror.com/readable-stream/download/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.1.1":
  "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
  "resolved" "https://registry.npmmirror.com/readable-stream/download/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.4.0":
  "integrity" "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.6.0":
  "integrity" "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@^2.2.1":
  "integrity" "sha1-DodiKjMlqjPokihcr4tOhGUppSU="
  "resolved" "https://registry.npmmirror.com/readdirp/download/readdirp-2.2.1.tgz?cache=0&sync_timestamp=1584985910691&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freaddirp%2Fdownload%2Freaddirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"readdirp@~3.4.0":
  "integrity" "sha1-n9zN+ekVWAVEkiGsZF6DA6tbmto="
  "resolved" "https://registry.npmmirror.com/readdirp/download/readdirp-3.4.0.tgz?cache=0&sync_timestamp=1584985910691&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freaddirp%2Fdownload%2Freaddirp-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "picomatch" "^2.2.1"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"realpath-native@^2.0.0":
  "integrity" "sha1-c3esQptuH9WZ3DjQjtlC0Ne+uGY="
  "resolved" "https://registry.npmmirror.com/realpath-native/download/realpath-native-2.0.0.tgz?cache=0&sync_timestamp=1588859532761&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frealpath-native%2Fdownload%2Frealpath-native-2.0.0.tgz"
  "version" "2.0.0"

"rechoir@^0.6.2":
  "integrity" "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q="
  "resolved" "https://registry.npmmirror.com/rechoir/download/rechoir-0.6.2.tgz"
  "version" "0.6.2"
  dependencies:
    "resolve" "^1.1.6"

"regenerate-unicode-properties@^8.2.0":
  "integrity" "sha1-5d5xEdZV57pgwFfb6f83yH5lzew="
  "resolved" "https://registry.npmmirror.com/regenerate-unicode-properties/download/regenerate-unicode-properties-8.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerate-unicode-properties%2Fdownload%2Fregenerate-unicode-properties-8.2.0.tgz"
  "version" "8.2.0"
  dependencies:
    "regenerate" "^1.4.0"

"regenerate@^1.4.0":
  "integrity" "sha1-SoVuxLVuQHfFV1icroXnpMiGmhE="
  "resolved" "https://registry.npmmirror.com/regenerate/download/regenerate-1.4.0.tgz"
  "version" "1.4.0"

"regenerator-runtime@^0.12.1":
  "integrity" "sha1-+hpxVEdkwDb4xJsToIsllMn4oN4="
  "resolved" "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.12.1.tgz?cache=0&sync_timestamp=1584052597708&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerator-runtime%2Fdownload%2Fregenerator-runtime-0.12.1.tgz"
  "version" "0.12.1"

"regenerator-runtime@^0.13.3":
  "integrity" "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg=="
  "resolved" "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  "version" "0.13.11"

"regenerator-runtime@^0.13.4":
  "integrity" "sha1-2Hih0JS0MG0QuQlkhLM+vVXiZpc="
  "resolved" "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.13.5.tgz?cache=0&sync_timestamp=1584052597708&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerator-runtime%2Fdownload%2Fregenerator-runtime-0.13.5.tgz"
  "version" "0.13.5"

"regenerator-transform@^0.14.2":
  "integrity" "sha1-UmaFeJZRjRYWp4oEeTN6MOqXTMc="
  "resolved" "https://registry.npmmirror.com/regenerator-transform/download/regenerator-transform-0.14.4.tgz"
  "version" "0.14.4"
  dependencies:
    "@babel/runtime" "^7.8.4"
    "private" "^0.1.8"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="
  "resolved" "https://registry.npmmirror.com/regex-not/download/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.2.0":
  "integrity" "sha1-erqJs8E6ZFCdq888qNn7ub31y3U="
  "resolved" "https://registry.npmmirror.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.0.tgz?cache=0&sync_timestamp=1576388379660&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregexp.prototype.flags%2Fdownload%2Fregexp.prototype.flags-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.0-next.1"

"regexpu-core@^4.7.0":
  "integrity" "sha1-/L9FjFBDGwu3tF1pZ7gZLZHz2Tg="
  "resolved" "https://registry.npmmirror.com/regexpu-core/download/regexpu-core-4.7.0.tgz?cache=0&sync_timestamp=1583949899397&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregexpu-core%2Fdownload%2Fregexpu-core-4.7.0.tgz"
  "version" "4.7.0"
  dependencies:
    "regenerate" "^1.4.0"
    "regenerate-unicode-properties" "^8.2.0"
    "regjsgen" "^0.5.1"
    "regjsparser" "^0.6.4"
    "unicode-match-property-ecmascript" "^1.0.4"
    "unicode-match-property-value-ecmascript" "^1.2.0"

"registry-auth-token@3.3.2":
  "integrity" "sha1-hR/UkDjuy1hpERFa+EUmDuyYPyA="
  "resolved" "https://registry.npmmirror.com/registry-auth-token/download/registry-auth-token-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "rc" "^1.1.6"
    "safe-buffer" "^5.0.1"

"registry-url@3.1.0":
  "integrity" "sha1-PU74cPc93h138M+aOBQyRE4XSUI="
  "resolved" "https://registry.npmmirror.com/registry-url/download/registry-url-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "rc" "^1.0.1"

"regjsgen@^0.5.1":
  "integrity" "sha1-SPC/Gl6iBRlpKcDZeYtC0e2YRDw="
  "resolved" "https://registry.npmmirror.com/regjsgen/download/regjsgen-0.5.1.tgz?cache=0&sync_timestamp=1571560340910&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregjsgen%2Fdownload%2Fregjsgen-0.5.1.tgz"
  "version" "0.5.1"

"regjsparser@^0.6.4":
  "integrity" "sha1-p2n4aEMIQBpm6bUp0kNv9NBmYnI="
  "resolved" "https://registry.npmmirror.com/regjsparser/download/regjsparser-0.6.4.tgz"
  "version" "0.6.4"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@0.2.x":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://registry.npmmirror.com/relateurl/download/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "https://registry.npmmirror.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@^2.0.1":
  "integrity" "sha1-OAF5wv9a4TZcUivy/Pz/AcW3QUk="
  "resolved" "https://registry.npmmirror.com/renderkid/download/renderkid-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "css-select" "^1.1.0"
    "dom-converter" "^0.2"
    "htmlparser2" "^3.3.0"
    "strip-ansi" "^3.0.0"
    "utila" "^0.4.0"

"repeat-element@^1.1.2":
  "integrity" "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4="
  "resolved" "https://registry.npmmirror.com/repeat-element/download/repeat-element-1.1.3.tgz"
  "version" "1.1.3"

"repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://registry.npmmirror.com/repeat-string/download/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"request-promise-core@1.1.3":
  "integrity" "sha1-6aPAgbUTgN/qZ3M2Bh/qh5qCnuk="
  "resolved" "https://registry.npmmirror.com/request-promise-core/download/request-promise-core-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "lodash" "^4.17.15"

"request-promise-native@^1.0.7", "request-promise-native@^1.0.8":
  "integrity" "sha1-pFW5YLgm5E4r+Jma9k3/K/5YyzY="
  "resolved" "https://registry.npmmirror.com/request-promise-native/download/request-promise-native-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "request-promise-core" "1.1.3"
    "stealthy-require" "^1.1.1"
    "tough-cookie" "^2.3.3"

"request@^2.34", "request@^2.85.0", "request@^2.88.0", "request@^2.88.2":
  "integrity" "sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM="
  "resolved" "https://registry.npmmirror.com/request/download/request-2.88.2.tgz"
  "version" "2.88.2"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.3"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.5.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://registry.npmmirror.com/require-directory/download/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-main-filename@^2.0.0":
  "integrity" "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs="
  "resolved" "https://registry.npmmirror.com/require-main-filename/download/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://registry.npmmirror.com/requires-port/download/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resolve-cwd@^2.0.0":
  "integrity" "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo="
  "resolved" "https://registry.npmmirror.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "resolve-from" "^3.0.0"

"resolve-cwd@^3.0.0":
  "integrity" "sha1-DwB18bslRHZs9zumpuKt/ryxPy0="
  "resolved" "https://registry.npmmirror.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "resolve-from" "^5.0.0"

"resolve-from@^3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "https://registry.npmmirror.com/resolve-from/download/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@^5.0.0":
  "integrity" "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk="
  "resolved" "https://registry.npmmirror.com/resolve-from/download/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"resolve-path@^1.4.0":
  "integrity" "sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc="
  "resolved" "https://registry.npmmirror.com/resolve-path/download/resolve-path-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "http-errors" "~1.6.2"
    "path-is-absolute" "1.0.1"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz?cache=0&sync_timestamp=1585438700247&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve-url%2Fdownload%2Fresolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.1.6", "resolve@^1.1.7", "resolve@^1.10.0", "resolve@^1.17.0", "resolve@^1.3.2", "resolve@^1.8.1":
  "integrity" "sha1-sllBtUloIxzC0bt2p5y38sC/hEQ="
  "resolved" "https://registry.npmmirror.com/resolve/download/resolve-1.17.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.17.0.tgz"
  "version" "1.17.0"
  dependencies:
    "path-parse" "^1.0.6"

"resolve@1.1.7":
  "integrity" "sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs="
  "resolved" "https://registry.npmmirror.com/resolve/download/resolve-1.1.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.1.7.tgz"
  "version" "1.1.7"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "https://registry.npmmirror.com/restore-cursor/download/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="
  "resolved" "https://registry.npmmirror.com/ret/download/ret-0.1.15.tgz"
  "version" "0.1.15"

"retry@^0.12.0":
  "integrity" "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs="
  "resolved" "https://registry.npmmirror.com/retry/download/retry-0.12.0.tgz"
  "version" "0.12.0"

"rgb-regex@^1.0.1":
  "integrity" "sha1-wODWiC3w4jviVKR16O3UGRX+rrE="
  "resolved" "https://registry.npmmirror.com/rgb-regex/download/rgb-regex-1.0.1.tgz"
  "version" "1.0.1"

"rgba-regex@^1.0.0":
  "integrity" "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM="
  "resolved" "https://registry.npmmirror.com/rgba-regex/download/rgba-regex-1.0.0.tgz"
  "version" "1.0.0"

"rimraf@^2.5.4", "rimraf@^2.6.3", "rimraf@^2.7.1":
  "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
  "resolved" "https://registry.npmmirror.com/rimraf/download/rimraf-2.7.1.tgz?cache=0&sync_timestamp=1581229865753&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frimraf%2Fdownload%2Frimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.0":
  "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
  "resolved" "https://registry.npmmirror.com/rimraf/download/rimraf-3.0.2.tgz?cache=0&sync_timestamp=1581229865753&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frimraf%2Fdownload%2Frimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.2":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw="
  "resolved" "https://registry.npmmirror.com/ripemd160/download/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"rsvp@^4.8.4":
  "integrity" "sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ="
  "resolved" "https://registry.npmmirror.com/rsvp/download/rsvp-4.8.5.tgz"
  "version" "4.8.5"

"run-queue@^1.0.0", "run-queue@^1.0.3":
  "integrity" "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="
  "resolved" "https://registry.npmmirror.com/run-queue/download/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "^1.1.1"

"safe-area-insets@^1.4.1":
  "integrity" "sha1-iTCeAaUW3NfS/gEqnEEVGClXvYs="
  "resolved" "https://registry.npmmirror.com/safe-area-insets/download/safe-area-insets-1.4.1.tgz"
  "version" "1.4.1"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@^5.2.0", "safe-buffer@>=5.1.0", "safe-buffer@~5.2.0":
  "integrity" "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="
  "resolved" "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@~5.1.0", "safe-buffer@~5.1.1":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@5.1.2":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://registry.npmmirror.com/safe-regex/download/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@~2.1.0":
  "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
  "resolved" "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafer-buffer%2Fdownload%2Fsafer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sane@^4.0.3":
  "integrity" "sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0="
  "resolved" "https://registry.npmmirror.com/sane/download/sane-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    "anymatch" "^2.0.0"
    "capture-exit" "^2.0.0"
    "exec-sh" "^0.3.2"
    "execa" "^1.0.0"
    "fb-watchman" "^2.0.0"
    "micromatch" "^3.1.4"
    "minimist" "^1.1.1"
    "walker" "~1.0.5"

"sass-loader@^10.4.1":
  "integrity" "sha512-vMUoSNOUKJILHpcNCCyD23X34gve1TS7Rjd9uXHeKqhvBG39x6XbswFDtpbTElj6XdMFezoWhkh5vtKudf2cgQ=="
  "resolved" "https://registry.npmmirror.com/sass-loader/-/sass-loader-10.5.2.tgz"
  "version" "10.5.2"
  dependencies:
    "klona" "^2.0.4"
    "loader-utils" "^2.0.0"
    "neo-async" "^2.6.2"
    "schema-utils" "^3.0.0"
    "semver" "^7.3.2"

"sass@^1.3.0", "sass@1.32.13":
  "integrity" "sha512-dEgI9nShraqP7cXQH+lEXVf73WOPCse0QlFzSD8k+1TcOxCMwVXfQlr0jtoluZysQOyJGnfr21dLvYKDJq8HkA=="
  "resolved" "https://registry.npmmirror.com/sass/-/sass-1.32.13.tgz"
  "version" "1.32.13"
  dependencies:
    "chokidar" ">=3.0.0 <4.0.0"

"sax@>=0.6.0", "sax@~1.2.4":
  "integrity" "sha1-KBYjTiN4vdxOU1T6tcqold9xANk="
  "resolved" "https://registry.npmmirror.com/sax/download/sax-1.2.4.tgz"
  "version" "1.2.4"

"saxes@^3.1.9":
  "integrity" "sha1-1Z0f0zLskq2YouCy7mRHAjhLHFs="
  "resolved" "https://registry.npmmirror.com/saxes/download/saxes-3.1.11.tgz"
  "version" "3.1.11"
  dependencies:
    "xmlchars" "^2.1.1"

"schema-utils@^1.0.0":
  "integrity" "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-1.0.0.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-errors" "^1.0.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^2.0.0":
  "integrity" "sha1-KZ/mvUozZdwj2Z/URsr/jx1sMww="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.6.6.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.6.6.tgz"
  "version" "2.6.6"
  dependencies:
    "ajv" "^6.12.0"
    "ajv-keywords" "^3.4.1"

"schema-utils@^2.5.0":
  "integrity" "sha1-KZ/mvUozZdwj2Z/URsr/jx1sMww="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.6.6.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.6.6.tgz"
  "version" "2.6.6"
  dependencies:
    "ajv" "^6.12.0"
    "ajv-keywords" "^3.4.1"

"schema-utils@^2.6.5":
  "integrity" "sha1-KZ/mvUozZdwj2Z/URsr/jx1sMww="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.6.6.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.6.6.tgz"
  "version" "2.6.6"
  dependencies:
    "ajv" "^6.12.0"
    "ajv-keywords" "^3.4.1"

"schema-utils@^2.6.6":
  "integrity" "sha1-KZ/mvUozZdwj2Z/URsr/jx1sMww="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.6.6.tgz?cache=0&sync_timestamp=1587138145115&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.6.6.tgz"
  "version" "2.6.6"
  dependencies:
    "ajv" "^6.12.0"
    "ajv-keywords" "^3.4.1"

"schema-utils@^3.0.0":
  "integrity" "sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg=="
  "resolved" "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"select-hose@^2.0.0":
  "integrity" "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="
  "resolved" "https://registry.npmmirror.com/select-hose/download/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"selfsigned@^1.10.7":
  "integrity" "sha1-2lgZ/QSdVXTyjoipvMbbxubzkGs="
  "resolved" "https://registry.npmmirror.com/selfsigned/download/selfsigned-1.10.7.tgz"
  "version" "1.10.7"
  dependencies:
    "node-forge" "0.9.0"

"semver@^5.4.1", "semver@^5.5.0", "semver@^5.5.1", "semver@^5.6.0", "semver@2 || 3 || 4 || 5":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.1.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.3.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^7.0.0":
  "integrity" "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz"
  "version" "7.7.2"

"semver@^7.3.2":
  "integrity" "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz"
  "version" "7.7.2"

"semver@7.0.0":
  "integrity" "sha1-XzyjV2HkfgWyBsba/yz4FPAxa44="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-7.0.0.tgz"
  "version" "7.0.0"

"send@0.17.1":
  "integrity" "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg="
  "resolved" "https://registry.npmmirror.com/send/download/send-0.17.1.tgz"
  "version" "0.17.1"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "~1.7.2"
    "mime" "1.6.0"
    "ms" "2.1.1"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.1"
    "statuses" "~1.5.0"

"serialize-javascript@^2.1.2":
  "integrity" "sha1-7OxTsOAxe9yV73arcHS3OEeF+mE="
  "resolved" "https://registry.npmmirror.com/serialize-javascript/download/serialize-javascript-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-2.1.2.tgz"
  "version" "2.1.2"

"serialize-javascript@^3.0.0":
  "integrity" "sha1-SS5Imi13t7gErTkaX12XhwlSVI4="
  "resolved" "https://registry.npmmirror.com/serialize-javascript/download/serialize-javascript-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-3.0.0.tgz"
  "version" "3.0.0"

"serve-index@^1.9.1":
  "integrity" "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk="
  "resolved" "https://registry.npmmirror.com/serve-index/download/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.14.1":
  "integrity" "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk="
  "resolved" "https://registry.npmmirror.com/serve-static/download/serve-static-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.17.1"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://registry.npmmirror.com/set-blocking/download/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-immediate-shim@~1.0.1":
  "integrity" "sha1-SysbJ+uAip+NzEgaWOXlb1mfP2E="
  "resolved" "https://registry.npmmirror.com/set-immediate-shim/download/set-immediate-shim-1.0.1.tgz"
  "version" "1.0.1"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
  "resolved" "https://registry.npmmirror.com/set-value/download/set-value-2.0.1.tgz?cache=0&sync_timestamp=1585775409029&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fset-value%2Fdownload%2Fset-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "https://registry.npmmirror.com/setimmediate/download/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="
  "resolved" "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.0.tgz?cache=0&sync_timestamp=1563425414995&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsetprototypeof%2Fdownload%2Fsetprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.1.1":
  "integrity" "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="
  "resolved" "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.1.tgz?cache=0&sync_timestamp=1563425414995&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsetprototypeof%2Fdownload%2Fsetprototypeof-1.1.1.tgz"
  "version" "1.1.1"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc="
  "resolved" "https://registry.npmmirror.com/sha.js/download/sha.js-2.4.11.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsha.js%2Fdownload%2Fsha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://registry.npmmirror.com/shebang-command/download/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo="
  "resolved" "https://registry.npmmirror.com/shebang-command/download/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="
  "resolved" "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.4.3", "shell-quote@^1.6.1":
  "integrity" "sha1-Z6fQLHbJ2iT5nSCAj8re0ODgS+I="
  "resolved" "https://registry.npmmirror.com/shell-quote/download/shell-quote-1.7.2.tgz"
  "version" "1.7.2"

"shelljs@^0.8.1":
  "integrity" "sha1-3naE/ut2f4cWsyYHiooAh1iQ48I="
  "resolved" "https://registry.npmmirror.com/shelljs/download/shelljs-0.8.4.tgz?cache=0&sync_timestamp=1587787177094&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fshelljs%2Fdownload%2Fshelljs-0.8.4.tgz"
  "version" "0.8.4"
  dependencies:
    "glob" "^7.0.0"
    "interpret" "^1.0.0"
    "rechoir" "^0.6.2"

"shellwords@^0.1.1":
  "integrity" "sha1-1rkYHBpI05cyTISHHvvPxz/AZUs="
  "resolved" "https://registry.npmmirror.com/shellwords/download/shellwords-0.1.1.tgz"
  "version" "0.1.1"

"signal-exit@^3.0.0", "signal-exit@^3.0.2":
  "integrity" "sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw="
  "resolved" "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.3.tgz"
  "version" "3.0.3"

"simple-swizzle@^0.2.2":
  "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo="
  "resolved" "https://registry.npmmirror.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "^0.3.1"

"sisteransi@^1.0.4":
  "integrity" "sha1-E01oEpd1ZDfMBcoBNw06elcQde0="
  "resolved" "https://registry.npmmirror.com/sisteransi/download/sisteransi-1.0.5.tgz"
  "version" "1.0.5"

"slash@^1.0.0":
  "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
  "resolved" "https://registry.npmmirror.com/slash/download/slash-1.0.0.tgz"
  "version" "1.0.0"

"slash@^2.0.0":
  "integrity" "sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q="
  "resolved" "https://registry.npmmirror.com/slash/download/slash-2.0.0.tgz"
  "version" "2.0.0"

"slash@^3.0.0":
  "integrity" "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ="
  "resolved" "https://registry.npmmirror.com/slash/download/slash-3.0.0.tgz"
  "version" "3.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="
  "resolved" "https://registry.npmmirror.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="
  "resolved" "https://registry.npmmirror.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="
  "resolved" "https://registry.npmmirror.com/snapdragon/download/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"socket.io-adapter@~1.1.0":
  "integrity" "sha1-qz8Nb2a4/H/KOVmrWZH4IiF4m+k="
  "resolved" "https://registry.npmmirror.com/socket.io-adapter/download/socket.io-adapter-1.1.2.tgz"
  "version" "1.1.2"

"socket.io-client@2.3.0":
  "integrity" "sha1-FNW6LgC5vNFFrkQ6uWs/hsvMG7Q="
  "resolved" "https://registry.npmmirror.com/socket.io-client/download/socket.io-client-2.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsocket.io-client%2Fdownload%2Fsocket.io-client-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "backo2" "1.0.2"
    "base64-arraybuffer" "0.1.5"
    "component-bind" "1.0.0"
    "component-emitter" "1.2.1"
    "debug" "~4.1.0"
    "engine.io-client" "~3.4.0"
    "has-binary2" "~1.0.2"
    "has-cors" "1.1.0"
    "indexof" "0.0.1"
    "object-component" "0.0.3"
    "parseqs" "0.0.5"
    "parseuri" "0.0.5"
    "socket.io-parser" "~3.3.0"
    "to-array" "0.1.4"

"socket.io-parser@~3.3.0":
  "integrity" "sha1-K1KpalCf3zFEC6QP7WCUx9TxJi8="
  "resolved" "https://registry.npmmirror.com/socket.io-parser/download/socket.io-parser-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "component-emitter" "1.2.1"
    "debug" "~3.1.0"
    "isarray" "2.0.1"

"socket.io-parser@~3.4.0":
  "integrity" "sha1-sGr4ODApdYN+qy3JgAN9okBU1ko="
  "resolved" "https://registry.npmmirror.com/socket.io-parser/download/socket.io-parser-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "component-emitter" "1.2.1"
    "debug" "~4.1.0"
    "isarray" "2.0.1"

"socket.io@^2.2.0":
  "integrity" "sha1-zXYu1qT67KWbwfPiQ8CWkxHrc/s="
  "resolved" "https://registry.npmmirror.com/socket.io/download/socket.io-2.3.0.tgz?cache=0&sync_timestamp=1569002852515&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsocket.io%2Fdownload%2Fsocket.io-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "debug" "~4.1.0"
    "engine.io" "~3.4.0"
    "has-binary2" "~1.0.2"
    "socket.io-adapter" "~1.1.0"
    "socket.io-client" "2.3.0"
    "socket.io-parser" "~3.4.0"

"sockjs-client@1.4.0":
  "integrity" "sha1-yfJWjhnI/YFztJl+o0IOC7MGx9U="
  "resolved" "https://registry.npmmirror.com/sockjs-client/download/sockjs-client-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "debug" "^3.2.5"
    "eventsource" "^1.0.7"
    "faye-websocket" "~0.11.1"
    "inherits" "^2.0.3"
    "json3" "^3.3.2"
    "url-parse" "^1.4.3"

"sockjs@0.3.20":
  "integrity" "sha1-smooPsVi74smh7RAM6Tuzqx12FU="
  "resolved" "https://registry.npmmirror.com/sockjs/download/sockjs-0.3.20.tgz"
  "version" "0.3.20"
  dependencies:
    "faye-websocket" "^0.10.0"
    "uuid" "^3.4.0"
    "websocket-driver" "0.6.5"

"sort-keys@^1.0.0":
  "integrity" "sha1-RBttTTRnmPG05J6JIK37oOVD+a0="
  "resolved" "https://registry.npmmirror.com/sort-keys/download/sort-keys-1.1.2.tgz?cache=0&sync_timestamp=1565864727994&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsort-keys%2Fdownload%2Fsort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-list-map@^2.0.0":
  "integrity" "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ="
  "resolved" "https://registry.npmmirror.com/source-list-map/download/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-resolve@^0.5.0", "source-map-resolve@^0.5.2":
  "integrity" "sha1-GQhmvs51U+H48mei7oLGBrVQmho="
  "resolved" "https://registry.npmmirror.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz?cache=0&sync_timestamp=1584831908370&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-resolve%2Fdownload%2Fsource-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@^0.5.16", "source-map-support@^0.5.3", "source-map-support@^0.5.5", "source-map-support@^0.5.6", "source-map-support@~0.5.12":
  "integrity" "sha1-qYti+G3K9PZzmWSMCFKRq56P7WE="
  "resolved" "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.19.tgz?cache=0&sync_timestamp=1587719517036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.19.tgz"
  "version" "0.5.19"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="
  "resolved" "https://registry.npmmirror.com/source-map-url/download/source-map-url-0.4.0.tgz"
  "version" "0.4.0"

"source-map@^0.5.0":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmmirror.com/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.6":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmmirror.com/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0", "source-map@^0.6.1", "source-map@~0.6.0", "source-map@~0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha1-UwL4FpAxc1ImVECS5kmB91F1A4M="
  "resolved" "https://registry.npmmirror.com/source-map/download/source-map-0.7.3.tgz"
  "version" "0.7.3"

"spdx-correct@^3.0.0":
  "integrity" "sha1-+4PlBERSaPFUsHTiGMh8ADzTHfQ="
  "resolved" "https://registry.npmmirror.com/spdx-correct/download/spdx-correct-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0="
  "resolved" "https://registry.npmmirror.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz?cache=0&sync_timestamp=1587422410312&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fspdx-exceptions%2Fdownload%2Fspdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk="
  "resolved" "https://registry.npmmirror.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ="
  "resolved" "https://registry.npmmirror.com/spdx-license-ids/download/spdx-license-ids-3.0.5.tgz"
  "version" "3.0.5"

"spdy-transport@^3.0.0":
  "integrity" "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE="
  "resolved" "https://registry.npmmirror.com/spdy-transport/download/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.2":
  "integrity" "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s="
  "resolved" "https://registry.npmmirror.com/spdy/download/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="
  "resolved" "https://registry.npmmirror.com/split-string/download/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"split@~0.3.3":
  "integrity" "sha512-wD2AeVmxXRBoX44wAycgjVpMhvbwdI2aZjCkvfNcH1YqHQvJVa1duWc73OyVGJUc05fhFaTZeQ/PYsrmyH0JVA=="
  "resolved" "https://registry.npmmirror.com/split/-/split-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "through" "2"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://registry.npmmirror.com/sprintf-js/download/sprintf-js-1.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsprintf-js%2Fdownload%2Fsprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sshpk@^1.7.0":
  "integrity" "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc="
  "resolved" "https://registry.npmmirror.com/sshpk/download/sshpk-1.16.1.tgz"
  "version" "1.16.1"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"ssri@^6.0.1":
  "integrity" "sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg="
  "resolved" "https://registry.npmmirror.com/ssri/download/ssri-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "figgy-pudding" "^3.5.1"

"ssri@^7.0.0", "ssri@^7.1.0":
  "integrity" "sha1-ksJBv23oI2W1x/tL126XVSLhKU0="
  "resolved" "https://registry.npmmirror.com/ssri/download/ssri-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "figgy-pudding" "^3.5.1"
    "minipass" "^3.1.1"

"stable@^0.1.8":
  "integrity" "sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88="
  "resolved" "https://registry.npmmirror.com/stable/download/stable-0.1.8.tgz"
  "version" "0.1.8"

"stack-utils@^1.0.1":
  "integrity" "sha1-M+ujiXeIVYvr/C2wWdwVjsNs67g="
  "resolved" "https://registry.npmmirror.com/stack-utils/download/stack-utils-1.0.2.tgz"
  "version" "1.0.2"

"stackframe@^1.1.1":
  "integrity" "sha1-/+8KMxixtgw7WFZJiaylZgcp7HE="
  "resolved" "https://registry.npmmirror.com/stackframe/download/stackframe-1.1.1.tgz?cache=0&sync_timestamp=1578261993899&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstackframe%2Fdownload%2Fstackframe-1.1.1.tgz"
  "version" "1.1.1"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://registry.npmmirror.com/static-extend/download/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@^1.5.0", "statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", "statuses@~1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1587328859420&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz"
  "version" "1.5.0"

"stealthy-require@^1.1.1":
  "integrity" "sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks="
  "resolved" "https://registry.npmmirror.com/stealthy-require/download/stealthy-require-1.1.1.tgz"
  "version" "1.1.1"

"stream-browserify@^2.0.1":
  "integrity" "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs="
  "resolved" "https://registry.npmmirror.com/stream-browserify/download/stream-browserify-2.0.2.tgz?cache=0&sync_timestamp=1587041519870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstream-browserify%2Fdownload%2Fstream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-each@^1.1.0":
  "integrity" "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64="
  "resolved" "https://registry.npmmirror.com/stream-each/download/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "^1.1.0"
    "stream-shift" "^1.0.0"

"stream-http@^2.7.2":
  "integrity" "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw="
  "resolved" "https://registry.npmmirror.com/stream-http/download/stream-http-2.8.3.tgz?cache=0&sync_timestamp=1588701035785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstream-http%2Fdownload%2Fstream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"stream-shift@^1.0.0":
  "integrity" "sha1-1wiCgVWasneEJCebCHfaPDktWj0="
  "resolved" "https://registry.npmmirror.com/stream-shift/download/stream-shift-1.0.1.tgz?cache=0&sync_timestamp=1576147145118&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstream-shift%2Fdownload%2Fstream-shift-1.0.1.tgz"
  "version" "1.0.1"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "https://registry.npmmirror.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"stricter-htmlparser2@^3.9.6":
  "integrity" "sha1-/RlfXkvAmJxrFfx57KhcZAG7UEU="
  "resolved" "https://registry.npmmirror.com/stricter-htmlparser2/download/stricter-htmlparser2-3.9.6.tgz"
  "version" "3.9.6"
  dependencies:
    "domelementtype" "^1.3.0"
    "domutils" "^1.5.1"
    "entities" "^1.1.1"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.2"
    "x-domhandler" "^2.4.2"

"string_decoder@^1.0.0", "string_decoder@^1.1.1":
  "integrity" "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4="
  "resolved" "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g="
  "resolved" "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-length@^3.1.0":
  "integrity" "sha1-EH74wjRW4Yeoq9SmEWL/SsbiWDc="
  "resolved" "https://registry.npmmirror.com/string-length/download/string-length-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "astral-regex" "^1.0.0"
    "strip-ansi" "^5.2.0"

"string-width@^1.0.2 || 2 || 3 || 4", "string-width@^3.0.0", "string-width@^3.1.0":
  "integrity" "sha1-InZ74htirxCBV0MG9prFG2IgOWE="
  "resolved" "https://registry.npmmirror.com/string-width/download/string-width-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "emoji-regex" "^7.0.1"
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^5.1.0"

"string-width@^2.0.0":
  "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
  "resolved" "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^4.1.0":
  "integrity" "sha1-lSGCxGzHssMT0VluYjmSvRY7crU="
  "resolved" "https://registry.npmmirror.com/string-width/download/string-width-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.0"

"string-width@^4.2.0":
  "integrity" "sha1-lSGCxGzHssMT0VluYjmSvRY7crU="
  "resolved" "https://registry.npmmirror.com/string-width/download/string-width-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.0"

"string-width@^4.2.3":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string.prototype.trimend@^1.0.0":
  "integrity" "sha1-hYEqa4R6wAInD1gIFGBkyZX7aRM="
  "resolved" "https://registry.npmmirror.com/string.prototype.trimend/download/string.prototype.trimend-1.0.1.tgz?cache=0&sync_timestamp=1586465409341&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimend%2Fdownload%2Fstring.prototype.trimend-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.5"

"string.prototype.trimleft@^2.1.1":
  "integrity" "sha1-RAiqLl1t3QyagHObCH+8BnwDs8w="
  "resolved" "https://registry.npmmirror.com/string.prototype.trimleft/download/string.prototype.trimleft-2.1.2.tgz?cache=0&sync_timestamp=1585584322600&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimleft%2Fdownload%2Fstring.prototype.trimleft-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.5"
    "string.prototype.trimstart" "^1.0.0"

"string.prototype.trimright@^2.1.1":
  "integrity" "sha1-x28c7zDyG7rYr+uNsVEUls+w8qM="
  "resolved" "https://registry.npmmirror.com/string.prototype.trimright/download/string.prototype.trimright-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.5"
    "string.prototype.trimend" "^1.0.0"

"string.prototype.trimstart@^1.0.0":
  "integrity" "sha1-FK9tnzSwU/fPyJty+PLuFLkDmlQ="
  "resolved" "https://registry.npmmirror.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.1.tgz?cache=0&sync_timestamp=1586465413621&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimstart%2Fdownload%2Fstring.prototype.trimstart-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.5"

"strip-ansi@^3.0.0":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-ansi@^5.0.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^5.1.0", "strip-ansi@^5.2.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^6.0.0":
  "integrity" "sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "ansi-regex" "^5.0.0"

"strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-bom@^4.0.0":
  "integrity" "sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg="
  "resolved" "https://registry.npmmirror.com/strip-bom/download/strip-bom-4.0.0.tgz"
  "version" "4.0.0"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0="
  "resolved" "https://registry.npmmirror.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@^2.0.1", "strip-json-comments@~2.0.1":
  "integrity" "sha1-PFMZQukIwml8DsNEhYwobHygpgo="
  "resolved" "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz?cache=0&sync_timestamp=1586160054577&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-json-comments%2Fdownload%2Fstrip-json-comments-2.0.1.tgz"
  "version" "2.0.1"

"stylehacks@^4.0.0":
  "integrity" "sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU="
  "resolved" "https://registry.npmmirror.com/stylehacks/download/stylehacks-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "https://registry.npmmirror.com/supports-color/download/supports-color-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^5.3.0", "supports-color@^5.4.0":
  "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
  "resolved" "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^6.1.0":
  "integrity" "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM="
  "resolved" "https://registry.npmmirror.com/supports-color/download/supports-color-6.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.0.0", "supports-color@^7.1.0":
  "integrity" "sha1-aOMlkd9z4lrRxLSRCKLsUHliv9E="
  "resolved" "https://registry.npmmirror.com/supports-color/download/supports-color-7.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-hyperlinks@^2.0.0":
  "integrity" "sha1-9mPfJSr183xdSbvX7u+p4Lnlnkc="
  "resolved" "https://registry.npmmirror.com/supports-hyperlinks/download/supports-hyperlinks-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "has-flag" "^4.0.0"
    "supports-color" "^7.0.0"

"svg-tags@^1.0.0":
  "integrity" "sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q="
  "resolved" "https://registry.npmmirror.com/svg-tags/download/svg-tags-1.0.0.tgz"
  "version" "1.0.0"

"svgo@^1.0.0":
  "integrity" "sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc="
  "resolved" "https://registry.npmmirror.com/svgo/download/svgo-1.3.2.tgz?cache=0&sync_timestamp=1572433264480&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsvgo%2Fdownload%2Fsvgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "^2.4.1"
    "coa" "^2.0.2"
    "css-select" "^2.0.0"
    "css-select-base-adapter" "^0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "^4.0.2"
    "js-yaml" "^3.13.1"
    "mkdirp" "~0.5.1"
    "object.values" "^1.1.0"
    "sax" "~1.2.4"
    "stable" "^0.1.8"
    "unquote" "~1.1.1"
    "util.promisify" "~1.0.0"

"symbol-tree@^3.2.2":
  "integrity" "sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I="
  "resolved" "https://registry.npmmirror.com/symbol-tree/download/symbol-tree-3.2.4.tgz"
  "version" "3.2.4"

"tapable@^1.0.0", "tapable@^1.1.3":
  "integrity" "sha1-ofzMBrWNth/XpF2i2kT186Pme6I="
  "resolved" "https://registry.npmmirror.com/tapable/download/tapable-1.1.3.tgz"
  "version" "1.1.3"

"tar-fs@^2.0.0":
  "integrity" "sha512-090nwYJDmlhwFwEW3QQl+vaNnxsO2yVsd45eTKRBzSzu+hlb1w2K9inVq5b0ngXuLVqQ4ApvsUHHnu/zQNkWAg=="
  "resolved" "https://registry.npmmirror.com/tar-fs/-/tar-fs-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "chownr" "^1.1.1"
    "mkdirp-classic" "^0.5.2"
    "pump" "^3.0.0"
    "tar-stream" "^2.1.4"

"tar-stream@^2.1.4":
  "integrity" "sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ=="
  "resolved" "https://registry.npmmirror.com/tar-stream/-/tar-stream-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "bl" "^4.0.3"
    "end-of-stream" "^1.4.1"
    "fs-constants" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^3.1.1"

"teen_process@^1.5.1":
  "integrity" "sha512-RnW7HHZD1XuhSTzD3djYOdIl1adE3oNEprE3HOFFxWs5m4FZsqYRhKJ4mDU2udtNGMLUS7jV7l8vVRLWAvmPDw=="
  "resolved" "https://registry.npmmirror.com/teen_process/-/teen_process-1.16.0.tgz"
  "version" "1.16.0"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "bluebird" "^3.5.1"
    "lodash" "^4.17.4"
    "shell-quote" "^1.4.3"
    "source-map-support" "^0.5.3"
    "which" "^2.0.2"

"terminal-link@^2.0.0":
  "integrity" "sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ="
  "resolved" "https://registry.npmmirror.com/terminal-link/download/terminal-link-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "supports-hyperlinks" "^2.0.0"

"terser-webpack-plugin@^1.4.3":
  "integrity" "sha1-Xsry29xfuZdF/QZ5H0b8ndscmnw="
  "resolved" "https://registry.npmmirror.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "cacache" "^12.0.2"
    "find-cache-dir" "^2.1.0"
    "is-wsl" "^1.1.0"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^2.1.2"
    "source-map" "^0.6.1"
    "terser" "^4.1.2"
    "webpack-sources" "^1.4.0"
    "worker-farm" "^1.7.0"

"terser-webpack-plugin@^2.3.5":
  "integrity" "sha1-pAFLMRph+HxqGyF+9PWnW9BmWmk="
  "resolved" "https://registry.npmmirror.com/terser-webpack-plugin/download/terser-webpack-plugin-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "cacache" "^13.0.1"
    "find-cache-dir" "^3.3.1"
    "jest-worker" "^25.4.0"
    "p-limit" "^2.3.0"
    "schema-utils" "^2.6.6"
    "serialize-javascript" "^3.0.0"
    "source-map" "^0.6.1"
    "terser" "^4.6.12"
    "webpack-sources" "^1.4.3"

"terser@^4.1.2", "terser@^4.6.12":
  "integrity" "sha1-FYUs8aCOMlaoBCjoZaL6iT/7oAY="
  "resolved" "https://registry.npmmirror.com/terser/download/terser-4.7.0.tgz?cache=0&sync_timestamp=1589825699609&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fterser%2Fdownload%2Fterser-4.7.0.tgz"
  "version" "4.7.0"
  dependencies:
    "commander" "^2.20.0"
    "source-map" "~0.6.1"
    "source-map-support" "~0.5.12"

"test-exclude@^6.0.0":
  "integrity" "sha1-BKhphmHYBepvopO2y55jrARO8V4="
  "resolved" "https://registry.npmmirror.com/test-exclude/download/test-exclude-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    "glob" "^7.1.4"
    "minimatch" "^3.0.4"

"thenify-all@^1.0.0":
  "integrity" "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY="
  "resolved" "https://registry.npmmirror.com/thenify-all/download/thenify-all-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "thenify" ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  "integrity" "sha1-5p44obq+lpsBCCB5eLn2K4hgSDk="
  "resolved" "https://registry.npmmirror.com/thenify/download/thenify-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "any-promise" "^1.0.0"

"thread-loader@^2.1.3":
  "integrity" "sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo="
  "resolved" "https://registry.npmmirror.com/thread-loader/download/thread-loader-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "loader-runner" "^2.3.1"
    "loader-utils" "^1.1.0"
    "neo-async" "^2.6.0"

"throat@^5.0.0":
  "integrity" "sha1-xRmSNYA6rRh1SmZ9ZZtecs4Wdks="
  "resolved" "https://registry.npmmirror.com/throat/download/throat-5.0.0.tgz"
  "version" "5.0.0"

"through@^2.3.8", "through@2":
  "integrity" "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="
  "resolved" "https://registry.npmmirror.com/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^2.0.0":
  "integrity" "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0="
  "resolved" "https://registry.npmmirror.com/through2/download/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"thunky@^1.0.2":
  "integrity" "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30="
  "resolved" "https://registry.npmmirror.com/thunky/download/thunky-1.1.0.tgz?cache=0&sync_timestamp=1571043401546&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthunky%2Fdownload%2Fthunky-1.1.0.tgz"
  "version" "1.1.0"

"timers-browserify@^2.0.4":
  "integrity" "sha1-gAsfPu4nLlvFPuRloE0OgEwxIR8="
  "resolved" "https://registry.npmmirror.com/timers-browserify/download/timers-browserify-2.0.11.tgz"
  "version" "2.0.11"
  dependencies:
    "setimmediate" "^1.0.4"

"timm@^1.6.1":
  "integrity" "sha512-IjZc9KIotudix8bMaBW6QvMuq64BrJWFs1+4V0lXwWGQZwH+LnX87doAYhem4caOEusRP9/g6jVDQmZ8XOk1nw=="
  "resolved" "https://registry.npmmirror.com/timm/-/timm-1.7.1.tgz"
  "version" "1.7.1"

"timsort@^0.3.0":
  "integrity" "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="
  "resolved" "https://registry.npmmirror.com/timsort/download/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tinycolor2@^1.4.1":
  "integrity" "sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw=="
  "resolved" "https://registry.npmmirror.com/tinycolor2/-/tinycolor2-1.6.0.tgz"
  "version" "1.6.0"

"tmpl@1.0.x":
  "integrity" "sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE="
  "resolved" "https://registry.npmmirror.com/tmpl/download/tmpl-1.0.4.tgz"
  "version" "1.0.4"

"to-array@0.1.4":
  "integrity" "sha1-F+bBH3PdTz10zaek/zI46a2b+JA="
  "resolved" "https://registry.npmmirror.com/to-array/download/to-array-0.1.4.tgz"
  "version" "0.1.4"

"to-arraybuffer@^1.0.0":
  "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
  "resolved" "https://registry.npmmirror.com/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://registry.npmmirror.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://registry.npmmirror.com/to-object-path/download/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ="
  "resolved" "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="
  "resolved" "https://registry.npmmirror.com/to-regex/download/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toidentifier@1.0.0":
  "integrity" "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="
  "resolved" "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz"
  "version" "1.0.0"

"toposort@^1.0.0":
  "integrity" "sha1-LmhELZ9k7HILjMieZEOsbKqVACk="
  "resolved" "https://registry.npmmirror.com/toposort/download/toposort-1.0.7.tgz"
  "version" "1.0.7"

"tough-cookie@^2.3.3", "tough-cookie@~2.5.0":
  "integrity" "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI="
  "resolved" "https://registry.npmmirror.com/tough-cookie/download/tough-cookie-2.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftough-cookie%2Fdownload%2Ftough-cookie-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "psl" "^1.1.28"
    "punycode" "^2.1.1"

"tough-cookie@^3.0.1":
  "integrity" "sha1-nfT1fnOcJpMKAYGEiH9K233Kc7I="
  "resolved" "https://registry.npmmirror.com/tough-cookie/download/tough-cookie-3.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftough-cookie%2Fdownload%2Ftough-cookie-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ip-regex" "^2.1.0"
    "psl" "^1.1.28"
    "punycode" "^2.1.1"

"tr46@^1.0.1":
  "integrity" "sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk="
  "resolved" "https://registry.npmmirror.com/tr46/download/tr46-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "punycode" "^2.1.0"

"tryer@^1.0.1":
  "integrity" "sha1-8shUBoALmw90yfdGW4HqrSQSUvg="
  "resolved" "https://registry.npmmirror.com/tryer/download/tryer-1.0.1.tgz"
  "version" "1.0.1"

"ts-pnp@^1.1.6":
  "integrity" "sha1-pQCtCEsHmPHDBxrzkeZZEshrypI="
  "resolved" "https://registry.npmmirror.com/ts-pnp/download/ts-pnp-1.2.0.tgz"
  "version" "1.2.0"

"tslib@^1.9.0":
  "integrity" "sha1-yIHhPMcBWJTtkUhi0nZDb6mkcEM="
  "resolved" "https://registry.npmmirror.com/tslib/download/tslib-1.13.0.tgz"
  "version" "1.13.0"

"tsscmp@1.0.6":
  "integrity" "sha1-hbmVg6w1iexL/vgltQAKqRHWBes="
  "resolved" "https://registry.npmmirror.com/tsscmp/download/tsscmp-1.0.6.tgz"
  "version" "1.0.6"

"tty-browserify@0.0.0":
  "integrity" "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="
  "resolved" "https://registry.npmmirror.com/tty-browserify/download/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tunnel-agent@^0.6.0":
  "integrity" "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0="
  "resolved" "https://registry.npmmirror.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="
  "resolved" "https://registry.npmmirror.com/tweetnacl/download/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-check@~0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "https://registry.npmmirror.com/type-check/download/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-detect@4.0.8":
  "integrity" "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw="
  "resolved" "https://registry.npmmirror.com/type-detect/download/type-detect-4.0.8.tgz"
  "version" "4.0.8"

"type-fest@^0.11.0":
  "integrity" "sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E="
  "resolved" "https://registry.npmmirror.com/type-fest/download/type-fest-0.11.0.tgz"
  "version" "0.11.0"

"type-fest@^0.6.0":
  "integrity" "sha1-jSojcNPfiG61yQraHFv2GIrPg4s="
  "resolved" "https://registry.npmmirror.com/type-fest/download/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha1-CeJJ696FHTseSNJ8EFREZn8XuD0="
  "resolved" "https://registry.npmmirror.com/type-fest/download/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"type-is@^1.6.14", "type-is@^1.6.16", "type-is@~1.6.17", "type-is@~1.6.18":
  "integrity" "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE="
  "resolved" "https://registry.npmmirror.com/type-is/download/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"typedarray-to-buffer@^3.1.5":
  "integrity" "sha1-qX7nqf9CaRufeD/xvFES/j/KkIA="
  "resolved" "https://registry.npmmirror.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "is-typedarray" "^1.0.0"

"typedarray@^0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "https://registry.npmmirror.com/typedarray/download/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"uglify-js@3.4.x":
  "integrity" "sha1-mtlWPY6zrN+404WX0q8dgV9qdV8="
  "resolved" "https://registry.npmmirror.com/uglify-js/download/uglify-js-3.4.10.tgz"
  "version" "3.4.10"
  dependencies:
    "commander" "~2.19.0"
    "source-map" "~0.6.1"

"unbzip2-stream@^1.3.3":
  "integrity" "sha512-mlExGW4w71ebDJviH16lQLtZS32VKqsSfk80GCfUlwT/4/hNRFsoscrF/c++9xinkMzECL1uL9DDwXqFWkruPg=="
  "resolved" "https://registry.npmmirror.com/unbzip2-stream/-/unbzip2-stream-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "buffer" "^5.2.1"
    "through" "^2.3.8"

"unicode-canonical-property-names-ecmascript@^1.0.4":
  "integrity" "sha1-JhmADEyCWADv3YNDr33Zkzy+KBg="
  "resolved" "https://registry.npmmirror.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz"
  "version" "1.0.4"

"unicode-match-property-ecmascript@^1.0.4":
  "integrity" "sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw="
  "resolved" "https://registry.npmmirror.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^1.0.4"
    "unicode-property-aliases-ecmascript" "^1.0.4"

"unicode-match-property-value-ecmascript@^1.2.0":
  "integrity" "sha1-DZH2AO7rMJaqlisdb8iIduZOpTE="
  "resolved" "https://registry.npmmirror.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funicode-match-property-value-ecmascript%2Fdownload%2Funicode-match-property-value-ecmascript-1.2.0.tgz"
  "version" "1.2.0"

"unicode-property-aliases-ecmascript@^1.0.4":
  "integrity" "sha1-3Vepn2IHvt/0Yoq++5TFDblByPQ="
  "resolved" "https://registry.npmmirror.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.1.0.tgz?cache=0&sync_timestamp=1583945805856&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funicode-property-aliases-ecmascript%2Fdownload%2Funicode-property-aliases-ecmascript-1.1.0.tgz"
  "version" "1.1.0"

"union-value@^1.0.0":
  "integrity" "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc="
  "resolved" "https://registry.npmmirror.com/union-value/download/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"uniq@^1.0.1":
  "integrity" "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="
  "resolved" "https://registry.npmmirror.com/uniq/download/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@^2.0.0":
  "integrity" "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="
  "resolved" "https://registry.npmmirror.com/uniqs/download/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@^1.1.1":
  "integrity" "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA="
  "resolved" "https://registry.npmmirror.com/unique-filename/download/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw="
  "resolved" "https://registry.npmmirror.com/unique-slug/download/unique-slug-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "imurmurhash" "^0.1.4"

"universalify@^0.1.0":
  "integrity" "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="
  "resolved" "https://registry.npmmirror.com/universalify/download/universalify-0.1.2.tgz?cache=0&sync_timestamp=1583531006552&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funiversalify%2Fdownload%2Funiversalify-0.1.2.tgz"
  "version" "0.1.2"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://registry.npmmirror.com/unpipe/download/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unquote@~1.1.1":
  "integrity" "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ="
  "resolved" "https://registry.npmmirror.com/unquote/download/unquote-1.1.1.tgz"
  "version" "1.1.1"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://registry.npmmirror.com/unset-value/download/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"upath@^1.1.1":
  "integrity" "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ="
  "resolved" "https://registry.npmmirror.com/upath/download/upath-1.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fupath%2Fdownload%2Fupath-1.2.0.tgz"
  "version" "1.2.0"

"update-check@^1.5.3":
  "integrity" "sha1-W1COJZVY8a19vItLBFfUydKMh0M="
  "resolved" "https://registry.npmmirror.com/update-check/download/update-check-1.5.4.tgz"
  "version" "1.5.4"
  dependencies:
    "registry-auth-token" "3.3.2"
    "registry-url" "3.1.0"

"upper-case@^1.1.1":
  "integrity" "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="
  "resolved" "https://registry.npmmirror.com/upper-case/download/upper-case-1.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fupper-case%2Fdownload%2Fupper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@^4.2.2":
  "integrity" "sha1-lMVA4f93KVbiKZUHwBCupsiDjrA="
  "resolved" "https://registry.npmmirror.com/uri-js/download/uri-js-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "punycode" "^2.1.0"

"urijs@^1.18.12", "urijs@^1.19.0":
  "integrity" "sha1-+b4J8AxMUTS3yzz0dcHdOUUmJlo="
  "resolved" "https://registry.npmmirror.com/urijs/download/urijs-1.19.2.tgz"
  "version" "1.19.2"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-loader@^2.1.0", "url-loader@^2.2.0":
  "integrity" "sha1-4OLvZY8APvuMpBsPP/v3a6uIZYs="
  "resolved" "https://registry.npmmirror.com/url-loader/download/url-loader-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "loader-utils" "^1.2.3"
    "mime" "^2.4.4"
    "schema-utils" "^2.5.0"

"url-parse@^1.4.3":
  "integrity" "sha1-qKg1NejACjFuQDpdtKwbm4U64ng="
  "resolved" "https://registry.npmmirror.com/url-parse/download/url-parse-1.4.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furl-parse%2Fdownload%2Furl-parse-1.4.7.tgz"
  "version" "1.4.7"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"url@^0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "https://registry.npmmirror.com/url/download/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8="
  "resolved" "https://registry.npmmirror.com/use/download/use-3.1.1.tgz"
  "version" "3.1.1"

"utif@^2.0.1":
  "integrity" "sha512-Z/S1fNKCicQTf375lIP9G8Sa1H/phcysstNrrSdZKj1f9g58J4NMgb5IgiEZN9/nLMPDwF0W7hdOe9Qq2IYoLg=="
  "resolved" "https://registry.npmmirror.com/utif/-/utif-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pako" "^1.0.5"

"util-deprecate@^1.0.1", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@~1.0.0":
  "integrity" "sha1-a693dLgO6w91INi4HQeYKlmruu4="
  "resolved" "https://registry.npmmirror.com/util.promisify/download/util.promisify-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.2"
    "has-symbols" "^1.0.1"
    "object.getownpropertydescriptors" "^2.1.0"

"util.promisify@1.0.0":
  "integrity" "sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA="
  "resolved" "https://registry.npmmirror.com/util.promisify/download/util.promisify-1.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "define-properties" "^1.1.2"
    "object.getownpropertydescriptors" "^2.0.3"

"util@^0.11.0":
  "integrity" "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE="
  "resolved" "https://registry.npmmirror.com/util/download/util-0.11.1.tgz?cache=0&sync_timestamp=1588238457176&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil%2Fdownload%2Futil-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha1-evsa/lCAUkZInj23/g7TeTNqwPk="
  "resolved" "https://registry.npmmirror.com/util/download/util-0.10.3.tgz?cache=0&sync_timestamp=1588238457176&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil%2Fdownload%2Futil-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"utila@^0.4.0", "utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://registry.npmmirror.com/utila/download/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://registry.npmmirror.com/utils-merge/download/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^3.3.2", "uuid@^3.4.0":
  "integrity" "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="
  "resolved" "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz"
  "version" "3.4.0"

"uuid@^8.0.0":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"uview-ui@2.0.31":
  "integrity" "sha512-I/0fGuvtiKHH/mBb864SGYk+SJ7WaF32tsBgYgeBOsxlUp+Th+Ac2tgz2cTvsQJl6eZYWsKZ3ixiSXCAcxZ8Sw=="
  "resolved" "https://registry.npmmirror.com/uview-ui/-/uview-ui-2.0.31.tgz"
  "version" "2.0.31"

"v8-to-istanbul@^4.1.3":
  "integrity" "sha1-uXk28hwOLZmW1JheXFFW6dTknNY="
  "resolved" "https://registry.npmmirror.com/v8-to-istanbul/download/v8-to-istanbul-4.1.4.tgz"
  "version" "4.1.4"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    "convert-source-map" "^1.6.0"
    "source-map" "^0.7.3"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha1-/JH2uce6FchX9MssXe/uw51PQQo="
  "resolved" "https://registry.npmmirror.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vary@^1.1.2", "vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://registry.npmmirror.com/vary/download/vary-1.1.2.tgz"
  "version" "1.1.2"

"vendors@^1.0.0":
  "integrity" "sha1-4rgApT56Kbk1BsPPQRANFsTErY4="
  "resolved" "https://registry.npmmirror.com/vendors/download/vendors-1.0.4.tgz?cache=0&sync_timestamp=1579857147055&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvendors%2Fdownload%2Fvendors-1.0.4.tgz"
  "version" "1.0.4"

"verror@1.10.0":
  "integrity" "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA="
  "resolved" "https://registry.npmmirror.com/verror/download/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"vm-browserify@^1.0.1":
  "integrity" "sha1-eGQcSIuObKkadfUR56OzKobl3aA="
  "resolved" "https://registry.npmmirror.com/vm-browserify/download/vm-browserify-1.1.2.tgz?cache=0&sync_timestamp=1572870776965&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvm-browserify%2Fdownload%2Fvm-browserify-1.1.2.tgz"
  "version" "1.1.2"

"vue-hot-reload-api@^2.3.0":
  "integrity" "sha1-UylVzB6yCKPZkLOp+acFdGV+CPI="
  "resolved" "https://registry.npmmirror.com/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-hot-reload-api%2Fdownload%2Fvue-hot-reload-api-2.3.4.tgz"
  "version" "2.3.4"

"vue-loader@^15.6.4", "vue-loader@^15.9.1":
  "integrity" "sha1-rgH19MnGoEv/RIORLnLvkaQCwa4="
  "resolved" "https://registry.npmmirror.com/vue-loader/download/vue-loader-15.9.2.tgz?cache=0&sync_timestamp=1589274987464&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-loader%2Fdownload%2Fvue-loader-15.9.2.tgz"
  "version" "15.9.2"
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "vue-hot-reload-api" "^2.3.0"
    "vue-style-loader" "^4.1.0"

"vue-style-loader@^4.1.0", "vue-style-loader@^4.1.2":
  "integrity" "sha1-3t80mAbyXOtOZPOtfApE+6c1/Pg="
  "resolved" "https://registry.npmmirror.com/vue-style-loader/download/vue-style-loader-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-template-compiler@^2.0.0", "vue-template-compiler@^2.6.10", "vue-template-compiler@^2.6.11", "vue-template-compiler@^2.6.7":
  "integrity" "sha1-wEcE749JixUxMAGJk+VjCdRpgIA="
  "resolved" "https://registry.npmmirror.com/vue-template-compiler/download/vue-template-compiler-2.6.11.tgz"
  "version" "2.6.11"
  dependencies:
    "de-indent" "^1.0.2"
    "he" "^1.1.0"

"vue-template-es2015-compiler@^1.9.0":
  "integrity" "sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU="
  "resolved" "https://registry.npmmirror.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz"
  "version" "1.9.1"

"vue@^2.0.0", "vue@^2.6.11":
  "integrity" "sha1-dllNh31LEiNEBuhONSdcbVFBJcU="
  "resolved" "https://registry.npmmirror.com/vue/download/vue-2.6.11.tgz"
  "version" "2.6.11"

"vuex@^3.2.0":
  "integrity" "sha1-IMwIYGLXUHafzh/rs05/zurr3kU="
  "resolved" "https://registry.npmmirror.com/vuex/download/vuex-3.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvuex%2Fdownload%2Fvuex-3.4.0.tgz"
  "version" "3.4.0"

"w3c-hr-time@^1.0.1":
  "integrity" "sha1-ConN9cwVgi35w2BUNnaWPgzDCM0="
  "resolved" "https://registry.npmmirror.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz?cache=0&sync_timestamp=1583455604765&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fw3c-hr-time%2Fdownload%2Fw3c-hr-time-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "browser-process-hrtime" "^1.0.0"

"w3c-xmlserializer@^1.1.2":
  "integrity" "sha1-MEhcp9cKb9BSQgo9Ev2Q5jOc55Q="
  "resolved" "https://registry.npmmirror.com/w3c-xmlserializer/download/w3c-xmlserializer-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "domexception" "^1.0.1"
    "webidl-conversions" "^4.0.2"
    "xml-name-validator" "^3.0.0"

"walker@^1.0.7", "walker@~1.0.5":
  "integrity" "sha1-L3+bj9ENZ3JisYqITijRlhjgKPs="
  "resolved" "https://registry.npmmirror.com/walker/download/walker-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "makeerror" "1.0.x"

"watchpack-chokidar2@^2.0.0":
  "integrity" "sha1-mUihhmy71suCTeoTp+1pH2yN3/A="
  "resolved" "https://registry.npmmirror.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "chokidar" "^2.1.8"

"watchpack@^1.6.1":
  "integrity" "sha1-wC5NTUmRPD5+EiwzJTZa+dMx6ao="
  "resolved" "https://registry.npmmirror.com/watchpack/download/watchpack-1.7.2.tgz"
  "version" "1.7.2"
  dependencies:
    "graceful-fs" "^4.1.2"
    "neo-async" "^2.5.0"
  optionalDependencies:
    "chokidar" "^3.4.0"
    "watchpack-chokidar2" "^2.0.0"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98="
  "resolved" "https://registry.npmmirror.com/wbuf/download/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"wcwidth@^1.0.1":
  "integrity" "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g="
  "resolved" "https://registry.npmmirror.com/wcwidth/download/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webidl-conversions@^4.0.2":
  "integrity" "sha1-qFWYCx8LazWbodXZ+zmulB+qY60="
  "resolved" "https://registry.npmmirror.com/webidl-conversions/download/webidl-conversions-4.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebidl-conversions%2Fdownload%2Fwebidl-conversions-4.0.2.tgz"
  "version" "4.0.2"

"webpack-bundle-analyzer@^3.6.1":
  "integrity" "sha1-zms/kI2vBp/R9yZvaSy7O97ZuhY="
  "resolved" "https://registry.npmmirror.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.8.0.tgz"
  "version" "3.8.0"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-walk" "^7.1.1"
    "bfj" "^6.1.1"
    "chalk" "^2.4.1"
    "commander" "^2.18.0"
    "ejs" "^2.6.1"
    "express" "^4.16.3"
    "filesize" "^3.6.1"
    "gzip-size" "^5.0.0"
    "lodash" "^4.17.15"
    "mkdirp" "^0.5.1"
    "opener" "^1.5.1"
    "ws" "^6.0.0"

"webpack-chain@^6.4.0":
  "integrity" "sha1-IvCye2qbye48uk+eZRPPZjlANOI="
  "resolved" "https://registry.npmmirror.com/webpack-chain/download/webpack-chain-6.4.0.tgz?cache=0&sync_timestamp=1580740803516&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-chain%2Fdownload%2Fwebpack-chain-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "deepmerge" "^1.5.2"
    "javascript-stringify" "^2.0.1"

"webpack-dev-middleware@^3.7.2":
  "integrity" "sha1-ABnD23FuP6XOy/ZPKriKdLqzMfM="
  "resolved" "https://registry.npmmirror.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.2.tgz?cache=0&sync_timestamp=1582191620751&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-dev-middleware%2Fdownload%2Fwebpack-dev-middleware-3.7.2.tgz"
  "version" "3.7.2"
  dependencies:
    "memory-fs" "^0.4.1"
    "mime" "^2.4.4"
    "mkdirp" "^0.5.1"
    "range-parser" "^1.2.1"
    "webpack-log" "^2.0.0"

"webpack-dev-server@^3.10.3":
  "integrity" "sha1-jxVKO84bz9HMYY705wMniFXn/4w="
  "resolved" "https://registry.npmmirror.com/webpack-dev-server/download/webpack-dev-server-3.11.0.tgz"
  "version" "3.11.0"
  dependencies:
    "ansi-html" "0.0.7"
    "bonjour" "^3.5.0"
    "chokidar" "^2.1.8"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^1.6.0"
    "debug" "^4.1.1"
    "del" "^4.1.1"
    "express" "^4.17.1"
    "html-entities" "^1.3.1"
    "http-proxy-middleware" "0.19.1"
    "import-local" "^2.0.0"
    "internal-ip" "^4.3.0"
    "ip" "^1.1.5"
    "is-absolute-url" "^3.0.3"
    "killable" "^1.0.1"
    "loglevel" "^1.6.8"
    "opn" "^5.5.0"
    "p-retry" "^3.0.1"
    "portfinder" "^1.0.26"
    "schema-utils" "^1.0.0"
    "selfsigned" "^1.10.7"
    "semver" "^6.3.0"
    "serve-index" "^1.9.1"
    "sockjs" "0.3.20"
    "sockjs-client" "1.4.0"
    "spdy" "^4.0.2"
    "strip-ansi" "^3.0.1"
    "supports-color" "^6.1.0"
    "url" "^0.11.0"
    "webpack-dev-middleware" "^3.7.2"
    "webpack-log" "^2.0.0"
    "ws" "^6.2.1"
    "yargs" "^13.3.2"

"webpack-log@^2.0.0":
  "integrity" "sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8="
  "resolved" "https://registry.npmmirror.com/webpack-log/download/webpack-log-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-colors" "^3.0.0"
    "uuid" "^3.3.2"

"webpack-merge@^4.1.4", "webpack-merge@^4.2.2":
  "integrity" "sha1-onxS6ng9E5iv0gh/VH17nS9DY00="
  "resolved" "https://registry.npmmirror.com/webpack-merge/download/webpack-merge-4.2.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-merge%2Fdownload%2Fwebpack-merge-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "lodash" "^4.17.15"

"webpack-sources@^1.1.0", "webpack-sources@^1.3.0", "webpack-sources@^1.4.0", "webpack-sources@^1.4.1", "webpack-sources@^1.4.3":
  "integrity" "sha1-7t2OwLko+/HL/plOItLYkPMwqTM="
  "resolved" "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack@^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^3.0.0 || ^4.1.0 || ^5.0.0-0", "webpack@^4.0.0", "webpack@^4.0.0 || ^5.0.0", "webpack@^4.29.5", "webpack@^4.36.0 || ^5.0.0", "webpack@^4.4.0", "webpack@>=2", "webpack@>=4.0.0":
  "integrity" "sha1-xIVHsR1WMiTFYdrRFyyKoLimeOY="
  "resolved" "https://registry.npmmirror.com/webpack/download/webpack-4.43.0.tgz"
  "version" "4.43.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "acorn" "^6.4.1"
    "ajv" "^6.10.2"
    "ajv-keywords" "^3.4.1"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^4.1.0"
    "eslint-scope" "^4.0.3"
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^2.4.0"
    "loader-utils" "^1.2.3"
    "memory-fs" "^0.4.1"
    "micromatch" "^3.1.10"
    "mkdirp" "^0.5.3"
    "neo-async" "^2.6.1"
    "node-libs-browser" "^2.2.1"
    "schema-utils" "^1.0.0"
    "tapable" "^1.1.3"
    "terser-webpack-plugin" "^1.4.3"
    "watchpack" "^1.6.1"
    "webpack-sources" "^1.4.1"

"websocket-driver@>=0.5.1":
  "integrity" "sha1-otTg1PTxFvHmKX66WLBdQwEA6fk="
  "resolved" "https://registry.npmmirror.com/websocket-driver/download/websocket-driver-0.7.3.tgz"
  "version" "0.7.3"
  dependencies:
    "http-parser-js" ">=0.4.0 <0.4.11"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-driver@0.6.5":
  "integrity" "sha1-XLJVbOuF9Dc8bYI4qmkchFThOjY="
  "resolved" "https://registry.npmmirror.com/websocket-driver/download/websocket-driver-0.6.5.tgz"
  "version" "0.6.5"
  dependencies:
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha1-XS/yKXcAPsaHpLhwc9+7rBRszyk="
  "resolved" "https://registry.npmmirror.com/websocket-extensions/download/websocket-extensions-0.1.3.tgz"
  "version" "0.1.3"

"whatwg-encoding@^1.0.1", "whatwg-encoding@^1.0.5":
  "integrity" "sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA="
  "resolved" "https://registry.npmmirror.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "iconv-lite" "0.4.24"

"whatwg-mimetype@^2.2.0", "whatwg-mimetype@^2.3.0":
  "integrity" "sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78="
  "resolved" "https://registry.npmmirror.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz"
  "version" "2.3.0"

"whatwg-url@^7.0.0":
  "integrity" "sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY="
  "resolved" "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-7.1.0.tgz?cache=0&sync_timestamp=1588965133257&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhatwg-url%2Fdownload%2Fwhatwg-url-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "lodash.sortby" "^4.7.0"
    "tr46" "^1.0.1"
    "webidl-conversions" "^4.0.2"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "https://registry.npmmirror.com/which-module/download/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.9", "which@^1.3.1":
  "integrity" "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo="
  "resolved" "https://registry.npmmirror.com/which/download/which-1.3.1.tgz?cache=0&sync_timestamp=1574116262707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.0":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "https://registry.npmmirror.com/which/download/which-2.0.2.tgz?cache=0&sync_timestamp=1574116262707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.2":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "https://registry.npmmirror.com/which/download/which-2.0.2.tgz?cache=0&sync_timestamp=1574116262707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wide-align@^1.1.2":
  "integrity" "sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg=="
  "resolved" "https://registry.npmmirror.com/wide-align/-/wide-align-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "string-width" "^1.0.2 || 2 || 3 || 4"

"word-wrap@~1.2.3":
  "integrity" "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="
  "resolved" "https://registry.npmmirror.com/word-wrap/download/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"worker-farm@^1.7.0":
  "integrity" "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag="
  "resolved" "https://registry.npmmirror.com/worker-farm/download/worker-farm-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "errno" "~0.1.7"

"wrap-ansi@^5.1.0":
  "integrity" "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz?cache=0&sync_timestamp=1587574768060&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "string-width" "^3.0.0"
    "strip-ansi" "^5.0.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha1-6Tk7oHEC5skaOyIUePAlfNKFblM="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz?cache=0&sync_timestamp=1587574768060&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-loader@^0.2.0":
  "integrity" "sha1-ggn4fsgAR6ZXoq2rvP1iz61/Cq4="
  "resolved" "https://registry.npmmirror.com/wrap-loader/download/wrap-loader-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "loader-utils" "^1.1.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.npmmirror.com/wrappy/download/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@^3.0.0":
  "integrity" "sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug="
  "resolved" "https://registry.npmmirror.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrite-file-atomic%2Fdownload%2Fwrite-file-atomic-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "imurmurhash" "^0.1.4"
    "is-typedarray" "^1.0.0"
    "signal-exit" "^3.0.2"
    "typedarray-to-buffer" "^3.1.5"

"ws@^6.0.0":
  "integrity" "sha1-RC/fCkftZPWbal2P8TD0dI7VJPs="
  "resolved" "https://registry.npmmirror.com/ws/download/ws-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "async-limiter" "~1.0.0"

"ws@^6.2.1":
  "integrity" "sha1-RC/fCkftZPWbal2P8TD0dI7VJPs="
  "resolved" "https://registry.npmmirror.com/ws/download/ws-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "async-limiter" "~1.0.0"

"ws@^7.0.0", "ws@^7.1.2", "ws@^7.2.3":
  "integrity" "sha1-Sy9/IZs9Nze8Gi+/FF2CW5TTj/0="
  "resolved" "https://registry.npmmirror.com/ws/download/ws-7.3.0.tgz"
  "version" "7.3.0"

"ws@~6.1.0":
  "integrity" "sha1-W1yIAK+rkl6UzLKdFTyNAsF3bvk="
  "resolved" "https://registry.npmmirror.com/ws/download/ws-6.1.4.tgz"
  "version" "6.1.4"
  dependencies:
    "async-limiter" "~1.0.0"

"x-domhandler@^2.4.2":
  "integrity" "sha1-Ia+y1xl3EYaI5J4FwwiUSXzj1ek="
  "resolved" "https://registry.npmmirror.com/x-domhandler/download/x-domhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1"

"xhr@^2.0.1":
  "integrity" "sha512-/eCGLb5rxjx5e3mF1A7s+pLlR6CGyqWN91fv1JgER5mVWg1MZmlhBvy9kjcsOdRk8RrIujotWyJamfyrp+WIcA=="
  "resolved" "https://registry.npmmirror.com/xhr/-/xhr-2.6.0.tgz"
  "version" "2.6.0"
  dependencies:
    "global" "~4.4.0"
    "is-function" "^1.0.1"
    "parse-headers" "^2.0.0"
    "xtend" "^4.0.0"

"xml-name-validator@^3.0.0":
  "integrity" "sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo="
  "resolved" "https://registry.npmmirror.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz"
  "version" "3.0.0"

"xml-parse-from-string@^1.0.0":
  "integrity" "sha512-ErcKwJTF54uRzzNMXq2X5sMIy88zJvfN2DmdoQvy7PAFJ+tPRU6ydWuOKNMyfmOjdyBQTFREi60s0Y0SyI0G0g=="
  "resolved" "https://registry.npmmirror.com/xml-parse-from-string/-/xml-parse-from-string-1.0.1.tgz"
  "version" "1.0.1"

"xml2js@^0.5.0":
  "integrity" "sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA=="
  "resolved" "https://registry.npmmirror.com/xml2js/-/xml2js-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "sax" ">=0.6.0"
    "xmlbuilder" "~11.0.0"

"xmlbuilder@~11.0.0":
  "integrity" "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA=="
  "resolved" "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz"
  "version" "11.0.1"

"xmlchars@^2.1.1":
  "integrity" "sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs="
  "resolved" "https://registry.npmmirror.com/xmlchars/download/xmlchars-2.2.0.tgz"
  "version" "2.2.0"

"xmlhttprequest-ssl@~1.5.4":
  "integrity" "sha1-wodrBhaKrcQOV9l+gRkayPQ5iz4="
  "resolved" "https://registry.npmmirror.com/xmlhttprequest-ssl/download/xmlhttprequest-ssl-1.5.5.tgz"
  "version" "1.5.5"

"xregexp@4.0.0":
  "integrity" "sha1-5pgYneSd0qGMxWh7BeF8jkOUMCA="
  "resolved" "https://registry.npmmirror.com/xregexp/download/xregexp-4.0.0.tgz"
  "version" "4.0.0"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="
  "resolved" "https://registry.npmmirror.com/xtend/download/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^4.0.0":
  "integrity" "sha1-le+U+F7MgdAHwmThkKEg8KPIVms="
  "resolved" "https://registry.npmmirror.com/y18n/download/y18n-4.0.0.tgz"
  "version" "4.0.0"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "https://registry.npmmirror.com/yallist/download/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.2":
  "integrity" "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="
  "resolved" "https://registry.npmmirror.com/yallist/download/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI="
  "resolved" "https://registry.npmmirror.com/yallist/download/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yargs-parser@^13.1.2":
  "integrity" "sha1-Ew8JcC667vJlDVTObj5XBvek+zg="
  "resolved" "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-13.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-13.1.2.tgz"
  "version" "13.1.2"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^18.1.1":
  "integrity" "sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A="
  "resolved" "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-18.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-18.1.3.tgz"
  "version" "18.1.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs@^13.3.2":
  "integrity" "sha1-rX/+/sGqWVZayRX4Lcyzipwxot0="
  "resolved" "https://registry.npmmirror.com/yargs/download/yargs-13.3.2.tgz?cache=0&sync_timestamp=1589566384961&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-13.3.2.tgz"
  "version" "13.3.2"
  dependencies:
    "cliui" "^5.0.0"
    "find-up" "^3.0.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^3.0.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^13.1.2"

"yargs@^15.0.0", "yargs@^15.3.1":
  "integrity" "sha1-lQW0cnY5Y+VK/mAUitJ6MwgY6Ys="
  "resolved" "https://registry.npmmirror.com/yargs/download/yargs-15.3.1.tgz?cache=0&sync_timestamp=1589566384961&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-15.3.1.tgz"
  "version" "15.3.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.1"

"yauzl@^2.10.0":
  "integrity" "sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g=="
  "resolved" "https://registry.npmmirror.com/yauzl/-/yauzl-2.10.0.tgz"
  "version" "2.10.0"
  dependencies:
    "buffer-crc32" "~0.2.3"
    "fd-slicer" "~1.1.0"

"yeast@0.1.2":
  "integrity" "sha1-AI4G2AlDIMNy28L47XagymyKxBk="
  "resolved" "https://registry.npmmirror.com/yeast/download/yeast-0.1.2.tgz"
  "version" "0.1.2"

"ylru@^1.2.0":
  "integrity" "sha1-9Xa2M0FUeYnB3nuiiHYJI7J/6E8="
  "resolved" "https://registry.npmmirror.com/ylru/download/ylru-1.2.1.tgz"
  "version" "1.2.1"
